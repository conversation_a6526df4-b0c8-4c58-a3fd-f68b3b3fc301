@import '../popupTemplate/popupTemplate.wxss';

page {}

.background {
  border: 0px solid red;
  height: 549rpx;
}

/* .background_one {
  display: flex;
  justify-content: center;
  align-items: center;
} */

.background_two {
  border: 0px solid red;
  margin: 0px auto;
  width: 434rpx;
  height: 95rpx;
  font-size: 84rpx;
  font-family: FZPinShangHeiS-DB-GB;
  font-weight: 400;
  line-height: 95rpx;
  color: #FFF6C3;
  opacity: 1;
}

.background_three {
  margin: 0px auto;
  margin-top: 10rpx;
  width: 312rpx;
  height: 50rpx;
  font-size: 36rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 50rpx;
  color: #FFF6C3;
  opacity: 1;
}

.strategy {
  border: 0px solid red;
  text-align: center;
  width: 100rpx;
  height: 53rpx;
  float: right;
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 53rpx;
  color: #FE5B37;
  opacity: 1;
  background: #fff;
  border-radius: 44rpx 0px 0px 44rpx;
  margin-top: 55rpx;
}

.colStatus {
  padding-bottom:24rpx;
  width: 710rpx;
  margin: 0px auto;
  background: #FFFFFF;
  opacity: 1;
  border-radius: 16px;

}

.notice_one {
  border: 0px solid red;
  width: 600rpx;
  height: 33rpx;
  margin: 0px auto;
  padding-top: 20rpx;
}

.notice_two {
  border: 0px solid green;
  width: 28rpx;
  height: 30rpx;
  float: left
}

.notice_three {
  border: 0px solid green;
  width: 550rpx;
  float: right;
}

.example {
  display: block;
  width: 100%;
  height: 33rpx;
  overflow: hidden;
}

.marquee_box {
  width: 100%;
  position: relative;

}

.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
  font-family: PingFang SC;
  font-weight: 400;
  color: #333333;
}

.collect_one {
  border: 0px solid grey;
  width: 600rpx;
  height: 300rpx;
  margin: 0px auto;
  border-radius: 16rpx;
}

.collect_two {
  width: 132rpx;
  height: 132rpx;
  margin: 0px auto;
  padding-top: 38rpx;
}

.collect_three {
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #AA641F;
  opacity: 1;
  text-align: center;
  margin-top: 16rpx;
}

.collect_four {
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #AA641F;
  opacity: 1;
  text-align: center;
}

.collect_five {
  margin: 0px auto;
  width: 140rpx;
  height: 140rpx;
  font-size: 86rpx;
  font-family: PingFang SC;
  font-weight: bold;
  line-height: 140rpx;
  opacity: 1;
  text-align: center;
}

.collect_six {
  margin-top: 28rpx;
  border-radius: 49rpx;
  width: 232rpx;
  height: 64rpx;
  background: #F36743;
  opacity: 1;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 64rpx;
  color: #FFFFFF;
}

.collect_seven {
  padding-top: 38rpx;
  font-size: 46rpx;
  font-family: PingFang SC;
  color: #FE5406;
  opacity: 1;
  text-align: center;
}

.collect_eight {
  font-size: 46rpx;
  font-family: PingFang SC;
  color: #FE5406;
  opacity: 1;
  text-align: center;
}

.collect_nine {
  margin-top: 28rpx;
  border-radius: 49rpx;
  width: 232rpx;
  height: 64rpx;
  background: #FE5406;
  opacity: 1;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 64rpx;
  color: #FFFFFF;
}

.collect_ten {
  text-align: center;
  padding-top: 38rpx;
  font-size: 40rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #AA641F;
  opacity: 1;
}

.collect1_one {
  width: 100rpx;
  height: 100rpx;
  margin: 0px auto;
  margin-top: 30rpx;
}

.collect1_two {
  text-align: center;
  font-size: 30rpx;
  margin-top: 10rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #525252;
  opacity: 1;
}

.collect1_three {
  width: 600rpx;
  height: 112rpx;
  margin: 0px auto;
  margin-top: 45rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.collect1_four {
  position: relative;
  border: 0px solid red;
  width: 112rpx;
  height: 100%;
  font-size: 70rpx;
  font-family: PingFang SC;
  font-weight: bold;
  line-height: 112rpx;
  opacity: 1;
  text-align: center;
}

.collect1_five {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  background: #FF7E00;
  border-radius: 50%;
  color: #fff;
  display: block;
  cursor: pointer;
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  bottom: 90rpx;
  left: 80rpx;
  line-height: 40rpx;
}

.collect1_six {
  padding-top: 24rpx;
  text-align: center;
  height: 50rpx;
  font-size: 36rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 63rpx;
  color: #843706;
  opacity: 1;
}

.collect1_seven {
  margin: 0px auto;
  margin-top: 32rpx;
  width: 600rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.collect1_eight {
  /*width: 375rpx;*/
  height: 100%;
  margin-left: 32rpx;
}

.collect1_nine {
  width: 100%;
  height: auto;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #AA641F;
  opacity: 1;
}

.collect1_ten {
  width: 100%;
  height: auto;
  font-size: 23rpx;
  font-family: PingFang SC;
  font-weight: 300;
  line-height: 44rpx;
  color: #AA641F;
  opacity: 1;
}

.collect2_one {
  margin-right: 0px;
  /*width: 140rpx;*/
  height: 56rpx;
  background: #FE5406;
  opacity: 1;
  border-radius: 36rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  line-height: 56rpx;
  color: #FFFFFF;
}

.collect2_two {
  width: 312rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #FF9002 0%, #FE5406 100%);
  opacity: 1;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 300;
  line-height: 80rpx;
  color: #FFFFFF;
  text-align: center;
}

.collect2_three {
  display: flex;
  align-items: center;
  justify-items: center;
  width: 650rpx;
  margin: 0px auto;
  height: 160rpx;
}

.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.shareBox {
  width: 710rpx;
  height: 360rpx;
  z-index: 999;
  text-align: center;
  position: fixed;
  background: #fff;
  bottom: 0;
  left: 0;
  right: 0;
  color: #333333;
  font-size: 24rpx;
  padding: 20rpx;
}

.closeTemp {
  position: absolute;
  right: 30rpx;
  top: 30rpx
}
.flex-wrp {
  flex-direction: row;
  background: #fff;
  padding-top:30rpx;
  padding-left:10rpx;
  padding-right:10rpx;

}

.flex-item {
  width: 356rpx;
  float: left;
  background: #fff;
  margin-bottom: 10rpx;
  border-radius:6rpx;
  box-shadow:1px 1px 1px 1px #f8f8f8;
  border:1rpx solid #dedede;
}
.flex-item:nth-child(2n+1) {
  margin-right: 10rpx;
}

.goods_tips {
  color: #958353;
  border: 1px solid #bcb396;
  top: 20rpx;
  left: 20rpx;
  width: 24rpx;
  height: 124rpx;
  line-height: 32rpx;
  display: block;
  position: absolute;
  font-size: 24rpx;
  word-wrap: break-word;
  padding: 8rpx;
  border-radius: 6rpx;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.soonlist{ 
  font-size: 28rpx;
  writing-mode:vertical-lr; 
  position: absolute;
  top:10rpx;
  left: 10rpx;
  z-index: 2;
  color: #988250;
  border: 1px solid #988250;
  border-radius: 10rpx;
  padding: 10rpx 0; 
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  border-top-right-radius:10rpx;
  border-top-left-radius:10rpx;
}


.goods_title {
  display: block;
  /**height: 28px;
  line-height: 28px;**/
  overflow: hidden;
  font-size: 32rpx;
  margin: 10rpx 0;
  text-align:center;
}

.goods_price {
  display: block;
  color: #DE535F;
  font-size: 24rpx;
  margin: 8rpx 0;
  text-align:center;
}
.good_shopCart{
  text-align:center;
  background:#DE535F;
  display:inline-block;
  color:#fff;
  margin:8rpx 0 30rpx 0;
  padding:4rpx 20rpx;
  border-radius:6rpx;
  margin-left:calc(30% - 20rpx);
  font-size:24rpx;
}
.pic_two{
  display:flex;
  width:700rpx;
  margin:0 auto;
  flex-wrap: wrap;
}
.pic_two > view:nth-child(2n+1){
  margin-right:20rpx;
}
.pic_two  .pic_two_goods{
  width:340rpx;
  background:#fff;
  margin-bottom:20rpx;
}
.pic_two  .pic_two_goods image{
  width:340rpx;
  height:340rpx;
}
.commodity_box3{
  width:340rpx;
  background:#fff;
  margin-bottom:20rpx;
}
.commodity_box3 image{
  width:340rpx;
  height:340rpx;
}
.line_price{
  font-size:18rpx;
  text-decoration: line-through;
  color:#666;
  margin-left:6rpx;
}
.desc_title{
  font-size:28rpx;
  height:80rpx;
  overflow: hidden;
}
.desc_price{
  margin-top:20rpx;
}
.price_tag{
  color:#FF7E00;
  font-size:22rpx;
}
.price_inner{
  color:#FF7E00;
  font-size:32rpx;
}
.line_price{
  font-size:18rpx;
  text-decoration: line-through;
  color:#666;
  margin-left:6rpx;
}
.goods_desc{
  padding:14rpx 10rpx;
}
.s_w{
  display:flex;
  justify-content:space-between;
  margin:16rpx 0;
  align-items: center;
}
.s_w image{
  margin-right:16rpx;
}
.s_w view{
  display:flex;
  align-items: center;
}
.act_btn{
  background:#ff6600;
  padding:6rpx 20rpx;
  color:#fff;
  border-radius:30rpx;
  font-size:28rpx;
}
.grey_b{
  background:#c4c4c4;
}