// pages/collectStrategy/collectStrategy.js
const app = getApp();
const popup = require('../popupTemplate/popupTemplate.js')
const http = require('../../utils/http')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    money: app.imageUrl + 'sign_spmoney.png',
    intergral: app.imageUrl + 'sign_integral.png',
    coupon: app.imageUrl + 'sign_coupon.png'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.findConfig();
  },
  /*查询配置*/
  findConfig: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    http.get({
      urlName: 'activity',
      url: 'collect/findConfig',
      showLoading: false,
      data: {
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId()
      },
      success: (res) => {
        var collectConfig = res.collectConfig;
        that.setData({
          collectConfig: collectConfig
        })
      }
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})