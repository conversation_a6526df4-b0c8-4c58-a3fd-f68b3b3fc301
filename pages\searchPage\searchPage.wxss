page {
  width: 100%;
  height: 100%;
  background:#f5f5f5;
}

.contant_box {
  width: 100%;
  height: 100%;
}
/* 定位 搜索 */
.location_box {
  height: 52rpx;
  position: fixed;
  top: 0px;
  left: 0;
  right:0;
  font-size: 28rpx;
  line-height: 26px;
  background: #fff;
  z-index:1999;
  display:flex;
  align-items: center;
  padding:40rpx 30rpx 30rpx;
}
.s_inner{
  display:flex;
  flex:1;
  height:70rpx;
  border-radius:40rpx;
  background:#fff;
  border:1px solid #ededed;
  background:#ededed;
  align-items: center;
}
/* 搜索框 */

.remove_box {
  padding: 5px 10px;
}

.remove_box text {
  font-size: 13px;
  float: left;
  line-height: 30px;
}

.remove_box image {
  width: 22px;
  height: 22px;
  float: right;
  margin-bottom: 8px;
  opacity: 0.9;
}

.search_box {
  height: 26px;
  background: #fff;
  color: #848489;
  position: fixed;
  font-size:28rpx;
  padding:24rpx 20rpx 28rpx;
  top:0;
  left:0;
  right:0;
  z-index:9999;
}

.search_box icon {
  float: left;
  position: absolute;
  top: 36rpx;
  left: 60px;
  z-index: 10;
  line-height: 26px;
}

.search_box input {
  width: 80%;
  height: 62rpx;
  line-height: 62rpx;
  background: #ededed;
  color: #272727;
  font-size:28rpx;
  padding-left: 40px;
  float: left;
  border-radius: 14rpx;
}

.search_box text {
  line-height: 60rpx;
  float: right;
  font-size: 28rpx;
  padding-left:20rpx;
}

.goods_name {
  padding: 0 10px;
  color: #666
}

.goods_name text {
  padding: 6rpx 10rpx;
  float: left;
  margin: 0 15px 15px 0;
  border: 1px solid #666;
  border-radius: 5px;
  font-size: 14px;
}

/* 商品 */

.goods_box {
  /**background: #f5f5f5;**/
  width: 100%;
}

.flex-wrp {
  flex-direction: row;
  /**background: #f0f0f0;**/
}

.flex-item {
  width: 46%;
  float: left;
  background: #fff;
  margin-bottom:20rpx;
  border-radius:20rpx;
  padding-bottom:26rpx;
}

.flex-item {
  margin-left:2.6%;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-top-left-radius:20rpx;
  border-top-right-radius:20rpx;
}

.goods_title {
  display: block;
  /**height: 28px;
  line-height: 28px;**/
  overflow: hidden;
  font-size: 30rpx;
  margin: 0 5px;
  height:70rpx;
  line-height:34rpx;
  margin-top:12rpx;
}

.goods_price {
  color: #FF7E00;
  font-size:26rpx;
  margin-left:10rpx;
}

.tips_pic {
  display: block;
  margin: 0 auto;
}
/*加入购物车*/
.scroll_blo {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  right:0;
  z-index: 99999;
  background:#fff;
  border-top-left-radius:30rpx;
  border-top-right-radius:30rpx;
  /*padding-bottom: 46px;*/
}

.addgoods_box {
  width: 100%;
  height: 60px;
  /* border-bottom: 1px solid #e5e5e5; */
  background: #fff;
}

.addgoods_pic {
  width: 86px;
  height: 86px;
  background: #fff;
  position: absolute;
  top: -43px;
  left: 15px;
  border: 1px solid #ccc;
  /* overflow: hidden; */
}

.addgoods_pic image {
  width: 100%;
  height: 100%;
}

.addgoods_title,
.addgoods_price {
  height: 20px;
  overflow: hidden;
  white-space: nowrap;
  padding-right: 30px;
  text-overflow: ellipsis;
  padding-top: 3px;
}

.addgoods_title {
  font-size: 14px;
  background: #fff;
  color: #000;
}

.addgoods_price {
  color: #FF7E00;
  font-size: 13px;
}

.goods_classify {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 10px 5px 10px;
}

.goods_classify label {
  margin-bottom: 10px;
  line-height: 28px;
  font-size: 14px;
}

.goods_classify view text {
  float: left;
  padding: 0 16px;
  color: #333;
  background: #ececec;
  font-size: 12px;
  margin-bottom: 8px;
  margin-right: 10px;
  line-height: 28px;
  border-radius: 5px;
}

.addgoods_number {
  width: 100%;
  background: #fff;
  padding-top: 15px;
  padding-bottom: 15px;
  /**border-bottom: 1px solid #e5e5e5;**/
}

.limited_quantity {
  font-size: 13px;
  float: left;
  padding-left: 15px;
  padding-top: 5px;
}

.stock {
  font-size: 13px;
  float: right;
  margin-right: 5px;
  margin-top: 4px;
}

.plus_minus {
  float: right;
  padding-right: 15px;
  margin-top: 3px;
}

.minus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 22px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 24px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_minus input {
  width: 50px;
  height: 26px;
  line-height: 10px;
  background: #e4e4e4;
  float: left;
  border: none;
  font-size: 13px;
  margin: 0 5px;
  text-align: center;
  color: #333;
}

.pay-add-to-shoppingcart {
  display: block;
  padding: 0px;
  font-size: 14px;
  line-height: 44px;
  height: 44px;
  border-radius: 0px;
  flex: 1;
  color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
}

.page-dialog-close {
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 10;
  display: block;
  width: 20px;
  height: 20px;
}

.code {
  width: 80%;
  margin-left: 5%;
}

.outCode {
  width: 55% !important;
}
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.active_classify{
  background: #FFF8F9 !important;
  color: #FF7E00 !important;
  border: 1px solid #FF7E00;
  border-radius: 10rpx;
}
/*嵌套的template*/
.mark1{
  align-items:flex-end;
  position:absolute;
  bottom:0;
  left:0;
  right:0;
  display:flex;
}
.mark1_l{
  border:3rpx solid #db2f04;
  border-left:none;
  border-bottom:none;
  font-size:30rpx;
  z-index:10;
  border-radius:100rpx 100rpx 100rpx 0;
  width:100rpx;
  height:100rpx;
  background:#f5f5f5;
  color:#db2f04;
  text-align:center;
}
.mark1_l view{
  padding:8rpx;
  /*padding-left:20rpx;*/
  line-height:40rpx;
}
.mark1_r{
  margin-left:-50rpx;
  padding-left:50rpx;
  display:flex;
  flex:1;
  height:50rpx;
  line-height:50rpx;
  font-size:30rpx;
  background:#db2f04;
  color:#fff; 
  z-index:9;
}
/*一排三个字号小一号*/
.commodity_box4 .mark1_l,.commodity_box2 .mark1_l,.slide_wrap .mark1_l,.oneGoods .mark1_l{
  border-radius:60rpx 60rpx 60rpx 0;
  width:60rpx;
  height:60rpx;
  font-size:18rpx;
}
.commodity_box4 .mark1_l view,.commodity_box2 .mark1_l view,.slide_wrap .mark1_l view,.oneGoods .mark1_l view{
  padding:4rpx;
  padding-left:10rpx;
}
.commodity_box4 .mark1_r,.commodity_box2 .mark1_r,.slide_wrap .mark1_r,.oneGoods .mark1_r{
  height:40rpx;
  line-height:40rpx;
  font-size:18rpx;
}
/**/
.mark2_1 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 0rpx;
  left: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;

}

.mark2_2 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 0rpx;
  left: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
}

.mark2_3 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.mark2_4 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 0rpx;
  right: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.mark_1 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 30rpx;
  left: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
  z-index:10;
}

.mark_2 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 30rpx;
  left: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
  z-index:10;
}

.mark_3 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 30rpx;
  right: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
  z-index:10;
}

.mark_4 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 30rpx;
  right: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
  z-index:10;
}

.showTag{
  z-index:99999;position:absolute;background:#ddd;top:0;left:0;right:0;bottom:0;opacity:0.7;
}
.tag_content{
  /*display:flex;
  flex-wrap:wrap;
  align-items:center;
  justify-content:center;*/
  z-index:99999;
  position:absolute;
  top:10%;
  left:5%;
  width:90%;
  background:#fff;
  border-radius:20rpx;
  padding:20rpx 0;
}
.tag_content::after {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-bottom-color: #fff;
  top: -14px;
  left: var(--topLeft);
}
.tagInner{
  display:flex;
  justify-content: space-around;
  align-items: center;
  padding:0 10rpx;
}
.tagInner>view{
  align-items: center;
  flex:1;
  padding:12rpx 0rpx;
  background:#f6f6f6;
  margin:10rpx;
  text-align:center;
  font-size:24rpx;
  border-radius:10rpx;
}
.active_tag{
  background:#FFF8F9;
  border:1px solid #ff6600;
  border-radius:10rpx;
}
.g_t{
  font-size:22rpx;
  padding:2rpx 8rpx;
  border-radius:6rpx;
  color:#ff6600;
  border:1px solid #ff6600;
}