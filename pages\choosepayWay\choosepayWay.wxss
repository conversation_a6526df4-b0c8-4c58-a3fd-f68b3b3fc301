.cardWrap{
  padding:40rpx 0;
  border-radius:10rpx;
  width:80%;
  margin:0 auto;
  margin-bottom:20rpx;
  position:relative;
  height:240rpx;
}
.cardWrap image{
  border-radius:10rpx;
  z-index:-1;
  position:absolute;
  width:100%;
  top:0;
  left:0;
  right:0;
  bottom:0;
}
.card_title{
  font-size:26rpx;
  margin-left:20rpx;
  text-align:center;
  color:#fff;
}
.card_title text{
  color:#fff;
  float:right;
  margin-right:20rpx;
}
.card_no{
  margin-top:20rpx;
  font-size:26rpx;
  text-align:left;
  margin-left:20rpx;
}
.amount_wrap{
  margin:40rpx 0;
  text-align:center;
  color:#fff;
}
.remain_amount{
  font-size:26rpx;
}
.remain_amount text{
  font-size:32rpx;
  font-weight:bold;
}
.remain_score{
  font-size:26rpx;
  margin-left:40rpx;
}
.remain_score text{
  font-size:32rpx;
  font-weight:bold;
}
.validDate{
  font-size:26rpx;
  margin-top:20rpx;
  margin-right:20rpx;
  text-align:right;
  color:#fff;
}
.card_id{
  text-align:left;
  padding-top:20rpx;
  padding-left:20rpx;
  color:#fff;
  font-size:24rpx;
}
.cardTip{
  font-weight:bold;
  text-align:center;
  font-size:32rpx;
  padding-bottom:40rpx;
}
.way_checked{
  float:left;
  margin-left:20rpx;
  margin-right:10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #FF7E00;
  background: #FF7E00;
  margin-top:100rpx;

}
.way_checked image,.way_unchecked image{
  width: 80%;
  height: 70%;
  margin-left: -16rpx;
  margin-bottom: 2rpx;
}
.way_unchecked{
  float:right;
  margin-right:10px;
  width:16px;
  height:16px;
  border-radius:50%;
  border:1px solid #ddd;
  background:#fff;
  margin-top:28rpx;
}
.wechat_checked{
  margin-top:0;
}
.confirm_btn{
  position:fixed;
  padding:30rpx 0;
  color:#fff;
  text-align:center;
  bottom:0;
  width:100%;
  background:#FF7E00;
}