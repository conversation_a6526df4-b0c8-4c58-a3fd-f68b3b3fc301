<view bindtap='getUserLocation' hidden='{{localtionHidden}}' class="location_txt">点击开启定位获取店铺位置</view>
<view class='search_box' hidden='{{!localtionHidden}}'>
	<icon type="search" size='16' />
	<input placeholder='请输入门店' bindinput='storeNameBindInput'></input>
	<text bindtap='searchBindTap'>搜索</text>
</view>
<view class="shopWrap" hidden='{{!localtionHidden}}'>
	<block wx:key="unique" wx:for="{{showReturnStoreList}}" wx:for-item="store">
		<!--单个门店-->
		<view style="margin:16rpx 0rpx;backgrouund:#fff;">
			<view class="oneShop clearfix" data-latitude="{{store.latitude}}" data-longitude="{{store.longitude}}"
				bindtap='selectStoreBindTap' data-name='{{store.storeName}}' data-id='{{store.id}}'
				data-address="{{store.province+store.city+store.area+store.storeAddress}}">
				<view style="float:left">
					<image class='substore_logo' src='{{store.storeLogo}}'></image>
				</view>
				<view style="float:left" style="width:100%;">
					<label class="store_name">{{store.storeName}}
						<text class="disposition">距离{{store.storeDistance}}km</text>
					</label>
					<view>
						<label class="storeDetail">
							{{store.province+store.city+store.area+store.storeAddress}}
						</label>
					</view>
					<view>
						<label class="detail_append">
							联系人:{{store.storeMan}}
						</label>
						<label class="detail_append">
							电话:{{store.storePhone}}
						</label>
					</view>
				</view>
			</view>
			<view style="padding:20rpx;background:#fff;">
				<view bindtap='makePhoneBindTap' data-phone='{{store.storePhone}}'
					style="display:inline-block;width:50%;text-align:center;font-size:26rpx;">
					<image style="margin:0 6rpx;vertical-align:middle;width:36rpx" mode="widthFix" src="{{phone}}"></image>电话
				</view>
				<view data-name="{{store.storeName}}" data-address="{{store.province+store.city+store.area+store.storeAddress}}"
					data-latitude="{{store.latitude}}" data-longitude="{{store.longitude}}" bindtap="startNavigationBindTap"
					style="display:inline-block;width:50%;text-align:center;font-size:26rpx;">
					<image style="margin:0 6rpx; vertical-align:middle;width:36rpx" mode="widthFix" src="{{navi}}"></image>导航
				</view>
			</view>
		</view>
		<!--单个门店-->
	</block>
</view>