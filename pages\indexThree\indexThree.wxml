<import src="../CouponTem/CouponTem.wxml" />
<wxs src="../../wxs/subutil.wxs" module="tools" />
<!-- 券弹出层 -->
<view class="pop_bg" hidden='{{putCardHidden}}'></view>
<block wx:if="{{orderFlag}}">
	<template is="coupon5" data="{{img7,storeCardList}}" />
</block>
<!-- 券弹出层 -->
<test id="test"></test>
<view>
	<view class="tab-h">
		<view class="tab-item {{currentTab==0?'active':''}}" data-current="0" bindtap="swichNav">
			全部
		</view>
		<view class="tab-item {{currentTab==2?'active':''}}" data-current="2" bindtap="swichNav">
			待付款
		</view>
		<view class="tab-item {{currentTab==3?'active':''}}" data-current="3" bindtap="swichNav">
			待发货
		</view>
		<view class="tab-item {{currentTab==4?'active':''}}" data-current="4" bindtap="swichNav">
			待收货
		</view>
		<view class="tab-item {{currentTab==7?'active':''}}" data-current="7" bindtap="swichNav">
			待评价
		</view>
	</view>
	<view class="tab-content" current="{{currentTab}}" duration="300" bindchange="switchTab"
		style="height:{{winHeight}}rpx">
		<scroll-view scroll-y="true" class="scoll-h" bindscrolltoupper="DingBu" bindscrolltolower="DiBu">
			<block wx:if="{{orderList==null||orderList.length<=0}}">
				<image class='order_none' src='{{order_none}}' mode='widthFix' style="margin-top:300rpx;"></image>
				<view style="text-align:center;font-size:30rpx;color:#666;">暂无订单</view>
			</block>
			<view style='width:100%;'>
				<!-- 单个订单 -->
				<block wx:key="unique" wx:for="{{orderList}}" wx:for-item="order">
					<view class='single_order'>
						<label class='order_top'>
							<text>订单编号: {{order.orderNo}}</text>
							<image lazy-load="true" bindtap='verificationBindTap' data-orderId='{{order.orderId}}'
								style="vertical-align:top;width:38rpx;height:38rpx;margin-left:20rpx;" mode="widthFix"
								src="{{orderCode}}"></image>
							<text class='goods_static'>{{orderStatus[order.orderStatus]}}</text>
						</label>
						<block wx:key="unique" wx:for-index="goodsIndex" wx:for="{{order.orderDetail}}" wx:for-item="goods">
							<view class='goods_detail' bindtap='queryLogisticsBindTap' data-orderId='{{order.orderId}}'>
								<image class='goods_pic' src='{{goods.commodityMainPic==""?goods_pic:goods.commodityMainPic}}'></image>
								<view class='right_detail'>
									<view class='right_top' style="display:flex;justify-content: space-between;">
										<view class='goods_name'>{{goods.commodityName}}</view>
										<view>
											<text class='goods_number' hidden="{{goods.commoditySendOtNum>0?false:true}}">
												{{goods.commoditySendOtNum}}{{goods.commodityOtUnit}}
											</text>
											<text class='goods_number' style="margin:0" hidden="{{goods.commoditySendOmNum>0?false:true}}">
												{{goods.commoditySendOmNum}}{{goods.commodityOmUnit}}
											</text>
											<text style="float:right">x</text>
										</view>
									</view>
									<!-- sku属性位置 -->
									<label style='font-size:14px;'>
										<block wx:for="{{goods.skuUnitList}}" wx:for-item="sku" wx:key="" wx:for-index="skuIdx">
											<block wx:if="{{goods.skuUnitList.length-1==skuIdx}}">
												{{sku.skuName}}:{{sku.skuValue}}
											</block>
											<block wx:else>{{sku.skuName}}:{{sku.skuValue}};</block>
										</block>
									</label>
									<!-- sku属性位置 -->
									<label class='right_bottom'>
										<block wx:if="{{goods.commodityType==4}}">
											<label class='package_number'>赠品</label>
										</block>
										<block wx:else>
											<block wx:if="{{order.orderBillType!=3}}">
												<text class='goods_tips' style="color:#FF7E00"
													hidden="{{goods.commoditySendOmNum>0?false:true}}">￥{{tools.sub.formatAmount(goods.commoditySendOmPrice,2)}}/{{goods.commodityOmUnit}}
												</text>
												<text class='goods_tips' style="color:#FF7E00"
													hidden="{{goods.commoditySendOtNum>0?false:true}}">{{tools.sub.formatAmount(goods.commoditySendOtPrice,2)}}/{{goods.commodityOtUnit}}
												</text>
											</block>
											<block wx:if="{{goods.commodityLargessNum>0}}">
												<text class='package_number'>
													包含赠品{{goods.commodityLargessNum}}{{goods.commodityLargessUnit}}
												</text>
											</block>
										</block>
									</label>
								</view>
							</view>
						</block>
						<view class='detail_box'>
							<text class='order_time'>下单时间:{{order.orderGenerateDate}}</text>
							<block wx:if="{{order.orderBillType==3}}">
								<label class='order_detail'>
									{{order.exchangeOrderTotal[0].payMoney}}积分
									<text>(含运费 ￥{{order.orderDistributionPay}})</text>
								</label>
							</block>
							<block wx:else>
								<label class='order_detail'>
									合计￥{{tools.sub.formatAmount(order.orderTotalMoney,2)}}
									<text>(含运费 ￥{{tools.sub.formatAmount(order.orderDistributionPay,2)}})</text>
								</label>
							</block>
						</view>
						<view class='button_box'>
							<block wx:if="{{order.orderStatus==2}}">
								<button bindtap='cancelOrderformSubmit' data-orderId='{{order.orderId}}'>
									取消订单
								</button>
								<button bindtap='nowPayBindTap' data-orderNo='{{order.orderNo}}' class='button2'>
									立即付款
								</button>
							</block>
							<block wx:if="{{order.orderStatus==12}}">
								<button bindtap='cancelOrderformSubmit' data-orderId='{{order.orderId}}'>
									取消订单
								</button>
							</block>
							<block wx:elif="{{order.orderStatus==3}}">
								<block wx:if="{{order.orderRejectedStatus==0}}">
									<button bindtap='appleyReturnMoneyBindTap' data-orderId='{{order.orderId}}'>
										申请退款
									</button>
								</block>
								<block wx:if="{{order.orderRejectedStatus==6}}">
									<text class='goods_static'>正在申请退款</text>
								</block>
								<block wx:if="{{order.orderRejectedStatus==7}}">
									<text class='goods_static'>退款成功</text>
								</block>
								<!-- <button bindtap='queryLogisticsBindTap' data-orderId='{{order.orderId}}'>查看物流</button>
          +3        <block wx:if="order.orderPayStatus==1">
                    <button bindtap='returnGoodsBindTap'>申请退款</button>
                  </block> -->
								<block wx:if="{{order.orderDistributionWay=='SELF_EXTRACT'}}">
									<button style="color:#FF7E00 !important;" bindtap='selfMentionBindTap'
										data-orderId='{{order.orderId}}'>
										提货码
									</button>
								</block>
							</block>
							<block wx:elif="{{order.orderStatus==4}}">
								<block wx:if="{{order.orderDeliver[0].deliverWay == 'SELF_DISTRIBUTION'}}">
									<button bindtap='queryDistributionBindTap' data-orderId='{{order.orderId}}'>
										查看物流
									</button>
								</block>
								<block wx:else>
									<button bindtap='queryLogisticsBindTap1' data-totalmoney='{{tools.sub.formatAmount(order.orderTotalMoney,2)}}' data-orderId='{{order.orderId}}'>
										查看物流
									</button>
								</block>
								<button bindtap='receiptGoodsBindTap' data-orderId='{{order.orderId}}' class='button2'>
									确认收货
								</button>
							</block>
							<block wx:elif="{{order.orderStatus==7}}">
								<button bindtap='evaluateBindTap' data-orderId='{{order.orderId}}'>评价</button>
								<block wx:if="{{order.orderBillType!=3}}">
									<button class='button2' bindtap='buyAgainBindTap' data-orderId='{{order.orderId}}'>
										再来一单
									</button>
								</block>
							</block>
							<block wx:elif="{{order.orderStatus==5||order.orderStatus==6||order.orderStatus==8}}">
								<button data-orderId='{{order.orderId}}' bindtap='removeOrderBindTap'>删除订单</button>
								<block wx:if="{{order.orderBillType!=3}}">
									<button class='button2' bindtap='buyAgainBindTap' data-orderId='{{order.orderId}}'>
										再来一单
									</button>
								</block>
							</block>
						</view>
					</view>
				</block>
				<!-- 单个订单 -->
			</view>
		</scroll-view>
	</view>
</view>