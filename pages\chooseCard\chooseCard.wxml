<!--强制登录-->
<view hidden="{{boundTelephoneHidden}}" class="bl_bg" style="z-index:1;"></view>
<view hidden="{{boundTelephoneHidden}}" style="position:absolute;z-index:999;top:20%;width:90%;background:#fff;margin-left:5%;border-radius:10rpx;">
  <view class="topWrap">
    <image src="{{defaultLogo}}">
    </image>
    <view>{{storeName}}</view>
  </view>
  <view style="text-align:center;font-size:30rpx;">您还未绑定手机号</view>
  <view style="text-align:center;color:#666;font-size:26rpx;margin-top:20rpx;">为让你更好的使用会员卡，请先绑定手机号</view>
  <view style="width:600rpx;margin:0 auto;">
    <button class='confirm_btn' style="background:#999;color:#fff;" bindtap='noBoundTelephoneBindTap'>暂不绑定</button>
    <button class='confirm_btn' style="background:#07c160;color:#fff;" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">立即绑定</button>
  </view>
</view>
<!--强制登录-->
<view style="margin-top:60rpx;">
  <view class="cardTip">选择支付会员卡</view>
  <view bindtap="noUseVipCardBindTap" style="text-align:center;padding:20rpx 0;font-size:30rpx;width:90%;margin-left:5%;margin-bottom:10rpx;;background:#999;color:#fff;border-radius:10rpx;">暂不使用会员卡</view>
  <block wx:key="unique" wx:for="{{cardList}}" wx:for-item="card" wx:for-index="index">
    <!--单张卡-->
    <view class="cardWrap" bindtap="chooseCardBindTap" data-index="{{index}}">
      <image src="{{card_bg}}" mode="widthFix"></image>
      <view class="card_title" style="text-align:center">{{companyName}}
      </view>
      <view class="card_id">No.{{card.cardId}}</view>
      <view class="amount_wrap">
        <label class="remain_amount">余额：
          <text>{{card.spareCash}}</text>
        </label>
        <label class="remain_score">积分：
          <text>{{card.integral}}</text>
        </label>
      </view>
      <block wx:if="{{card.source==1}}">
        <view class="validDate">有效期：{{card.validityEndDate}}</view>
      </block>
      <block wx:elif="{{card.source==0}}">
        <view class="validDate">有效期：长期</view>
      </block>
    </view>
    <!--单张卡-->
  </block>
</view>