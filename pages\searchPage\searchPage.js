const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchHistory: [],
    searchValue: "",
    isShowHistory: false,
    isSHowGoods: true,
    currPage: 1,
    remove: app.imageUrl + 'remove.png',
    isNoGoods: true,
    noGoodsSrc: app.imageUrl + 'no_goods.png',
    pageSize: 10,
    goodsList: [],
    totalHidden: true,
    inputShowed: false,
    shop_cart1: app.imageUrl + 'shop_cart1.png',
    addToShoppingCartHidden: true,
    skuId: '',
    groupBuyUserId: 0,
    scan: app.imageUrl + 'scan.png',
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    app.getSupplierSetting(app.getExtCompanyId());
    var filterName = options.filterName;
    if (filterName != null && filterName != '') {
      that.queryGoodsByGoodsName(filterName);
    }
    if (filterName == '' || typeof (filterName) == "undefined") {
      that.setData({
        inputShowed: true
      })
    }
    var oldHistory = wx.getStorageSync('searchHistory');
    if (oldHistory != "") {
      that.setData({
        searchHistory: oldHistory,
        isShowHistory: oldHistory.length > 0 ? false : true
      });
    } else {
      that.setData({
        isShowHistory: true
      });
    }
  },
  sweepCodeBindTap:function(){
    var that = this;
    wx.scanCode({
      success(res) {
        var filterName = res.result;
        app.redirectToPage('/pages/searchPage/searchPage?filterName=' + filterName);
      }
    })
  },
  searchBindFocus: function () {
    var that = this;
    that.setData({
      isSHowGoods: true,
      isShowHistory: that.data.searchHistory.length > 0 ? false : true
    });
  },
  searchBindTap: function () {
    var that = this;
    var newValue = that.data.searchValue.replace(/\s+/g, '');
    that.setData({
      goodsList: [],
      currPage: 1
    })
    if (newValue != "") {
      that.saveSearchHistory(newValue);
      that.queryGoodsByGoodsName(newValue);
    } else {
      app.showModal({
        content: '请输入你需要查询的商品名称'
      });
      return;
    }
  },
  /**
   * 点击历史记录搜索
   */
  historyBindTap: function (e) {
    var searchValue = e.currentTarget.dataset.name;
    this.setData({
      searchValue: searchValue
    });
    this.queryGoodsByGoodsName(searchValue);
  },
  saveSearchHistory: function (newValue) {
    this.setData({
      isShowHistory: true
    });
    var that = this;
    var oldHistory = wx.getStorageSync('searchHistory');
    if (oldHistory == "") {
      oldHistory = [];
      if (newValue != "") {
        oldHistory.push(newValue);
      }
    } else {
      var isHave = false;
      for (var i = 0; i < oldHistory.length; i++) {
        if (oldHistory[i] == newValue) {
          isHave = true;
          break;
        }
      }
      if (!isHave) {
        if (newValue != "") {
          oldHistory.push(newValue);
        }
      }
    }
    app.setStorage({
      key: 'searchHistory',
      data: oldHistory
    });
    that.setData({
      searchHistory: oldHistory
    });
  },
  /**
   * 清空历史记录
   */
  deleteHistoryBindTap: function () {
    wx.removeStorageSync("searchHistory");
    this.setData({
      searchHistory: [],
      isShowHistory: true
    });
  },
  searchBindInput: function (e) {
    var that = this;
    that.setData({
      searchValue: e.detail.value,
      isShowHistory: that.data.searchHistory.length > 0 ? false : true
    });
  },
  /**
   * 根据
   */
  queryGoodsByGoodsName: function (newValue) {
    var that = this;
    wx.showLoading({
      title: '数据加载中...',
    })
    wx.request({
      url: app.projectName + '/applet/goods/queryGoodsByGoodsName',
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "currPage": that.data.currPage,
        "pageSize": that.data.pageSize,
        "goodsName": newValue,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var totalRecords = res.data.totalRecords;
        var goodsList = res.data.goodsList;
        var oldCommodityBaseList = [];
        if (that.data.currPage > 1) {
          oldCommodityBaseList = that.data.goodsList;
        }
        var showTotal = that.data.currPage * that.data.pageSize;
        if (goodsList != null && goodsList.length > 0) {
          goodsList = oldCommodityBaseList.concat(goodsList);
          that.setData({
            isShowHistory: true,
            isSHowGoods: false,
            isNoGoods: true,
            goodsList: goodsList,
            totalHidden: showTotal < totalRecords ? true : false
          });
        } else {
          if (oldCommodityBaseList != null && oldCommodityBaseList.length > 0) {
            that.setData({
              isShowHistory: true,
              isSHowGoods: false,
              isNoGoods: true,
              goodsList: oldCommodityBaseList,
              totalHidden: showTotal < totalRecords ? true : false
            });
          } else {
            that.setData({
              isShowHistory: true,
              isSHowGoods: true,
              isNoGoods: false,
              totalHidden: showTotal < totalRecords ? true : false
            });
          }
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  onReachBottom: function () {
    var that = this;
    if (that.data.totalHidden) {
      that.setData({
        currPage: that.data.currPage + 1
      });
      that.queryGoodsByGoodsName(that.data.searchValue);
    }
  },
  imageClick: function (e) {
    var goodsId = e.target.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /*添加商品到购物车*/
  addCartClick: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/appletGoodsDetail',
      data: {
        "commodityId": id,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        that.setData({
          addToShoppingCartHidden: false,
          commodityUnitOtDefault: res.data.commodityUnitOtDefault,
          commodityUnitOmDefault: res.data.commodityUnitOmDefault,
          cutPriceOM: res.data.cutPriceOM,
          goodsOMUnit: res.data.goodsOMUnit,
          commodityVirtualStoreOM: res.data.commodityVirtualStoreOM,
          commodityMultiple: res.data.commodityMultiple,
          commodityVirtualStore: res.data.commodityVirtualStore,
          goodsPriceOM: res.data.goodsPriceOM,
          goodsOtUnit: res.data.goodsOtUnit,
          goodsPrice: res.data.goodsPrice,
          goodsDetail: res.data.list,
          showskuAllAttrList: res.data.skuList,
          cutPrice: res.data.cutPrice,
          showGoodsPrice: res.data.goodsPrice,
          showViewGoodsPrice: res.data.goodsPrice,
          buyOtNum: 0,
          buyOmNum: 0,
          commodityId: id
        });
        that.getGoodsDetailEvaluate(app.getExtStoreId(), app.getExtCompanyId());
      }
    })

  },
  getGoodsDetailEvaluate: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/getGoodsDetailEvaluate',
      data: {
        "commodityId": that.data.commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      success: function (res) {
        that.setData({
          salesVolume: res.data.salesVolume,
          overallStock: res.data.overallStock,
          stockBean: res.data.stockBean,
          isOnlineService: res.data.isOnlineService,
          fullCut: res.data.fullCut,
          orderEvaluateList: res.data.orderEvaluateList,
          shopCartNum: res.data.shopCartNum,
        });
      }
    })
  },
  hiddeAddToShoppingCart: function () {
    var that = this;
    that.setData({
      addToShoppingCartHidden: true,
      skuId: ''
    })
  },
  otMinusNumBindTap: function () {
    var that = this;
    if (that.data.buyOtNum > 0) {
      that.setData({
        buyOtNum: that.data.buyOtNum - 1
      });
    }
  },
  otPlusNumBindTap: function () {
    var that = this;
    that.setData({
      buyOtNum: parseInt(that.data.buyOtNum) + 1
    });
  },
  otNumBindInput: function (e) {
    var that = this;
    that.setData({
      buyOtNum: e.detail.value >= 0 ? e.detail.value : 0
    });
  },
  omMinusNumBindTap: function () {
    var that = this;
    if (that.data.buyOmNum > 0) {
      that.setData({
        buyOmNum: that.data.buyOmNum - 1
      });
    }
  },
  omPlusNumBindTap: function () {
    var that = this;
    that.setData({
      buyOmNum: that.data.buyOmNum + 1
    });
  },
  omNumBindInput: function (e) {
    var that = this;
    that.setData({
      buyOmNum: e.detail.value >= 0 ? e.detail.value : 0
    });
  },
  changeSKUBindTap: function (e) {
    var that = this;
    var name = e.target.dataset.name;
    var childname = e.target.dataset.childname;
    var updateSkuList = that.data.showskuAllAttrList;
    for (var i = 0; i < updateSkuList.length; i++) {
      if (name == updateSkuList[i].skuAttrName) {
        for (var j = 0; j < updateSkuList[i].skuAttrValueList.length; j++) {
          if (childname == updateSkuList[i].skuAttrValueList[j].skuAttrName) {
            updateSkuList[i].skuAttrValueList[j].isSelect = true;
          } else {
            updateSkuList[i].skuAttrValueList[j].isSelect = false;
          }
        }
      }
    }
    that.setData({
      showskuAllAttrList: updateSkuList
    });
    that.bachGoodsPrice();
  },
  bachGoodsPrice: function () {
    var that = this;
    that.setData({
      skuId: ''
    })
    var selectSKUArray = [];
    var skuList = that.data.showskuAllAttrList;
    for (var i = 0; i < skuList.length; i++) {
      for (var j = 0; j < skuList[i].skuAttrValueList.length; j++) {
        if (skuList[i].skuAttrValueList[j].isSelect) {
          var skuBean = {};
          skuBean.name = skuList[i].skuAttrName;
          skuBean.value = skuList[i].skuAttrValueList[j].skuAttrName;
          selectSKUArray.push(skuBean);
        }
      }
    }

    var skuGoodsPrice = "";
    var skuGroup = that.data.goodsDetail.skuList;
    for (var i = 0; i < skuGroup.length; i++) {
      var skuAttrList = skuGroup[i].skuAttrList;
      var count = 0;
      for (var j = 0; j < skuAttrList.length; j++) {
        var skuName = skuAttrList[j].skuName;
        var skuValue = skuAttrList[j].skuValue;
        var flag = false;
        for (var z = 0; z < selectSKUArray.length; z++) {
          var skuBean = selectSKUArray[z];
          if (skuName == skuBean.name && skuValue == skuBean.value) {
            flag = true;
            break;
          }
        }
        if (flag) {
          count++;
        } else {
          count--;
        }
      }
      if (count == selectSKUArray.length) {
        if (skuGroup[i].skuPrice != null && skuGroup[i].skuPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPrice;
        }
        if (skuGroup[i].skuPromotionPrice != null && skuGroup[i].skuPromotionPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPromotionPrice;
        }
        if (skuGroup[i].regionPriceList != null && skuGroup[i].regionPriceList.length > 0) {
          var goodsCount = that.data.buyCount + (that.data.buyOmCount * that.data.commodityMultiple);
          for (var k = 0; k < skuGroup[i].regionPriceList.length; k++) {
            var beginRegion = skuGroup[i].regionPriceList[k].beginRegion;
            var endRegion = skuGroup[i].regionPriceList[k].endRegion;
            var commoditySalePrice = skuGroup[i].regionPriceList[k].commoditySalePrice;
            if (goodsCount >= beginRegion && goodsCount <= endRegion) {
              skuGoodsPrice = commoditySalePrice;
              break;
            }
          }
        }
        var selectSkuStr = "";
        for (var m = 0; m < skuGroup[i].skuAttrList.length; m++) {
          selectSkuStr += skuGroup[i].skuAttrList[m].skuName + ":" + skuGroup[i].skuAttrList[m].skuValue + ";";
        }
        selectSkuStr = selectSkuStr.substring(0, selectSkuStr.length - 1);
        that.setData({
          selectSkuStr: selectSkuStr,
          showGoodsPrice: skuGoodsPrice,
          skuId: skuGroup[i].skuId,
          commodityVirtualStore: skuGroup[i].skuInventory
        });
        break;
      }
    }
    if (that.data.skuId == '' && selectSKUArray.length == skuList.length && skuList.length != 0) {
      app.showModal({
        title: '提示',
        content: "您选择的分类暂无货"
      });
    }
  },
  /*商品添加到购物车*/
  addShoppingCartBindTap: function () {
    var that = this;
    var updateSkuList = that.data.showskuAllAttrList;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (updateSkuList != null && updateSkuList.length > 0) {
      if (that.data.skuId == "" || that.data.skuId == null) {
        app.showModal({
          title: '提示',
          content: "请选择规格"
        });
        return;
      }
    }
    var otBuyNum = that.data.buyOtNum;
    var omBuyNum = that.data.buyOmNum;
    if (otBuyNum + omBuyNum == 0) {
      app.showModal({
        title: '提示',
        content: "购买数量不能为0"
      });
      return;
    }
    if (!that.checkStock()) {
      return;
    };
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/addRetailShppingCart',
      data: {
        "a_num": otBuyNum,
        "omNum": omBuyNum,
        "a_id": that.data.commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "skuId": that.data.skuId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "recommendUserId": app.recommendData.recommendUserId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          that.setData({
            skuId: '',
            addToShoppingCartHidden: true,
            buyOtNum: 0,
            buyOmNum: 0
          })
          app.countRetailCartTotal();
          wx.showToast({
            title: '添加成功',
            duration: 1000
          })
        } else {
          wx.showToast({
            title: '添加失败',
            duration: 1000
          })
        }
      }
    })
  },
  /*立即购买*/
  nowPayClick: function (e) {
    var that = this;
    if (!that.checkStock()) {
      return;
    };
    var goodsDetail = that.data.goodsDetail;
    var unitList = goodsDetail.unitList;
    var commodityMoq = 0;
    var commodityWeightUnit = "";
    var commodityMultiple = 0;
    if (unitList.length == 1) {
      commodityMoq = goodsDetail.unitList[0].commodityMoq;
      commodityWeightUnit = goodsDetail.unitList[0].commodityWeightUnit;
    } else if (unitList.length == 2) {
      for (var i = 0; i < unitList.length; i++) {
        if (unitList[i].commodityWeightType == "OT") {
          commodityMoq = unitList[i].commodityMoq;
          commodityWeightUnit = unitList[i].commodityWeightUnit;
        } else if (unitList[i].commodityWeightType == "OM") {
          commodityMultiple = unitList[i].commodityMultiple;
        }
      }
    }
    if (parseInt(commodityMoq) > 0) {
      if (that.data.buyOtNum + (that.data.buyOmNum * commodityMultiple) < parseInt(commodityMoq)) {
        app.showModal({
          title: '提示',
          content: "商品购买数量必须满足" + commodityMoq + commodityWeightUnit + "!"
        });
        return;
      }
    }
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId);
    }
    else {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId(),
          "phone": app.getTelephone()
        },
        url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
        success: function (res) {
          var cardList = res.data.show_vipCardList;
          if (cardList != null && cardList.length > 0) {
            if (cardList.length == 1) { //一张会员卡自动选择
              var cardBean = cardList[0];
              app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&vipCardNo=" + cardBean.cardId + '&vipCardCode=' + cardBean.vipCardCode);
            } else if (cardList.length > 1) {
              //多张会员卡进行选择
              app.navigateToPage("/pages/chooseCard/chooseCard?payType=2&goodsId=" + commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId);
            }
          } else {
            //没有会员卡
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId);
          }
        }
      })
    }
  },
  /**
  * 发起拼团
  */
  nowGroupBuyBindTap: function (e) {
    var that = this;
    var groupBuyUserId = e.currentTarget.dataset.id;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (!that.checkStock()) {
      return;
    };
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          var skuList = that.data.showskuAllAttrList;
          if (skuList != null && skuList.length > 0) {
            if (that.data.skuId == "" || that.data.skuId == null) {
              app.showModal({
                title: '提示',
                content: "请选择规格"
              });
              return;
            }
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&groupBuyUserId=" + groupBuyUserId + "&joinPromotion=2" + "&isTuanGou=1");
          }
          else {
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&groupBuyUserId=" + groupBuyUserId + "&joinPromotion=2" + "&isTuanGou=1");
          }
        }
      }
    })
  },
  /*控制库存时,检查商品是否超过了库存数量*/
  checkStock: function () {
    console.log('33333333333');
    var that = this;
    var openStock = that.data.stockBean.openStock;
    var overallStock = that.data.overallStock;
    var buyNum = that.data.buyOtNum + (that.data.buyOmNum * that.data.commodityMultiple);
    var commodityVirtualStore = that.data.commodityVirtualStore;
    var flag = true;
    console.log('33333333333'+openStock);
    console.log('33333333333'+overallStock);
    if (openStock || overallStock == 1) {
      if (parseInt(buyNum) > parseInt(commodityVirtualStore)) {
        app.showModal({
          title: '提示',
          content: "购买数量超过库存"
        });
        flag = false;
      }
    }
    return flag;
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})