var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    zfbAccount: "",
    zfbUserName: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.queryAlipayAccountInfo();
  },
  queryAlipayAccountInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/boundCard/queryAlipayAccountInfo',
      success: function (res) {
        var userList = res.data.userList;
        if (userList != null && userList.length > 0) {
          that.setData({
            zfbAccount: userList[0].zfbAccount,
            zfbUserName: userList[0].zfbUserName
          })
        }
      }
    })
  },
  zfbAccountBindInput: function (e) {
    this.setData({
      zfbAccount: e.detail.value
    })
  },
  zfbUserNameBindInput: function (e) {
    this.setData({
      zfbUserName: e.detail.value
    })
  },
  updateAlipayAccountInfoBindTap: function () {
    var that = this;
    var zfbAccount = that.data.zfbAccount.replace(/\s+/g, '');
    var zfbUserName = that.data.zfbUserName.replace(/\s+/g, '');
    if (zfbAccount.length == 0) {
      wx.showToast({
        title: '支付宝账号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (zfbUserName.length == 0) {
      wx.showToast({
        title: '真实姓名不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "zfbAccount": zfbAccount,
        "zfbUserName": zfbUserName
      },
      url: app.projectName + '/boundCard/updateAlipayAccountInfo',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "操作成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1000);
            }
          })
        } else {
          wx.showToast({
            title: '操作失败',
            icon: 'fail',
            duration: 1000,
            mask: true
          })
        }
      }
    })
  }
})