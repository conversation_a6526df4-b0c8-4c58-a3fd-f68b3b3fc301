<!--补签模板-->
<template name="signature">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:546rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view class="signature_one">
					<view class="signature_two">补签卡</view>
					<view class="signature_three">你有 <span style="color:#FF9002;">{{usableNum}}张</span>补签卡</view>
					<view class="signature_four">可进行{{usableNum}}次补签</view>
					<view class="signature_five">
						<button class="signature_six" style="opacity: {{usableNum==0?0.3:1}}; background: #FE5406; color: #FFFFFF;"
							disabled="{{disabled}}" bindtap="toSignatureAll">立即补签</button>
					</view>
          <view style="font-size:29rpx;padding:20rpx;">
            <view style="color:#666;">获取补签卡</view>
            <block wx:for="{{supplyData}}" wx:key="index">
              <view class="s_w">
                <view>
                  <image style="width:30rpx;height:30rpx;" mode="widthFix" src="{{item.imagesUrl}}"></image>
                  <label>{{item.businessName}}</label>
                </view>
                <block wx:if="{{item.eventType == 1}}">
                  <label bindtap="toIndexClick" class="act_btn">去购买</label>
                </block>
                <block wx:if="{{item.eventType == 2}}">
                  <label bindtap="toOrderClick" class="act_btn">去评论</label>
                </block>
                <block wx:if="{{item.eventType == 3}}">
                  <label bindtap="toCardClick" class="act_btn">成为会员</label>
                </block>
                <block wx:if="{{item.eventType == 4}}">
                  <label bindtap="toCardClick" class="act_btn">去充值</label>
                </block>
                <block wx:if="{{item.eventType == 5}}">
                  <label wx:if="{{isFill}}" bindtap="toProfileClick" class="act_btn">去完善</label>
                  <label wx:else class="act_btn grey_b">已完善</label>
                </block>
              </view>
            </block>
          </view>
				</view>
				<view class="cancal-wrapper">
					<image class="signature_img" src="{{close}}" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>
<!--补签提示选择弹出层-->
<template name="supplysignature">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:546rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view class="signature_one">
					<view class="signature_two">漏签提示</view>
					<view class="signature_three">你有<span style="color:#FF9002;">{{missNum}}</span>天漏签</view>
					<view class="signature_four">是否补签？</view>
          <view style="padding:50rpx 20rpx;font-size:30rpx;display:flex;justify-content:space-around">
            <view bindtap="goSupplyClick" style="border-radius:30rpx;color:#666;padding:12rpx 30rpx;background:#ff6600;color:#fff;">去补签</view>
            <view bindtap="hiddenFloatView" style="border-radius:30rpx;color:#666;padding:12rpx 30rpx;background:#ff6600;color:#fff;">关闭</view>
          </view>
				</view>
				<view class="cancal-wrapper">
					<image class="signature_img" src="{{close}}" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>
<!--满签模板-->
<template name="fullSignature">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:552rpx;height:954rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view class="base64" style="background-image:url({{fullSignImg}});">
					<block wx:for="{{fullPrize}}" wx:key="index">
						<view class="fsignature_one">
							<view class="fsignature_two">
								{{item.prizeName}}
							</view>
							<button class="fsignature_three" bindtap="getFullSignReward" data-prizeid="{{item.id}}"
								data-prizetype="{{item.prizeType}}">领取</button>
						</view>
					</block>
					<view class="fsignature_four">详情可在“我的奖励”中查询</view>
				</view>

				<view class="cancal-wrapper">
					<image src="{{close}}" class="signature_img" model="widthFix" bindtap="hiddenGivenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>

<!--漏签模板-->
<template name="missSignature">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:586rpx;height:455rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view class="msignature_one">
					<view class="msignature_two">漏签提醒
						<view class="msignature_three">
							<view class="msignature_four">
								<view class="msignature_five">
									<view class="msignature_six">
										<image src="{{newuser}}" class="pay_four" mode="widthFix"></image>
									</view>
									<view>
										<view class="invite_one">
											进行补签
										</view>
										<view class="invite_two">
											<text bindtap="">每使用1张补签卡可补签1次</text>
										</view>
									</view>
								</view>
								<button class="invite_three" bindtap="toSignature">补签</button>
							</view>
							<view class="invite_four">
								<view class="invite_five">
									<view class="invite_six">
										<image src="{{share}}" class="pay_four" mode="widthFix"></image>
									</view>
									<view>
										<view class="invite_seven">
											放弃补签
										</view>
										<view class="invite_eight">
											<text bindtap="">放弃补签将重新签到请谨慎操作</text>
										</view>
									</view>
								</view>
								<button class="invite_nine" bindtap="toSign">放弃</button>
							</view>
							<!-- <view class="pay_one">
								<view class="pay_two">
									<view class="pay_three">
										<image src="{{money}}" class="pay_four" mode="widthFix"></image>
									</view>
									<view>
										<view class="pay_five">
											支付获取
										</view>
										<view class="pay_six">
											<text bindtap="">每支付50元可获得1张补签卡</text>
										</view>
									</view>
								</view>
								<button class="pay_seven" bindtap="toSign">去完成</button>
							</view> -->
						</view>
					</view>
				</view>
				<view class="cancal-wrapper">
					<image src="{{close}}" class="signature_img" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>

<!--获取字弹窗-->
<template name="getWord">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:528rpx;height:560rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view style="width:528rpx;height:560rpx;border-radius:16rpx;">
					<image src="{{addWordImg}}" style="width:528rpx;height:auto" mode="widthFix"></image>
				</view>
				<view class="cancal-wrapper">
					<image class="signature_img" src="{{close}}" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>

<!--获取兑换奖励弹窗-->
<template name="getExchange">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:436rpx;height:543rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view style="width:436rpx;height:543rpx;border-radius:10rpx;">
					<view style="width:436rpx;height:189rpx;">
						<image src="{{exchange}}" style="width:436rpx;height:auto;" mode="widthFix"></image>
					</view>
					<view style="width:436rpx;height:354rpx;background:#fff">
						<view style="margin:0px auto;width:150rpx;height:135rpx;padding-top:68rpx;">
							<image src="{{prize}}" style="width:150rpx;height:135rpx;" mode="widtnFix"></image>
						</view>
						<button
							style="margin:0px auto;margin-top:40rpx;width:291rpx;height:72rpx;background: #FE5406;color:#fff;border-radius:49rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 400;line-height: 72rpx;"
							bindtap="hiddenFloatView">确定</button>
					</view>
				</view>
				<view class="cancal-wrapper">
					<image class="signature_img" src="{{close}}" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>

<!--获取签到奖励弹窗-->
<template name="signReward">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:520rpx;height:658rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view wx:if="{{prizeType==0}}" class="signReward_one" style="background-image:url({{successmode}});">
					<view class="signReward_two">{{prizeName}}</view>
					<view class="signReward_three" bindtap="hiddenFloatView"></view>

				</view>
				<view wx:if="{{prizeType==1}}" class="signReward_one" style="background-image:url({{couponmode}});">
					<view class="signReward_two">{{prizeName}}</view>
					<view class="signReward_four" bindtap="toCoupons"></view>
					<view class="signReward_five" bindtap="hiddenFloatView"></view>
				</view>
				<view wx:if="{{prizeType==2}}" class="signReward_one" style="background-image:url({{intergralmode}});">
					<view class="signReward_two">{{prizeName}}</view>
					<view class="signReward_four" bindtap="toVipCard"></view>
					<view class="signReward_five" bindtap="hiddenFloatView"></view>
				</view>
				<view wx:if="{{prizeType==3}}" class="signReward_one" style="background-image:url({{moneymode}});">
					<view class="signReward_two">{{prizeName}}</view>
					<view class="signReward_four" bindtap="toVipCard"></view>
					<view class="signReward_five" bindtap="hiddenFloatView"></view>
				</view>
				<view wx:if="{{prizeType==4}}" class="signReward_one" style="background-image:url({{turnmode}});">
					<view class="signReward_two">{{prizeName}}</view>
					<view class="signReward_four" bindtap="toLuckdraw"></view>
					<view class="signReward_five" bindtap="cancellottery"></view>
				</view>
				<view class="cancal-wrapper">
					<image class="signature_img" src="{{close}}" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>

<!--评论模板-->
<template name="comment">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}"
		bindtap="hiddenFloatView">
		<view
			style="height:120rpx;width:100%;background:#fff; padding: 0; position: fixed;margin-bottom:{{bottom}}rpx;bottom:0; display: flex;left: 0;  align-items: center;justify-items: center;z-index:999"
			catchtap="nono">
			<view style="height:80rpx;width:70%;background:#efeff4;margin-left:10rpx;">
				<input value="{{msg}}" type="text"
					style="border:none;margin-left:20rpx;height:80rpx;font-size:28rpx;color:grey;text-align:left;"
					bindinput="inputComment"></input>
				<!-- focus="{{isFocus}}" -->
			</view>
			<view bindtap="emojiComment" style="width:50rpx;height:50rpx;margin-left:20rpx;">
				<image src="{{imgUrl}}" style="width:100%;height:100%"></image>
			</view>
			<button class="input" bindtap="releaseEvent" data-currentpage="{{currentPage}}" data-index="{{index}}">发送</button>
			<!-- <button wx:else class="input disabled" >发送</button> -->
			<scroll-view scroll-y="true" catchtouchmove="preventD" wx:if="{{state}}"
				style="height:500rpx;padding: 0; bottom:0;position: fixed;">
				<view class="emojis_box">
					<view class="emoji_wrap" wx:for="{{emojiList}}" wx:key="key">
						<view catchtap="clickEmoji" data-key="{{item.key}}" class="emoji">{{item.img}}</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<!--分享模板-->
<template name="share">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<view
			style="height:364rpx;width:100%;background:#fff; padding: 0; bottom: 0;position: fixed;margin-bottom:0rpx;border-radius: 20rpx 20rpx 0rpx 0rpx;overflow:hidden"
			catchtap="nono">
			<view
				style="height:264rpx;width:100%;background:#F2F2F2; display: flex;justify-content: center; align-items: center;">
				<view style="width:153rpx;height:100%;display: flex;align-items: center;">
					<button open-type='share' style="width:100%;height:133rpx;background:#F2F2F2">
						<image style="width:95rpx;height:auto" src="{{find_wechat}}" mode="widthFix"></image>
					</button>
					<view
						style="position:absolute;top:168rpx;left:212rpx;font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #333333;text-align:center;">
						微信</view>
				</view>
				<view style="width:190rpx"></view>
				<view style="width:93rpx;height:100%;display: flex;align-items: center;">
					<view style="width:93rpx;height:133rpx;" bindtap="getPosterBindTap" data-share="{{isDistribution}}">
						<image style="width:93rpx;height:auto" src="{{find_image}}" mode="widthFix"></image>
						<view style="font-size: 22rpx;font-family: PingFang SC;font-weight: 400;color: #333333;text-align:center;">
							生成海报</view>
					</view>
				</view>
			</view>
			<view
				style="height:100rpx;width:100%;font-size: 32rpx;font-family: PingFang SC;font-weight: 500;line-height: 100rpx;text-align:center;color: #646464;"
				bindtap="hiddenFloatView">取消</view>
		</view>
	</view>
</template>

<!--删除或举报模板-->
<template name="reportOrdel">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}"
		bindtap="hiddenFloatView">
		<view
			style="height:270rpx;width:100%;background: #F2F2F2;; padding: 0; bottom: 0;position: fixed;margin-bottom:0rpx;border-radius: 20rpx 20rpx 0rpx 0rpx;overflow:hidden"
			catchtap="nono">
			<view
				style="height:100rpx;background: #fff;width:100%;font-size: 32rpx;font-family: PingFang SC;font-weight: 500;line-height: 100rpx;text-align:center;color: red;margin-bottom:20rpx;"
				bindtap="reportOrdelComment" data-reportordeltext="{{reportOrdelText}}" data-commentid="{{commentId}}"
				data-currentpage="{{currentPage}}" data-index="{{index}}" data-findinfoid="{{findinfoId}}">{{reportOrdelText}}
			</view>
			<view
				style="height:150rpx;width:100%;background: #fff;font-size: 32rpx;font-family: PingFang SC;font-weight: 500;line-height: 100rpx;text-align:center;color: #646464;"
				bindtap="hiddenFloatView">取消</view>
		</view>
	</view>
</template>

<!--奖励弹窗模板-->
<template name="reward">
	<view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}">
		<picker-view indicator-style="height: 50rpx;" style="width:575rpx;" catchtap="nono">
			<view style="margin:0px auto;">
				<view
					style="background-image:url({{backpage_top}}); width: 575rpx;height: 227rpx;background-size:100% 100%;border:0px solid red;">
				</view>
				<view wx:for="{{rewardList}}" wx:key="index"
					style="width: 570rpx;background:#F09039;margin-left:5.99rpx;margin-top:-5rpx;padding-top:20rpx;">
					<view class="reward_two eval1_two">
						<view class="reward_three">
							<view class="reward_dashed">
								<image class="reward_img" src="{{item.imagesUrl}}" mode="widthFix"></image>
							</view>
						</view>
						<view style="width:65%;">
							<view class="reward_five">
								{{item.businessName}}
							</view>
							<view class="reward_six">
								{{item.prizeExplain}}
							</view>
						</view>
						<view class="reward_seven eval1_three">
              <block wx:if="{{item.rightsType == 1}}">
                <view data-configid="{{item.configId}}" class="eval1_four {{item.checked==true?'':'grey_b'}}" bindtap="goSignBindTap">{{item.checked==true?'去领取':'已领取'}}</view>
              </block>
              <block wx:elif="{{item.rightsType == 2}}">
                <view class="eval1_four {{item.checked==true?'':'grey_b'}}" data-configid="{{item.configId}}" data-gameid="{{item.prizeId}}" bindtap="gocjBindTap">{{item.checked==true?'去抽奖':'已抽奖'}}</view>
              </block>
              <block wx:elif="{{item.rightsType == 3}}">
                <view data-configid="{{item.configId}}" class="eval1_four {{item.checked==true?'':'grey_b'}}" bindtap="goWordBindTap">{{item.checked==true?'去领取':'已领取'}}</view>
              </block>
              <block wx:elif="{{item.rightsType == 4}}">
                <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
              </block>
              <block wx:elif="{{item.rightsType == 5}}">
                <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
              </block>
              <block wx:elif="{{item.rightsType == 6}}">
                <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
              </block>
							<!--<view class="eval1_four" wx:if="{{item.rightsType==2}}" data-gameid="{{item.prizeId}}"
								bindtap="gocjBindTap">
								去抽奖</view>
							<view class="eval1_five" wx:else>
								已发放</view>-->
						</view>
					</view>
				</view>

				<view
					style="width: 570rpx;height: 24rpx;background:#F09039;border-radius:0 0 32rpx 32rpx;margin-left:5.99rpx;margin-top:-5rpx;">
				</view>

				<view class="cancal-wrapper">
					<image src="{{close}}" class="signature_img" model="widthFix" bindtap="hiddenFloatView"></image>
				</view>
			</view>
		</picker-view>
	</view>
</template>