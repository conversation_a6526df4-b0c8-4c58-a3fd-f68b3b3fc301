<view>
	<view class="tabWrap">
		<view class="{{index==0?'tab_active':''}}" data-index="0" bindtap="selectCardTypeBindTap">全部</view>
		<view class="{{index==1?'tab_active':''}}" data-index="1" bindtap="selectCardTypeBindTap">可使用</view>
		<view class="{{index==2?'tab_active':''}}" data-index="2" bindtap="selectCardTypeBindTap">已使用</view>
		<view class="{{index==3?'tab_active':''}}" data-index="3" bindtap="selectCardTypeBindTap">已过期</view>
	</view>
	<view style="padding-top:100rpx;">
		<!--暂无卡券显示-->
		<image hidden="{{cardList.length>0?true:false}}" src="{{couponBox}}"
			style="width:250rpx;margin-left:250rpx;margin-top:50px;" mode="widthFix"></image>
		<block wx:key="unique" wx:for="{{cardList}}" wx:for-item="sCard" wx:for-index="idx">
			<!--已使用或者过期-->
			<block wx:if="{{sCard.cardUseState==0&&sCard.expireState==1||sCard.cardUseState==1||sCard.cardUseState==2}}">
				<view class="couponBox">
					<view class="couponTop">
						<view class="couponLeft">
							<image src="{{invalidCoupon}}"></image>
							<view style="position: absolute;top:80rpx;">
								<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
									<text style="font-size:44rpx;">{{sCard.discountAmount}}</text>
									<text>折</text>
								</block>
								<block wx:else>
									<text>￥</text>
									<text style="font-size:44rpx;">{{sCard.discountAmount}}</text>
								</block>
							</view>
						</view>
						<view class="couponRight">
							<view style="font-weight:bold;line-height:50rpx">
								{{sCard.cardName}}
							</view>
							<view style="color:#919398;max-height:58rpx;">
								<block wx:if="{{sCard.usageScenarios==1}}">适用场景：仅线上使用</block>
								<block wx:elif="{{sCard.usageScenarios==2}}">适用场景：仅线下门店使用</block>
								<block wx:else>适用场景：线上线下均可使用</block>
							</view>
							<view style="color:#919398;font-size:24rpx;">有效时间:{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
							<view style="color:#919398;font-size:24rpx;margin-top:4rpx;">领取时间:{{sCard.receiveTime}}</view>
						</view>
					</view>
					<view class="couponCenter">
						<view style="display:flex;justify-content: space-between;">
							<view style="color:#919398;" bindtap="ruleSwitchFun" data-idx="{{idx}}" data-cardId="{{sCard.id}}">
								使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow_grey:downArrow_grey}}"></image>
							</view>
							<view style="display:flex">
								<view class="invalidBtn">
									<block wx:if="{{sCard.cardUseState==1}}">已使用</block>
									<block wx:elif="{{sCard.cardUseState==2}}">已赠送</block>
									<block wx:else>已过期</block>
								</view>
							</view>
						</view>
						<view style="line-height:40rpx;color:#919398;" hidden='{{!sCard.ruleSwitch}}'>
							<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
								{{sCard.fullAmount==0?"无门槛享受"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"元可用"}}
							</block>
							<block wx:else>
								{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"元可用"}}
							</block>
							{{sCard.rule}}
						</view>
					</view>
				</view>
			</block>
			<!--可使用-->
			<block wx:else>
				<view class="couponBox">
					<view class="couponTop">
						<view class="couponLeft">
							<image src="{{hongbaoImg}}"></image>
							<view style="position: absolute;top:80rpx;">
								<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
									<text style="font-size:44rpx;">{{sCard.discountAmount}}</text>
									<text>折</text>
								</block>
								<block wx:else>
									<text>￥</text>
									<text style="font-size:44rpx;">{{sCard.discountAmount}}</text>
								</block>
							</view>
						</view>
						<view class="couponRight">
							<view style="font-weight:bold;line-height:50rpx">
								{{sCard.cardName}}
							</view>
							<view style="color:#919398;height:58rpx;">
								<block wx:if="{{sCard.usageScenarios==1}}">适用场景：仅线上使用</block>
								<block wx:elif="{{sCard.usageScenarios==2}}">适用场景：仅线下门店使用</block>
								<block wx:else>适用场景：线上线下均可使用</block>
							</view>
							<view style="color:#919398;font-size:24rpx;">有效时间:{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
							<view style="color:#919398;font-size:24rpx;margin-top:4rpx;">领取时间:{{sCard.receiveTime}}</view>
						</view>
					</view>
					<view class="couponCenter">
						<view style="display:flex;justify-content: space-between;">
							<view style="color:#919398;" bindtap="ruleSwitchFun" data-idx="{{idx}}" data-cardId="{{sCard.id}}">
								使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow_grey:downArrow_grey}}"></image>
							</view>
							<view style="display:flex">
								<block wx:if="{{sCard.cardUseState==0||sCard.cardUseState==1}}">
									<block wx:if="{{sCard.giftState==1}}">
										<view class="emptyBtn" style="margin-right:10rpx" bindtap="goShareTap" data-id="{{sCard.id}}"
											data-no="{{sCard.cardNo}}">转赠好友</view>
									</block>
									<block wx:if="{{sCard.cardUseState==0&&sCard.expireState==0}}">
										<block wx:if="{{sCard.usageScenarios==1}}">
											<view class="solidBtn" bindtap="nowUseCardBindTap">立即使用</view>
										</block>
										<block wx:else>
											<view class="solidBtn" data-id="{{sCard.id}}" bindtap="goToIndexBindTap">立即使用</view>
										</block>
									</block>
									<block wx:elif="{{sCard.cardUseState==1}}">
										<view class="invalidBtn">已使用</view>
									</block>
								</block>

							</view>
						</view>
						<view style="line-height:40rpx" hidden='{{!sCard.ruleSwitch}}'>
							<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
								<view>
									{{sCard.fullAmount==0?"无门槛享受"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"元可用"}}
								</view>
							</block>
							<block wx:else>
								<view>
									{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"元可用"}}
								</view>
							</block>
							{{sCard.rule}}
						</view>
					</view>
				</view>
			</block>
		</block>
	</view>
</view>

<view class="black_bg" hidden="{{shareIsShow}}"></view>
<view class="shareBox" hidden="{{shareIsShow}}">
	<icon class="closeTemp" bindtap="closeShareBindTap" type="clear" size='26' color='#303337' />
	<view style="font-size:30rpx;line-height:80rpx;">将优惠券转赠给好友</view>
	<view>请注意：此券分享后将无法撤回</view>
	<view style="margin-top:40rpx">
		<button open-type="share" data-id="{{sCardId}}" data-no="{{sCardNo}}" style="background:#fff;line-height:40rpx">
			<image src="{{wechatShareImg}}" style="width:80rpx;height:80rpx;"></image>
			<view style="text-align:center;font-size:28rpx">微信</view>
		</button>

	</view>
</view>