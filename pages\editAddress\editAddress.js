var app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userName: "", //姓名
    telephone: "", //电话
    address: "", //地址
    isDefault: false,
    houseNumber: "", //门牌号
    latitude: 0,
    longitude: 0,
    province: "",
    city: "",
    area: "",
    id: null,
    region: ["请选择省市区"],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var id = options.id;
    var pickOrderStoreId = options.pickOrderStoreId;
    var that = this;
    wx.request({
      url: app.projectName + '/ios/queryDeliveryAddress',
      data: {
        "loginId": app.getUserId(),
        "loginRole": 1,
        "id": id,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),

      },
      success: function (res) {
        var addressBean = res.data.returnList[0];
        that.setData({
          pickOrderStoreId: pickOrderStoreId,
          province: addressBean.province,
          city: addressBean.city,
          area: addressBean.area,
          latitude: addressBean.latitude,
          longitude: addressBean.longitude,
          id: id,
          userName: addressBean.username,
          telephone: addressBean.telephone,
          houseNumber: addressBean.houseNumber,
          address: addressBean.province + addressBean.city + addressBean.area + addressBean.address,
          isDefault: addressBean.isdefault == 1 ? true : false,
          region: [addressBean.province, addressBean.city, addressBean.area],
        });
      }
    })
  },
  bindRegionChange: function (e) {
    this.setData({
      region: e.detail.value
    })
  },
  /**
   * 保存修改地址
   */
  saveNewAddressBindTap: function () {
    var that = this;
    var userName = that.data.userName;
    var telephone = that.data.telephone;
    var address = that.data.address;
    var houseNumber = that.data.houseNumber;
    var flag = that.checkTelephone(telephone, userName, address);
    var pac = that.data.province + that.data.city;
    if (that.data.area != null && that.data.area != "" && that.data.area != undefined) {
      pac += that.data.area;
    }
    address = address.substring(pac.length);
    if (!flag) {
      return;
    }
    var jsonStr = {
      "latitude": that.data.latitude,
      "longitude": that.data.longitude,
      "houseNumber": houseNumber,
      "id": that.data.id,
      "username": userName,
      "telephone": telephone,
      "province": that.data.province,
      "city": that.data.city,
      "area": that.data.area,
      "address": address,
      "isDefault": that.data.isDefault == true ? 1 : 2,
      "loginId": app.getUserId(),
      "loginRole": 1,
      "storeId": app.getExtStoreId(),
      "companyId": app.getExtCompanyId()
    };
    jsonStr = encodeURIComponent(encodeURIComponent(JSON.stringify(jsonStr)));
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/ios/updateDeliveryAddress',
      data: {
        "jsonStr": jsonStr,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var pages = getCurrentPages(),
          prevPage = pages[pages.length - 2];
        prevPage.initAddress(that.data.pickOrderStoreId);
        app.turnBack();
      }
    })
  },
  checkTelephone: function (telephone, userName, address) {
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(19[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (userName == "") {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    } else if (userName.length > 20) {
      wx.showToast({
        title: '姓名长度过长',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    }
    if (telephone == "") {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    } else if (telephone.length < 11) {
      wx.showToast({
        title: '手机号长度有误',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    } else if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    }
    if (address.length == 0) {
      wx.showToast({
        title: '请填写收货地址',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    }
    return true;
  },
  checkboxChange: function (e) {
    this.setData({
      isDefault: e.detail.value == 1 ? true : false
    })
  },
  saveUserNameBindInput: function (e) {
    this.setData({
      userName: e.detail.value
    })
  },
  saveTelephoneBindInput: function (e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  saveAddressBindInput: function (e) {
    this.setData({
      address: e.detail.value
    })
  },
  houseNumberBindInput: function (e) {
    this.setData({
      houseNumber: e.detail.value
    })
  },
  selectAddressBindTap: function () {
    app.navigateToPage('/pages/addressToCity/addressToCity?province=' + this.data.province + '&city=' + this.data.city + '&area=' + this.data.area + '&detailAddress=' + this.data.detailAddress + '&houseNumber=' + this.data.houseNumber);

  },
  /**
   * 删除地址
   */
  deleteAddressBindTap: function () {
    var that = this;
    app.showModal({
      content: '确定删除此收货地址？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/ios/deleteDeliveryAddress',
          data: {
            "loginId": app.getUserId(),
            "loginRole": 1,
            "jsonStr": that.data.id,
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            app.turnBack();
          }
        })
      }
    })
  }
})