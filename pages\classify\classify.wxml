+
<view class='location_box' style='height:80rpx;'>
    <icon type="search" size='16' color='#666' />
    <input placeholder='搜索商品' bindfocus='searchBindFocus'></input>
</view>
<view class='classify_box' style='height:{{second_height}}px; margin-top:80rpx;position:relative;'>
    <view class='classify_left'>
        <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
            <text bindtap='selectGoodsTypeBindTap' data-id='{{cate.categoryId}}' class='{{cate.select?"active":""}}'>{{cate.categoryName}}</text>
        </block>
    </view>
    <view class='classify_right' style='height:100%;'>
        <!--单个商品-->
        <block wx:key="unique" wx:for="{{commodityBaseList}}" wx:for-item="goods">
            <block wx:if="{goods.commoditySupplierId==1}">
            <view class='single_goods' bindtap='imageClick' data-commodityId='{{goods.commodityId}}'>
                <image src='{{goods.commodityAdEffectPic==""?pic_bg:goods.commodityAdEffectPic}}' mode='widthFix'></image>
                <!--活动标记-->
                <block wx:key="unique" wx:for="{{goods.retailPromotionList}}" wx:for-item="promotion">
                    <view class="eventWrap">
                        <image src="{{event}}"></image>
                        <block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
                            <text>秒杀</text>
                        </block>
                        <block wx:if="{{promotion.promotionType=='TUANGOU'}}">
                            <text>团购</text>
                        </block>
                        <block wx:if="{{promotion.promotionType=='TEJIA'}}">
                            <text>特价</text>
                        </block>
                        <block wx:if="{{promotion.promotionType=='QUDUAN'}}">
                            <text>区段</text>
                        </block>
                    </view>
                </block>
                <!--活动标记-->
                <label>
                    <block wx:if="{{goods.commodityAdTag!=''}}">
                        <text class='tips_box'>{{goods.commodityAdTag}}</text>
                    </block>
                    <text class='big_title'>{{goods.commodityName}}</text>
                    <text class='little_title'>{{goods.commodityAdContent}}</text>
                    <!--<text class='price_box'>￥{{goods.goodsPrice}}</text>-->
                    <block wx:if="{{goods.skuList.length>0}}">
                      <text class='price_box'>￥{{goods.skuList[0].skuPrice}}</text>
                    </block>
                    <block wx:else>
                      <block wx:key="unique" wx:for="{{goods.unitList}}" wx:for-item="unit">
                        <block wx:if="{{unit.commodityWeightType=='OT'}}">
                          <text class='price_box'>￥{{unit.commoditySalePrice}}</text>
                        </block>
                      </block>
                    </block>
                </label>
            </view>
            </block>
        </block>
        <!--单个商品-->

    </view>
</view>