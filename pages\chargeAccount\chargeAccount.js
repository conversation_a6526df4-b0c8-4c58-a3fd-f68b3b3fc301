// pages/chargeAccount/chargeAccount.js
var app = getApp();
const popup = require('../popupTemplate/popupTemplate.js')
const http = require('../../utils/http');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    charge_bg: app.imageUrl + 'accountManager/charge_bg.png',
    rechargeSchemeList: [],
    vipCardNo: "",
    spareCash: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var cardNo = options.cardNo;
    var spareCash = options.spareCash;
    this.setData({
      vipCardNo: cardNo,
      spareCash: spareCash
    });
    this.queryVipCardRechargeScheme();
  },
  queryVipCardRechargeScheme: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/queryVipCardRechargeScheme',
      success: function (res) {
        var rechargeSchemeList = res.data.rechargeSchemeList;
        that.setData({
          rechargeSchemeList: rechargeSchemeList
        });
      }
    })
  },
  selectPricePlanBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var rechargeSchemeList = that.data.rechargeSchemeList;
    for (var i = 0; i < rechargeSchemeList.length; i++) {
      if (i == index) {
        rechargeSchemeList[i].isSelect = true;
      } else {
        rechargeSchemeList[i].isSelect = false;
      }
    }
    that.setData({
      rechargeSchemeList: rechargeSchemeList
    });
  },
  goChargeDetail: function () {
    app.navigateToPage("/pages/chargeDetail/chargeDetail?cardNo=" + this.data.vipCardNo);
  },
  nowPayBindTap: function () {
    var that = this;
    var rechargeSchemeList = that.data.rechargeSchemeList;
    var rechargeSchemeListBean = null;
    for (var i = 0; i < rechargeSchemeList.length; i++) {
      if (rechargeSchemeList[i].isSelect) {
        rechargeSchemeListBean = rechargeSchemeList[i];
        break;
      }
    }
    if (rechargeSchemeListBean != null) {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/newSubscribe/querySystemTemplateId',
        data: {
          "companyId": app.getExtCompanyId(),
          "typeId": "5"
        },
        success: function (res) {
          var templateArray = res.data.templateArray;
          if (templateArray != null && templateArray.length > 0) {
            wx.requestSubscribeMessage({
              tmplIds: templateArray,
              success(res) {
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded' // 默认值
                  },
                  method: "POST",
                  data: {
                           "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
                    "loginId": app.getLoginId(),
                    "userRole": app.getUserRole(),
                    "localStoreId": app.getExtStoreId(),
                    "companyId": app.getExtCompanyId(),
                    "openId": app.getOpenId(),
                    "id": rechargeSchemeListBean.id,
                    "systemAmount": rechargeSchemeListBean.rechargeAmount,
                    "vipCardNo": that.data.vipCardNo
                  },
                  url: app.projectName + '/vipCard/vipCardRecharge',
                  success: function (res) {
                    var orderPay = res.data.return_Code;
                    var flag = res.data.flag;
                    var payMoney = res.data.payMoney;
                    that.setData({
                      payMoney: payMoney
                    })
                    if (flag) {
                      if (orderPay == 0) { //需要支付
                        var param = res.data;
                        wx.requestPayment({
                          'timeStamp': param.timeStamp,
                          'nonceStr': param.nonceStr,
                          'package': param.package,
                          'signType': param.signType,
                          'paySign': param.paySign,
                          success: function (res) {
                            that.checkReward(4);
                          },
                          fail: function (res) {
                            if (res.errMsg === 'requestPayment:fail cancel') {
                              app.showModal({
                                content: '取消支付'
                              })
                            } else {
                              app.showModal({
                                content: '支付失败'
                              })
                            }
                          }
                        })
                      } else if (orderPay == 1) { //不需要支付的
                        that.checkReward(4);
                      }
                    } else {
                      app.showModal({
                        content: '支付失败'
                      })
                    }
                  }
                })
              }, fail(e) {
              }
            })
          } else {
            wx.request({
              header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
              },
              method: "POST",
              data: {
                       "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
                "loginId": app.getLoginId(),
                "userRole": app.getUserRole(),
                "localStoreId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId(),
                "openId": app.getOpenId(),
                "id": rechargeSchemeListBean.id,
                "systemAmount": rechargeSchemeListBean.rechargeAmount,
                "vipCardNo": that.data.vipCardNo
              },
              url: app.projectName + '/vipCard/vipCardRecharge',
              success: function (res) {
                var orderPay = res.data.return_Code;
                var flag = res.data.flag;
                if (flag) {
                  if (orderPay == 0) { //需要支付
                    var param = res.data;
                    wx.requestPayment({
                      'timeStamp': param.timeStamp,
                      'nonceStr': param.nonceStr,
                      'package': param.package,
                      'signType': param.signType,
                      'paySign': param.paySign,
                      success: function (res) {
                        that.checkReward(4);
                      },
                      fail: function (res) {
                        if (res.errMsg === 'requestPayment:fail cancel') {
                          app.showModal({
                            content: '取消支付'
                          })
                        } else {
                          app.showModal({
                            content: '支付失败'
                          })
                        }
                      }
                    })
                  } else if (orderPay == 1) { //不需要支付的
                    that.checkReward(4);
                  }
                } else {
                  app.showModal({
                    content: '支付失败'
                  })
                }
              }
            })
          }
        }
      })
    } else {
      wx.showToast({
        title: '请选择方案',
        icon: 'none',
        duration: 1000,
        mask: true
      })
    }
  },
  //查询是否有奖励
  checkReward: function (eventType) {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'config/loadConfigList',
      showLoading: false,
      data: {
        eventType: 4,
        merchantId: app.getExtCompanyId(),
        minMoney: that.data.payMoney,
        userId: app.getUserId(),
        userName: app.getLoginName()
      },
      success: (res) => {
        if (res.length > 0) {
          for (var i = 0; i < res.length; i++) {
            res[i]["checked"] = true;
            if (res[i].rightsType == 4 || res[i].rightsType == 5 || res[i].rightsType == 6) {
              that.toUser(res[i].configId);
            }
          }
          that.setData({
            rewardList: res,
            result: res
          })
          that.reward();
        } else {
          wx.showToast({
            title: "支付成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              app.turnBack();
            }
          })
        }
      }, fail: function () {
        wx.showToast({
          title: "支付成功",
          icon: 'success',
          duration: 2000,
          mask: true,
          success: function () {
            app.turnBack();
          }
        })
      }
    })
  },
  //调用奖励弹窗
  reward: function () {
    var that = this;
    that.setData({
      which: "reward"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  /*给用户发放优惠券等*/
  toUser: function (configId) {
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.errorcode == '1000') {
          console.log("获取成功！")
        }
      }
    })
  },
  gocjBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/turntableActivity/turntableActivity?gameId=' + e.currentTarget.dataset.gameid);
  },
  goSignBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/sign/sign?configId=' + configId);
  },
  goWordBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/collect/collect?configId=' + configId);
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})