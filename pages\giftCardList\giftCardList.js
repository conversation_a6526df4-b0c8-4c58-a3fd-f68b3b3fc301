var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cardList:[],
    noCardHistory: app.imageUrl + 'noCardHistory.png',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    wx.request({
      url: app.giftCardProjectName + '/api/findCouponLogList',
      data: {
        "companyId": app.getExtCompanyId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var code = res.data.errorcode;
        var message = res.data.errormsg;
        if (code == 1000) {
           that.setData({
             cardList:res.data.result
           })
        } else {
          app.showModal({
            title: '提示',
            content: message == "" ? "系统异常，稍后在试" : message
          });
          return;
        }
      }
    })
  },
  giftCardDeliverBindTap:function(e){
    var loginId = e.currentTarget.dataset.id;
    app.navigateToPage('/pages/giftCardShowMore/giftCardShowMore?loginId='+loginId);
  },
  backIndexBindtap:function(){
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})