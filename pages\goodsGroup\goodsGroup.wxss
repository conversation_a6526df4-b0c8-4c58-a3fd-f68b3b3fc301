@import '../popupTemplate/popupTemplate.wxss';

page {
  position: relative;
  height: 100%;
  width: 100%;
  background: #f4f4f4;
}

.contant_box {
  padding-bottom: 44px;
  position: absolute;
  width: 100%;
}

.goods_content {
  padding: 1px 10px;
  background: #fff;
}

.goods_title {
  font-size: 36rpx;
  color: #191D21;
  font-family: PingFang-SC-Bold;
  /*height: 30px;*/
  line-height: 40rpx;
  /*overflow: hidden;*/
  display: block;
  width: 100%;
  font-weight: bold;
}

.goods_adv {
  font-size: 26rpx;
  overflow: hidden;
  display: block;
  width: 100%;
  margin-bottom: 10rpx;
  margin-top: 10rpx;
  width: 234px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #A2A2A2;

}

.goods_price {
  color: #FF7E00;
  display: block;
  width: 100%;
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
}

.goods_price>label:nth-of-type(1) {
  margin-right: 40rpx;
}

.goodsMoq {
  color: #686868;
  display: block;
  width: 100%;
  font-size: 24rpx;
}

.goodsMoq>label:nth-of-type(1) {
  margin: 0 80rpx 0 20rpx;
}

.goods-all-stock,
.express-fee {
  margin: 0 20px 6px 0;
  color: #666;
  display: inline-block;
  font-size: 12px;
  line-height: 20px;
}

.clock {
  font-size: 14px;
}

.goods_evaluate {
  padding: 8px;
  text-align: center;
  background: #f1f1f1;
  color: #b2b2b2;
  font-size: 12px;
}

.line {
  width: 45px;
  height: 1px;
  background: #ccc;
  display: inline-block;
  vertical-align: middle;
}

.icon-good-comment {
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px;
}

.goods_evaluateBox {
  padding: 10px;
  background: #fff;
}

.evaluate_box {
  border-bottom: 1px solid #f4f4f4;
  padding-bottom: 8px;
  font-size: 14px;
  margin-bottom: 10px;
}

.goods-comment-label {
  display: inline-block;
  padding: 2px 3px;
  background-color: #ffebe9;
  color: #333;
  margin-right: 5px;
  border-radius: 100px;
  font-size: 12px;
  width: 76px;
  text-align: center;
  height: 24px;
  line-height: 24px;
  margin-bottom: 10px;
}

.comment_detail {
  color: #666;
  padding: 5px 0;
  margin-bottom: 5px;
  border-bottom: 1px dashed #dadada;
}

.level_star {
  float: left;
  display: block;
  width: 24%;
  height: 20px;
  font-size: 16px;
  color: #FF7E00;
  line-height: 20px;
}

.comment_time {
  float: left;
  height: 20px;
  line-height: 20px;
  width: 46%;
  display: block;
  font-size: 12px;
}

.comment_name {
  float: left;
  width: 30%;
  line-height: 20px;
  overflow: hidden;
  font-size: 12px;
  text-align: right;
}

.commnet_text {
  width: 100%;
  display: block;
  font-size: 13px;
  color: #333;
  margin-top: 5px;
}

.more_evaluate {
  text-align: center;
  margin-top: 10px;
}

.allComment {
  color: #FF7E00;
  border: 1px solid #FF7E00;
  width: 104px;
  line-height: 26px;
  font-size: 12px;
  border-radius: 13px;
  display: inline-block;
}

.goods_detail {
  border-bottom: 1px solid #f4f4f4;
  font-size: 14px;
  display: block;
  padding: 5px 10px;
  background: #fff;
}

/*.goods_pic {
  padding: 0 10px;
   margin-top: 8px;
  
  margin-bottom: 50px;
}*/

.wxParse-section view {
  padding: 0 !important;
}

.wxParse-p image {
  width: 100% !important;
}

.foot_box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  height: calc(100rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.flex-sub-box-3 {
  width: 30%;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
}

.buyBtn {
  height: 100rpx;
  background: #FEC836;
  font-size: 32rpx;
  font-family: PingFang SC;
  color: #FFFFFF;
  text-align: center;
}


.addBtn {
  height: 100rpx;
  font-size: 32rpx;
  background: #F20100;
  font-family: PingFang SC;
  color: #FFFFFF;
  text-align: center;
}

.soldBtn {
  height: 100rpx;
  font-size: 32rpx;
  background: grey;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 100rpx;
  color: #FFFFFF;
  text-align: center;
}

.little_icon {
  background: #fff;
}

.flex-sub-box-2 {
  width: 49%;
  float: left;
  /*margin-top: 9px;*/
}

.flex-sub-box-2:first-child {
  width: 50%;
  /*border-right: 1px solid #e5e5e5;*/
}

.flex-sub-box-2 image {
  width: 52rpx;
  height: 52rpx;
  margin: 0px auto;
  display: block;
}

.flex-sub-box-2 label {
  font-size: 24rpx;
  text-align: center;
  width: 100%;
  display: block;
}

.btn {
  display: inline-block;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  /*padding: 7px;*/
  padding: 14rpx 0;
  margin: 0;
  font-size: 28px;
  cursor: pointer;
  line-height: 36rpx;
  -webkit-appearance: none;
  text-align: center;
}

.btn-yellow {
  background: #f5a623;
}

.btn-red {
  background: #FF7E00;
}

.add-to-shoppingcart,
.buy-goods-directly {
  width: 100%;
  padding-bottom: 0px;
  border: none;
  box-sizing: border-box;
  line-height: 24px;
  border-radius: 0;
  color: #fff;
  font-size: 13px;
}

/**黑色背景**/

.black_bg1 {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}

/**加入购物车 弹框**/

.scroll_block {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 130;
  padding-bottom: 140rpx;
}

.addgoods_box {
  width: 100%;
  height: 60px;
  /* border-bottom: 1px solid #e5e5e5; */
  background: #fff;
}

.addgoods_pic {
  width: 162rpx;
  height: 162rpx;
  background: #fff;
  position: absolute;
  left: 30rpx;
  /* overflow: hidden; */
}

.addgoods_pic image {
  width: 100%;
  height: 100%;
}

.addgoods_title,
.addgoods_price {
  padding-left: 230rpx;
  overflow: hidden;
  white-space: nowrap;
  padding-right: 60rpx;
  text-overflow: ellipsis;
  padding-top: 6rpx;
}

.addgoods_title {
  background: #fff;
  font-size: 34rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #191D21;
}

.addgoods_price {
  color: #FF7E00;
  font-size: 13px;
}

.addgoods_wrap {
  padding-left: 230rpx;
  height: 40rpx;
  display: block;
  white-space: nowrap;
  padding-right: 32rpx;
  text-overflow: ellipsis;
  padding-top: 6rpx;
  font-size: 52rpx;

}

.goods_classify {
  background: #fff;
  padding: 20rpx 20rpx 10rpx 20rpx;
}

.goods_classify label {
  margin-bottom: 20rpx;
  line-height: 56rpx;
  font-size: 28rpx;
}

.goods_classify view text {
  display: inline-block;
  padding: 0 32rpx;
  color: #333;
  background: #F5F5F5;
  font-size: 24rpx;
  margin-bottom: 16rpx;
  margin-right: 20rpx;
  line-height: 56rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.addgoods_number {
  width: 100%;
  background: #fff;
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  /**border-bottom: 1px solid #e5e5e5;**/
}

.limited_quantity {
  float: left;
  padding-left: 40rpx;
  padding-top: 10rpx;
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  color: #525252;
}

.stock {
  font-size: 13px;
  float: right;
  margin-right: 5px;
  margin-top: 4px;
}

.plus_minus {
  /* float: right; */
  padding-right: 15px;
  margin-top: 3px;
}

.minus_box {
  width: 60rpx;
  height: 60rpx;
  background: #FAFBFA;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
  float: left;
  color: #CBCBCB;
}

.plus_box {
  height: 60rpx;
  background: #FAFBFA;
  border-left: none;
  line-height: 60rpx;
  width: 60rpx;
  text-align: center;
  font-size: 40rpx;
  float: left;
  color: #CBCBCB;
}

.plus_minus input {
  width: 80rpx;
  height: 60rpx;
  float: left;
  border: none;
  font-size: 26rpx;
  text-align: center;
  border: 1rpx solid #ccc;

}

.pay-add-to-shoppingcart {
  display: block;
  padding: 0px;
  font-size: 28rpx;
  line-height: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  flex: 1;
  color: #fff;
  background-color: #FF7E00;
  width: 660rpx;
  position: fixed;
  left: 45rpx;
  right: 0;
  bottom: 30rpx;
  z-index: 1300;
}

.page-dialog-close1 {
  border: 0px solid red;
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  z-index: 10;
  display: block;
  /* border: 1px solid #67666f;
  color: #67666f; 
  text-align: center;
  line-height: 16px;
  border-radius: 50%;
  font-size: 26px;
  margin: 5px auto;*/
}

/**团购**/

.goods-seckill-original {
  margin-top: -2px;
  font-size: 12px;
}

.group_box {
  width: 100%;
  height: 120rpx;
  background: #FF7E00;
  z-index: 50;
}

.goods-seckill-left {
  width: 60%;
  float: left;
  position: absolute;
  padding-left: 40rpx;
  height: 80rpx;
  color: white
}

.goods-seckill-left:after {
  content: '';
  width: 0;
  height: 0;
  border-right: 10px solid transparent;
  vertical-align: top;
  position: absolute;
  right: 40rpx;
  top: 0;
  z-index: 1;
}

.goods-current-price {
  display: inline-block;
  vertical-align: middle;
  color: #fff;
  margin-top: 1px;
  font-size: 13px;
}

.goods-current-price label {
  font-size: 18px;
}

.goods-original-price {
  text-decoration: line-through;
  vertical-align: middle;
  color: #fff;
  display: inline-block;
  font-size: 13px;
}

.goods-seckill-sign {
  display: inline-block;
  padding: 19px 4px;
  /**border: 1px solid #fff;**/
  font-size: 30px;
  color: #fff;
  margin-left: 10px;
  line-height: 11px;
}

.activity_time {
  width: 35%;
  float: right;
  height: 120rpx;
  padding-left: 20rpx;
}

.activity_time>label:first-child {
  color: #fff;
  text-align: center;
  width: 100%;
  display: block;
  line-height: 26rpx;
  margin-top: 16px;
  font-weight: bold;
  font-size: 28rpx;
  background: none;
}

.activity_time label {
  width: 40rpx;
  height: 40rpx;
  background: #FFFFFF;
  border-radius: 4px;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: bold;
  line-height: 40rpx;
  color: #FF7E00;
  opacity: 1;
  text-align: center;
}


.active_classify {
  background: #FFF8F9 !important;
  color: #FF7E00 !important;
  border: 1px solid #FF7E00;
}

/*店铺入口*/

.shop_entrance {
  height: 40px;
  background: #fff;
  margin-top: 10px;
  padding: 10px;
  position: relative;
}

.shop_logo {
  float: left;
  width: 40px;
  height: 40px;
  border-radius: 3px;
}

.boxright {
  padding-left: 50px;
  padding-right: 90px;
}

.boxright label:first-child {
  width: 100%;
  display: block;
  color: #2e2e2e;
  font-size: 14px;
}

.boxright label:last-child {
  width: 100%;
  display: block;
  color: #9c9da1;
  font-size: 12px;
  margin-top: 5px;
}

.sales_volume {
  margin-left: 10px;
}

.enter_button {
  position: absolute;
  top: 18px;
  padding: 0 3px;
  right: 10px;
  font-size: 12px;
  border: 1px solid #b4b4b4;
  border-radius: 5px;
  line-height: 25px;
  color: #3f3f3f;
}

.enter_button image {
  width: 18px;
  height: 18px;
  float: left;
  margin-top: 4px;
}

/**晒图**/

.show_pic {
  width: 100%;
  margin: 10px 0 0 0;
}

.show_pic image {
  width: 60px;
  height: 60px;
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 5px;
}

/**服务**/

.service_note {
  padding: 10px 10px 0 10px;
  position: relative;
  margin-top: 10px;
  background: #fff;
}

.note_title {
  float: left;
  font-size: 14px;
}

.note_tips {
  padding-left: 50px;
  height: 60px;
}

.note_tips label {
  float: left;
  margin-right: 10px;
  height: 30px;
  color: #b2b2b2;
  font-size: 12px;
}

.quduan_tips label {
  display: block;
  height: 30px;
  color: #b2b2b2;
  font-size: 12px;
}

.quduan_tips label text {
  width: 5px;
  height: 5px;
  background: #FF7E00;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.note_tips label text {
  width: 5px;
  height: 5px;
  background: #FF7E00;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.personal_more {
  width: 18px;
  height: 18px;
  position: absolute;
  right: 10px;
  top: 20px;
}

/**服务弹框**/

.serviceBox {
  width: 100%;
  background: #fff;
  position: fixed;
  z-index: 130;
  bottom: 0;
}

.serviceBox_title {
  width: 100%;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #ececec;
  display: block;
  text-align: center;
  font-size: 15px;
}

.single_service {
  width: 94%;
  margin: 10px 3%;
}

.single_top {
  font-size: 14px;
}

.single_top text {
  width: 5px;
  height: 5px;
  background: #FF7E00;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.single_bottom {
  padding: 10px 13px;
  color: #888;
  display: block;
  font-size: 12px;
  line-height: 20px;
}

/**掌柜回复**/

.reply_box {
  width: 100%;
  background: #f1f1f1;
  border-radius: 5px;
  position: relative;
  margin-top: 15px;
}

.reply_row {
  position: absolute;
  top: -10px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #f1f1f1;
}

.reply_word {
  color: #666;
  font-size: 13px;
  line-height: 24px;
  padding: 10px;
  display: block;
}

.collageView {
  width: 100%;
  background: #fff;
  margin-top: 10px;
}

.collageFristView {
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
}

.collageFristLable {
  float: left;
  font-size: 14px;
  color: #444;
}

.collageTwoLable {
  float: right;
  color: #999;
  font-size: 12px;
}

.collageUserView {
  padding: 10px;
  height: 60px;
  border-top: 1px solid #f1f1f1;
}

.collageUserImage {
  width: 60px;
  height: 60px;
  float: left;
  border-radius: 50%;
}

.collageUserFristView {
  padding-left: 70px;
  height: 60px;
}

.collageUserLable {
  width: 38%;
  height: 60px;
  float: left;
  display: block;
  line-height: 60px;
  overflow: hidden;
  font-size: 13px;
}

.collageUserTwoView {
  float: left;
  height: 60px;
  width: 32%;
  text-align: right;
}

.collageUserpopView {
  float: left;
  height: 60px;
  width: 60%;
  text-align: left;
}

.collageUserThreeView {
  display: block;
  color: #444;
  font-size: 12px;
  line-height: 20px;
  margin-top: 9px;
}

.collageUserFristLable {
  font-size: 12px;
  color: #999;
  line-height: 20px;
}

.collageUserButton {
  width: 66px;
  height: 28px;
  line-height: 28px;
  margin-top: 16px;
  float: right;
  background: red;
  color: #fff;
  font-size: 12px;
}

.titleWrap {
  text-align: center;
  padding: 15px 0 20px 0;
}

.saleWrap {
  border: 1px solid #fff;
  padding: 0 5px;
  width: 80%;
  height: 350px;
  background: #fff;
  border-radius: 8px;
  position: fixed;
  top: 22%;
  left: 9%;
  z-index: 20;
}

.userWrap {
  border: 1px solid #fff;
  padding: 20px 5px 20px;
  width: 80%;
  background: #fff;
  border-radius: 8px;
  position: absolute;
  top: 2%;
  left: 9%;
  z-index: 20;
  text-align: center;
}

.personNeed {
  font-size: 11px;
  margin-left: 5px;
}

.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.popUserImage {
  width: 60px;
  height: 60px;
  background: pink;
  border-radius: 50%;
}

.waitUserImage {
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50%;
  border: 1px dashed #666;
  margin-left: 20px;
}

.goButton {
  width: 80%;
  height: 36px;
  line-height: 36px;
  margin-top: 16px;
  background: red;
  color: #fff;
  font-size: 14px;
}

.firstuser {
  padding: 2px 4px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 8px;
  background: #988250;
  border: 1px solid #988250;
  font-size: 12px;
}

.imageWrap {
  text-align: center;
  margin-top: 20px;
}

.firstWrap {
  position: relative;
  display: inline-block;
}

.bottom_price {
  height: 24px;
  line-height: 30px;
  width: 100%;
  display: inline-block;
  padding: 0px;
}

.bottom_title {
  height: 20px;
  line-height: 10px;
  display: inline-block;
  width: 100%;
  padding: 0px;
  vertical-align: top;
}

.cart_append {
  border-left: 1px solid #e6e6e6;
  font-size: 12px;
  padding: 0px;
  background: #FF7E00;
  color: #fff;
}

.cartNum {
  position: absolute;
  top: 0;
  left: 33%;
  background: #FF7E00;
  font-size: 20rpx;
  padding: 2rpx 12rpx;
  color: #fff;
  border-radius: 18rpx;
}

.icondirect {
  float: right;
  transform: rotate(90deg);
  color: #FF7E00;
}

/**详情展示的属性**/

.goods_attr {
  padding: 20rpx;
  background: #fff;
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
}

.goods_attr>view>view {
  margin-top: 20rpx;
}

.goods_attr text {
  color: #000;
}

.attr_wrap {
  background: #fff;
}

.choose_attr {
  padding: 20rpx;
  color: #666;
  font-size: 26rpx;
}

.choose_attr text {
  color: #333;
  margin-right: 10rpx;
}

.deliverFee {
  font-size: 20rpx;
  margin-top: 20rpx;
  color: #333;
  padding-bottom: 20rpx;
}

.shareGood {
  float: right;
  width: 15%;
  margin-top: 10rpx;
}

.shareGood image {
  width: 35rpx;
  height: auto;
  margin-left: 30%;
}

.shareGood view {
  font-size: 20rpx;
  margin-left: 30%;
  color: #666;
}

.onPromotion {
  background: #fff;
  padding: 20rpx;
  margin: 20rpx 0;
}

.pro_left {
  font-size: 24rpx;
  float: left;
  margin-top: 8rpx;
}

.pro_title {
  border-radius: 4rpx;
  border: 1rpx solid #FF7E00;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  color: #FF7E00;
}

.pro_inner {
  margin-left: 20rpx;
  font-size: 24rpx;
}

.shopImage {
  float: left;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.shopName {
  float: left;
  margin-top: 30rpx;
  margin-left: 30rpx;
}

.shopMark {
  margin-top: 15rpx;
  float: left;
  margin-right: 18rpx;
  background: #FF7E00;
  padding: 4rpx;
  color: #fff;
  font-size: 22rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

.e_content {
  color: #000;
  font-size: 24rpx;
}

.userImg {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  float: left;
}

.userN {
  float: left;
  margin-top: 10rpx;
  margin-left: 10rpx;
}

.userS {
  font-size: 25rpx;
  color: #FF7E00;
}

.noContact {
  width: 50% !important;
}

.noContact .cartNum {
  left: 31%;
}

.wxParse-img {
  margin-top: -12rpx;
}

.unit_num {
  vertical-align: top;
  margin-left: 10rpx;
  font-size: 28rpx;
  line-height: 60rpx;
}

.redBox {
  background-color: red;
  color: #fff;
  font-size: 24rpx;
  padding: 2rpx 6rpx;
}

.personal_more {
  float: right !important;
  width: 14px !important;
  height: 14px !important;
  margin-right: 0 !important;
}

.clearF:after {
  display: block;
  content: "";
  clear: both;

}

.redBox_empty {
  border: 1px solid red;
  padding: 2rpx 6rpx;
  font-size: 24rpx;
  color: red;
}


/*卡券*/
.noCoupon {
  height: 40px;
  font-size: 14px;
  color: #666;
  line-height: 40px;
  background: #fff;
  padding: 0 10px;
}

.oneCoupon {
  margin: 20rpx 0;
}

.couponInfo {
  width: 90%;
  margin: 0 auto;
  border-radius: 20rpx;
}

.couponInfo .couponValue {
  text-align: center;
  color: #fff;
  float: left;
  width: 22%;
  font-size: 40rpx;
  padding-top: 40rpx;
}

.couponValue text {
  font-size: 32rpx;
}

.couponInfo .couponDiscount {
  /* height: 130rpx; */
  float: left;
  width: 50%;
  font-size: 26rpx;
  padding: 28rpx 0;
  color: #fff;
  padding-left: 36rpx;
  border-left: 1px dashed #fff;
}

.couponDiscount view:last-child {
  margin-top: 8rpx;
}

.couponStatus {
  width: 20%;
  text-align: center;
  float: left;
  line-height: 130rpx;
}

.undrawStatus {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  background: #fff;
  border-radius: 10rpx;
}

.drawStatus {
  font-size: 26rpx;
  color: #fff;
}

.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.noblueColor {
  margin-left: 19rpx;
  margin-top: 5rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #181D21;
}

.blueColor {
  margin-left: 19rpx;
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
  color: #333333;
}

.indexShow {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background: #000000;
  opacity: 0.2;
  color: #FFF;
  font-size: 24rpx;
  padding: 5rpx 15rpx;
  border-radius: 30rpx;
}

.goodn_one {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #686868;
  float: right;
  margin-right: -80rpx;
  margin-bottom: 47rpx;
}

.goodn_two {
  font-size: 30rpx;
  font-family: PingFang-SC-Bold;
  color: #161616;
}

.goodn_three {
  border: 1px solid red;
  width: 710rpx;
  margin: 0px auto;
  border: 2rpx solid #F7F7F7;
  border-radius: 10rpx;
}

.goodn_four {
  display: flex;
  align-items: center
}

.goodn_five {
  padding: 13rpx 50rpx 20rpx 31rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #686868;
}

.goodn_six {
  padding: 13rpx 10rpx 20rpx 0rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #686868;
}

.goodn_seven {
  font-size: 30rpx;
  font-family: PingFang-SC-Bold;
  color: #161616;
}

.goodn_eight {
  float: right;
  font-size: 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #FF7E00;
}

.goodn_nine {
  display: flex;
  overflow: hidden;
  padding: 20rpx;
}

.goodn_ten {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.goodn1_one {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #8B8B8B;
  float: right;
}

.goodn1_two {
  margin-left: 19rpx;
  margin-top: 5rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #333333;
}

.goodn1_three {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #8B8B8B;
  opacity: 1;
  text-align: center;
  padding-bottom: 33rpx;
}

.goodn1_four {
  height: 373rpx;
  width: 750rpx;
}

.goodn1_five {
  width: 48rpx;
  margin-top: 4rpx;
}

.goodn1_six {
  position: relative;
  overflow: hidden;
  width: 33%;
}

.goodn1_seven {
  width: 100rpx;
  height: 375rpx;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.goodn1_eight {
  font-size: 20rpx;
  color: #666;
}

.goodn1_nine {
  padding-top: 10rpx;
  width: 600rpx;
  z-index: 999;
  position: absolute;
  top: 5%;
  background: #fff;
  margin-left: 75rpx;
}

.goodn1_ten {
  margin-top: 20rpx;
  background: #FF7E00;
  color: #fff;
  padding: 20rpx 0;
  text-align: center;
}

.goodn2_one {
  z-index: 10;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.goodn2_two {
  position: fixed;
  width: 100%;
  height: 200rpx;
  background: #fff;
  padding: 20rpx 0;
  border-radius: 16rpx 16rpx 0 0;
}

.goodn2_three {
  display: flex;
  justify-content: space-between;
  color: #919398
}

.goodn2_four {
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #686868;
  margin-top: 30rpx;
  color: #FF7E00
}

.goodn2_five {
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  color: #525252;
}

.goodn2_six {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #525252;
  margin-top: 13rpx;
}

.goodn2_seven {
  width: 710rpx;
  height: 160rpx;
  margin: 0px auto;
  display: flex;
  align-items: center;
  justify-items: center;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
}

.goodn2_eight {
  width: 128rpx;
  height: 64rpx;
  background: linear-gradient(90deg, #FF0202 0%, #FE5406 100%);
  border-radius: 8rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #FEFEFE;
  line-height: 64rpx;
  margin: 0px auto;
}

.goodn2_nine {
  height: 50rpx;
  line-height: 60rpx;
}

.goodn2_ten {
  height: 50rpx;
  line-height: 40rpx;
  font-size: 24rpx;
}

.goodn3_one {
  font-size: 34rpx;
  font-family: PingFang SC;
  font-weight: 500;
}



.goodn3_two {
  font-size: 52rpx;
  font-family: PingFang SC;
  font-weight: 500;
}

.goodn3_three {
  font-size: 28rpx;
  font-family: PingFang-SC-Medium;
  color: #FFFFFF;
}

.goodn3_four {
  text-decoration: line-through;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  padding: 15rpx;
}

.goodn3_five {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  padding-left: 5rpx;
}

.goodn3_six {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  padding: 0 10rpx 0 10rpx;
}

.goodn3_seven {
  height: 100rpx;
  font-size: 32rpx;
  background: #FE5406;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 100rpx;
  color: #FFFFFF;
  text-align: center;
}

.goodn3_eight {
  height: 100rpx;
  background: grey;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 100rpx;
  color: #FFFFFF;
  text-align: center;
}

.goodn3_nine {
  height: 100rpx;
  background: #FEC836;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 100rpx;
  color: #FFFFFF;
  text-align: center;
}

.goodn3_ten {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #FFFFFF
}

.goodn4_one {
  font-size: 34rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #F20100;
}

.goodn4_two {
  font-size: 52rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #F20100;
}

.goodn4_four {
  text-decoration: line-through;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 73rpx;
  color: #707070;
}

.goodn4_five {
  margin-left: 44rpx;
  padding: 5rpx 20rpx;
  width: 151rpx;
  height: 40rpx;
  background: #F20100;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 100;
  line-height: 40px;
  text-align: center;
  color: #FFFFFF;
}

.swiper-view {
  margin-top: 16rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 320rpx;
}

.swiper_container {
  height: 320rpx;
  width: 100%;
}