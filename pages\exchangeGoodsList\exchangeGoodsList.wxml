<!-- <view style="display:flex;">
  <view style="width:280rpx;height:280rpx;">
    <image src="{{exchangeImg}}" style="width:280rpx;height:280rpx;"></image>
  </view>
  <view class="activity">
    <view class="activityName">活动名称</view>
    <view class="activityDescription">描述</view>
    <view class="activityCondition">换购条件</view>
    <view class="activityInfo">活动说明</view>
  </view>
</view> -->
<!-- 换购商品 -->
<block wx:for="{{exchangeCommodityList}}" wx:for-item="egoods" wx:key="">
	<view class='changeGoodsView'>
		<image class='changeGoodsImage' src='{{egoods.commodityPic}}'></image>
		<view class='changeGoodschildView'>
			<label class='changeGoodsName'>{{egoods.commodityName}}</label>
			<label class='changeGoodsNumber'>
				{{egoods.commoditySaletotal}}人换购
				<block wx:if="{{egoods.skuAttrStr!=''}}">({{egoods.skuAttrStr}})</block>
			</label>
			<view style='width:100%; height:30px;'>
				<view style='float:left; width:50%;'>
					<label class='changeGoodsPrice'>¥{{egoods.commoityExchangePrice}}</label>
					<label class='changeGoodsPrice1'>¥{{egoods.commodityOldPrice}}</label>
				</view>
				<!-- 加满商品 -->
				<view style='float:right;' hidden='{{!egoods.select}}'>
					<view class='goodsreduce' data-skuId='{{egoods.skuId}}' data-id='{{egoods.commodityId}}' bindtap='goodsReduceBindTap'>
						-
					</view>
					<label class='goodsNumber'>{{egoods.actualExchangeNum}}</label>
					<view class='goodsplus' data-skuId='{{egoods.skuId}}' data-id='{{egoods.commodityId}}' bindtap='goodsPlusBindTap'>
						+
					</view>
				</view>
				<!-- 加满商品 -->
				<!-- 换购按钮 -->
				<view style='float:right;' hidden='{{egoods.select}}'>
					<button class='changeGoodsBtn' data-skuId='{{egoods.skuId}}' data-id='{{egoods.commodityId}}' bindtap='changePurchaseBindTap'>
						换购
					</button>
				</view>
				<!-- 换购按钮 -->
			</view>
		</view>
	</view>
</block>
<!-- 换购商品 -->
<view class="submitBtn" bindtap="submit">确认</view>