page {
  background: #f4f4f4;
}

.tips_pic {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.top_nav::after {
  content: "";
  float: none;
  display: block;
}

.top_nav {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 20;
  background: #fff;
}

.next_btn {
  font-size: 14px;
  text-align: right;
  padding-right: 10px;
  height: 26px;
  line-height: 26px;
}

.content_box {
  width: 100%;
  height: auto;
  font-size: 14px;
}

.single_goods {
  background: #fff;
  width: 100%;
  height: 100px;
  border-bottom: 1px solid #efefef;
  overflow: hidden;
  position: relative;
}

.check-box {
  margin-left: 10px;
  margin-top: 40px;
}

.check-box2 {
  background: #FF7E00;
}

.goods_pic {
  width: 75px;
  height: 75px;
  margin: 12.5px 10px;
  float: left;
  background: pink;
}

.goods_box {
  height: 70px;
  padding-left: 130px;
  padding-right: 10px;
  margin-top: 15px;
}

.goods_box {
  height: 70px;
  padding-left: 130px;
  padding-right: 10px;
  margin-top: 15px;
}

.goods_name {
  height: 26px;
  line-height: 26px;
  overflow: hidden;
  display: block;
}

.goodsBox {
  width: 100%;
  margin-top: 15px;
  height: 30px;
  line-height: 30px;
}

.goods_price {
  float: left;
}

.goods_num {
  width: 110px;
  float: right;
  margin-top: 2.5px;
}

.minus {
  width: 25px;
  height: 25px;
  background: #f1f1f1;
  display: block;
  float: left;
  margin-right: 5px;
  text-align: center;
  line-height: 25px;
}

.goods_num input {
  width: 50px;
  background: #f1f1f1;
  color: #FF7E00;
  float: left;
  margin-right: 5px;
  height: 25px;
  line-height: 25px;
  text-align: center;
}

.adds {
  width: 25px;
  height: 25px;
  background: #f1f1f1;
  display: block;
  float: left;
  text-align: center;
  line-height: 25px;
}

/**合计**/
.all_box {
  width: 100%;
  height: 44px;
  line-height: 44px;
  border-top: 1px solid #ececec;
  background: #fff;
  margin-bottom:20rpx;
}

.checkBox {
  margin-top: 12px;
}

.allBox {
  float: right;
  margin-right: 10px;
}
.reason text{
  float: left;
  width: 80px;

}
.reason{
  display: flex;
  padding:10px;
  background-color: #fff;
}

.addPic {
  width: 96%;
  padding: 5px 2%;
  /**height: 70px;
  border-bottom: 1px solid #e0e0e0;**/
}

.add_pic {
  width: 60px;
  height: 60px;
  /**float: left;
  margin-right: 8px;**/
}
.submitBtn{
  width: 750rpx;
  height:88rpx;
  line-height:88rpx;
  background-color: red;
  color:#fff;
  text-align: center;
  position: fixed;
  bottom: 0;
  left:0;

}
