// var app = getApp();
// Page({

//   /**
//    * 页面的初始数据
//    */
//   data: {
//     evaluateSuccess: app.imageUrl + 'evaluateSuccess.png',
//     turntableBanner: app.imageUrl + 'turntableBanner.png',
//     iscjActivity: false
//   },

//   /**
//    * 生命周期函数--监听页面加载
//    */
//   onLoad: function (options) {
//     var orderTotalMoney = options.orderTotalMoney;
//     this.querySupplierSetting(orderTotalMoney);
//   },
//   gocjBindTap: function () {
//     app.reLaunchToPage('/pages/turntableActivity/turntableActivity?sceneType=2');
//   },
//   goIndex: function () {
//     wx.switchTab({
//       url: "/pages/index/index"
//     });
//   },
//   goOrderList: function () {
//     app.turnToPage("/pages/indexThree/indexThree");
//   },
//   querySupplierSetting: function (orderTotalMoney) {   //查询是否有抽奖权限
//     var that = this;
//     wx.request({
//       header: {
//         'content-type': 'application/x-www-form-urlencoded' // 默认值
//       },
//       method: "POST",
//       url: app.projectName + '/newSupplierSetting/querySupplierSetting',
//       data: {
//         "companyId": app.getExtCompanyId(),
//         "type": 2   //评价成功
//       },
//       success: function (res) {
//         if (res.data.flag) {
//           wx.request({
//             header: {
//               'content-type': 'application/x-www-form-urlencoded' // 默认值
//             },
//             method: "POST",
//             url: app.gameProjectName + '/game/gameInfoController/getGameInfo',
//             data: {
//               "merchantId": app.getExtCompanyId(),
//                      "odbtoken":app.getodbtoken(),
        //"loginToken":app.getloginToken(),
//               "sceneType": 2,
//             },
//             success: function (res) {
//               if (res.data.errorcode == 1000) {
//                 var money = res.data.result.money;
//                 if (parseFloat(orderTotalMoney) >= parseFloat(money)) {
//                   that.setData({
//                     iscjActivity: true
//                   })
//                 }
//               }
//             }
//           })
//         }
//       }
//     })
//   }
// })

const app = getApp();
const http = require('../../utils/http')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    backpage_comment: app.imageUrl + 'backpage_comment.png'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var eventType=options.eventType;
    var minMoney=0;
    if (options.orderTotalMoney){
      minMoney = options.orderTotalMoney
    }
    /*http.post({
      urlName: 'activity',
      url: 'config/loadConfigList',
      showLoading: false,
      data: {
        eventType:eventType,
        minMoney:minMoney,
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName:app.getLoginName()
      },
      success: (res) => {
        for (var i = 0; i < res.length; i++) {
          res[i]["checked"] = true;
          if (res[i].rightsType == 4 || res[i].rightsType == 5 || res[i].rightsType == 6) {
            that.toUser(res[i].configId);
          }
        }
        that.setData({
          result: res
        })      
      }
    })*/
  },
  /*给用户发放优惠券等*/
  toUser: function (configId) {
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.errorcode == '1000') {
          console.log("获取成功！")
        }
      }
    })
  },
  goIndex: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  goOrder: function () {
    app.redirectToPage('/pages/indexThree/indexThree');
  },
  gocjBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData
    })
    app.navigateToPage('/pages/turntableActivity/turntableActivity?gameId=' + e.currentTarget.dataset.gameid);
  },
  goSignBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData
    })
    app.navigateToPage('/pages/sign/sign?configId=' + configId);
  },
  goWordBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData
    })
    app.navigateToPage('/pages/collect/collect?configId=' + configId);
  }
})