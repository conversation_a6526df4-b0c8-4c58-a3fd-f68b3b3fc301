const app = getApp();
const http = require('../../utils/http')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    startPage:1,
    pageSize:20,
    coupon:app.imageUrl + 'sign_coupon.png',
    integral:app.imageUrl + 'sign_integral.png',
    spmoney:app.imageUrl + 'sign_spmoney.png',
    game:app.imageUrl + 'sign_game.png',
    rewardState:["","待发放","发放中","发放成功","发放失败"]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that=this;
    var promotionId = options.promotionId;
    that.setData({
      promotionId:promotionId
    })
    that.getMyReward();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that=this;
    if (that.data.resultList.length ==that.data.pageSize){
      that.selectComponent("#loadMoreView").loadMore()
    }else{
      that.selectComponent("#loadMoreView").noMore()
    }
  },

  getMyReward:function(options){
    var that = this;
    var data = {};
    data["tm"] = "/signin/retailClient/getReward";
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["promotionId"] = that.data.promotionId;
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/simpleRouter/stickness',
          data: data,
          success: function (res) {
            if(res.data.code == 1){
              var resData = JSON.parse(res.data.data);
              console.log(resData);
              that.setData({
                resData:resData.resultList
              })
            }
            else{
            }
          },
          fail: function () {
            wx.hideLoading();
          }
        })
  },
  loadMoreListener: function (e) {
    this.getMyReward(false);
  },
  clickLoadMore: function (e) {
    this.getMyReward(false);
  },
  /**
   * 去我的券包
   */
  toCoupons:function(){
    app.navigateToPage('/pages/person_coupon/person_coupon');
  },
  /**
   * 去我的会员卡
   */
  toVipCard:function(){
    app.navigateToPage('/pages/openonecard/openonecard');
  }
})