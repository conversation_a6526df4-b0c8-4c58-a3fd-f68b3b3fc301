var app = getApp();
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatarUrl: defaultAvatarUrl,
    loginAccount: "",
    isFromBack: false,
    waitPay: app.imageUrl + "2.png",
    waitDeliver: app.imageUrl + "1.png",
    alreadyDeliver: app.imageUrl + "3.png",
    waitEvaluate: app.imageUrl + "4.png",
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
    middleVip: app.imageUrl + 'accountManager/middleVip.png',
    middleScore: app.imageUrl + 'accountManager/middleScore.png',
    middleAmount: app.imageUrl + 'accountManager/middleAmount.png',
    noticeIcon:app.imageUrl + 'noticesmall2.png',
    identity: app.getIdentity(),
    desc: "请您先登录",
    nickName: "",
    cardList: [],
    bg_image: "",
    card_score: 0,
    card_Amount: 0,
    isLogin: false,
    boundTelephoneHidden: true,
    appletVersion: app.appletVersion,
    adId: '',
    adType: '',
    lotteryPic:app.imageUrl +'person/turntableLottery.png',
    menuNameArray: ["", "我的券包", "我的积分", "积分商城", "推广奖励", "我的团队", "绑定银行卡", "收货地址", "退出登录", "客服热线", "修改密码", "个人信息", "", "邀请好友", "退货售后", "直播房间", "礼券兑换", "转盘抽奖", "集字领奖"],
    menuImageArray: ["", app.imageUrl + 'person/coupon.png', app.imageUrl + 'person/score.png',
      app.imageUrl + 'person/scoreEx.png', app.imageUrl + 'person/form.png',
      app.imageUrl + 'person/myTeam.png', app.imageUrl + 'person/lowSum.png', app.imageUrl + 'person/address.png',
      app.imageUrl + 'person/logOut.png', app.imageUrl + 'person/online.png', app.imageUrl + 'person/changePassword.png',
      app.imageUrl + 'person/personInfo.png', "", app.imageUrl + 'person/shareOther.png', app.imageUrl + 'person/returnGoods.png',
      app.imageUrl + 'person/onlineCast.png', app.imageUrl + 'person/giftExchange.png', app.imageUrl + 'person/turntableLottery.png', app.imageUrl + 'person/collectAward.png'],
    sign_mark: app.imageUrl + 'sign_mark.png',
    
    pageArray: ["", "", "", "", "/pages/index/index", "/pages/goods_classify/goods_classify", "/pages/shopCart/shopCart", "/pages/accountManager/accountManager", "/pages/indexThree/indexThree", "/pages/person_coupon/person_coupon", "/pages/scoreMall/scoreMall", "/pages/onlineCast/onlineCast", "/pages/turntableActivity/turntableActivity?sceneType=3", "/pages/vipCard/vipCard", "/pages/sign/sign", "/pages/couponCenter/couponCenter", "/pages/collect/collect"],
  },
  queryPersonalInfo: function (companyId, storeId) {
    var that = this;
    var odbtoken = wx.getStorageSync('odbtoken');
    var logintoken = wx.getStorageSync('loginToken');
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "storeId": storeId,
        "companyId": companyId,
        "odbtoken":odbtoken,
        "loginToken":logintoken
      },
      url: app.projectName + '/wechatAppletLogin/queryPersonalInfo',
      success: function (res) {
        if(res.data.code == -99){ /*表示登录已经失效*/
          wx.removeStorageSync("odbtoken");
          wx.removeStorageSync("loginToken");
          app.init_userOpenId();
          return;
        }
        var userBean = res.data.userBean;
        if (userBean != null) {
          var pacStr = userBean.pac;
          var pacArray = [];
          if (pacStr != null && pacStr != "") {
            pacArray = pacStr.split(" ");
          } else {
            pacArray = that.data.pac;
          }
          if (pacStr == "省 市 区" || pacStr == "") {
            /*that.getUserLocation();*/
          }
          var isUpdateBirthday = false;
          if (userBean.birthday == null || userBean.birthday.length == 0) {
            isUpdateBirthday = true;
          }
          that.setData({
            isUpdateBirthday: isUpdateBirthday,
            userBean: userBean,
            oldAccount: userBean.account,
            id: userBean.id,
            userName: userBean.userName,
            wechatId: userBean.wechatId,
            headImage: userBean.headImage?userBean.headImage:that.data.avatarUrl,
            sex: userBean.sex,
            birthday: userBean.birthday,
            pac: pacArray,
            address: userBean.address,
            telephone: userBean.telephone
          });
        }
      }
    })
  },
  goLuckDrawClick:function(){
    var that = this;
    var data = {};
    data["tm"] = "/luckdraw/retailClient/outlines";
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["currentPage"] = 1;
    data["pageSize"] = 10;
    data["storeId"] = app.getExtStoreId();
        /*获取当前任务信息*/
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/simpleRouter/stickness',
          data: data,
          success: function (res) {
              if(res.data.code == 1){
                var resultList = JSON.parse(res.data.data).resultList;
                if(resultList != null && resultList.length>0){
                    app.navigateToPage('/pages/turntableActivityList/turntableActivityList');
                }
                else{
                  wx.showToast({
                    title: "您暂抽奖权益",
                    icon: 'none',
                    duration: 1500,
                    mask: true
                  })
                }
              }
          },
          fail: function () {
            wx.hideLoading();
          }
        })
  },
  /*goLuckDrawClick:function(){
    app.navigateToPage('/pages/turntableActivity/turntableActivity?id='+app.globalData.pId);
  },*/
  goMessageClick:function(){
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    app.navigateToPage('/pages/messageNotice/messageNotice');
  },
  goSignClick: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    app.navigateToPage('/pages/sign/sign');
  },
  jumpPageBindTap: function (e) {
    var that = this;
    var type = e.currentTarget.dataset.type;
    var id = e.currentTarget.dataset.id;
    that.commonJumpContent(id, type, 1);
  },
  commonJumpContent: function (content, relationType, type) {
    var that = this;
    if (relationType == 2) {/*关联的是某一个分类*/
      var id = "";
      id = content;
      if (id != null && id.length > 0) {
        app.navigateToPage("/pages/secondCategory/secondCategory?categoryId=" + id);
      }
    }
    else if (relationType == 4) {/*关联的是活动页面*/
      var id = "";
      id = content;
      if (id != null && id.length > 0) {
        app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
      }
    }
    else if (relationType == 1) { /*跳到对应页面*/
      var id = content;
      if (id == 4) {
        wx.switchTab({
          url: "/pages/index/index"
        });
      }
      if (id == 5) {
        wx.switchTab({
          url: "/pages/goods_classify/goods_classify"
        });
      }
      else if (id == 6) {
        wx.switchTab({
          url: "/pages/shopCart/shopCart"
        });
      }
      else if (id == 7) {
        wx.switchTab({
          url: "/pages/accountManager/accountManager"
        });
      }
      else if (id == 17) {
        app.reLaunchToPage("/pages/find/find");
      }
      else if(id == 19){
        app.navigateToPage("/pages/sign/sign");
      }
      else {
        app.navigateToPage(that.data.pageArray[id]);
      }
    }
    else if (relationType == 5) {/*代表跳转到图文素材页面*/
      var id = "";
      id = content;
      app.navigateToPage("/pages/picMatter/picMatter?id=" + id);
    }
  },
  isDistribution:function(){
      var that = this;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "companyId": app.getExtCompanyId()
        },
        url: app.projectName + '/newDistrution/queryUserIsDistribution',
        success: function (res) {
          var flag = res.data.flag;
          that.setData({
            isFlag:flag
          })
        }
      })
  },
  menuClickBindTap: function (e) {
    var type = e.currentTarget.dataset.type;
    if (type == 1) { //我的优惠券
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/person_coupon/person_coupon');
    } else if (type == 2) { //我的积分
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/customerScore/customerScore');
    } else if (type == 3) { //积分兑换
      app.navigateToPage('/pages/scoreMall/scoreMall');
    } else if (type == 4) { //我的报表
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      var that = this;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "companyId": app.getExtCompanyId()
        },
        url: app.projectName + '/newDistrution/queryUserIsDistribution',
        success: function (res) {
          var flag = res.data.flag;
          if (flag) {
            app.navigateToPage('/pages/accountRebate/rebateLog/rebateLog');
          } else {
            wx.showToast({
              title: "暂无权限，请联系商家开通",
              icon: 'none',
              duration: 1500,
              mask: true
            })
          }
        }
      })
    } else if (type == 5) { //我的团队
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      var that = this;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "companyId": app.getExtCompanyId()
        },
        url: app.projectName + '/newDistrution/queryUserIsDistribution',
        success: function (res) {
          var flag = res.data.flag;
          if (flag) {
            app.navigateToPage("/pages/myTeam/myTeam");
          } else {
            wx.showToast({
              title: "暂无权限，请联系商家开通",
              icon: 'none',
              duration: 1500,
              mask: true
            })
          }
        }
      })
      // app.navigateToPage('/pages/person_agentDetail/person_agentDetail?type=2');
    } else if (type == 6) { //下级消费
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage("/pages/bindCard/bindCard");
    } else if (type == 7) { //收货地址管理
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/personAddressManager/personAddressManager');
    } else if (type == 8) { //退出登录
      app.showModal({
        content: '确定退出登录？',
        showCancel: true,
        confirm: function () {
          /*wx.removeStorageSync("odbtoken");*/
          wx.removeStorageSync("loginToken");
          wx.removeStorageSync("userSession");
          wx.removeStorageSync("isLoginSession");
          wx.removeStorageSync("");
          /*app.init_userOpenId();*/
          app.navigateToPage('/pages/login/login');
          var isLoginSession = false;
          app.setStorage({
            key: 'isLoginSession',
            data: isLoginSession
          });
        }
      });
    } else if (type == 9) { //客服热线
      var storeInfoStorageKey = wx.getStorageSync('storeInfoStorageKey');
      if (storeInfoStorageKey) {
        wx.makePhoneCall({
          phoneNumber: storeInfoStorageKey.storePhone
        })
      }
    } else if (type == 11) { //修改个人信息
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/changeProfile/changeProfile');
    } else if (type == 13) { //分享海报
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/qrcode/qrcode');
    } else if (type == 14) { //退货
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/customerService/customerService');
    } else if (type == 15) { //直播房间
      app.navigateToPage('/pages/onlineCast/onlineCast');
    }
    else if (type == 16) { //礼券兑换
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/giftCardExchange/giftCardExchange');
    } else if (type == 17) { //转盘抽奖（现为我的权益）
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      //app.navigateToPage('/pages/turntableActivity/turntableActivity?sceneType=3');
      app.navigateToPage('/pages/myRight/myRight');
    } else if (type == 18) { //集字领奖
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      app.navigateToPage('/pages/collect/collect');
    }
  },
  getPhoneNumber: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.login({
        success: function (res) {
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/wechatAppletLogin/jscode2session',
            data: {
              "code": res.code,
              "companyId": app.getExtCompanyId(),
              "storeId": app.getExtStoreId(),
              "withEncryption":1
            },
            success: function (res) {
              var session_Key = decodeURIComponent(res.data.session_Key);
              that.getUserTelephone(iv, encryptedData, session_Key);
            },
            fail: function () {
              app.showModal({
                title: '提示',
                content: "获取手机号码失败"
              });
            }
          })
        },
        fail: function (res) {
          app.showModal({
            title: '提示',
            content: "获取手机号码失败"
          });
        }
      })
    }
  },
  goToLoginBindTap: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
  },
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wechatAppletLogin/getUserTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var telephone = res.data.telephone;
        if (telephone != null && telephone.length > 0) {
          var userSession = wx.getStorageSync('userSession');
          userSession.telephone = telephone;
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
          that.setData({
            boundTelephoneHidden: true
          });
          wx.showToast({
            title: "绑定成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.navigateToPage('/pages/vipCard/vipCard');
              }, 2000);
            }
          })
        } else {
          that.setData({
            boundTelephoneHidden: true
          });
        }
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "请再试一次"
        });
      }
    })
  },
  /**
   * 暂不绑定手机号码
   */
  noBoundTelephoneBindTap: function () {
    this.setData({
      boundTelephoneHidden: true
    })
  },
  /**
   * 跳转到一张卡
   **/
  goCardsBind: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wholesaleStore/getStoreInfoSetting',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var returnResult = res.data.returnResult;
        if (returnResult != null && returnResult != undefined) {
          var exchangePoints = returnResult.exchangePointsBean.exchangePoints;
          if (exchangePoints == 2) {//线上积分
            app.navigateToPage("/pages/customerScore/customerScore");
          } else {
            if (that.data.cardList.length == 1) {
              var no = that.data.cardList[0].cardId;
              app.navigateToPage('/pages/myvipCard/myvipCard?cardNo=' + no);
            } else {
              app.navigateToPage('/pages/vipCard/vipCard');
            }
          }
        }
      }
    })
  },
  /**
   * 查询线上积分值
   */
  queryRetailUserAccount: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/queryRetailUserAccount',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "localStoreId": app.getExtStoreId()
      },
      success: function (res) {
        var accountBean = res.data.accountBean;
        if (accountBean != null) {
          that.setData({
            card_score: accountBean.accountValidQuota.toFixed(2),
            card_Amount: 0
          })
        } else {
          that.setData({
            card_score: 0,
            card_Amount: 0
          })
        }
      }
    })
  },
  /**
   * 查询订单
   **/
  myOrderBindTap: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var id = e.currentTarget.dataset.id;
    app.navigateToPage("/pages/indexThree/indexThree?currentTab=" + id);
  },
  /*
   *查询个人信息
   */
  updatePersonBindTap: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    app.navigateToPage('/pages/changeProfile/changeProfile');
  },
  /**
   * 会员卡信息
   **/
  goVipCardBind: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    if (wx.getStorageSync('isLoginSession') == null || wx.getStorageSync('isLoginSession').length == 0 || wx.getStorageSync('isLoginSession') == undefined) {
      app.navigateToPage('/pages/vipCard/vipCard');
    } else {
      that.getMyVipCardInfo();
    }
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          var cardNo = cardList[0].cardId;
          app.navigateToPage('/pages/myvipCard/myvipCard?cardNo=' + cardNo);
        } else {
          app.navigateToPage('/pages/vipCard/vipCard');
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPersonal();
    this.getOrderTotal();
    this.queryMyHaveCard(1);
    this.getSupplierConfig();
    this.queryPersonalInfo();
    this.isDistribution();
  },
  /**
   * 查询商家配置信息
   */
  getSupplierConfig: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wholesaleStore/getStoreInfoSetting',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var returnResult = res.data.returnResult;
        if (returnResult != null && returnResult != undefined) {
          var exchangePoints = returnResult.exchangePointsBean.exchangePoints;
          if (exchangePoints == 2) {//线上积分
            that.queryRetailUserAccount();
          } else {//线下会员卡积分
            that.getMyVipCardInfoScore();
          }
        } else {
          //不配置，默认读取会员卡积分
          that.getMyVipCardInfoScore();
        }
      }
    })
  },
  /**
 * 查询我的会员卡信息
 */
  getMyVipCardInfoScore: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          var card_score = 0;
          var card_Amount = 0;
          for (var i = 0; i < cardList.length; i++) {
            card_score += parseFloat(cardList[0].integral);
            card_Amount += parseFloat(cardList[0].spareCash);
          }
          that.setData({
            companyName: app.getExtStoreName(),
            cardList: cardList,
            card_score: card_score.toFixed(2),
            card_Amount: card_Amount.toFixed(2)
          })
        } else {

        }
      }
    })
  },
  queryMyHaveCard: function (index) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryUserCardPackage',
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "cardType": index
      },
      success: function (res) {
        var cardList = res.data.returnList;
        that.setData({
          couponNum: cardList.length
        });
      }
    })
  },
  goCouponBind: function () {
    app.navigateToPage('/pages/person_coupon/person_coupon');
  },
  /**
   * 生命周期函数--监听页面加载
   */
  giftCardBindTap: function () {
    app.navigateToPage('/pages/giftCardExchange/giftCardExchange');
  },
  /**
   * 获取订单数量
   */
  getOrderTotal: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (isLogin) {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/applet/buyOrder/retailSummaryStatus',
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "levelRole": app.getIdentity(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        success: function (res) {
          var new_orderList = res.data.orderList;
          var waitfk = 0; //待付款
          var waitfh = 0; //待发货
          var waitsh = 0; //待收货
          var waitpj = 0; //待评价
          if (new_orderList != null && new_orderList.length > 0) {
            for (var i = 0; i < new_orderList.length; i++) {
              var orderStatus = new_orderList[i].orderStatus;
              var orderTotal = new_orderList[i].orderTotal;
              if (orderStatus == 2) {
                waitfk = orderTotal;
              } else if (orderStatus == 3) {
                waitfh = orderTotal;
              } else if (orderStatus == 4) {
                waitsh = orderTotal;
              } else if (orderStatus == 7) {
                waitpj = orderTotal;
              }
            }
          }
          that.setData({
            waitfk: waitfk,
            waitfh: waitfh,
            waitsh: waitsh,
            waitpj: waitpj
          })
        }
      })
    } else {
      that.setData({
        waitfk: 0,
        waitfh: 0,
        waitsh: 0,
        waitpj: 0
      })
    }
  },
  /**
   * 初始化默认方法
   */
  initPersonal: function () {
    var that = this;
    app.init_getExtMessage().then(res => {
      var isLogin = app.isLogin();
      console.log(isLogin);
      that.setData({
        isLogin: isLogin
      });
      app.getSupplierSetting(res.companyId);
      if (!isLogin) {
        that.setData({
          nickName: "点击登录",
          avatarUrl: app.getExtStoreImage(),
          storeName: app.getExtStoreName(),
          storePhone: app.getExtStorePhone()
        });
      } else {
        that.queryPersonalInfo(res.companyId, res.storeId);
      }
      that.queryPersonalCenterTempalteNew(res.storeId, res.companyId);
    });
  },
  /**
   * 查询个人资料
   */
  /*queryPersonalInfo: function (companyId, storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "storeId": storeId,
        "companyId": companyId
      },
      url: app.projectName + '/wechatAppletLogin/queryPersonalInfo',
      success: function (res) {
        var userBean = res.data.userBean;
        if (userBean != null) {
          that.setData({
            nickName: userBean.userName,
            avatarUrl: userBean.headImage,
            storeName: app.getExtStoreName(),
            storePhone: app.getExtStorePhone()
          });
        }
      }
    })
  },*/
  /**
   * 查询个人中心配置新
   **/
  queryPersonalCenterTempalteNew: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      url: app.projectName + '/personalCenterTemplate/queryNewPersonalCenterTempalte',
      success: function (res) {
        var personalList = res.data.personalList;
        var openStoreTime = res.data.openStoreTime;
        that.setData({
          openStoreTime: openStoreTime
        });
        var returnResult = res.data.returnResult;
        var floorContent = returnResult.floorContent;
        for (var i = 0; i < floorContent.length; i++) {
          if (floorContent[i].floorNum == 1) {/*代表的是头部颜色*/
            var top_color = floorContent[i].bgColor;
            if (top_color == "#ffffff") {
              wx.setNavigationBarColor({
                frontColor: '#000000',
                backgroundColor: top_color
              })
            }
            else {
              wx.setNavigationBarColor({
                frontColor: '#ffffff',
                backgroundColor: top_color
              })
            }
          }
          else if (floorContent[i].floorNum == 2) {/*代表的是会员卡模块*/
            var bgStyle = floorContent[i].bgStyle  /*1代表的是纯色，2代表的是图片*/
            if (bgStyle == 1) {
              that.setData({
                bgColor: floorContent[i].bgColor
              })
            }
            else if (bgStyle == 2) {
              that.setData({
                bgPic: floorContent[i].bgPic
              })
            }
            var signShow = floorContent[i].signShow;
            var vipShow = floorContent[i].vipShow;
            that.setData({
              signShow: signShow,
              vipShow: vipShow,
              vipTextColor: floorContent[i].vipTextColor,
              vipColor: floorContent[i].vipColor,
              vipText: floorContent[i].vipText,
            })
          }
          else if (floorContent[i].floorNum == 3) {/*代表的是个人资产显示*/
            that.setData({
              assetOpen: floorContent[i].isOpen
            })
          }
          else if (floorContent[i].floorNum == 4) {/*订单固定模块*/

          }
          else if (floorContent[i].floorNum == 5) {/*广告模块*/
            that.setData({
              adOpen: floorContent[i].isOpen,
              adPic: floorContent[i].adPic,
              adId: floorContent[i].r_id,
              adType: floorContent[i].r_type,
            })
          }
          else if (floorContent[i].floorNum == 6) {/*自定义菜单模块*/
            var menuShowType = floorContent[i].menuStyle;
            that.setData({
              menuShowType: menuShowType,
              menuList: floorContent[i].menuList
            })
          }
        }
        /*if (personalList != null && personalList.length > 0) {
          var peronalBackImage = "";
          var showVipHidden = true;
          var showPicHidden = true;
          var showPicContent = "";
          var menuShowType = 1;
          var menuIdArray = [];
          for (var i = 0; i < personalList.length; i++) {
            var blockId = personalList[i].blockId;
            var isEnalble = personalList[i].isEnalble;
            if (blockId == 1 && isEnalble == 1) {
              peronalBackImage = personalList[i].showContent;
            } else if (blockId == 2) {
              showVipHidden = isEnalble == 1 ? false : true;
            } else if (blockId == 3) {
              showPicHidden = isEnalble == 1 ? false : true;
              showPicContent = personalList[i].showContent;
            } else if (blockId == 5) {
              menuShowType = personalList[i].showType;
              menuIdArray = personalList[i].showContent.split(",");
            }
          }
          that.setData({
            peronalBackImage: peronalBackImage,
            showVipHidden: showVipHidden,
            showPicHidden: showPicHidden,
            showPicContent: showPicContent,
            menuShowType: menuShowType,
            menuIdArray: menuIdArray
          });
        }*/
      }
    })
  },
  /**
   * 查询个人中心模版配置
   */
  queryPersonalCenterTempalte: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      url: app.projectName + '/personalCenterTemplate/queryPersonalCenterTempalte',
      success: function (res) {
        var personalList = res.data.personalList;
        var openStoreTime = res.data.openStoreTime;
        that.setData({
          openStoreTime: openStoreTime
        });
        if (personalList != null && personalList.length > 0) {
          var peronalBackImage = "";
          var showVipHidden = true;
          var showPicHidden = true;
          var showPicContent = "";
          var menuShowType = 1;
          var menuIdArray = [];
          for (var i = 0; i < personalList.length; i++) {
            var blockId = personalList[i].blockId;
            var isEnalble = personalList[i].isEnalble;
            if (blockId == 1 && isEnalble == 1) {
              peronalBackImage = personalList[i].showContent;
            } else if (blockId == 2) {
              showVipHidden = isEnalble == 1 ? false : true;
            } else if (blockId == 3) {
              showPicHidden = isEnalble == 1 ? false : true;
              showPicContent = personalList[i].showContent;
            } else if (blockId == 5) {
              menuShowType = personalList[i].showType;
              menuIdArray = personalList[i].showContent.split(",");
            }
          }
          that.setData({
            peronalBackImage: peronalBackImage,
            showVipHidden: showVipHidden,
            showPicHidden: showPicHidden,
            showPicContent: showPicContent,
            menuShowType: menuShowType,
            menuIdArray: menuIdArray
          });
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,
        selected: 7,
        shopCartNum: wx.getStorageSync('shopCartNum'),
        color: app.globalData.bottomBean.color,
        selectedColor: app.globalData.bottomBean.selectedColor
      })
    }
    if (this.data.isFromBack) {
      this.initPersonal();
      this.getOrderTotal();
      this.queryMyHaveCard(1);
      this.getSupplierConfig();
      this.isDistribution();
    } else {
      this.setData({
        isFromBack: true
      });
    }
  }
})