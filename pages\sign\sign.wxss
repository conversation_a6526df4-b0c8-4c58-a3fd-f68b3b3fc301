@import '../popupTemplate/popupTemplate.wxss';

page {
  background: #F2F2F2;
  /* background:grey; */
}

.currency {
  height: 80rpx;
  background-color: #FF9002;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.currency_one {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 710rpx;
  height: 178rpx;
  background-color: #fff;
  border: 0px solid red;
  border-radius: 0 0 16rpx 16rpx;
  position: relative;
  z-index: 2;
}

.currency_two {
  display: flex;
  width: 60%;
}

.currency_three {
  width: 83rpx;
  height: 98rpx;
  border: 0px solid red;
}

.currency_four {
  height: 32rpx;
  border: 0px solid blue;
  font-size: 32rpx;
  font-family: SourceHanSansCN-Medium;
  line-height: 32rpx;
  color: #2F3F5A;
  opacity: 1;
  overflow: hidden;
  margin-top: 24rpx;
  margin-left: 24rpx
}

.currency_five {
  width: 156rpx;
  height: 33rpx;
  border: 0px solid green;
  margin-top: 10rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 33rpx;
  color: #FE5406;
  opacity: 1;
  margin-left: 24rpx
}

.currency_button {
  width: 176rpx;
  height: 72rpx;
  background: #FE5406;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 72rpx;
  height: 72rpx;
  color: #FFFFFF;
}


.myRward {
  margin: 0px auto;
  width: 710rpx;
  height: 120rpx;
  border: 0px solid blue;
  margin-top: 118rpx;
}

.myRward_one {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45%;
  height: 100%;
  border: 0px solid red;
  border-radius: 16rpx;
}

.myRward_two {
  display: flex;
}

.myRward_three {
  width: 80rpx;
  height: 80rpx;
}

.myRward_four {
  width: 156rpx;
  height: 34rpx;
  border: 0px solid green;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 34rpx;
  color: #525252;
  margin-left: 20rpx
}

.myRward_five {
  width: 156rpx;
  height: 43rpx;
  border: 0px solid green;
  font-size: 36rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 43rpx;
  color: #FE5406;
  opacity: 1;
  margin-left: 20rpx
}

.myRward_img {
  width: 100%;
  height: auto;
}

.notice {
  margin: 0px auto;
  align-items: center;
  justify-content: center;
  width: 710rpx;
  background: #FFFFFF;
  opacity: 1;
  border-radius: 8rpx;
  background-color: #fff;
  border: 0px solid blue;
  border-radius: 16rpx;
  margin-top: 20rpx;
}

.notice_one {
  border: 0px solid red;
  width: 90%;
  height: 33rpx;
  margin: 0px auto;
  padding-top: 20rpx;
}

.notice_two {
  border: 0px solid green;
  width: 28rpx;
  height: 30rpx;
  float: left
}

.notice_three {
  border: 0px solid green;
  width: 595rpx;
  float: right;
}

.notice_four {
  border: 0px solid red;
  width: 290rpx;
  height: 20rpx;
  margin: 0px auto;
  margin-top: 10rpx;
  text-align: center;
}

.notice_five {
  height: 50rpx;
  border: 0px solid blue;
  font-size: 36rpx;
  font-family: PingFang SC;
  line-height: 50rpx;
  color: #2F3F5A;
  opacity: 1;
  overflow: hidden;
  font-weight: 500;
}

.notice_six {
  width: 90%;
  height: 102rpx;
  margin: 0px auto;

  align-items: center;
  justify-content: center;
}

.notice_seven {
  height: 102rpx;
  width: 91.3rpx;
  float: left;
  margin-top: 32rpx;
}

.notice_eight {
  border: 0px solid green;
  height: 65rpx;
  width: 65rpx;
  margin: 0px auto
}

.notice_nine {
  border: 0 solid red;
  height: 25%;
  width: 75%;
  font-size: 24rpx;
  font-family: iekie-jianheiti;
  line-height: 29rpx;
  text-align: center;
  margin: 0px auto;
  margin-top: 10rpx;
}

.example {
  display: block;
  width: 100%;
  height: 33rpx;
  overflow: hidden;
}

.marquee_box {
  width: 100%;
  position: relative;

}

.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
  font-family: PingFang SC;
  font-weight: 400;
  color: #333333;
}

.signAD {
  margin: 0px auto;
  width: 710rpx;
  margin-top: 20rpx;
}

.goods_tips {
  color: #958353;
  border: 1px solid #bcb396;
  top: 20rpx;
  left: 20rpx;
  width: 24rpx;
  height: 124rpx;
  line-height: 32rpx;
  display: block;
  position: absolute;
  font-size: 24rpx;
  word-wrap: break-word;
  padding: 8rpx;
  border-radius: 6rpx;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.soonlist {
  font-size: 28rpx;
  writing-mode: vertical-lr;
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  z-index: 2;
  color: #988250;
  border: 1px solid #988250;
  border-radius: 10rpx;
  padding: 10rpx 0;
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  border-top-right-radius: 10rpx;
  border-top-left-radius: 10rpx;
}


.goods_title {
  display: block;
  /**height: 28px;
  line-height: 28px;**/
  overflow: hidden;
  font-size: 32rpx;
  margin: 10rpx 0;
  text-align: center;
}

.goods_price {
  display: block;
  color: #DE535F;
  font-size: 24rpx;
  margin: 8rpx 0;
  text-align: center;
}

.good_shopCart {
  text-align: center;
  background: #DE535F;
  display: inline-block;
  color: #fff;
  margin: 8rpx 0 30rpx 0;
  padding: 4rpx 20rpx;
  border-radius: 6rpx;
  margin-left: calc(30% - 20rpx);
  font-size: 24rpx;
}

.pic_two {
  display: flex;
  width: 700rpx;
  margin: 0 auto;
  flex-wrap: wrap;
}

.pic_two>view:nth-child(2n+1) {
  margin-right: 20rpx;
}

.pic_two .pic_two_goods {
  width: 340rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.pic_two .pic_two_goods image {
  width: 340rpx;
  height: 340rpx;
}

.commodity_box3 {
  width: 340rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.commodity_box3 image {
  width: 340rpx;
  height: 340rpx;
}

.line_price {
  font-size: 18rpx;
  text-decoration: line-through;
  color: #666;
  margin-left: 6rpx;
}

.desc_title {
  font-size: 28rpx;
  height: 80rpx;
  overflow: hidden;
}

.desc_price {
  margin-top: 20rpx;
}

.price_tag {
  color: #FF7E00;
  font-size: 22rpx;
}

.price_inner {
  color: #FF7E00;
  font-size: 32rpx;
}

.line_price {
  font-size: 18rpx;
  text-decoration: line-through;
  color: #666;
  margin-left: 6rpx;
}

.goods_desc {
  padding: 14rpx 10rpx;
}
.new_state, .old_state {
  position: relative;
  border-left: 1px solid #ccc;
  width: 100%;
  height: 80px;
  font-size: 14px;
}

.new_state {
  color: #FF7E00;
}
.prizeBox{
  width: 473rpx;
  height: 564rpx;
  border-radius: 10rpx;
  margin: 0 auto;
  position: fixed;
  top: 280rpx;
  left: 130rpx;
  z-index: 99999;
  text-align: center;
  color:#fff;
  font-size: 28rpx;
  background-color: #fff;
}
.sumbitBtn{
  width: 267rpx;
  height: 66rpx;
  line-height: 66rpx;
  border-radius: 66rpx;
  text-align: center;
  color:#fff;
  background-color: #547AEC;
  margin:0 auto;
}
.pop_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #000;
  opacity: 0.6;
}