<import src="../CouponTem/CouponTem.wxml" />
<import src="../floorTemplate/floorTemplate.wxml" />
<import src="../tagComponent/tagComponent.wxml" />
<block wx:if="{{indexFlag}}">
  <template is="coupon3" data="{{img5,img11,sendStoreCardList}}" />
</block>
<block wx:if="{{isVipCardSwitch}}">
  <template is="coupon7" data="{{img10,isVipCardSwitch}}" />
</block>
<view class="pop_bg" hidden='{{putCardHidden}}'></view>
<!--券弹出层--->
<!--<view bindtap="isCollectClick" class="box" wx:if="{{SHOW_TOP}}" style="top:{{custom?navbarHeight:0}}px;" id="add-tips">
  <view class='arrow' style="margin-right:{{arrowR}}px;"></view>
  <view class='body' bindtap='hidden' style="margin-right:{{bodyR}}px;">
    <view class="tips">点击"..."添加到我的小程序，购物更方便</view>
  </view>
</view>-->
<view class="page" style="background:{{bg_color}};">
  <!--店铺导航-->
  <view class="location_store" style="text-align:left;" bindtap="switchSelectStoreBindTap">
    <image src="{{myAddress}}" lazy-load='true' mode="widthFix"
      style="width:28rpx;vertical-align:middle;margin-top:-20rpx;"></image>
    <label class="storeName" style="font-weight:bold;font-size:30rpx;color:#000;">{{storeName}}</label>
    <block wx:if="{{rStoreList.length>1}}">
      <label style="font-size:24rpx;margin:0 14rpx;vertical-align:top;">|</label>
      <text style="font-size:24rpx;" class="storeChange">切换</text>
    </block>
  </view>
  <!--店铺导航-->
  <scroll-view scroll-y="true" style="height:{{winHeight}}rpx;margin-bottom:180rpx;">
    <view class="tab-content">
      <block wx:key="unique" wx:for="{{indexListConfig}}" wx:for-item="indexConfig">
        <!--搜索框开始-->
        <block wx:if="{{indexConfig.templateType==17}}">
          <template is="searchItem" data="{{indexConfig,scan,vipCode}}" />
        </block>
        <!--搜索框结束-->
        <!--banner图片开始-->
        <block wx:if="{{indexConfig.templateType==1}}">
          <block wx:if="{{indexConfig.bannerBean.wechatAppletIndexBannerImagerContentEntity.length>0}}">
            <template is="bannerItem" data="{{indexConfig,imgheights,current,imgwidth}}" />
          </block>
        </block>
        <!--banner图片结束-->
        <!--公告开始-->
        <block wx:elif="{{indexConfig.templateType==2}}">
          <block wx:if="{{indexConfig.noticeBean.wehcatAppletIndexNoticeContentEntities.length>1}}">
            <template is="noticeItem"
              data="{{indexConfig,announctList,announce,orientation,marqueeDistance2,size,marquee2copy_status,marquee2_margin,bg_color}}" />
          </block>
          <block wx:elif="{{indexConfig.noticeBean.wehcatAppletIndexNoticeContentEntities.length==1}}">
            <template is="noticeItem" data="{{indexConfig,announce,announctList,marqueeW,allT,size,bg_color}}" />
          </block>
        </block>
        <!--公告结束-->
        <!--中部导航开始-->
        <block wx:elif="{{indexConfig.templateType==3}}">
          <block wx:if="{{indexConfig.navigationBean.wechatAppletIndexNavigationContentEntities.length>0}}">
            <template is="navigateItem" data="{{indexConfig}}" />
          </block>
        </block>
        <!--中部导航结束-->
        <!--图片导航开始-->
        <block wx:elif="{{indexConfig.templateType==4}}">
          <block wx:if="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities.length>0}}">
            <template is="picNavItem" data="{{indexConfig}}" />
          </block>
        </block>
        <!--图片导航结束-->
        <!--商品模块开始-->
        <block wx:elif="{{indexConfig.templateType==6}}">
          <block wx:if="{{indexConfig.wechatAppletGoodsList.length>0}}">
            <template is="commodityItem" data="{{indexConfig,shop_cart1,shop_cart2}}" />
          </block>
        </block>
        <!--商品模块结束-->
        <!--视频模块开始-->
        <block wx:elif="{{indexConfig.templateType==7}}">
          <template is="videoItem" data="{{indexConfig}}" />
        </block>
        <!--视频模块结束-->
        <!--文本模块开始-->
        <block wx:elif="{{indexConfig.templateType==8}}">
          <template is="textItem" data="{{indexConfig}}" />
        </block>
        <!--文本模块结束-->
        <!--占位符模块开始-->
        <block wx:elif="{{indexConfig.templateType==16}}">
          <template is="placeholderItem" data="{{indexConfig}}" />
        </block>
        <!--占位符模块结束-->
        <!--优惠券模块开始-->
        <block wx:elif="{{indexConfig.templateType==9}}">
          <template is="couponItem" data="{{indexConfig}}" />
        </block>
        <!--优惠券模块结束-->
        <!--大转盘模块开始-->
        <block wx:elif="{{indexConfig.templateType==13}}">
          <template is="lotteryItem" data="{{indexConfig}}" />
        </block>
        <!--大转盘模块结束-->
        <!--直播模块开始-->
        <block wx:elif="{{indexConfig.templateType==14}}">
          <template is="castItem" data="{{indexConfig}}" />
        </block>
        <!--直播模块结束-->
        <!--活动模块开始-->
        <block
          wx:elif="{{indexConfig.templateType==10 || indexConfig.templateType==11 || indexConfig.templateType==12}}">
          <template is="promotionItem" data="{{countDownList,indexConfig}}" />
        </block>
        <!--活动模块结束-->
        <!--倒计时模块开始-->
        <block wx:elif="{{indexConfig.templateType==18}}">
          <template is="countItem" data="{{countDownList,indexConfig}}" />
        </block>
        <!--倒计时模块结束-->
      </block>
      <!-- 团购 start -->
      <!-- <view class="groupbuy">
				<view class="groupTop clearfix">
					<view style="font-size:40rpx;float:left;">超值拼团</view>
					<view style="color:#FF7E00;float:left;margin-left:20rpx">4款商品正在拼团。。。</view>
					<view style="float:right;">
						<text>更多</text>
						<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
					</view>
				</view>
				<view class="groupCenter">
					<view class="groupGoods">
						<view>
							<image src="https://www.cn2b2c.com/image/img/crm/202008/201AEC19CD79094F42F2DF0FFBC8349F.jpg" style="width:225rpx;height:176rpx;"></image>
						</view>
						<view class="goodsName">的粉色闪电放松放松粉色闪电放松放松</view>
						<view style="color:#FF7E00;margin:4rpx 0;">3人拼团￥10</view>
						<view style="font-size:22rpx;color:#919398">单买价 ¥32.7</view>
					</view>
				</view>
			</view> -->
      <!-- 团购 end -->
      <view style="text-align:center;margin-top:10px;font-size:13px;color:#444;">
        <image src="{{bottom_logo}}" style="width:100%;height:auto;" mode="widthFix"></image>
      </view>
    </view>
  </scroll-view>
</view>
<!--在底部的购物车-->

<!--加入购物车   立即购买-->
<view class='black_bg' hidden="{{addToShoppingCartHidden}}"></view>
<view class='scroll_blo' hidden="{{addToShoppingCartHidden}}">
  <view>
    <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='hiddeAddToShoppingCart' />
    <view style="display:flex;padding:30rpx;border-top-left-radius:30rpx;border-top-right-radius:30rpx;">
      <view>
        <image style="width:190rpx" lazy-load='true' mode="{{mode}}" src="{{goodsDetail.picList[0].commodityPicPath}}">
        </image>
      </view>
      <view style="margin-left:30rpx;display:flex;justify-content:space-between;flex-direction:column;">
        <label class='addgoods_title'>{{goodsDetail.commodityName}}</label>
        <view>
          <block wx:if="{{showskuAllAttrList.length==0}}">
            <view hidden="{{commodityUnitOmDefault==1?false:true}}" class='addgoods_price'>
              ￥{{goodsPriceOM}}/{{goodsOMUnit}}<text
                style="color:#666;margin-left:20rpx;">(1{{goodsOMUnit}}={{commodityMultiple}}{{goodsOtUnit}})</text>
            </view>
            <view hidden="{{commodityUnitOtDefault==1?false:true}}" class='addgoods_price'>￥{{goodsPrice}}
              <block wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOtUnit}}</block>
            </view>
          </block>
          <block wx:if="{{showskuAllAttrList.length!=0}}">
            <view class='addgoods_price'>￥{{showGoodsPrice}}</view>
          </block>
          <view>
            <block wx:if="{{stockBean!=null}}">
              <block wx:if="{{stockBean.openStock}}">
                <block wx:if="{{stockBean.stockShowType==1}}">
                  <view class="attr_wrap" style="margin:0;">
                    <view class="choose_attr" style="padding:0">
                      <text style="font-size:24rpx;">库存：{{commodityVirtualStore}}</text>
                    </view>
                  </view>
                </block>
                <block wx:elif="{{stockBean.stockShowType==2}}">
                  <view class="attr_wrap" style="margin:0;">
                    <view class="choose_attr" style="padding:0">
                      <text style="font-size:24rpx;">库存：{{stockBean.showContent}}</text>
                    </view>
                  </view>
                </block>
              </block>
            </block>
            <block wx:else>
              <view class="attr_wrap" style="margin:0;" hidden="{{commodityVirtualStore>0?false:true}}">
                <view class="choose_attr" style="padding:0">
                  <text style="font-size:24rpx;">库存：{{commodityVirtualStore}}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <block wx:key="unique" wx:for="{{showskuAllAttrList}}" wx:for-item="sku">
      <view class='goods_classify'>
        <label>{{sku.skuAttrName}}</label>
        <view class='clearfix'>
          <block wx:key="unique" wx:for="{{sku.skuAttrValueList}}" wx:for-item="skuChild">
            <text bindtap='changeSKUBindTap' data-name='{{sku.skuAttrName}}' data-childname='{{skuChild.skuAttrName}}'
              class='{{skuChild.isSelect?"active_classify":""}}'>{{skuChild.skuAttrName}}</text>
          </block>
        </view>
      </view>
    </block>

    <view class='addgoods_number clearfix' style="padding:40rpx 0;display:flex;justify-content:space-between;">
      <label class='limited_quantity' style="line-height:70rpx;">购买数量
        <block wx:if="{{goodsDetail.participatePromotion}}">
          <block wx:key="unique" wx:for="{{goodsDetail.retailPromotionList}}" wx:for-item="promotion">
            <block wx:if="{{promotion.promotionLimitEnabled}}">
              <label style='font-size:12px; color:#888;'>(限购{{promoton.promotionLimitOtNum}}件)</label>
            </block>
          </block>
        </block>
      </label>
      <view class='clearfix plus_minus' style="flex:1;display:flex;flex-direction:column;align-items:flex-end;">
        <view hidden="{{commodityUnitOmDefault==1?false:true}}" style="margin-top:30rpx;">
          <label class="minus_box" bindtap='omMinusNumBindTap'>-</label>
          <input type='number' value="{{buyOmNum}}" bindinput="otNumBindInput"></input>
          <label class="plus_box" bindtap='omPlusNumBindTap'>+</label>
          <block wx:if="{{showskuAllAttrList.length==0}}"><text
              style="line-height:50rpx;font-size:26rpx;margin-left:20rpx;">{{goodsOMUnit}}</text></block>
        </view>
        <view style="margin-top:20rpx;" hidden="{{commodityUnitOtDefault==1?false:true}}">
          <label class="minus_box" bindtap='otMinusNumBindTap'>-</label>
          <input type='number' value='{{buyOtNum}}' bindinput="otNumBindInput"></input>
          <label class="plus_box" bindtap='otPlusNumBindTap'>+</label>
          <block wx:if="{{showskuAllAttrList.length==0}}"><text
              style="line-height:50rpx;font-size:26rpx;margin-left:20rpx;">{{goodsOtUnit}}</text></block>
        </view>
      </view>
    </view>
  </view>
  <block wx:if="{{goodsDetail.commoditySaleWay==3}}">
    <block wx:if="{{overallStock==1&&commodityVirtualStore<=0}}">
      <view
        style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#999;height:80rpx;line-height:80rpx;">
        已售罄</view>
    </block>
    <block wx:else>
      <block wx:if="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='TUANGOU'}}">
        <view
          style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#FF7E00;height:80rpx;line-height:80rpx;"
          data-type='2' bindtap='nowGroupBuyBindTap'>发起拼团</view>
      </block>
      <block
        wx:elif="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='MIAOSHA'}}">
        <view
          style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#FF7E00;height:80rpx;line-height:80rpx;"
          data-type='1' bindtap='nowPayClick'>立即购买</view>
      </block>
      <block wx:else>
        <view
          style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#FF7E00;height:80rpx;line-height:80rpx;"
          bindtap='addShoppingCartBindTap'>加入购物车</view>
      </block>
    </block>
  </block>
  <block wx:elif="{{goodsDetail.commoditySaleWay==4}}">
    <view
      style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#999;height:80rpx;line-height:80rpx;">
      已售罄</view>
  </block>
  <block wx:elif="{{goodsDetail.commoditySaleWay==1}}">
    <view
      style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#999;height:80rpx;line-height:80rpx;">
      即将上市</view>
  </block>
</view>
<!--加入购物车   立即购买-->
<!--在底部的购物车-->
<!--客户标签弹出-->
<template is="tagItem"
  data="{{tagHidden,topPage,topLeft,currentTag,casualGoods,cheapGoods,popularGoods,disLikeGoods,tagImg5,tagImg6,tagImg7,tagImg8,c_casualGoods,c_cheapGoods,c_popularGoods,c_disLikeGoods,c_tagImg5,c_tagImg6,c_tagImg7,c_tagImg8}}"></template>
<!--公告弹出层-->
<view class='black_bg' hidden="{{contentHidden}}"></view>
<view class='scroll_block' hidden="{{contentHidden}}">
  <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='cancelButtonBindTap' />
  <view class="announce_Title">
    {{announceTitle}}
  </view>
  <view class="announce_Content">
    {{announceContent}}
  </view>
</view>
<!--联系客服-->
<view style="position:fixed;right:20rpx;bottom:160rpx;z-index:9999;" hidden="{{!isOnlineService}}">
  <image src="{{online_service}}" mode="widthFix" style="text-align:right;width:140rpx;height:auto;"></image>
  <button open-type="contact" size="20" session-from="weapp"
    style="z-index:2;height:140rpx;width:140rpx;position:absolute;bottom:0;right:0;opacity:0;"></button>
</view>
<!--联系客服-->



