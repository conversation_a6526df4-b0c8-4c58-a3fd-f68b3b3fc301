<view class="addressBox">
	<view style="margin-bottom:20rpx">
		<block wx:for="{{normalAddressList}}" wx:for-item="address" wx:for-index="addressIndex" wx:key="unique">
			<view class='address_box'>
				<view bindtap='selectAddressBindTap' data-id="{{address.id}}">
					<view class='address'>
						<block wx:if="{{address.isdefault==1}}">
							<label class="title">[默]</label>
						</block>
						<label class="addressCo">{{address.province+address.city+address.area+address.address+address.houseNumber}}</label>
					</view>
					<view class='clearfix contacts_phone'>
						<label class='contacts'>{{address.username}}</label>
						<label class='phone'>{{address.telephone}}</label>
					</view>
				</view>
				<view class='clearfix operate_box' style="position:relative">
					<view style="position:absolute;bottom:8rpx;right:0">
						<view class="edit_box" data-id="{{address.id}}" bindtap='updateAddressBindTap'>
							<label>
								<image src='{{edit}}'></image><text style="font-size: 26rpx;">编辑</text>
							</label>
						</view>
						<block wx:if="{{address.isdefault==2}}">
							<view class="default_address" data-id="{{address.id}}" bindtap='setDefaultAddressBindTap'>设置默认地址</view>
						</block>
					</view>
				</view>
			</view>
		</block>
	</view>

	<!-- 超出配送范围 -->
	<block wx:if="{{outOfRangeAddressList.length>0}}">
		<view>
			<view style="font-weight:bold;font-size:30rpx;line-height:80rpx;background: #fff;padding: 0 20rpx;border-bottom: 1px solid #f6f6f6;">以下地址超出配送范围</view>
			<block wx:for="{{outOfRangeAddressList}}" wx:for-item="address" wx:for-index="addressIndex" wx:key="unique">
				<view class='address_box'>
					<view>
						<view class='address'>
							<label class="addressCo" style="float:none;color:#bcbcbc">{{address.province+address.city+address.area+address.address+address.houseNumber}}</label>
						</view>
						<view class='clearfix contacts_phone'>
							<label class='contacts' style="color:#bcbcbc;">{{address.username}}</label>
							<label class='phone' style="color:#bcbcbc;">{{address.telephone}}</label>
						</view>
					</view>
					<view class='clearfix operate_box' style="position:relative">
						<view style="position:absolute;bottom:8rpx;right:0">
							<view class="edit_box" data-id="{{address.id}}" bindtap='updateAddressBindTap'>
								<label>
									<image src='{{edit}}'></image><text>编辑</text>
								</label>
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>
	</block>
</view>
<view class='add_address'>
	<button bindtap='addNewAddressBindTap'>
		添加新地址
	</button>
</view>