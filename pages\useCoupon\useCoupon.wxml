<!--pages/useCoupon/useCoupon.wxml-->
<view>
  <view>
    <!--不使用券-->
    <view class="noCoupon" bindtap='notUseCardBindTap' style="margin:10px 0;text-align:center;">
      <label class="darkRed">不使用优惠券</label>
    </view>
    <!--不使用券-->
    <block wx:key="unique" wx:for="{{storeCard}}" wx:for-item="sCard" wx:for-index="cardIndex">
      <block wx:if="{{sCard.expireState==0}}">
        <!--可使用券-->
        <view class="oneCoupon clearfix" bindtap='goToUserCardBindTap' data-index='{{cardIndex}}'>
          <view class="red_bg couponInfo clearF">
            <block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
              <view class="couponValue">{{sCard.discountAmount}}
                <text>折</text>
              </view>
              <view class="couponDiscount">
                <view>
                  {{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"元可用"}}
                </view>
                <view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
                <view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
                  style="padding:10rpx 0;">
                  使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
                </view>
                <view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
              </view>
            </block>
            <block wx:else>
              <view class="couponValue">{{sCard.discountAmount}}
                <text>元</text>
              </view>
              <view class="couponDiscount">
                <view>
                  {{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"元可用"}}
                </view>
                <view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
                <view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
                  style="padding:10rpx 0;">
                  使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
                </view>
                <view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
              </view>
            </block>
            <view class="couponStatus">
              <text class="undrawStatus">立即使用</text>
            </view>
          </view>
        </view>
        <!--可使用券-->
      </block>
    </block>
    <block wx:key="unique" wx:for="{{storeCard}}" wx:for-item="sCard" wx:for-index="cardIndex">
      <block wx:if="{{sCard.expireState!=0}}">
        <view class="oneCoupon clearfix" bindtap='noUserCardBindTap' data-index='{{cardIndex}}'>
          <view class="red_bg couponInfo clearF" style="background-color:#9e9e9e;">
            <block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
              <view class="couponValue">{{sCard.discountAmount}}
                <text>折</text>
              </view>
              <view class="couponDiscount">
                <view>
                  {{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"元可用"}}
                </view>
                <view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
                <text style="display:block;font-size:24rpx;">{{sCard.unavailableCause}}</text>
                <view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
                  style="padding:10rpx 0;">
                  使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
                </view>
                <view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
              </view>
            </block>
            <block wx:else>
              <view class="couponValue">{{sCard.discountAmount}}
                <text>元</text>
              </view>
              <view class="couponDiscount">
                <view>
                  {{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"元可用"}}
                </view>
                <view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
                <text style="display:block;font-size:24rpx;">{{sCard.unavailableCause}}</text>
                <view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
                  style="padding:10rpx 0;">
                  使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
                </view>
                <view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
              </view>
            </block>
            <!--<view class="couponStatus">
              <text class="undrawStatus">{{sCard.unavailableCause}}</text>
            </view>-->
          </view>
        </view>
      </block>
    </block>
  </view>
</view>