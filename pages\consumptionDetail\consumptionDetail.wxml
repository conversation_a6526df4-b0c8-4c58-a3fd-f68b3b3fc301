<view class="timePicker">
  <view class="timeBox">
    <picker mode="date" value="{{date1}}" start="" end="" bindchange="bindDateChange1">
      <view class="picker">
        {{date1}}
      </view>
    </picker>
  </view>
  <view style="padding-top: 20rpx;">~</view>
  <view class="timeBox">
    <picker mode="date" value="{{date2}}" bindchange="bindDateChange2">
      <view class="picker">
        {{date2}}
      </view>
    </picker>
  </view>
  <view class="timeSearchBtn" bindtap="timeSearchFun">搜索</view>
</view>
<block wx:key="unique" wx:for="{{consumptionList}}" wx:for-item="consum">
  <view class="goods_w">
    <view style="border-bottom:1px solid #eee;padding:20rpx;font-size:26rpx !important;color:#333;">
      <view style="margin-bottom:10rpx;">{{consum.operDate}}</view>
      <view>销售订单号：{{consum.flowNo}}</view>
    </view>
    <view class="oneGood">
      <text>{{consum.itemName}}</text>
      <view class="m_w">
        <label class="m_c">
          <text>￥</text>
          {{consum.saleMoney}}/{{consum.itemUnit}}
        </label>
        <text>× {{consum.saleQnty}}</text>
      </view>
    </view>
  </view>
</block>