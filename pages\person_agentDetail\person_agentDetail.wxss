/* pages/person_agentDetail/person_agentDetail.wxss */
.clearfix:after{
  display: block;
  clear:both;
  visibility: hidden;
  content: ".";
  height:0;
}
.agentWrap{
  padding-top:60rpx;
  color:#3d3a3a;
  margin:0rpx 20rpx;
  font-size:28rpx;
}
.agentInfo{
  border-bottom:1px solid #e8e8e8;
  padding:30rpx 0rpx;
}
.agent_left{
  width:30%;
  text-align:center;
  float:left;
  border-right:1px solid #e8e8e8;
}
.agent_left image{
  width:150rpx;
  height:150rpx;
}
.agent_right{
  width:60%;
  float:left;
}
.agent_right label{
  display:block;
  margin:8rpx 0;
  margin-left:10%;
}

/* 定位 搜索 */

.location_box {
  height: 52rpx;
  position: fixed;
  top: 0px;
  left: 0;
  font-size: 24rpx;
  line-height: 52rpx;
  width: 100%;
  padding: 10rpx 0;
  z-index: 100;
  background: #fff;
  margin-bottom: 20rpx;
}

/*.location_box text {
  line-height: 52rpx;
  height: 52rpx;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #a6a6a6;
  color: #fff;
  border-radius: 30rpx !important;
  padding: 0 2%;
  overflow: hidden;
}*/

.location_box icon {
  float: left;
  position: absolute;
  top: 9px;
  left: 9%;
  z-index: 10;
  line-height: 68rpx;
}

.location_box input {
  line-height: 56rpx;
  height: 56rpx;
  width: 66%;
  display: block;
  float: left;
  margin:0 2% 0 6%;
  background: #ededed;
  color: #272727;
  border-radius: 30rpx;
  padding-left: 8%;
  padding-right: 4%;
}
.searchText{
  font-size:28rpx;
  color:#333;
  position:absolute;
  right:0;
  width:9%;
  background:none;
  text-align:right;
  margin-right: 2%;
  padding: 0 2%;
  line-height: 56rpx;
  height: 56rpx;
}