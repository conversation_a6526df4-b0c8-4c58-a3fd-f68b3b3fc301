<view class='content_box'>

  <!-- 报表年月 -->
  <label class='month_forms'>{{localStr}}月</label>
  <view class="section">
    <picker mode="date" value="{{date}}" start="2018-01-01" end="2038-01-01" bindchange="bindDateChange">
      <view class="picker">
        <text class='choose'>请选择年月</text>{{date}}
      </view>
    </picker>
  </view>

  <view class='reportforms_bg' bindtap="goReportDetail">
    <label class='report_title'>我的奖励总额<text style="color:#333;">明细</text></label>
    <label class='report_number'>{{totalRebateMoney}}
      <text>元</text>
    </label>
  </view>
  <view class='reportforms_bg'>
    <label class='report_title'>个人消费奖励
    </label>
    <label class='report_number' style="font-size:34rpx;color:#000;">
      <label style="margin-right:80rpx;">待结算：{{fFrozenRebateMoney}}
        <text style="font-size:24rpx;">元</text> </label>
      <label style="color:#000;">已结算：{{fValidRebateMoney}}
        <text style="font-size:24rpx;">元</text>
      </label>
    </label>
  </view>
  <view class='reportforms_bg'>
    <label class='report_title'>我的一级推广奖励
    </label>
    <label class='report_number' style="font-size:34rpx;color:#000;">
      <label style="margin-right:80rpx;">待结算：{{sFrozenRebateMoney}}
        <text style="font-size:24rpx;">元</text> </label>
      <label style="color:#000;">已结算：{{sValidRebateMoney}}
        <text style="font-size:24rpx;">元</text>
      </label>
    </label>
  </view>
</view>
<view class="bankCard" bindtap="bindcard">
  绑定结算银行卡
</view>