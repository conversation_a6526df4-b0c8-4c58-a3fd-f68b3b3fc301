// pages/chooseCard/chooseCard.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    card_bg: app.imageUrl + "card_bg.png",
    payType: "",
    cart_id_arr: "",
    cardList: [],
    goodsId: "",
    storeId: "",
    buyNum: "",
    skuId: "",
    groupBuyUserId: "",
    joinPromotion: "",
    isFromBack: false,
    boundTelephoneHidden: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.data.payType = options.payType;
    that.data.cart_id_arr = options.cart_arr;
    that.data.goodsId = options.goodsId;
    that.data.storeId = options.storeId;
    that.data.buyNum = options.buyNum;
    that.data.skuId = options.skuId;
    that.data.groupBuyUserId = options.groupBuyUserId;
    that.data.joinPromotion = options.joinPromotion;
    that.data.recommendUserId = options.recommendUserId;
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      that.setData({
        boundTelephoneHidden: false
      })
    } else {
      this.getMyVipCardInfo();
    }
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          if (cardList.length == 1) {
            var cardBean = cardList[0];
            if (that.data.payType == 1) {
              var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + that.data.cart_id_arr + '&vipCardNo=' + cardBean.cardId + '&vipCardCode=' + cardBean.vipCardCode + "&recommendUserId=" + that.data.recommendUserId;
              app.turnToPage(pagePath);
            } else if (that.data.payType == 2) {
              app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.goodsId + "&storeId=" + that.data.storeId + "&buyNum=" + that.data.buyNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&vipCardNo=" + cardBean.cardId + "&vipCardCode=" + cardBean.vipCardCode + "&recommendUserId=" + that.data.recommendUserId);
            }
          } else if (cardList.length > 1) {
            that.setData({
              companyName: app.getExtStoreName(),
              cardList: cardList
            });
          }
        } else {
          wx.showToast({
            title: "暂无可用会员卡",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                if (that.data.payType == 1) {
                  var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + that.data.cart_id_arr;
                  app.turnToPage(pagePath);
                } else if (that.data.payType == 2) {
                  app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.goodsId + "&storeId=" + that.data.storeId + "&buyNum=" + that.data.buyNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&recommendUserId=" + that.data.recommendUserId);
                }
              }, 1000);
            }
          })
        }
      }
    })
  },
  noUseVipCardBindTap: function () {
    var that = this;
    if (that.data.payType == 1) {
      var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + that.data.cart_id_arr;
      app.turnToPage(pagePath);
    } else if (that.data.payType == 2) {
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.goodsId + "&storeId=" + that.data.storeId + "&buyNum=" + that.data.buyNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&recommendUserId=" + that.data.recommendUserId);
    }
  },
  /**
   * 选择会员卡进行结算
   */
  chooseCardBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var cardBean = that.data.cardList[index];
    if (that.data.payType == 1) {
      var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + that.data.cart_id_arr + '&vipCardNo=' + cardBean.cardId + '&vipCardCode=' + cardBean.vipCardCode;
      app.turnToPage(pagePath);
    } else if (that.data.payType == 2) {
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.goodsId + "&storeId=" + that.data.storeId + "&buyNum=" + that.data.buyNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&vipCardNo=" + cardBean.cardId + "&vipCardCode=" + cardBean.vipCardCode + "&recommendUserId=" + that.data.recommendUserId);
    }
  },
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.getMyVipCardInfo();
    } else {
      that.setData({
        isFromBack: true
      });
    }
  },
  getPhoneNumber: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.login({
        success: function (res) {
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/wechatAppletLogin/jscode2session',
            data: {
              "code": res.code,
              "companyId": app.getExtCompanyId(),
              "storeId": app.getExtStoreId(),
              "withEncryption":1
            },
            success: function (res) {
              var session_Key = decodeURIComponent(res.data.session_Key);
              that.getUserTelephone(iv, encryptedData, session_Key);
            },
            fail: function () {
              app.showModal({
                title: '提示',
                content: "获取手机号码失败"
              });
            }
          })
        },
        fail: function (res) {
          app.showModal({
            title: '提示',
            content: "获取手机号码失败"
          });
        }
      })
    }
  },
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wechatAppletLogin/getUserTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var telephone = res.data.telephone;
        if (telephone != null && telephone.length > 0) {
          var userSession = wx.getStorageSync('userSession');
          userSession.telephone = telephone;
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
          that.getMyVipCardInfo();
        } else {
          that.setData({
            boundTelephoneHidden: true
          });
        }
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "请再试一次"
        });
      }
    })
  },
  /**
   * 暂不绑定手机号码
   */
  noBoundTelephoneBindTap: function () {
    this.setData({
      boundTelephoneHidden: true
    })
  }
})