/* pages/chooseCard/chooseCard.wxss */
.cardWrap{
  padding:40rpx 0;
  border-radius:10rpx;
  width:90%;
  margin:0 auto;
  margin-bottom:20rpx;
  position:relative;
  height:240rpx;
}
.cardWrap image{
  border-radius:10rpx;
  z-index:-1;
  position:absolute;
  width:100%;
  top:0;
  left:0;
  right:0;
  bottom:0;
}
.card_title{
  font-size:26rpx;
  margin-left:20rpx;
  text-align:center;
  color:#fff;
}
.card_title text{
  color:#fff;
  float:right;
  margin-right:20rpx;
}
.card_no{
  margin-top:20rpx;
  font-size:26rpx;
  text-align:left;
  margin-left:20rpx;
}
.amount_wrap{
  margin:40rpx 0;
  text-align:center;
  color:#fff;
}
.remain_amount{
  font-size:26rpx;
}
.remain_amount text{
  font-size:32rpx;
  font-weight:bold;
}
.remain_score{
  font-size:26rpx;
  margin-left:40rpx;
}
.remain_score text{
  font-size:32rpx;
  font-weight:bold;
}
.validDate{
  font-size:26rpx;
  margin-top:20rpx;
  margin-right:20rpx;
  text-align:right;
  color:#fff;
}
.card_id{
  text-align:left;
  padding-top:20rpx;
  padding-left:20rpx;
  color:#fff;
  font-size:24rpx;
}
.cardTip{
  font-weight:bold;
  text-align:center;
  font-size:32rpx;
  padding-bottom:40rpx;
}

/*弹出*/
.topWrap {
  text-align: center;
  margin: 15px 0;
  margin-top: 100rpx;
}

.topWrap image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}

.topWrap view {
  font-size: 12px;
  color: #999;
}

.companyLog {
  float: left;
  height: 100%;
  width: 49%;
  border-right: 1px solid #ececec;
  text-align: center;
  color: #b2b2b2;
}

.personLog {
  float: left;
  height: 100%;
  width: 49%;
  text-align: center;
  color: #b2b2b2;
}

.active {
  border-bottom: 2px solid #FF7E00;
  color: #444;
}

.loginType {
  background: #fff;
  font-size: 15px;
  margin-bottom: 20px;
  height: 44px;
  line-height: 44px;
}
.confirm_btn, .confirm_btn2 {
  display: inline-block;
  width: 35%;
  margin: 20px auto;
  height: 70rpx;
  line-height: 70rpx;
  border: none;
  margin-top: 60rpx;
  margin-right: 6%;
  margin-left: 6%;
  font-size: 26rpx;
}
.bl_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}