var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    card_bg: app.imageUrl + 'card_bg.png',
    cardList: [],
    telephone: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var telephone = options.telephone;
    if (telephone.length > 0) {
      this.setData({
        telephone: telephone
      });
      this.getUnderTheLineVipCardMessage(telephone);
    }
  },
  /**
   * 获取百威线下卡券
   */
  getUnderTheLineVipCardMessage: function (telephone) {
    var that = this;
    wx.showLoading({
      title: '正在处理，请稍后',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "vipTelephone": telephone
      },
      url: app.projectName + '/vipCard/querySureBoundUnderTheLineVipCard',
      success: function (res) {
        wx.hideLoading();
        var cardList = res.data.showVipCardList;
        if (cardList != null && cardList.length > 0) {
          that.setData({
            cardList: cardList,
            companyName: app.getExtStoreName()
          });
        } else {
          wx.showToast({
            title: "线下暂无会员卡可绑定",
            icon: 'none',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 2000);
            }
          })
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  offLineCardBind: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var cardBean = that.data.cardList[index];
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "vipTelephone": that.data.telephone,
        "cardNo": cardBean.cardId,
        "cardType": cardBean.vipType
      },
      url: app.projectName + '/vipCard/addUserVipCard',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "绑定成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.reLaunchToPage("/pages/accountManager/accountManager");
              }, 2000);
            }
          })
        } else {
          wx.showToast({
            title: res.data.returnMessage.length>0?res.data.returnMessage:"绑卡失败",
            icon: 'none',
            duration: 2000,
            mask: true
          })
        }
      }
    })
  },
  wechatAuthionTelephone: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.checkSession({
        success() {
          //session_key 未过期，并且在本生命周期一直有效
          var session_Key = wx.getStorageSync("session_Key");
          that.getUserTelephone(iv, encryptedData, session_Key);
        },
        fail() {
          // session_key 已经失效，需要重新执行登录流程
          wx.login({
            success: function (res) {
              if (res.code) {
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded'
                  },
                  method: "POST",
                  url: app.projectName + '/newAppletLogin/getUserOpenId',
                  data: {
                    "companyId": app.getExtCompanyId(),
                    "code": res.code
                  },
                  success: function (res) {
                    var openid = res.data.openid;
                    var session_Key = res.data.session_Key;
                    that.getUserTelephone(iv, encryptedData, session_Key);
                    wx.removeStorageSync("openId");
                    app.setStorage({
                      key: 'openId',
                      data: openid
                    });
                    wx.removeStorageSync("session_Key");
                    app.setStorage({
                      key: 'session_Key',
                      data: session_Key
                    });
                  }
                })
              }
            }
          })
        }
      })
    }
  },
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newAppletLogin/getUserTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var telephone = res.data.telephone;
        if (telephone.length > 0) {
          var userSession = wx.getStorageSync('userSession');
          userSession.telephone = telephone;
          userSession.loginAccount = telephone;
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
        } else {
          app.showModal({
            title: '提示',
            content: "请您再试一次"
          });
        }
        that.setData({
          telephone: telephone
        })
        that.getUnderTheLineVipCardMessage(telephone);
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "请再试一次"
        });
      }
    })
  },
})