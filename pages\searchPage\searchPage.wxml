<wxs src="../../wxs/subutil.wxs" module="tools" />
<view class="contant_box">

  <!-- 搜索框 -->
  <view class='search_box location_box'>
    <image  src="{{scan}}" bindtap='sweepCodeBindTap' 
        style="margin-right:40rpx;width:48rpx;margin-right:20rpx;height:67rpx"></image>
    <view class="s_inner">
      <icon type="search" size='16' />
      <input placeholder='请输入要搜索的商品' value='{{searchValue}}' bindinput='searchBindInput' focus="{{inputShowed}}"
        name="search" bindconfirm="searchBindTap" confirm-type="search" bindfocus='searchBindFocus'></input>
    </view>
    <text bindtap='searchBindTap'>搜索</text>
  </view>

  <!-- 搜索框 -->
  <!-- 历史搜索 -->
  <view style="padding-top:120rpx;">
    <view hidden='{{isShowHistory}}'>
      <view class='remove_box clearfix'>
        <text>历史记录</text>
        <image lazy-load='true' src='{{remove}}' bindtap='deleteHistoryBindTap'></image>
      </view>
      <view class='goods_name clearfix'>
        <block wx:key="unique" wx:for="{{searchHistory}}" wx:for-item="search">
          <text bindtap='historyBindTap' data-name='{{search}}'>{{search}}</text>
        </block>
      </view>
    </view>
    <image class='tips_pic' src='{{noGoodsSrc}}' hidden='{{isNoGoods}}'></image>
    <!-- 历史搜索 -->
    <!-- 商品 -->
    <view class='goods_box' hidden='{{isSHowGoods}}'>
      <view class="flex-wrp clearfix">
        <block wx:key="unique" wx:for="{{goodsList}}" wx:for-item="goods">
          <block wx:if="{{goods.commoditySaleWay==3}}">
            <view class="flex-item">
              <view class="goods_pic">
                <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
                  <template is="mark1"
                    data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
                </block>
                <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
                  <template is="mark2"
                    data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
                </block>
                <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
                  <template is="mark3"
                    data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
                </block>
                <image lazy-load='true' bindtap='imageClick' data-commodityId='{{goods.commodityId}}'
                  src="{{goods.commodityMainPic}}"></image>
              </view>
              <label class="goods_title">{{goods.commodityName}}</label>
              <label class="goods_price" style="margin-right:10rpx"
                hidden="{{(goods.commodityUnitOmDefault==1&&goods.commodityUnitOtDefault!=1)?false:true}}">￥<text
                  style="font-size:28rpx">{{tools.sub.formatAmount(goods.omPrice,2)}}/{{goods.omName}}</text></label>
              <label class="goods_price" hidden="{{goods.commodityUnitOtDefault==1?false:true}}">￥<text
                  style="font-size:28rpx;">{{tools.sub.formatAmount(goods.otPrice,2)}}/{{goods.otName}}</text></label>
              <image data-id="{{goods.commodityId}}" bindtap="addCartClick"
                style="margin-right:24rpx;float:right;width:46rpx;" mode="widthFix" src="{{shop_cart1}}"></image>
            </view>
          </block>
        </block>
      </view>
    </view>
  </view>
</view>
<!--加入购物车   立即购买-->
<view class='black_bg' hidden="{{addToShoppingCartHidden}}"></view>
<view class='scroll_blo' hidden="{{addToShoppingCartHidden}}">
  <view>
    <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='hiddeAddToShoppingCart' />
    <view style="display:flex;padding:30rpx;border-top-left-radius:30rpx;border-top-right-radius:30rpx;">
      <view>
        <image style="width:190rpx" lazy-load='true' mode="widthFix" src="{{goodsDetail.picList[0].commodityPicPath}}">
        </image>
      </view>
      <view style="margin-left:30rpx;display:flex;justify-content:space-between;flex-direction:column;">
        <label class='addgoods_title'>{{goodsDetail.commodityName}}</label>
        <view>
          <view hidden="{{commodityUnitOmDefault==1?false:true}}" class='addgoods_price'>
            ￥{{goodsPriceOM}}/{{goodsOMUnit}}<text
              style="color:#666;margin-left:20rpx;">(1{{goodsOMUnit}}={{commodityMultiple}}{{goodsOtUnit}})</text>
          </view>
          <view hidden="{{commodityUnitOtDefault==1?false:true}}" class='addgoods_price'>￥{{goodsPrice}}
            <block wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOtUnit}}</block>
          </view>
          <view>
            <block wx:if="{{stockBean!=null}}">
              <block wx:if="{{stockBean.openStock}}">
                <block wx:if="{{stockBean.stockShowType==1}}">
                  <view class="attr_wrap" style="margin:0;">
                    <view class="choose_attr" style="padding:0">
                      <text style="font-size:24rpx;">库存：{{commodityVirtualStore}}</text>
                    </view>
                  </view>
                </block>
                <block wx:elif="{{stockBean.stockShowType==2}}">
                  <view class="attr_wrap" style="margin:0;">
                    <view class="choose_attr" style="padding:0">
                      <text style="font-size:24rpx;">库存：{{stockBean.showContent}}</text>
                    </view>
                  </view>
                </block>
              </block>
            </block>
            <block wx:else>
              <view class="attr_wrap" style="margin:0;" hidden="{{commodityVirtualStore>0?false:true}}">
                <view class="choose_attr" style="padding:0">
                  <text style="font-size:24rpx;">库存：{{commodityVirtualStore}}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <block wx:key="unique" wx:for="{{showskuAllAttrList}}" wx:for-item="sku">
      <view class='goods_classify'>
        <label>{{sku.skuAttrName}}</label>
        <view class='clearfix'>
          <block wx:key="unique" wx:for="{{sku.skuAttrValueList}}" wx:for-item="skuChild">
            <text bindtap='changeSKUBindTap' data-name='{{sku.skuAttrName}}' data-childname='{{skuChild.skuAttrName}}'
              class='{{skuChild.isSelect?"active_classify":""}}'>{{skuChild.skuAttrName}}</text>
          </block>
        </view>
      </view>
    </block>

    <view class='addgoods_number clearfix' style="padding:40rpx 0;display:flex;justify-content:space-between;">
      <label class='limited_quantity' style="line-height:70rpx;">购买数量
        <block wx:if="{{goodsDetail.participatePromotion}}">
          <block wx:key="unique" wx:for="{{goodsDetail.retailPromotionList}}" wx:for-item="promotion">
            <block wx:if="{{promotion.promotionLimitEnabled}}">
              <label style='font-size:12px; color:#888;'>(限购{{promoton.promotionLimitOtNum}}件)</label>
            </block>
          </block>
        </block>
      </label>
      <view class='clearfix plus_minus' style="flex:1;display:flex;flex-direction:column;align-items:flex-end;">
        <view hidden="{{commodityUnitOmDefault==1?false:true}}" style="margin-top:30rpx;">
          <label class="minus_box" bindtap='omMinusNumBindTap'>-</label>
          <input type='number' value="{{buyOmNum}}" bindinput="otNumBindInput"></input>
          <label class="plus_box" bindtap='omPlusNumBindTap'>+</label>
          <block wx:if="{{showskuAllAttrList.length==0}}"><text
              style="line-height:50rpx;font-size:26rpx;margin-left:20rpx;">{{goodsOMUnit}}</text></block>
        </view>
        <view style="margin-top:20rpx;" hidden="{{commodityUnitOtDefault==1?false:true}}">
          <label class="minus_box" bindtap='otMinusNumBindTap'>-</label>
          <input type='number' value='{{buyOtNum}}' bindinput="otNumBindInput"></input>
          <label class="plus_box" bindtap='otPlusNumBindTap'>+</label>
          <block wx:if="{{showskuAllAttrList.length==0}}"><text
              style="line-height:50rpx;font-size:26rpx;margin-left:20rpx;">{{goodsOtUnit}}</text></block>
        </view>
      </view>
    </view>
  </view>
  <block wx:if="{{goodsDetail.commoditySaleWay==3}}">
    <block wx:if="{{overallStock==1&&commodityVirtualStore<=0}}">
      <view
        style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#999;height:80rpx;line-height:80rpx;">
        已售罄</view>
    </block>
    <block wx:else>
      <block wx:if="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='TUANGOU'}}">
        <view
          style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#FF7E00;height:80rpx;line-height:80rpx;"
          data-type='2' bindtap='nowGroupBuyBindTap'>发起拼团</view>
      </block>
      <block
        wx:elif="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='MIAOSHA'}}">
        <view
          style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#FF7E00;height:80rpx;line-height:80rpx;"
          data-type='1' bindtap='nowPayClick'>立即购买</view>
      </block>
      <block wx:else>
        <view
          style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#FF7E00;height:80rpx;line-height:80rpx;"
          bindtap='addShoppingCartBindTap'>加入购物车</view>
      </block>
    </block>
  </block>
  <block wx:elif="{{goodsDetail.commoditySaleWay==4}}">
    <view
      style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#999;height:80rpx;line-height:80rpx;">
      已售罄</view>
  </block>
  <block wx:elif="{{goodsDetail.commoditySaleWay==1}}">
    <view
      style="text-align:center;color:#fff;font-size:30rpx;border-radius:40rpx;margin:30rpx;background:#999;height:80rpx;line-height:80rpx;">
      即将上市</view>
  </block>
</view>
<!--加入购物车   立即购买-->
<!--嵌套的模块-->
<template name="mark3">
    <view class="mark1">
        <view class="mark1_l">
            <view>{{nameCircle}}</view>
        </view>
        <view class="mark1_r">
            {{name}}
        </view>
    </view>
</template>
<template name="mark2">
    <view class="mark2_{{pos}}">
    {{name}}
    </view>
</template>
<template name="mark1">
    <view class="mark_{{pos}}">
    {{name}}
    </view>
</template>