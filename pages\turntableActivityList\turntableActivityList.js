const app = getApp();
Page({

    /**
     * 页面的初始数据
     */
    data: {

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this;
        that.goLuckDrawClick()

    },
    goLuckDrawClick:function(){
        var that = this;
        var data = {};
        data["tm"] = "/luckdraw/retailClient/outlines";
        data["companyId"] = app.getExtCompanyId();
        data["storeId"] = app.getExtStoreId();
        data["userId"] = app.getUserId();
        data["loginId"] = app.getLoginId();
        data["userRole"] = app.getUserRole();
        data["odbtoken"] = app.getodbtoken();
        data["loginToken"] = app.getloginToken();
        data["companyId"] = app.getExtCompanyId();
        data["currentPage"] = 1;
        data["pageSize"] = 10;
        data["storeId"] = app.getExtStoreId();
            /*获取当前任务信息*/
            wx.request({
              header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
              },
              method: "POST",
              url: app.projectName + '/simpleRouter/stickness',
              data: data,
              success: function (res) {
                  if(res.data.code == 1){
                    var resultList = JSON.parse(res.data.data).resultList;
                    if(resultList != null && resultList.length>0){
                        that.setData({
                            resultList:resultList
                        })
                    }
                    else{
                      
                    }
                  }
              },
              fail: function () {
                wx.hideLoading();
              }
            })
      },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    oneLuckDrawClick:function(e){
        var id = e.currentTarget.dataset.id;
        app.navigateToPage('/pages/turntableActivity/turntableActivity?id='+id);
    },
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})