<view style="padding-bottom:60px;">
  <view style="width:100%;">
    <!--配送方式-->
    <view class="pay_way clearfix" style="padding-top:10rpx;margin-bottom:20rpx;font-size:28rpx;">
      <text style="float:left;line-hieght:70rpx;margin-top:20rpx;">配送方式</text>
      <block wx:if="{{disWayList.length==2}}">
        <view style="float:left;padding-left:70rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="1">
          <label class="{{index==1?'de_checked':'de_unchecked'}}">
            <image src="{{shop_cart}}"></image>
          </label>
          <label style="vertical-align:top;margin-top:20rpx;line-height:70rpx;color:#666;font-size:26rpx;">商家配送</label>
        </view>
        <view style="float:left;padding-left:50rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="2">
          <label class="{{index==2?'de_checked':'de_unchecked'}}">
            <image src="{{shop_cart}}"></image>
          </label>
          <label style="vertical-align:top;line-height:70rpx;color:#666;font-size:26rpx;">门店自提</label>
        </view>
      </block>
      <block wx:else>
        <block wx:if="{{disWayList[0]==1}}">
          <view style="float:left;padding-left:70rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="1">
            <label class="de_checked">
              <image src="{{shop_cart}}"></image>
            </label>
            <label style="vertical-align:top;margin-top:20rpx;line-height:70rpx;color:#666;font-size:26rpx;">商家配送</label>
          </view>
        </block>
        <block wx:if="{{disWayList[0]==2}}">
          <view style="float:left;padding-left:50rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="2">
            <label class="de_checked'}}">
              <image src="{{shop_cart}}"></image>
            </label>
            <label style="vertical-align:top;line-height:70rpx;color:#666;font-size:26rpx;">门店自提</label>
          </view>
        </block>
      </block>
    </view>
    <!--配送方式-->
    <!--选择门店-->
    <block wx:if="{{disWayList.length==2}}">
      <block wx:if="{{index==1}}">
        <view class='add_customer'>
          <view style="font-size:28rpx;color:#000;">配送门店</view>
          <block wx:if="{{chooseStoreValue!=''}}">
            <text style="color:#666;">{{chooseStoreValue}}</text>
          </block>
          <block wx:else>
            选择门店
          </block>
        </view>
      </block>
      <block wx:elif="{{index==2}}">
        <view class='add_customer' bindtap="chooseStore">
          <view style="font-size:28rpx;color:#000;">自取门店</view>
          <block wx:if="{{chooseStoreValue!=''}}">
            <text style="color:#666;">{{chooseStoreValue}}</text>
          </block>
          <block wx:else>
            选择门店
          </block>
          <label class="icondirect">︿</label>
        </view>
      </block>
    </block>
    <block wx:else>
      <block wx:if="{{disWayList[0]==1}}">
        <view class='add_customer'>
          <view style="font-size:28rpx;color:#000;">配送门店</view>
          <text style="color:#666;">{{chooseStoreValue}}</text>
        </view>
      </block>
      <block wx:if="{{disWayList[0]==2}}">
        <view class='add_customer' bindtap="chooseStore">
          <view style="font-size:28rpx;color:#000;">自取门店</view>
          <block wx:if="{{chooseStoreValue!=''}}">
            <text style="color:#666;">{{chooseStoreValue}}</text>
          </block>
          <block wx:else>
            选择门店
          </block>
          <label class="icondirect">︿</label>
        </view>
      </block>
    </block>
  </view>
  <!--自取门店-->
  <!-- 自取人信息 -->
  <view class="leave_word clearfix" hidden='{{index==2?false:true}}'>
    <view style="font-size:28rpx;color:#000;height:56rpx;line-height:56rpx;">自取信息</view>
    <view class="clearfix" style="font-size:28rpx;padding:8rpx 0;">
      <text style="float:left;font-size:28rpx;color:#333;">姓名</text>
      <input bindinput="pickOrderUserNameBindInput" style="float:left;padding-left:20rpx;line-height: 52px;" maxlength="5" placeholder="请输入姓名" value="{{pickOrderUserName}}"></input>
    </view>
    <view class="clearfix" style="font-size:28rpx;padding:8rpx 0;">
      <text style="float:left;color:#333;">手机</text>
      <input maxlength="11" bindinput="pickOrderContactNumBindInput" value="{{pickOrderContactNum}}" style="float:left;padding-left:20rpx;" placeholder="请输入手机"></input>
    </view>
  </view>
  <!--期望到货时间-->
  <view class='add_customer' hidden="{{index==2?false:true}}" style="padding-bottom:70rpx;">
    <view style="font-size:30rpx;color:#000;font-weight:bold">自取日期</view>
    <picker bindchange="bindExpectDateChange" range="{{DateArray}}">
      <!--mode="date"-->
      <view class="selectArea">
        {{DateArray[date_index]}}
        <label class="icondirect">︿</label>
      </view>
    </picker>
    <view class="selectArea">
      {{pickTime}}
    </view>
  </view>
  <!--收货地址-->
  <view class='add_customer' bindtap='selectAddressBindTap' hidden='{{index==2?true:false}}'>
    <view style="font-size:30rpx;color:#000;font-weight:bold">收货地址</view>
    <block wx:if="{{receiveAddress!=''}}">
      <view>
        <text>姓名：{{username}}</text>
        <text style="margin-left:10px;">电话 {{telephone}}</text>
      </view>
      {{receiveAddress}}
    </block>
    <block wx:else>
      添加地址
      <label class="directIcon">︿</label>
    </block>
  </view>
  <!--支付方式-->
  <!--订单信息-->
  <view class="infoWrap">
    <view class="payInfo" style="font-weight:bold;padding:10rpx 26rpx;background:#fff;font-size: 30rpx;">商品信息</view>
    <view style="max-height:400px;overflow:scroll;">
      <block wx:for="{{ goodsList}}" wx:for-item="goods" wx:for-index="goodsIndex" wx:key="">
        <view class='single_box clearfix'>
          <image src='{{goods.commodityMainPic}}'></image>
          <view class="product_detail">
            <view class="detail_line">
              <label class='single_title'>{{goods.commodityName}}</label>
            </view>
            <view class='classify_box'>
              <block wx:for="{{goods.skuUnitList}}" wx:for-item="sku" wx:key="" wx:for-index="skuIdx">
                <block wx:if="{{goods.skuUnitList.length-1==skuIdx}}">
                  {{sku.skuName}}:{{sku.skuValue}}
                </block>
                <block wx:else>
                  {{sku.skuName}}:{{sku.skuValue}};
                </block>
              </block>
            </view>
            <view class='clearfix plus_minus' style="width:100%;margin-top:20rpx;text-align:right;">
              <block wx:if="{{goods.commodityType==4}}">
                <label class='goods_number' style="color:#FF7E00">赠品</label>
              </block>
              <view class="clearfix" style="height:60rpx;">
                <view style="color:#FF7E00;float:right;font-size:26rpx;">{{orderTotalMoney}}积分</view>
              </view>
              <view style="text-align:right;margin-right:15px;font-size:26rpx;">数量:X1
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <view class='add_customer diliver_add'>
      运费
      <label style="margin-right:5px;">¥{{orderDistributionPay}}</label>
    </view>
    <view class='add_customer' style="margin-top:0px">
      <input type="text" placeholder='买家留言' bindinput='orderCommentBindInput'></input>
    </view>
    <view class="allPrice">
      <view class="view_total">
        <label>共{{goodsList.length}}件 小计</label>
        <label>{{orderTotalMoney}}积分</label>
      </view>
    </view>
  </view>
</view>
<view class='foot_box'>
  <view class='total_box'>合计:
    <label>{{orderTotalMoney}}积分</label>
  </view>
  <view class="settle_accounts" bindtap='submitOrderBindTap'>提交订单</view>
</view>