var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var wxMarkerData = [];
Page({
  data: {
    commodityBaseList: [],
    pageSize: 10,
    currpage: 1,
    categoryId: "",
    mode: "widthFix",
    event: app.imageUrl + 'event.png',
    scan: app.imageUrl + 'scan.png',
  },
  onLoad: function (options) {
    var that = this;
    var id = that.options.categoryId;
    that.queryGoodsByTypeId(id);
    that.setData({
      categoryId: id,
    });
  },
  /**
   * 点击搜索框跳转
   */
  searchBindFocus: function () {
    app.navigateToPage('/pages/searchPage/searchPage');
  },
    /**
   * 扫码识别商品
   */
  sweepCodeBindTap: function () {
    var that = this;
    wx.scanCode({
      success(res) {
        var filterName = res.result;
        app.navigateToPage('/pages/searchPage/searchPage?filterName=' + filterName);
      }
    })
  },
  secondCatogroyTap: function (e) {
    var categoryId = e.currentTarget.dataset.id;
    app.navigateToPage("/pages/secondCategory/secondCategory?categoryId=" + categoryId);
  },
  /**
 * 查询商品分类
 */
  queryGoodsType: function () {
    var that = this;
    wx.showLoading({
      title: '加载数据中',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/applet/goods/queryGoodsType',
      success: function (res) {
        wx.hideLoading();
        var categoryBean = res.data.categoryBean;
        var cataId = that.data.cataId;
        for (var i = 0; i < categoryBean.length; i++) {
          if (categoryBean[i].categoryId == that.data.categoryId) {
            that.setData({
              stockBean: res.data.stockBean,
              secondType: categoryBean[i].children
            });
            return;
          }
        }
      }
    })
  },
  /**
   * 查询商品根据分类Id
   */
  queryGoodsByTypeId: function (categoryId) {
    wx.showLoading({
      title: '加载数据中',
      mask: true
    })
    var that = this;
    that.queryGoodsType();
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "categoryId": categoryId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "pageSize": that.data.pageSize,
        "currpage": that.data.currpage,
      },
      url: app.projectName + '/applet/goods/queryGoodsByTypeId',
      success: function (res) {
        wx.hideLoading();
        var newCommodityBaseList = res.data.commodityBaseList;
        var oldCommodityBaseList = that.data.commodityBaseList;
        if (newCommodityBaseList != null && newCommodityBaseList.length > 0) {
          for (var i = 0; i < newCommodityBaseList.length; i++) {
            newCommodityBaseList[i].goodsPrice = newCommodityBaseList[i].goodsPrice.toFixed(2);
            newCommodityBaseList[i].cutOffThePrice = newCommodityBaseList[i].cutOffThePrice.toFixed(2);
          }
          newCommodityBaseList = oldCommodityBaseList.concat(newCommodityBaseList);
        } else {
          newCommodityBaseList = oldCommodityBaseList;
        }
        that.setData({
          commodityBaseList: newCommodityBaseList,
        });
      }
    })
  },
  /**
   * 
   * 
   **/
  searchScrollLower: function () {
    var that = this;
    that.setData({
      currpage: that.data.currpage + 1
    });
    that.queryGoodsByTypeId(that.data.categoryId);
  },
  /**
   * 跳转到商品详情页面
   */
  imageClick: function (e) {
    var id = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + id);
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }



})