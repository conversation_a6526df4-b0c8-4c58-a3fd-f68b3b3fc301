<block wx:key="unique" wx:for="{{goodsList}}" wx:for-item="goods">
  <view class='single_goods'>
    <view class='goods_box'>
      <image class='goods_pic' src='{{goods.commodityMainPic==""?goods_pic:goods.commodityMainPic}}'></image>
      <view class='right_detail'>
        <text class='goods_name'>{{goods.commodityName}}</text>
        <text class="goods_tips">{{goods.commoditySpec}}</text>
      </view>
    </view>
    <view class="comment1-description">
      <view class="comment1-description1">评分</view>
      <view class="star-pos">
        <view data-id='{{goods.commodityId}}' data-score='1' class="starsM  {{goods.evaluateScore>=1? '': 'hideStar'}}" bindtap="changeScoreBindTap"></view>
        <view data-id='{{goods.commodityId}}' data-score='2' class="starsM  {{goods.evaluateScore>=2? '': 'hideStar'}}" bindtap="changeScoreBindTap"></view>
        <view data-id='{{goods.commodityId}}' data-score='3' class="starsM  {{goods.evaluateScore>=3? '': 'hideStar'}}" bindtap="changeScoreBindTap"></view>
        <view data-id='{{goods.commodityId}}' data-score='4' class="starsM  {{goods.evaluateScore>=4? '': 'hideStar'}}" bindtap="changeScoreBindTap"></view>
        <view data-id='{{goods.commodityId}}' data-score='5' class="starsM  {{goods.evaluateScore>=5? '': 'hideStar'}}" bindtap="changeScoreBindTap"></view>
      </view>
    </view>
    <textarea data-id='{{goods.commodityId}}' bindinput='evaluateContentBindInput' placeholder='填写高质量的的评价，即可参与抽奖哦~'></textarea>
    <view class='addPic'>
      <block wx:key="unique" wx:for="{{goods.evaluateImageList}}" wx:for-item="ge">
        <view style='position:relative;width:60px;height:60px; float:left;margin-right:14px;margin-bottom:14px;'>
          <image class='add_pic' src='{{ge}}' data-src='{{ge}}' bindtap='previewImage'></image>
          <image src='{{close_btn}}' bindtap='deleteEvaluateImage' data-id='{{goods.commodityId}}' data-src='{{ge}}' style='position:absolute; top:-10px;right:-10px;width:20px; height:20px;'></image>
        </view>

      </block>
      <image class='add_pic' data-id='{{goods.commodityId}}' src='{{add_pic}}' bindtap='chooseImageBindTap'>

      </image>
    </view>
  </view>
</block>
<view class='button_box'>
  <button bindtap='submitEvaluateBindTap'>提交</button>
</view>