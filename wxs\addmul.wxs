var filters = {
  toFix2: function (value) {
    return value.toFixed(2)//此处2为保留两位小数
  },
  toFix1: function (value) {
    return value.toFixed(1)//此处1为保留一位小数
  },
  toFix: function (value) {
    return value.toFixed(0)//此处0为取整数
  },
  //截取日期或价格加金钱符号
  subPre: function (val) {
    if (val.length == 0 || val == undefined) {
      return;
    }
    if (val.indexOf("-") >= 0) {
      return val.substring(0, 10) + "至" + val.substring(20, 30);
    } else {
      return "￥" + val;
    }
  },
  //超过字符截取加省略号
  sub: function (val,num) {
    if (val.length == 0 || val == undefined) {
      return;
    }
    if (val.length > num) {
      return val.substring(0, num) + " ... ";
    } else {
      return val;
    }
  }
}
module.exports = {
  toFix2: filters.toFix2,
  toFix1: filters.toFix1,
  toFix: filters.toFix,
  subPre: filters.subPre,
  sub: filters.sub
}