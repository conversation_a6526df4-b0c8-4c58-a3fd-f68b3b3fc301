var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    return_apply: app.imageUrl + "accountManager/return_kefuDo.png",
    shop_cart: app.imageUrl + "shopDetails/zc_app_fontschecked.png",
    logistic: app.imageUrl + "pay_icon/logistic.png",
    supplierDeliver: app.imageUrl + "pay_icon/supplierDeliver.png",
    selfGet: app.imageUrl + "pay_icon/selfGet.png",
    wechat: app.imageUrl + "pay_icon/wechat.png",
    unionPay: app.imageUrl + "pay_icon/unionPay.png",
    payFirst: app.imageUrl + "pay_icon/payFirst.png",
    payLater: app.imageUrl + "pay_icon/payLater.png",
    cardDesc: "未使用任何优惠券",
    cardNo: "", //卡券编号
    orderComment: "", //订单留言
    distributionMode: [2, 1, 1],
    submitDeliveryWay: 0,
    storePayArray: [],
    payList: [{
      "payWay": "微信支付",
      "ishidden": true,
      "isCheck": false
    }, {
      "payWay": "银联支付",
      "ishidden": true,
      "isCheck": false
    }],
    receiveAddress: "", //收货地址
    username: "", //收货人姓名
    telephone: "", //收货人电话
    myStoreId: "", //我的门店Id
    myStoreName: "", //我的门店地址
    cart_id_arr: "", //购物车Id
    goodsId: "", //商品Id
    DateArray: ['选择自取日期', '2018-12-10 星期一', '2018-12-11 星期二', '2018-12-12 星期三', '2018-12-13 星期四', '2018-12-14 星期五', '2018-12-15 星期六', '2018-12-16 星期天'],
    date_index: 2,
    TimeArray: ['选择自取时间', '14:00'],
    time_index: 1,
    pickOrderStoreId: "", //自取店铺Id
    chooseStoreValue: "选择门店",
    date_expect_index: "期望到达日期",
    time_expect_index: "14:00",
    index: 1,
    changeGoodsPriceTotal: 0,
    exchangeCommodityId: "",
    pickOrderUserName: "", //自取联系人姓名
    pickOrderContactNum: "", //自取联系人手机号码
    pickTime: "08:00 - 18:00"
  },
  /*选择配送方式*/
  switchNav: function (e) {
    var currentTab = e.currentTarget.dataset.tab;
    var that = this;
    if (currentTab == 2 && that.data.pickOrderStoreId == "") {
      that.chooseStore();
    }
    that.setData({
      index: currentTab,
      submitDeliveryWay: currentTab,
    });
  },
  chooseStore: function (options) {
    app.navigateToPage('/pages/supplierShop/supplierShop');
  },
  goInvoiceBindTap: function (options) {
    app.navigateToPage('/pages/invoice/invoice');
  },
  bindDateChange: function (e) {
    this.setData({
      date_index: e.detail.value
    })
  },
  bindTimeChange: function (e) {
    this.setData({
      time_index: e.detail.value
    })
  },
  bindExpectDateChange: function (e) {
    this.setData({
      date_index: e.detail.value
    })
  },
  bindExpectTimeChange: function (e) {
    this.setData({
      time_expect_index: e.detail.value
    })
  },
  pickOrderUserNameBindInput: function (e) {
    this.setData({
      pickOrderUserName: e.detail.value
    })
  },
  pickOrderContactNumBindInput: function (e) {
    this.setData({
      pickOrderContactNum: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var exchangeCommodityId = options.exchangeCommodityId;
    this.setData({
      exchangeCommodityId: exchangeCommodityId
    })
    this.dataInitial(exchangeCommodityId);
  },
  //2018-12-10 星期一
  dateInit: function (selfTaking) {
    var dateArry = ['选择自取日期'];
    for (var i = selfTaking; i < 7 + selfTaking; i++) {
      var dateObj = this.dateLater(this.getCurrentMonthFirst(), i);
      dateArry.push(dateObj.year + "-" + dateObj.month + "-" + dateObj.day + " " + dateObj.week)
    }
    this.setData({
      DateArray: dateArry,
      date_expect_index: dateArry[1],
    });
  },
  dateLater: function (dates, later) {
    let dateObj = {};
    let show_day = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六');
    let date = new Date(dates);
    date.setDate(date.getDate() + later);
    let day = date.getDay();
    dateObj.year = date.getFullYear();
    dateObj.month = ((date.getMonth() + 1) < 10 ? ("0" + (date.getMonth() + 1)) : date.getMonth() + 1);
    dateObj.day = (date.getDate() < 10 ? ("0" + date.getDate()) : date.getDate());
    dateObj.week = show_day[day];
    return dateObj;
  },
  /**
   * 
   */
  getCurrentMonthFirst: function () {
    var date = new Date();
    var todate = date.getFullYear() + "-" + ((date.getMonth() + 1) < 10 ? ("0" + (date.getMonth() + 1)) : date.getMonth() + 1) + "-" + (date.getDate() < 10 ? ("0" + date.getDate()) : date.getDate());
    return todate;
  },
  dataInitial: function (exchangeCommodityId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/pregenerateCommodityRetailOrder',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "localStoreId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "exchangeCommodityId": exchangeCommodityId
      },
      success: function (res) {
        var wholesaleOrderBean = res.data.wholesaleOrderBean;
        var payMoney = res.data.payMoney;
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          if (wholesaleOrderBean != null) {
            var disWayList = res.data.disWayList;
            var disWayIndex = 1;
            var submitDeliveryWay = 1;
            if (disWayList.length == 1) {
              disWayIndex = disWayList[0];
              submitDeliveryWay = disWayList[0];
            }
            that.dateInit(res.data.selfTaking);
            that.setData({
              pickTime: res.data.pickTime, //自取时间
              submitDeliveryWay: submitDeliveryWay,
              index: disWayIndex,
              pickOrderStoreId: res.data.distributionStoreId, //自取店铺Id
              chooseStoreValue: res.data.distributionStoreName,
              disWayList: disWayList,
              goodsList: wholesaleOrderBean.orderDetail,
              orderDistributionPay: wholesaleOrderBean.orderDistributionPay, //运费
              orderTotalMoney: payMoney, //订单总金额
              orderMoney: payMoney,
              orderCommodityTotalMoney: payMoney, //不加运费的
              receiveAddress: wholesaleOrderBean.receiveAddress, //默认收货地址
              username: wholesaleOrderBean.receiverName,
              telephone: wholesaleOrderBean.receiveContactNum,
              pickOrderUserName: wholesaleOrderBean.receiverName,
              pickOrderContactNum: wholesaleOrderBean.receiveContactNum,
            });
          }
        } else {
          wx.showToast({
            title: message,
            icon: 'none',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                wx.navigateBack({
                  delta: 2
                })
              }, 1000);
            }
          })
        }
        var storePayArray = res.data.storePayArray;
        var payList = that.data.payList;
        var k = 0;
        if (storePayArray != null && storePayArray.length > 0) {
          for (var i = 0; i < storePayArray.length; i++) {
            for (var j = 0; j < payList.length; j++) {
              var pay = payList[j];
              if (storePayArray[i] == pay.payWay) {
                k++;
                if (k == 1) {
                  pay.isCheck = true;
                }
                pay.ishidden = false;
              }
            }
          }
        }
        that.setData({
          payList: payList
        });
      }
    })
  },
  /**
   * 去查看我的优惠券
   */
  goCouponBindTap: function () {
    app.navigateToPage('/pages/useCoupon/useCoupon?goodsList=' + JSON.stringify(this.data.goodsList) + "&orderTotalMoney=" + this.data.orderTotalMoney + "&orderMoney=" + this.data.orderMoney);
  },
  /**
   * 买家留言
   */
  orderCommentBindInput: function (e) {
    this.setData({
      orderComment: e.detail.value
    });
  },
  /**
   * 选择支付方式
   */
  selectPayWayBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var payList = that.data.payList;
    for (var i = 0; i < payList.length; i++) {
      if (i == index) {
        payList[i].isCheck = true;
      } else {
        payList[i].isCheck = false;
      }
    }
    that.setData({
      payList: payList
    });
  },
  /**
   * 选择收货地址
   */
  selectAddressBindTap: function () {
    app.navigateToPage('/pages/addAddress/addAddress');
  },
  /**
   * 提交订单
   */
  submitOrderBindTap: function () {
    var that = this;

    if (that.data.submitDeliveryWay == 2) {
      if (that.data.pickOrderStoreId == "") {
        app.showModal({
          content: '请选择自取门店'
        });
        return;
      }
      if (that.data.date_index == 0) {
        app.showModal({
          content: '选择自取日期'
        });
        return;
      }
      if (that.data.time_index == 0) {
        app.showModal({
          content: '选择自取时间'
        });
        return;
      }
      if (that.data.pickOrderUserName == "") {
        app.showModal({
          content: '请输入自取联系人'
        });
        return;
      }
      if (that.data.pickOrderContactNum == "") {
        app.showModal({
          content: '请输入自取联系电话'
        });
        return;
      }
    }

    if (that.data.submitDeliveryWay == 1) {
      if (that.data.receiveAddress == "" || that.data.telephone == "" || that.data.username == "") {
        app.showModal({
          content: '请选择收货地址'
        });
        return;
      }
    }
    var distributionWay = "";
    distributionWay = that.data.index;


    var pickTime = that.data.pickTime;
    var pickExpectArriveTime = pickTime.substring(0, pickTime.indexOf("-")).replace(/\s+/g, '');
    var pickExpectArriveTimeEnd = pickTime.substring(pickTime.indexOf("-") + 1).replace(/\s+/g, '');

    wx.showLoading({
      title: '正在提交，请稍后',
      mask: true
    });

    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/generateExchangeOrder',
      data: {
        "date_expect_index": that.data.date_expect_index,
        "time_expect_index": that.data.time_expect_index,
        "pickExpectArriveDate": that.data.DateArray[that.data.date_index],
        "pickExpectArriveTime": pickExpectArriveTime,
        "pickExpectArriveTimeEnd": pickExpectArriveTimeEnd,
        "pickOrderStoreId": that.data.pickOrderStoreId,
        "openId": app.getOpenId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "localStoreId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "goodsId": that.data.exchangeCommodityId,
        "distributionWay": distributionWay, //配送方式
        "address": that.data.receiveAddress,
        "username": that.data.username,
        "telephone": that.data.telephone,
        "orderComment": that.data.orderComment,
        "exchangeCommodityId": that.data.exchangeCommodityId,
        "vipCardNo": that.data.vipCardNo,
        "pickOrderUserName": that.data.pickOrderUserName,
        "pickOrderContactNum": that.data.pickOrderContactNum
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "兑换成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnToPage("/pages/indexThree/indexThree");
              }, 2000);
            }
          })
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                wx.navigateBack({
                  delta: 2
                })
              }, 2000);
            }
          })
        }
      }
    })
  },
  calculationOrderTotal: function () {
    var that = this;
    var orderTotal_money = 0.00;
    //获取订单价格
    var orderMoney = that.data.orderMoney;
    //获取换购商品价格
    var exchangeMoney = that.data.changeGoodsPriceTotal;
    orderTotal_money = parseFloat(orderMoney) + parseFloat(exchangeMoney);
    //获取优惠券价格
    var cardType = that.data.cardType;
    if (cardType >= 4 && cardType <= 6) {
      orderTotal_money = (orderTotal_money * (that.data.cardMoney / 10)).toFixed(2);
    } else {
      orderTotal_money = (orderTotal_money - parseFloat(that.data.cardMoney)).toFixed(2);
    }
    that.setData({
      orderTotalMoney: orderTotal_money < 0 ? 0 : orderTotal_money
    });
  }
})