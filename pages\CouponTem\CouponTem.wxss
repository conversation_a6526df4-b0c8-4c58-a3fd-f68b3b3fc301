/* 通用劵start */
.couponWrapTe {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 300rpx;
  overflow-y: auto;
  left: 0;
  z-index: 9999;

}

.closeTemp1 {
  position: absolute;
  right: 56rpx;
  top: 0
}

.oneName {
  color: #FF4D00;
  font-size: 36rpx;
  text-align: center;
  margin-top: 10rpx
}

.onetitle {
  color: #000000;
  font-size: 30rpx;
  text-align: center;
  margin-top: 20rpx
}

.oneTime {
  font-size: 26rpx;
  text-align: center;
  margin-top: 40rpx;
  color: rgb(77, 77, 77);
}

.onePrice {
  position: absolute;
  top: 224rpx;
  right: 150rpx;
  font-size: 34rpx;
  color: #fff;
}

.oneBtn {
  width: 400rpx;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 46rpx;
  margin: 0 auto;
  background-color: #FF5B2A;
  color: #fff;
  text-align: center;
  font-size: 32rpx;
}



/* 通用劵end */

/* 新人券 start */
.couponWrapTe2 {
  padding-bottom: 20rpx;
  margin: 0 auto;
  position: fixed;
  top: 220rpx;
  overflow-y: auto;
  left: 75rpx;
  z-index: 9999;
  width: 600rpx;
  min-height: 300rpx;
  background-color: rgb(255, 146, 21);
  border-radius: 16rpx;
}

.closeTemp2 {
  position: absolute;
  right: 30rpx;
  top: 26rpx
}

.twoPrice {
  font-size: 26rpx;
  color: #fff;
  position: absolute;
  top: 48rpx;
  left: 70rpx;
}

.twoTitle {
  font-size: 30rpx;
  position: absolute;
  top: 14rpx;
  left: 200rpx
}

.twoTitle text {
  line-height: 50rpx;
}

/* 新人券 end */

/* 开屏弹券 start */
.couponWrapTe3 {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 160rpx;
  left: 0;
  z-index: 9999;

}

.closeTemp3 {
  position: absolute;
  right: 30rpx;
  top: 54rpx
}

.threeCircal {
  background: #fff;
  position: relative;
  border-radius: 10rpx;
  margin:0 auto 10rpx;
  width: 420rpx;
  height: 140rpx;
  background: #fff;
}

.threeCouponProduct {
  padding-top: 20rpx;
  padding-left: 20rpx;
  vertical-align: top;
  overflow: hidden;
  width: 67%;
  float: left;
}

.threeCoupon {
  height: 140rpx;
  float: left;
  width: 24%;
  line-height: 140rpx;
  margin: 0 auto;
  padding-left: 12rpx;
  color: #fff;
  background:#FF9E02;
  border-radius: 10rpx 0 0 10rpx;
  text-align: center;
}

.threeMoney {
  font-size: 30rpx;
  height: 120rpx;
  overflow: hidden;
}

.threeCircal1{
  border-radius: 10rpx;
  margin:0 auto 10rpx;
  width: 570rpx;
  height: 480rpx;
  overflow: hidden;
  overflow-y: auto;
}

.threeCoupon1{
  position: relative;
  width: 570rpx;
  height: 144rpx;
  margin-bottom: 30rpx;
}

.amount-group{
  position: absolute;
  left: 30rpx;
  bottom: 20rpx;
}

.amount{
  font-size: 90rpx;
  color: #fee8c7;
}

.unit{
  font-size: 28rpx;
  color: #fee8c7;
}

.txt-1{
  width: 30rpx;
  height: 100rpx;
  position: absolute;
  top: 20rpx;
  left: 180rpx;
  font-size: 26rpx;
  color: #fee8c7;
}

.text-group{
  position: absolute;
  top: 40rpx;
  left: 230rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.txt-2{
  font-size: 22rpx;
  color: #333;
  background: #f1ce9a;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
}

.txt-3{
  font-size: 24rpx;
  color: #fee8c7;
}


.couponGift1 {
  font-size: 30rpx;
  display: block;
  line-height: 24rpx;

}

.couponGift2 {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-top: 10rpx
}

.couponRight {
  float: left;
  margin-top: 60rpx;
  width: 20%;
  padding: 10rpx 6rpx;
  border-radius: 30rpx;
  color: #fff;
  background: #FF7E00;
  text-align: center;
  font-size: 26rpx;
}

.circal_top3 {
  display: inline-block;
  width: 12rpx;
  height: 24rpx;
  position: absolute;
  top: 60rpx;
  left:0;
  background: #FFDB84;
  border-radius: 0 12rpx 12rpx 0;
  border-top: none;
}

.circal_bottom3 {
  display: inline-block;
  width: 24rpx;
  height: 12rpx;
  position: absolute;
  bottom: -1px;
  right: calc(26%);
  background: #FD9046;
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: none;
}

.threeBox {
  width: 220rpx;
  height: 100rpx;
  background-color: #fff;
  padding: 20rpx 10rpx;
  border-radius: 10rpx;

}

.threeBox:nth-of-type(1) {
  margin-right: 10rpx;
}

.threeBox .log {
  font-size: 24rpx;
  color: #666;
}

.circal_top4 {
  display: inline-block;
  width: 20rpx;
  height: 10rpx;
  position: absolute;
  top: -1px;
  right: calc(55%);
  background: #fc6366;
  border-radius: 0 0 12rpx 12rpx;
  border-top: none;
}

.circal_bottom4 {
  display: inline-block;
  width: 20rpx;
  height: 10rpx;
  position: absolute;
  bottom: -1px;
  right: calc(55%);
  background: #fc6366;
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: none;
}

/* 开屏弹券 end */

/* 生日券 start */
.couponWrapTe4 {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 224rpx;
  overflow-y: auto;
  left: 0;
  z-index: 9999;

}

.closeTemp4 {
  position: absolute;
  right: 54rpx;
  top: 54rpx
}

.fourTitle {
  color: #616161;
  font-size: 34rpx;
  width: 400rpx;
  text-align: center;
}

.fourDate {
  font-size: 26rpx;
  color: #666;
  width: 400rpx;
  margin-top: 20rpx;
  text-align: center;
}

/* 生日券 end */

/* 支付 start */
.couponWrapTe5 {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 224rpx;
  overflow-y: auto;
  left: 0;
  z-index: 9999;

}

.closeTemp5 {
  position: absolute;
  right: 54rpx;
  top: 34rpx
}

.fiveBox {
  padding: 10rpx 0;
  color: #fff;
}

.fiveBox>view {
  margin-bottom: 16rpx;
  width: 400rpx;
  text-align: center;


}

.fiveBtn {
  width: 340rpx;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  background-color: #FD8301;
  color: #fff;
  font-size: 34rpx;
  border-radius: 50rpx;
  margin-top: 60rpx;
}

/* 支付 end */

/* 红包 start */
.couponWrapTe6 {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 224rpx;
  overflow-y: auto;
  left: 0;
  z-index: 9999;

}

.closeTemp6 {
  position: absolute;
  right: 54rpx;
  top: 16rpx
}

/* 红包 end */

.couponWrapTe7 {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 300rpx;
  left: 0;
  z-index: 9999;
}

.closeTemp7 {
  position: absolute;
  right: 26rpx;
  top: -16rpx
}