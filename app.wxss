.allBlack {
  color: #3c3c3c;
}

.darkBlack {
  color: #717071;
}

.middleBlack {
  color: #c6c7c8;
}

.lightBlack {
  color: #ececed;
}

.darkRed {
  color: #FF7E00;
}

.red_bg {
  background: #FF7E00;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}

button:after {
  border: none;
}

.hide {
  display: none !important;
}

html,
body {
  width: 100%;
  font-size: 14px;
  color: #444;
  background-repeat: repeat;
  overflow-x: hidden;
}

.page-container-parent {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

.page-container {
  width: 100%;
  z-index: 1;
}

#dialog-page-container {
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 50;
}

.video-container {
  position: relative;
  z-index: 70;
}

.video-container .full-screen-iframe {
  position: fixed;
  left: 0;
  top: 0;
  display: none;
  -webkit-box-pack: center;
  -webkit-box-orient: vertical;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding-top: 45px;
  z-index: 1;
  background: #000;
}

.video-container .full-screen-iframe iframe {
  width: 100% !important;
  height: 100% !important;
}

.video-container .video-close {
  position: absolute;
  top: 0px;
  right: 10px;
  width: 40px;
  height: 36px;
  line-height: 36px;
  color: rgb(255, 255, 255);
  font-size: 16px;
  text-align: center;
  z-index: 3;
}

.page {
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.page.dialog-page {
  position: absolute !important;
}

.shadow {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: black;
  opacity: 0.8;
  z-index: 99;
}

.shadow-content {
  position: absolute;
  z-index: 100;
}

.page.dialog-page .shadow {
  height: 150%;
  z-index: 1;
}

.img-thumb-wrap {
  display: block;
  text-align: center;
  font-size: 0;
  height: 100%;
}

.img-thumb-wrap:before,
.img-thumb-wrap:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
}

.img-thumb-wrap image {
  height: 100%;
  width: 100%;
  vertical-align: middle;
}

.img-upload-input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 5;
}

.ui-slider {
  height: 100%;
  padding-top: 0;
}

page {
  font-size: 32.8125rpx;
}

a {
  color: inherit;
}

.element>a {
  display: block;
  width: 100%;
  height: 100%;
}

.top-nav,
.bottom-nav {
  position: fixed !important;
  z-index: 20;
}

.top-nav:before,
.top-nav:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
}

.app .ele-container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 100%;
  clear: both;
}

.app .ele-container>.element {
  position: relative;
}

.app .list-vessel {
  margin-bottom: 7.03rpx;
}

.app .layout-vessel {
  width: 100%;
  padding-top: 23.4375rpx;
  min-height: 117.1875rpx;
  height: auto !important;
}

.app .layout-vessel .cell {
  position: relative;
  display: inline-block;
  vertical-align: top;
  min-height: 35.15625rpx;
}

.app .layout-vessel>a {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.app .text image {
  width: 100%;
  height: 750rpx;
}

.app .text br {
  display: none;
}

.clearfix:after {
  content: "";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  font-size: 0;
}

.page-dialog-wrap {
  position: fixed;
  left: 0;
  right: 0;
  /* top: 0; */
  bottom: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.7);
}

.page-bottom-dialog {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.page-bottom-content {
  max-width: 640px;
  margin: 0 auto;
  background-color: #fff;
}

.page-dialog-close {
  position: absolute;
  right: 3px;
  top: 0;
  width: 40px;
  height: 40px;
  z-index: 10;
  font-size: 30px;
  color: #ccc;
  line-height: 40px;
  text-align: center;
}

.dialog-block-item {
  display: block;
  padding: 10px 0;
  position: relative;
  border-bottom: 1px solid #eee;
}

.dialog-box-item {
  display: box;
  display: -webkit-box;
  display: -o-box;
  display: -moz-box;
}

.btn {
  display: inline-block;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  padding: 7px;
  margin: 0;
  font-size: 14px;
  cursor: pointer;
  line-height: 22px;
  -webkit-appearance: none;
  text-align: center;
}

.btn.btn-yellow {
  color: #fff;
  border-color: #ff5468;
  background-color: #ff5468;
}

.btn.btn-orange {
  color: #fff;
  border-color: #ff7100;
  background-color: #ff7100;
}

.btn.btn-red {
  color: #fff;
  border-color: #FF7E00;
  background-color: #FF7E00;
}

.btn.btn-block {
  display: block;
}

.flex-sub-box-2,
.flex-sub-box-3,
.flex-sub-box-4,
.flex-sub-box-5 {
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
}

.flex-sub-box-2 {
  width: 50%;
}

.flex-sub-box-3 {
  width: 33.3%;
}

.flex-sub-box-4 {
  width: 25%;
}

.flex-sub-box-5 {
  width: 20%;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.pills-list {
  list-style: none;
}

.pills-list:after {
  content: '';
  display: table;
  clear: both;
}

.pills-list label {
  float: left;
  position: relative;
  display: block;
  padding: 6px 9px;
  margin: 10px 10px 10px 0;
  border: 1px solid #dedede;
  border-radius: 3px;
}

.quantity {
  display: inline-block;
  position: relative;
}

.quantity .minus,
.quantity .plus {
  position: relative;
  display: inline-block;
  width: 45rpx;
  height: 45rpx;
  line-height: 50rpx;
  border: 1px solid #ff7100;
  vertical-align: middle;
  font-size: 16px;
  font-weight: normal;
  border-radius: 50%;
  color: #666;
  outline: 0 !important;
  background-color: #ff7100;
  text-indent: -9999px;
  overflow: hidden;
}

.quantity .minus {
  background-color: #fff;
  border-color: #ff7100;
}

.quantity .minus:before,
.quantity .plus:before {
  content: '';
  position: absolute;
  width: 30rpx;
  height: 5rpx;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.quantity .minus:before {
  background-color: #ff7100;
}

.quantity .minus.disabled {
  color: #dedede;
  border-color: #dedede;
}

.quantity .minus.disabled:before {
  background-color: #dedede;
}

.quantity .plus.disabled:after,
.quantity .plus.disabled:before {
  background-color: #ddd;
}

.quantity .plus:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 5rpx;
  height: 30rpx;
  margin: auto;
  background-color: #fff;
}

.quantity .plus:after,
.quantity .plus:before {
  background-color: #fff;
}

.quantity input {
  display: inline-block;
  width: 25px;
  height: 25px;
  padding: 1px;
  margin: 0;
  box-sizing: content-box;
  text-align: center;
  vertical-align: middle;
}

.quantity .response-area {
  width: 80rpx;
  height: 89rpx;
  top: -7px;
  position: absolute;
}

.quantity .response-area-minus {
  left: -5px;
}

.quantity .response-area-plus {
  right: -5px;
}

.page-top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 42px;
  line-height: 42px;
  padding: 0 10px;
  margin-bottom: 10px;
  text-align: center;
  z-index: 9;
  box-shadow: 0 1px 2px #eee;
  background-color: #fff;
}

.page-top-nav .nav-back {
  position: absolute;
  left: 0;
  width: 32px;
  font-size: 20px;
}

.list-goods-cover {
  position: absolute;
  left: 10px;
  width: 50px;
  height: 50px;
}

.list-goods-content {
  padding: 0 73px;
  min-height: 50px;
  font-size: 12px;
  overflow: hidden;
}

.list-goods-right {
  position: absolute;
  top: 10px;
  right: 0;
  line-height: 20px;
  text-align: right;
}

.txt-r {
  text-align: right;
}

.txt-c {
  text-align: center;
}

.txt-l {
  text-align: left;
}

.top-nav-back {
  position: absolute;
  left: 0;
  height: 100%;
  width: 40px;
  font-size: 20px;
  line-height: 40px;
}

.top-nav-right {
  position: absolute;
  top: 0;
  right: 10px;
  width: 55px;
}

.fade.in {
  opacity: 1;
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 200;
  display: none;
  overflow: hidden;
  outline: 0;
}

.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 150px 10% 0;
}

.modal.fade .modal-dialog {
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  -o-transform: translate(0, -25%);
  transform: translate(0, -25%);
}

.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}

.modal-body {
  position: relative;
  padding: 20px 15px;
  line-height: 22px;
}

.modal-content {
  position: relative;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  outline: 0;
  color: #4d4e53;
  text-align: left;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
}

.modal-footer {
  padding: 10px 15px 15px;
  text-align: right;
}

.modal-footer>span {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
  color: #333;
  background-color: #fff;
  display: inline-block;
  margin: 0 6px;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
}

.modal-footer .confirm-btn {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 199;
  background-color: #000;
}

.modal-backdrop.in {
  opacity: 0.5;
}

.check-box {
  display: inline-block;
  width: 20px;
  height: 20px;
  padding: 4px 3px;
  margin-right: 6px;
  box-sizing: border-box;
  border-radius: 50%;
  vertical-align: middle;
  line-height: 13px;
  background-color: #eaeaea;
}

.check-box.checked {
  background-color: #ff5468;
}

.check-box image {
  width: 100%;
  height: 100%;
}

.right-arrow-bar {
  position: relative;
  padding-right: 40px;
  color: #acacac;
}

.right-arrow-bar .icon-rightarrow {
  position: absolute;
  right: 10px;
  display: block;
  font-size: 18px;
}

.double-list {
  width: 50% !important;
  float: left !important;
}

.double-goods-list {
  padding-left: 0 !important;
  width: 50%;
  float: left;
}

.double-goods-list .title-container {
  width: 100% !important;
}

.double-goods-list .list-img {
  display: block !important;
  margin: 0 auto;
}

.double-goods-list .title {
  margin: 5px 0 !important;
}

.goods-list .double-goods-list .title-container view:last-child {
  position: static !important;
  width: 100% !important;
}

.width-marketing-bar .page-container {
  padding-bottom: 30px;
}

.width-marketing-bar .bottom-nav,
.width-marketing-bar .goods-detail-wrap .goods-bottom-opt,
.width-marketing-bar #shoppingCart .shoppingCart-bottom-nav {
  bottom: 30px;
}

.homepage-marketing-bar {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  line-height: 30px;
  background-color: #3f3f3f;
  color: #a9a9a9;
  font-size: 12px;
  text-align: center;
}

.homepage-marketing-bar image {
  width: 13px;
  margin-right: 6px;
  margin-top: -3px;
  vertical-align: middle;
}

.homepage-marketing-bar .close-bar {
  position: absolute;
  right: 0;
  width: 30px;
  font-size: 20px;
  color: #d2d2d2;
}

.width-marketing-bar .homepage-marketing-bar {
  display: block;
}

.circle {
  border-radius: 50% !important;
}

.clearfix:after {
  content: ".";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  font-size: 0;
}

.center-ele {
  margin-left: auto !important;
  margin-right: auto !important;
}

.background-ele {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.centered {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.app {
  font-size: 14px;
}

.app .element {
  display: block;
  width: auto;
  max-width: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: 1;
  border-color: #222;
  border-width: 0;
}

.app .text {
  max-width: 100%;
  min-height: 10px;
  line-height: 1.4em;
  white-space: pre-wrap;
  word-break: break-all;
  height: auto !important;
}

.app .list-vessel-wrap .text,
.app .dynamic-vessel .text,
.app .form-vessel .text {
  white-space: normal;
}

.app .button {
  width: 100px;
  height: 30px;
  line-height: 30px;
  margin-left: auto;
  margin-right: auto;
  color: rgb(255, 255, 255);
  background-color: rgb(29, 198, 241);
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  padding: 0;
  border-radius: 0;
}

.app .picture {
  width: 234.375rpx;
  height: 187.5rpx;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
}

.app .picture image {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.app .carousel {
  width: auto !important;
  height: 351.5625rpx;
}

.app .carousel .slick-carousel {
  width: 100%;
  height: 100%;
}

.app .carousel .carousel-img {
  width: 100%;
  height: 100%;
}

.app .slick-list,
.app .slick-track,
.app .slick-slide {
  height: 100%;
}

.app .slick-slide>a {
  display: block;
  width: 100%;
  height: 100%;
}

.app .slick-dots {
  bottom: 0;
  text-align: center;
}

.app .slick-dots li button:before {
  color: #fff;
  opacity: 0.45;
}

.app .slick-dots li.slick-active button:before {
  color: #fff;
  opacity: 1;
}

.app .slick-dots li {
  margin: 0;
}

.slick-dots li button:before {
  font-size: 20px;
}

.app .slick-slider {
  margin-bottom: 0;
}

.app .video {
  height: 351.5625rpx;
  overflow: hidden;
}

.app .video video {
  height: 100%;
  width: 100%;
}

.app .album {
  width: 100% !important;
  height: auto !important;
  color: #fff;
  font-size: 12px;
  text-align: center;
}

.app .album-container {
  color: inherit;
  font-size: inherit;
  text-align: inherit;
  text-decoration: inherit;
}

.app .album.sec-mode {
  color: #444;
}

.app .album-container .album-pic {
  position: relative;
  float: left;
  width: 234.375rpx;
  text-decoration: inherit;
}

.app .album-container .album-pic>a {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app .album-container .title {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  line-height: 1.6em;
  background-color: rgba(0, 0, 0, 0.5);
  text-decoration: inherit;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app .album-container a {
  text-decoration: inherit;
  color: inherit;
}

.app .album.sec-mode .title {
  position: static;
  background-color: transparent;
}

.app .album-container image {
  float: left;
  width: 100%;
  height: 240rpx;
}

.app .list {
  width: 100%;
  font-size: 16px;
  color: #222;
}

.app .list-container {
  width: 100%;
  max-width: 100%;
  font-size: inherit;
  color: inherit;
  text-decoration: inherit;
  overflow: hidden;
}

.app .list-container .list-item {
  position: relative;
  height: 140.625rpx;
  padding-left: 23.4375rpx;
  margin-bottom: 1px;
  background-color: #e8e8e8;
  text-decoration: inherit;
}

.app .list-container .list-item:after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  background-color: #f00;
  vertical-align: middle;
}

.app .list .sec-mode .list-item {
  height: auto;
  padding-left: 0;
}

.app .list-container a {
  display: block;
  height: 100%;
  width: 100%;
}

.app .list-container a:after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  background-color: #f00;
  vertical-align: middle;
}

.app .list-container .list-img {
  display: inline-block;
  width: 60px;
  height: 60px;
  vertical-align: middle;
}

.app .list .sec-mode .list-img {
  display: block;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.app .list-container .title-container {
  display: inline-block;
  width: calc(100% - 70px);
  text-decoration: inherit;
  vertical-align: middle;
}

.app .list .sec-mode .title-container {
  display: block;
  width: 100%;
  padding-top: 3px;
}

.app .list-container .title-container view {
  width: 100%;
  line-height: 1.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-indent: 11.71875rpx;
}

.app .list-container .title-container .title {
  text-decoration: inherit;
}

.app .list-container .title-container .sec-title {
  display: inline-block;
  font-size: 28.125rpx;
  color: #666;
  font-weight: normal;
  font-style: normal;
  text-decoration: none;
  text-align: left;
}

.top-nav {
  top: 0;
  left: 0;
  width: 100%;
  height: 98.4375rpx;
  text-align: center;
  background-color: rgb(29, 198, 241);
  color: #fff;
}

.top-nav .img-thumb-wrap:before,
.top-nav .img-thumb-wrap:after {
  content: none;
}

.top-nav .left-btn,
.top-nav .right-btn,
.top-nav .nav-btn-text {
  box-sizing: border-box;
}

.top-nav .left-btn,
.top-nav .right-btn {
  display: none;
  position: absolute;
  top: 0;
  height: 100%;
  width: 131.25rpx;
  color: inherit;
}

.top-nav .left-btn {
  left: 0;
  text-align: left;
}

.top-nav .right-btn {
  right: 0;
  text-align: right;
}

.top-nav .nav-btn-img {
  display: none;
}

.top-nav .img-thumb-wrap .nav-btn-img {
  display: inline-block;
}

.top-nav .nav-btn-text {
  display: block;
  height: 100%;
  line-height: 1em;
  white-space: pre-wrap;
}

.top-nav .img-thumb-wrap {
  font-size: 1em;
}

.top-nav .img-thumb-wrap .nav-btn-text {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  overflow: hidden;
  font-size: 1em;
}

.top-nav .inner-content:before,
.top-nav .inner-content:after,
.top-nav .nav-btn-text:before,
.top-nav .nav-btn-text:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
}

.top-nav .page-title {
  display: inline-block;
  vertical-align: middle;
  text-decoration: inherit;
}

.bottom-nav {
  bottom: 0;
  left: 0;
  width: 100%;
  line-height: 1.4;
  text-align: center;
  background-color: rgb(29, 198, 241);
  color: #fff;
}

.bottom-nav .item {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  text-decoration: inherit;
  overflow: hidden;
}

.bottom-nav .item .bottom-text {
  text-align: center;
  display: block;
  width: 100%;
  line-height: 1.5em;
  text-decoration: inherit;
}

.bottom-nav .img-after {
  display: none;
}

.bottom-nav .img-thumb-wrap {
  height: 38px;
  width: 38px;
  margin: 0 auto;
}

.bottom-nav.bottom-nav-without-img .img-thumb-wrap {
  display: none;
}

.bottom-nav.bottom-nav-without-img .bottom-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  height: 100%;
}

.app .list-vessel,
.app .dynamic-vessel,
.app .form-vessel {
  height: auto;
}

.app .dynamic-vessel,
.app .form-vessel {
  min-height: 45px;
  padding-top: 10px;
}

.app .static-vessel {
  height: auto;
  min-height: 45px;
  padding-top: 10px;
}

.app .list-vessel-wrap {
  overflow-x: hidden;
  overflow-y: auto;
}

.app .list-vessel {
  position: relative;
  width: 100%;
}

.app .grade-ele {
  width: 168px;
  height: 23px;
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
}

.app .grade-wrap {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  background-color: inherit;
}

.app .grade-wrap .ico-moon {
  display: inline-block;
  width: 20%;
  height: 100%;
  font-size: 26px;
  text-align: center;
}

.app .grade-wrap .icon-star,
.app .grade-wrap .icon-star-empty {
  color: #fe8401;
}

.app .grade-wrap .icon-clubs,
.app .grade-wrap .icon-clubs-empty {
  color: #14b10c;
}

.app .grade-wrap .icon-heart,
.app .grade-wrap .icon-heart-empty {
  color: #fd4e4e;
}

.app .select-ele {
  width: 100%;
  line-height: 35px;
  margin-left: auto;
  margin-right: auto;
}

.app .select-container {
  text-decoration: inherit;
}

.app .select-container .title-container {
  display: inline-block;
  width: 100%;
  line-height: 30px;
  background-color: #3598db;
  color: #fff;
  text-indent: 10px;
  font-weight: normal;
  font-style: normal;
}

.app .select-container .select-item {
  position: relative;
  min-height: 24px;
  padding: 10px 5px;
  border-bottom: 1px solid #ddd;
  line-height: initial;
}

.app .select-container .select-content {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.app .select-container .select-text {
  display: inline-block;
  width: 85%;
  height: 100%;
  vertical-align: middle;
}

.app .select-container .select-input {
  position: absolute;
  right: 0;
}

.app .input-ele {
  width: 60%;
  height: 30px;
  margin-left: auto;
  margin-right: auto;
}

.app .input-ele input {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  border-radius: inherit;
  border: 1px solid #d3d3d3;
  text-indent: 6px;
  outline: none;
  background-color: #fff;
}

.app .textarea-ele {
  width: 80%;
  height: 100px;
  line-height: 30px;
  margin-left: auto;
  margin-right: auto;
}

.app .textarea-ele textarea {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  border-radius: inherit;
  border: 1px solid #d3d3d3;
  outline: none;
  resize: none;
  background-color: #fff;
}

.app .upload-img {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  color: #979797;
  font-size: 70.3125rpx;
  text-align: center;
}

.app .upload-img.js-uploaded {
  background-color: transparent;
}

.app .upload-img.js-uploaded:before,
.app .upload-img.js-uploaded:after {
  content: '';
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
}

.app .upload-img .plus-div {
  width: 60px;
  height: 60px;
  border: 1px solid #e9e9e9;
  text-align: center;
  line-height: 60px;
  display: inline-block;
  vertical-align: middle;
  margin-left: 15px;
}

.app .upload-img.js-uploaded .plus-div {
  display: none;
}

.app .upload-img image {
  display: inline-block;
  position: static;
  max-width: 100%;
  max-height: 100%;
}

.app .js-uploaded image {
  display: inline-block;
  position: static;
  width: auto;
  height: auto;
}

.app .hasuploadImg {
  width: 60px;
  height: 60px;
  display: inline-block;
  vertical-align: middle;
}

.deleteImg {
  position: absolute;
  top: 0;
  right: -8px;
  background-color: red;
  color: #fff;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  text-align: center;
  line-height: 18px;
  font-size: 14px;
  font-weight: 700;
}

.app .xcx-time-ele {
  width: 84%;
  margin: 0 auto;
}

.app .xcx-time-ele picker {
  display: inline-block;
  height: 100%;
  width: 50%;
  box-sizing: border-box;
  border: 1px solid #d3d3d3;
}

.app .xcx-time-ele .date-btn,
.app .xcx-time-ele .time-btn {
  display: inline-block;
  width: 100%;
  height: 100%;
  vertical-align: middle;
  text-align: center;
}

.select-ele.must .title {
  position: relative;
  display: inline-block;
  text-indent: -3px;
}

.input-ele.must:after,
.textarea-ele.must:after,
.upload-img.must:after,
.xcx-time-ele.must:after,
.grade-ele.must:after,
.select-ele.must .title:after {
  content: '*';
  position: absolute;
  right: -10px;
  top: 0;
  font-size: 16px;
  color: red;
}

.upload-img.must:after,
.time-ele.must:after {
  top: 0;
  line-height: 20px;
  right: -13px;
}

.app .bbs .lately-comment-p {
  line-height: 82.03125rpx;
  font-size: 42.1875rpx;
  color: #2b2b2b;
  border-bottom: 1px dashed #e3e3e3;
}

.app .bbs .lately-comment-p label {
  display: inline-block;
  width: 3px;
  height: 42.1875rpx;
  background-color: #05a172;
  vertical-align: text-top;
  margin-right: 11.7rpx;
}

.app .bbs .empty-tip {
  margin-top: 35.15625rpx;
  font-size: 37.5rpx;
  text-align: center;
  color: #666;
}

.app .bbs .empty-tip image {
  display: block;
  margin: 20px auto;
  width: 170px;
  height: 88px;
}

.app .bbs .comment-box {
  padding-bottom: 10px;
}

.app .bbs .comment-container {
  padding-bottom: 35.15625rpx;
}

.app .bbs .comment {
  padding-left: 23.4375rpx;
  padding-right: 23.4375rpx;
  padding-top: 35.1rpx;
  padding-bottom: 56.25rpx;
  border-bottom: 1px dashed #e3e3e3;
}

.app .bbs .comment .cover-img {
  width: 70.2rpx;
  height: 70.2rpx;
  margin-right: 11.71875rpx;
  border-radius: 9.36rpx;
  vertical-align: middle;
}

.app .bbs .comment .nickname {
  color: #656565;
  font-size: 13px;
  display: block;
}

.app .bbs .comment .add-time {
  float: right;
  margin-top: 9.375rpx;
  font-size: 28.125rpx;
  color: #bfbfbf;
}

.app .bbs .comment .comment-content {
  padding-top: 11.71875rpx;
  word-wrap: break-word;
}

.app .bbs .comment .comment-reference-text {
  margin-bottom: 5px;
  color: #577a9f;
}

.app .bbs .comment .comment-img {
  width: 262.5rpx;
  height: 262.5rpx;
}

.app .bbs .comment .bbs-content-btn {
  float: right;
  position: relative;
  margin-right: 23.4rpx;
  margin-top: 11.7rpx;
  font-size: 28.125rpx;
  line-height: 32.8125rpx;
  color: #999;
}

.app .bbs .comment .bbs-content-btn:before {
  margin-right: 2px;
  vertical-align: text-top;
}

.app .bbs .comment .bbs-content-btn.icon-heart {
  margin-right: 37.5rpx;
}

.app .bbs .comment .bbs-content-btn.icon-heart:before {
  color: #888;
}

.app .bbs .comment .bbs-content-btn.icon-heart.active:before {
  color: #f93030;
}

.app .bbs .comment .bbs-content-btn.icon-comment:before {
  color: #3f77bc;
}

.app .bbs .comment .replied-box {
  padding: 11.71875rpx;
  background-color: #f6f6f6;
  border: 1px solid #d6d6d8;
}

.app .bbs .comment .replied-box .replied-box {
  padding-bottom: 0;
  margin-bottom: 11.71875rpx;
}

.app .bbs .comment .nickname-wrap {
  display: inline-block;
  vertical-align: middle;
}

.app .bbs {
  width: 100% !important;
  padding-left: 3%;
  padding-right: 3%;
  background-color: #f3f4f6;
}

.app .bbs .comment-amount-p {
  padding-top: 15px;
  padding-bottom: 10px;
  font-size: 32.8125rpx;
}

.app .bbs .ca-span {
  display: inline-block;
  position: relative;
  width: 30px;
  margin: 0 10px;
  font-size: 26px;
  color: #f65110;
  vertical-align: middle;
}

.app .bbs .ca-span .ca-span-num {
  display: block;
  width: 30px;
  text-align: center;
  color: #fff;
  font-size: 16px;
  margin-top: 2px;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.app .bbs .reply-comment-box {
  border-top: none;
}

.app .bbs .comment-box .input-wrap {
  width: 94%;
  padding: 6px 3%;
  height: 22px;
  background-color: #fff;
  border: 1px solid #d6d6d8;
}

.app .bbs .focus-box .input-wrap {
  height: 80px;
}

.app .bbs .comment-box textarea {
  width: 100%;
  height: 100%;
  resize: none;
  border: none;
  outline: none;
}

.app .bbs .comment-box .comment-operate-wrap {
  height: 60.9375rpx;
  margin-top: 5px;
}

.app .bbs .focus-box .comment-operate-wrap {
  display: block;
}

.app .bbs .comment-operate-wrap .comment-btn {
  float: right;
  width: 80px;
  line-height: 60.9375rpx;
  margin-right: -1px;
  background-color: #3db7fa;
  text-align: center;
  color: #fff;
}

.app .bbs .comment-operate-wrap .upload-wrap {
  display: inline-block;
  position: relative;
  width: 79.6875rpx;
  height: 100%;
  margin-left: -1px;
}

.app .bbs .comment-operate-wrap .upload-wrap:before {
  position: absolute;
  font-size: 46.875rpx;
  padding: 7.03125rpx 18.75rpx;
  color: #666;
}

.app .bbs .comment-operate-wrap .upload-text {
  display: inline-block;
  vertical-align: top;
  line-height: 28px;
}

.app .bbs .upload-wrap image {
  position: absolute;
  left: 1px;
  background-color: #f3f4f6;
  width: 100%;
  height: 100%;
}

.app .bbs .upload-wrap .delete-img {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  line-height: 60.9375rpx;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 11;
}

.app .bbs-mode1 {
  background-color: #fff;
}

.app .bbs-mode1 .bbs-comment {
  vertical-align: middle;
  margin-right: 5px;
}

.app .bbs-mode1 .comment-box {
  border: none;
  overflow: hidden;
  padding-bottom: 10px;
  padding-top: 15px;
}

.app .bbs .reply-comment-box {
  padding-top: 0px !important;
}

.app .bbs-mode1 .comment-box .input-wrap {
  display: inline-block;
  width: 457.03125rpx;
  height: 46.875rpx;
  padding: 9.375rpx;
  vertical-align: top;
}

.app .bbs-mode1 .comment-box .comment-operate-wrap {
  display: inline-block;
  width: 222.65625rpx;
  height: 70.3125rpx;
  border-top: none;
  vertical-align: bottom;
  margin-top: 0px;
}

.app .bbs-mode1 .comment-box .comment-operate-wrap .comment-btn {
  width: 105.46875rpx;
  height: 63.18rpx;
  margin: 0;
  background-color: transparent;
  border: 1px solid #d6d6d8;
  border-radius: 3px;
  color: #666;
}

.app .bbs-mode1 .comment-box .comment-operate-wrap .upload-wrap {
  border: 1px solid #d6d6d8;
  height: 63.18rpx;
}

.app .count-ele {
  font-size: 32.8125rpx;
}

.app .count-ele .count-icon {
  display: inline-block;
  width: 70.3125rpx;
  height: 70.3125rpx;
  background-image: url(http://cdn.jisuapp.cn/zhichi_frontend/static/webapp/images/count_icons.png);
  background-repeat: no-repeat;
  background-size: auto 100%;
  background-position-y: -4.6875rpx;
  vertical-align: middle;
}

.app .count-ele .star-icon-active {
  background-position-x: 0;
}

.app .count-ele .star-icon {
  background-position-x: 9%;
}

.app .count-ele .flower-icon-active {
  background-position-x: 18%;
}

.app .count-ele .flower-icon {
  background-position-x: 27%;
}

.app .count-ele .like-icon-active {
  background-position-x: 36%;
}

.app .count-ele .like-icon {
  background-position-x: 45%;
}

.app .count-ele .hate-icon-active {
  background-position-x: 54%;
}

.app .count-ele .hate-icon {
  background-position-x: 63%;
}

.app .count-ele .love-icon-active {
  background-position-x: 72%;
}

.app .count-ele .love-icon {
  background-position-x: 81%;
}

.app .count-ele .view-icon {
  background-position-x: 90%;
}

.app .user-center {
  width: 100% !important;
  height: auto !important;
  color: #fff;
  font-size: 16px;
}

.app .user-center .show-view {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: 100%;
  background-repeat: no-repeat;
  /*background-position: center;*/
}

.app .user-center.with-horizontal-view .show-view {
  height: 234.375rpx;
}

.app .user-center .horizontal-view {
  border-bottom: 1px solid #eee;
}

.app .user-center.with-horizontal-view .horizontal-view {
  display: block;
}

.app .user-center .horizontal-router-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #fff;
  font-size: 14px;
}

.app .user-center .horizontal-router-container view {
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: center;
  color: #666;
  padding: 16.40625rpx 0;
  border-right: 2.34375rpx solid #eee;
}

.app .user-center .horizontal-router-container view:last-child {
  border: none;
}

.app .user-center .horizontal-router-container label {
  display: block;
  height: 70.3125rpx;
  width: 70.3125rpx;
  line-height: 70.3125rpx;
  font-size: 56.25rpx;
  margin: 0 auto 7.5rpx;
}

.app .user-center .horizontal-router-container .icon-rightarrow {
  display: none;
}

.app .user-center .show-view .cover-thumb {
  width: 117.1875rpx;
  height: 117.1875rpx;
  margin-right: 58.59375rpx;
  border-radius: 50%;
  vertical-align: middle;
}

.app .user-center .show-view .empty-span {
  display: inline-block;
  width: 58.59375rpx;
  height: 100%;
  vertical-align: middle;
}

.app .user-center .show-view .nickname {
  vertical-align: middle;
}

.app .user-center .show-view .icon-rightarrow {
  position: absolute;
  top: 50%;
  right: 23.4375rpx;
  margin-top: -35.15625rpx;
  width: 70.3125rpx;
  height: 70.3125rpx;
  line-height: 70.3125rpx;
  font-size: 46.875rpx;
}

.app .usercenter-mode1 .horizontal-router-container1 {
  overflow: hidden;
  /*background: #fff;*/
  font-size: 14px;
}

.app .usercenter-mode1 .horizontal-router-container1 view {
  line-height: 30px;
  height: 30px;
  padding: 7px 10px;
  color: #666;
  border-bottom: 1px solid #eee;
  background: #fff;
  border-top: 1px solid #eee;
}

.app .usercenter-mode1 .horizontal-router-container1 view:last-child {
  /*border: none;*/
  margin-bottom: -1px;
}

.app .usercenter-mode1 .horizontal-router-container1 label {
  float: left;
  font-size: 20px;
  line-height: 30px;
  height: 30px;
  margin-right: 10px;
}

.app .usercenter-mode1 .horizontal-router-container1 .icon-rightarrow {
  position: relative;
  width: 20px;
  height: 30px;
  line-height: 20px;
  font-size: 16px;
  float: right;
  color: #aaa;
  margin-top: 6px;
  margin-right: 0;
}

.app .border-cell {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  border-width: 0;
}

.app .classify {
  width: 100%;
  height: 35px;
  line-height: 35px;
  color: #888;
  font-size: 13px;
  background-color: #f2f2f2;
  overflow: hidden;
}

.app .classify scroll-view {
  height: 50px;
  width: 100%;
  white-space: nowrap;
  text-decoration: inherit;
  overflow-x: scroll;
  overflow-y: hidden;
}

.app .classify .classify-item {
  position: relative;
  display: inline-block;
  min-width: 71px;
  text-align: center;
  text-decoration: inherit;
  vertical-align: top;
}

.app .classify .classify-item text {
  padding-left: 0.7em;
  padding-right: 0.7em;
  white-space: nowrap;
  text-decoration: inherit;
}

.app .classify .classify-item .underline {
  display: none;
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0;
  width: 100%;
  border-top: 1px solid rgb(61, 183, 248);
}

.app .classify.classify-mode0 .selected text {
  padding-top: 0.4em;
  padding-bottom: 0.4em;
  border-radius: 0.4em;
  background-color: rgb(61, 183, 248);
  color: #fff;
}

.app .classify.classify-mode1 .selected {
  color: rgb(61, 183, 248);
}

.app .classify.classify-mode1 .selected .underline {
  display: block;
}

.app .sort {
  width: 100%;
  height: 35px;
  line-height: 35px;
  color: #888;
  font-size: 13px;
  background-color: #f2f2f2;
  overflow: hidden;
}

.app .sort ul {
  height: 50px;
  width: 100%;
  white-space: nowrap;
  text-decoration: inherit;
  overflow-x: scroll;
  overflow-y: hidden;
}

.app .sort .item {
  position: relative;
  display: inline-block;
  min-width: 71px;
  text-align: center;
  text-decoration: inherit;
  vertical-align: top;
}

.app .sort .item span {
  padding-left: 0.4em;
  white-space: nowrap;
  text-decoration: inherit;
}

.app .sort .arr-wrap {
  position: relative;
  display: inline-block;
  width: 12px;
  height: 48.75rpx;
  margin-right: 11.25rpx;
  vertical-align: middle;
}

.app .sort .sort-arr {
  position: absolute;
  right: 0;
  font-size: 12px;
  display: block;
  height: 12px;
  line-height: 12px;
  width: 12px;
  text-align: center;
  -webkit-transform: scale(0.8);
  -moz-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
}

.app .sort .arr-up {
  top: 0;
}

.app .sort .arr-down {
  bottom: 0;
}

.another-style {
  padding: inherit !important;
  line-height: 50px;
}

.another-style .location {
  float: left;
  width: 25%;
  text-align: center;
}

.another-style .search-input {
  width: 70% !important;
  display: inline-block;
  vertical-align: middle;
  padding-left: 2% !important;
}

.fakeSearch {
  width: 70%;
  display: inline-block;
  height: 30px;
  line-height: 30px;
}

.app .search {
  background: #efeff4;
  height: 50px;
  padding-top: 10px;
}

.app .search .search-input {
  position: relative;
  height: 30px;
  width: 90%;
  padding-left: 10px;
  margin: 0 auto;
  line-height: 30px;
  background: #fff;
  border-radius: 5px;
  color: #888;
  overflow: hidden;
}

.app .search .search-input icon {
  height: 14px;
  vertical-align: top;
  width: 14px;
  margin-right: 5px;
  margin-top: 7px;
}

.app .search .search-input input {
  width: 70%;
  display: inline-block;
  height: 30px;
  line-height: 30px;
}

.app .search .search-input .search-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 20%;
  text-align: center;
  background-color: #dfdfdf;
  color: #999;
}

.title-ele {
  line-height: 58.5rpx;
  cursor: pointer;
  background-color: #efefef;
}

.title-ele .title-content {
  height: 100%;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-ele .title-text {
  display: inline-block;
  position: relative;
  margin-left: 1em;
  white-space: pre-wrap;
}

.title-ele.title-mode0 .mark {
  background-color: red;
  display: none;
}

.title-ele.title-mode1 .mark {
  position: absolute;
  left: -1em;
  top: 0;
  height: 100%;
  width: 11.7rpx;
  background-color: red;
}

.title-ele.title-mode2 .mark {
  position: absolute;
  left: 0;
  bottom: 5rpx;
  width: 100%;
  height: 5rpx;
  background-color: red;
}

.title-ele.title-mode3 .mark {
  position: absolute;
  top: 50%;
  left: -.6em;
  width: 0.4em;
  height: 0.4em;
  margin-top: -0.2em;
  border-radius: 50%;
  background-color: red;
}

.app .hotspot {
  position: absolute !important;
  line-height: 1.8em;
  white-space: pre-wrap;
  text-align: center;
  z-index: 10 !important;
}

.app .breakline {
  border-width: 1px;
  border-color: #ddd;
  border-bottom-style: solid;
}

.app .free-vessel .breakline {
  width: 200px;
}

.app .page .ele-container .breakline {
  margin-top: 5px;
  margin-bottom: 5px;
}

.app .free-vessel {
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.app .goods-list,
.app .seckill {
  width: 100%;
  max-width: 100%;
  font-size: inherit;
  color: inherit;
  text-decoration: inherit;
  overflow-x: hidden;
  overflow-y: auto;
}

.app .goods-list .goods-list-item,
.app .seckill .goods-list-item {
  position: relative;
  height: 60px;
  padding-left: 10px;
  margin-bottom: 1px;
  text-decoration: inherit;
}

.app .goods-list .goods-list-item:after,
.app .seckill .goods-list-item:after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  background-color: #f00;
  vertical-align: middle;
}

.app .goods-list .list-img,
.app .seckill .list-img {
  display: inline-block;
  width: 60px;
  height: 60px;
  vertical-align: middle;
}

.app .goods-list .title-container,
.app .seckill .title-container {
  display: inline-block;
  width: calc(100% - 80px);
  text-decoration: inherit;
  vertical-align: top;
}

.app .goods-list .title-container view,
.app .seckill .title-container .seckill-list-bottom {
  width: calc(100% - 12px);
  padding-left: 10px;
  word-break: break-all;
}

.app .seckill .title-container .title,
.app .seckill .title-container .seckill-list-bottom {
  padding: 0 10px;
}

.app .goods-list .title-container view:last-child,
.app .seckill .title-container .seckill-list-bottom {
  position: absolute;
  bottom: 4px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.app .double-goods-list .title-container view:last-child,
.app .seckill.double-goods-list .title-container .seckill-list-bottom {
  position: static;
}

/*横划*/

.third-goods-list {
  white-space: nowrap;
}

.third-goods-list>view {
  display: inline-block;
}

.third-goods-list view:last-child {
  margin-right: 0;
}

.third-goods-list .list-img {
  display: block !important;
  margin: 0 auto;
}

.third-goods-list .title {
  margin: 5px 0 !important;
}

.goods-list .third-goods-list .title-container view:last-child {
  position: static !important;
  width: 100% !important;
}

.app .third-goods-list .title-container view:last-child,
.app .seckill.third-goods-list .title-container .seckill-list-bottom {
  position: static;
}

.app .goods-list .third-goods-list .title-container view {
  padding-left: 0 !important;
}

.app .third-goods-list .goods-list-item {
  position: relative;
  height: 60px;
  margin-bottom: 1px;
  padding-left: 0;
  text-decoration: inherit;
  margin-left: 10px !important;
}

.app .goods-list .title-container .title,
.app .seckill .title-container .title {
  text-decoration: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.app .goods-list .title-container .price,
.app .seckill .title-container .price {
  color: #f60;
  margin-right: 10px;
}

.app .goods-list .title-container .sales,
.app .seckill .title-container .sales {
  font-size: 28.125rpx;
  color: #a8a8a8;
}

.app .goods-list .title-container .goods-purchase,
.app .seckill .title-container .goods-purchase {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 20px;
  border: 1px solid;
  border-radius: 50%;
  text-indent: 0;
  text-align: center;
}

.app .goods-list .inner-content,
.app .seckill .inner-content {
  display: inline-block;
  vertical-align: middle;
  height: auto;
  position: relative;
  width: 100%;
  text-decoration: inherit;
  border-radius: inherit;
  z-index: 1;
}

.app .goods-list .title-container .addShoppingcart,
.app .seckill .title-container .addShoppingcart {
  display: inline-block;
  width: 70.2rpx;
  height: 58.5rpx;
  text-align: center;
  line-height: 58.5rpx;
  font-size: 37.44rpx;
  overflow: hidden;
  position: absolute;
  bottom: -4.68rpx;
  right: 11.7rpx;
}

/*秒杀*/

.app .seckill .oldPrice-wrap {
  margin-top: 11.7rpx;
}

.app .seckill .title-container .price {
  font-size: 32.76rpx;
  line-height: 46.8rpx;
  display: inline-block;
}

.app .seckill .title-container .oldprice {
  font-size: 23.4rpx;
  color: #a8a8a8;
  text-decoration: line-through;
}

.app .seckill .oldPrice-wrap .oldprice {
  display: inline-block;
  vertical-align: top;
}

.app .seckill .countdown {
  float: right;
  background-color: #f1f2f6;
  font-size: 23.4rpx;
  padding: 0 7.02rpx;
  color: #666;
  height: 46.8rpx;
  line-height: 46.8rpx;
}

.app .seckill .seckill-during .countdown {
  background-color: #f31e4a;
  color: #fff;
}

.app .seckill .countdown>label {
  vertical-align: top;
  display: inline-block;
}

.app .seckill .countdown>text {
  display: inline-block;
  min-width: 32.76rpx;
  height: 32.76rpx;
  background-color: #232227;
  color: #fff;
  text-align: center;
  line-height: 32.76rpx;
  margin: 7.02rpx 4.68rpx;
  padding: 0 7.02rpx;
  border-radius: 9.36rpx;
  vertical-align: top;
}

.app .seckill .seckill-during .countdown>text {
  background-color: #9b0020;
}

.app .seckill .seckill-end .countdown>text {
  background-color: #9a9a9a;
}

.seckill-progress-wrap {
  float: right;
  font-size: 23.4rpx;
  color: #999;
}

.seckill-progress {
  display: inline-block;
  margin-left: 10px;
  width: 140.4rpx;
  height: 6px;
  border-radius: 4px;
  border: 1px solid #f31e4a;
  overflow: hidden;
}

.seckill-end .seckill-progress {
  border-color: #9a9a9a;
}

.double-goods-list .seckill-progress-wrap,
.third-goods-list .seckill-progress-wrap,
.double-goods-list .countdown,
.third-goods-list .countdown {
  float: none !important;
  margin-top: 5px;
}

.double-goods-list .countdown,
.third-goods-list .countdown {
  text-align: center;
}

.app .seckill .double-goods-list .price,
.app .seckill .third-goods-list .price {
  margin-right: 5px;
}

.double-goods-list .seckill-progress {
  width: 182.52rpx;
  float: right;
  margin-top: 4px;
}

.third-goods-list .seckill-progress {
  float: right;
  width: 135.72rpx;
  margin-top: 4px;
}

/*秒杀 end*/

/*添加购物车弹窗样式*/

.page-addshoppingcart-dialog .dialog-block-item {
  padding: 10px;
}

.page-addshoppingcart-dialog .thumb {
  float: left;
  width: 200rpx;
  height: 130rpx;
  margin: 4.68rpx 23.4rpx 0 0;
  position: relative;
}

.page-addshoppingcart-dialog .thumb .thumb-wrap {
  width: 200rpx;
  height: 200rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  border: 1px solid #dedede;
  border-radius: 12rpx;
  background-color: #fff;
  overflow: hidden;
}

.page-addshoppingcart-dialog .thumb image {
  width: 100%;
  height: 100%;
}

.page-addshoppingcart-dialog .goods-base-info {
  margin-left: 55px;
  display: inline-block;
}

.page-addshoppingcart-dialog .pay-goods-title,
.page-addshoppingcart-dialog .pay-goods-price {
  font-size: 32.76rpx;
  padding-right: 50px;
  line-height: 46.8rpx;
}

.page-addshoppingcart-dialog .pay-checked-text {
  font-size: 32.76rpx;
  line-height: 46.8rpx;
}

#pagePayDialog .pay-goods-baseinfo {
  min-height: 135rpx;
}

#tostorePayDialog .pay-goods-price {
  padding-left: 30px;
}

#tostorePayDialog .dialog-block-item {
  margin-bottom: 10px;
  font-size: 12px;
  border: none;
}

#tostorePayDialog .quantity .plus {
  border: none;
}

#tostorePayDialog .pay-goods-title {
  text-align: center;
  padding-right: 30px;
  font-size: 16px;
}

#tostorePayDialog .page-bottom-dialog {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding-bottom: 45px;
  background: #fff;
}

#tostorePayDialog .quantity .minus:before {
  background-color: #dedede;
}

#tostorePayDialog .quantity .minus {
  border-color: #dedede;
}

#tostorePayDialog .pills-list label {
  color: #707070;
}

#tostorePayDialog .pills-list label {
  min-width: 42px;
  float: left;
  position: relative;
  display: block;
  padding: 3px 12px;
  border: 1px solid #dedede;
  border-radius: 15px;
  margin-left: 10px;
  margin-right: 10px;
}

#tostorePayDialog .pay-goods-price .pay-current-price {
  color: #ff7223;
  margin-top: 4px;
}

.toStore-bottom-nav {
  display: block;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  line-height: 110rpx;
  background-color: #fff;
  -webkit-user-select: none;
  user-select: none;
}

.bottom-nav-detail {
  display: inline-block;
  box-sizing: border-box;
  width: calc(100% - 200rpx);
  padding: 0 35rpx;
  background-color: #3f3f3f;
  color: #fff;
}

.bottom-nav-detail .icon-shoppingcart {
  float: left;
  width: 40px;
  font-size: 28px;
  vertical-align: middle;
}

.bottom-nav-detail .icon-shoppingcart .toStore-cart-tip {
  position: absolute;
  left: 66rpx;
  top: 13rpx;
  width: 38rpx;
  height: 38rpx;
  font-size: 28rpx;
  line-height: 33rpx;
  border-radius: 50%;
  background-color: #fa332f;
  text-align: center;
}

.bottom-nav-ready {
  display: inline-block;
  width: 200rpx;
  background-color: #ffcc01;
  color: #fff;
  text-align: center;
}

.bottom-nav-ready.disabled {
  background-color: #a8a8a8;
  color: #fff;
}

.page-addshoppingcart-dialog .pay-current-price {
  color: #f60;
}

.page-addshoppingcart-dialog .pay-current-price text {
  vertical-align: middle;
  font-size: 18px;
}

.page-addshoppingcart-dialog .pay-goods-models {
  margin: -10px 0;
  max-height: 300px;
  overflow: scroll;
}

.page-addshoppingcart-dialog .pay-goods-models .pills-list {
  display: inline-block;
  vertical-align: middle;
}

.page-addshoppingcart-dialog .pay-goods-models .model-title {
  display: block;
  vertical-align: middle;
  margin-bottom: 5px;
}

.page-addshoppingcart-dialog .pay-goods-models>view {
  margin: 0;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.page-addshoppingcart-dialog .pay-goods-models>view:last-child {
  border-bottom: none;
}

#tostorePayDialog .pay-goods-models view {
  margin: 0;
  padding: 2px 10px;
  border-bottom: none;
}

#tostorePayDialog .pills-list label.select {
  position: relative;
  color: #707070;
  background-color: #fdeab0;
}

.page-addshoppingcart-dialog .pills-list label {
  color: #353535;
  margin-bottom: 2px;
  margin-top: 2px;
  border: none;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.page-addshoppingcart-dialog .pills-list label.select {
  position: relative;
  color: #fff;
  background-color: #ff7100;
}

.page-addshoppingcart-dialog .pills-list label.select:after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
}

.page-addshoppingcart-dialog .pay-goods-stock {
  margin-right: 15px;
}

.page-addshoppingcart-dialog .pay-add-to-shoppingcart,
.page-addshoppingcart-dialog .pay-add-to-buynow {
  width: 50%;
  flex: 1;
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  box-flex: 1;
  line-height: 100rpx;
  text-align: center;
  color: #fff;
  background-color: #ff7100;
  margin-top: 10px;
}

.page-addshoppingcart-dialog .pay-add-to-shoppingcart {
  background-color: #f5a623;
}

.page-addshoppingcart-dialog .minus,
.page-addshoppingcart-dialog .plus {
  position: relative;
  display: inline-block;
  font-size: 16px;
  outline: 0 !important;
  text-indent: -9999px;
  overflow: hidden;
  vertical-align: middle;
  width: 25px;
  height: 25px;
  line-height: 25px;
  font-weight: normal;
  background-color: #f1f1f1;
  color: #878787;
  border-radius: 2px;
  border: none;
}

.page-addshoppingcart-dialog .minus.disabled,
.page-addshoppingcart-dialog .plus.disabled {
  background-color: #f9f9f9;
  color: #cacaca;
}

.page-addshoppingcart-dialog .minus:before,
.page-addshoppingcart-dialog .plus:before,
.page-addshoppingcart-dialog .plus:after {
  position: absolute;
  width: 8px;
  height: 2px;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: #878787;
  bottom: 0;
  content: '';
}

.page-addshoppingcart-dialog .plus:after {
  width: 2px;
  height: 8px;
}

.page-addshoppingcart-dialog .minus.disabled:before,
.page-addshoppingcart-dialog .plus.disabled:before,
.page-addshoppingcart-dialog .plus.disabled:after {
  background-color: #cacaca;
}

.page-addshoppingcart-dialog .page-dialog-close {
  display: block;
  width: 46.8rpx;
  height: 46.8rpx;
  border: 1px solid #67666f;
  color: #67666f;
  text-align: center;
  line-height: 46.8rpx;
  border-radius: 50%;
  font-size: 37.44rpx;
  top: 23.4rpx;
  right: 23.4rpx;
}

.page-addshoppingcart-dialog .quantity input {
  border: none;
  height: 25px;
  background-color: #f1f1f1;
  padding: 0;
  margin: 0 1px;
  border-radius: 0;
  width: 30px;
  min-height: 25px;
  line-height: 25px;
  font-size: 14px;
}

#tostorePayDialog .minus,
#tostorePayDialog .plus {
  position: relative;
  display: inline-block;
  font-size: 16px;
  color: #666;
  outline: 0 !important;
  border: 1px solid #999;
  text-indent: -9999px;
  overflow: hidden;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-weight: normal;
  background-color: #ffcc01;
  border-color: #ffcc01;
  border-radius: 50%;
}

#tostorePayDialog .minus {
  background-color: #fff;
  border-color: #dedede;
}

#tostorePayDialog .minus:before,
#tostorePayDialog .plus:after {
  position: absolute;
  width: 8px;
  height: 2px;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: #dedede;
  bottom: 0;
  content: '';
}

#tostorePayDialog .plus:after {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  content: '';
  width: 2px;
  height: 8px;
  background-color: #6c6c6c;
}

#tostorePayDialog .plus:before {
  position: absolute;
  width: 8px;
  height: 2px;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: #6c6c6c;
  bottom: 0;
  content: '';
}

#tostorePayDialog .plus:before,
#tostorePayDialog .plus:after {
  background-color: #fff;
}

/*添加购物车弹窗样式 end*/

.map .map-module {
  width: 100%;
  height: calc(100% - 30px);
  min-height: 40px;
}

.map div[onpositionupdate="return;"],
.map div[onresize="return;"] {
  display: none !important;
}

.map .map-link {
  margin-top: 5px;
  white-space: pre-wrap;
}

.app .code {
  height: 30px;
  overflow-x: hidden;
  overflow-y: auto;
}

.app .code iframe {
  height: 30px;
  width: 100%;
  border: none;
}

.classify-item {
  position: relative;
  display: inline-block;
  width: auto;
  padding: 0 5px;
}

.classify-item .underline {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0;
  width: 100%;
  border-top: 1px solid rgb(61, 183, 248);
}

.classify-mode-0-selected {
  padding-top: 0.4em;
  padding-bottom: 0.4em;
  border-radius: 0.4em;
  background-color: rgb(61, 183, 248);
  color: #fff;
}

.swiper-item {
  display: inline;
}

.app .takeout-shop-desc {
  height: 95px;
}

.app .waimai-goods-evaluate {
  height: 36px;
  border: 1px solid #f3f3f3;
  color: #666;
}

.app .takeout-shop-desc image {
  display: inline-block;
  width: 70px;
  height: 70px;
  border-radius: 4px;
}

.app .takeout-shop-desc .takeoutShopRest {
  position: absolute;
  font-size: 10px;
  top: 9px;
  transform: rotate(-45deg);
  background-color: #aaa;
  color: #fff;
  transform-origin: center center;
  height: 15px;
  line-height: 15px;
  width: 60px;
  text-align: center;
  left: -14px;
}

.tokeout-shop-info .icon-distribution {
  font-size: 32rpx;
}

.takeout-shop-desc .shop-info {
  display: inline-block;
  margin-top: 15px;
  vertical-align: top;
  font-size: 11px;
  color: #666;
  width: calc(100% - 100px);
}

.shop-info .shop-name {
  font-size: 18px;
  color: #333;
  margin-bottom: 12px;
  height: 20px;
  line-height: 20px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shop-info .shop-send {
  height: 13px;
}

.shop-info .icon-tokeout-mobile {
  width: 25px;
  height: 25px;
  display: inline-block;
  font-size: 15px;
  color: #3292e4;
  text-align: center;
  border-radius: 50%;
  line-height: 25px;
  float: right;
  margin-right: 15px;
  margin-top: -6px;
}

.cutoffLine {
  background-color: #f3f3f3;
  height: 5px;
}

.waimai-goods-evaluate>view {
  display: inline-block;
  vertical-align: top;
  width: 33.3%;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 16px;
}

.waimai-goods-evaluate .selected {
  border-bottom: 2px solid #ff7100;
  display: inline-block;
  vertical-align: middle;
  color: #ff7100;
  vertical-align: top;
  height: 34px;
  width: 50px;
}

.takeoutCategory {
  text-indent: 10px;
  background-color: #f4f4f4;
  color: #5c5c5c;
  padding: 2px 0;
}

.moreModel {
  float: right;
  border-radius: 4px;
  border: 1px solid #ff5800;
  color: #ff5800;
  font-size: 9px;
  cursor: pointer;
  display: inline-block;
  width: 34px;
  height: 13px;
  text-align: center;
  line-height: 13px;
}

.tokeout-shop-evaluate .takeout-assess-btn {
  display: inline-block;
  border: 1px solid #d9d9d9;
  color: #999;
  border-radius: 3px;
  padding: 4px 6px;
  margin-right: 10px;
  background-color: #fff;
}

.tokeout-shop-evaluate .takeout-assess-btn.active {
  border-color: #ff7100;
  color: #fff;
  background-color: #ff7100;
}

.takeoutAssessList {
  border-top: 1px dashed #f3f3f3;
  padding: 30rpx 0;
  font-size: 12px;
  color: #999;
  margin: 0 30rpx;
}

.takeoutAssessList .ico {
  color: #666;
}

.takeoutAssessList .ico.active {
  color: #ffa82a;
}

.takeoutAssessList>image {
  display: inline-block;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  vertical-align: top;
}

.takeoutAssessList .takeoutService {
  color: #666;
}

.takeoutAssessList .takeoutService label,
.takeoutAssessList .takeoutService text {
  vertical-align: middle;
  color: #666;
}

.takeoutAssessList .takeoutService .active {
  color: #ff7100;
}

.takeoutAssessList view image {
  width: 194rpx;
  height: 194rpx;
  border-radius: 0;
  margin-right: 17rpx;
}

.takeout-assess-title {
  margin-left: 74rpx;
  font-size: 28rpx;
  padding: 30rpx 0;
}

.takeout-assess-goodsTitle {
  color: #ff7100;
}

.takeout-assess-goodsContent {
  color: #333;
}

.takeoutAssessList image:last-child {
  margin: 0;
}

.takeoutAssessList .tackoutNickText {
  font-size: 28rpx;
  color: #333;
  margin-right: 9px;
}

.takeoutAssessList .takeoutNickname {
  padding-bottom: 4rpx;
  font-size: 22rpx;
}

.takeoutAssessList .assessInfoTop {
  display: inline-block;
  width: calc(100% - 78rpx);
  vertical-align: top;
  margin-left: 14rpx;
}

.tokeout-shop-info {
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 20px;
  font-size: 14px;
  color: #333;
}

.tokeout-shop-info>text {
  font-size: 30rpx;
  color: #333;
  padding-top: 30rpx;
  display: block;
}

.tokeout-shop-info view {
  padding-top: 20px;
  font-size: 26rpx;
}

.tokeout-shop-info label {
  margin-right: 10px;
  vertical-align: middle;
  width: 30rpx;
  height: 30rpx;
  text-align: center;
  line-height: 30rpx;
  font-size: 22rpx;
}

.tokeout-shop-info view text {
  display: inline-block;
  width: calc(100% - 44px);
  vertical-align: middle;
}

.tokeout-shop-info .takeoutDiscountBackground {
  background-image: linear-gradient(to top, rgb(247, 135, 35) 0%, rgb(253, 185, 74) 100%);
  background-image: -webkit-linear-gradient(bottom, rgb(247, 135, 35) 0%, rgb(253, 185, 74) 100%);
  color: #fff;
  display: inline-block;
  border-radius: 2px;
}

.tokeout-shop-info .takeoutReduceBackground {
  background-image: linear-gradient(to top, rgb(246, 62, 86) 0%, rgb(253, 125, 86) 100%);
  background-image: -webkit-linear-gradient(bottom, rgb(246, 62, 86) 0%, rgb(253, 125, 86) 100%);
  color: #fff;
  display: inline-block;
  border-radius: 2px;
}

.tokeout-shop-info .takeoutReduceBackground+text {
  height: 18px;
  line-height: 18px;
}

.app .waimai-img {
  width: 60px;
  height: 60px;
  float: left;
}

.app .waimai-title {
  margin-left: 70px;
}

.app .waimaigoods-list-item {
  padding: 0 0 6px;
  border-bottom: 1px solid #f8f8f8;
}

.app .waimaigoods-list-item:last-child {
  border-bottom: none;
}

.waimai-container-wrap {
  position: relative;
}

.waimai-container-wrap .typeList {
  display: inline-block;
  position: relative;
  overflow-y: auto;
  text-align: center;
}

.waimai-container-wrap .typeList view {
  box-sizing: border-box;
}

.waimai-container-wrap .typeList view:after {
  content: '';
  display: block;
  height: 1px;
  width: 80%;
  margin: 11px auto 0;
  background-color: #f5f5f5;
}

.waimai-container-wrap .waimaigoodslist {
  vertical-align: top;
  height: 100%;
}

.waimaiCategory.selected {
  background-color: #eee;
  color: rgb(255, 255, 225);
}

.waimaigoodslist .dead-line {
  text-align: center;
  padding: 20rpx 0;
  color: #666;
}

.waimaigoodslist .inner-content {
  padding: 15px 10px 0;
  position: relative;
}

.waimai-title .waimai-title-text {
  display: inline-block;
  font-weight: 500;
  font-size: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 300rpx;
}

.waimai-title .waimai-desc {
  font-size: 12px;
  padding: 5px;
  padding-left: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.7;
}

.waimai-title .waimai-item-bottom {
  padding: 20px 5px 5px 0;
  height: 22px;
  line-height: 22px;
}

.waimai-title .waimai-price-per {
  color: rgb(255, 88, 0);
  font-size: 15px;
  font-weight: 500;
}

.waimai-title .waimai-number-change {
  float: right;
}

.waimai-title .waimai-number {
  display: inline-block;
  width: 30px;
  border: none;
  text-align: center;
  background: transparent;
  outline: none;
  vertical-align: middle;
  color: #333;
}

.waimai-title .waimai-count-minus,
.waimai-title .waimai-count-plus,
.cart-btn .takeout-count-minus,
.cart-btn .takeout-count-plus {
  position: relative;
  display: inline-block;
  width: 22px;
  height: 22px;
  border: 1px solid #ff7100;
  text-align: center;
  border-radius: 50%;
  color: rgb(255, 88, 0);
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  vertical-align: middle;
}

.waimai-title .waimai-count-plus,
.cart-btn .takeout-count-plus {
  background-color: rgb(255, 88, 0);
  color: #fff;
  border-color: rgb(255, 88, 0);
}

.waimai-title .waimai-count-minus:before,
.cart-btn .takeout-count-minus:before {
  content: '';
  display: inline-block;
  width: 10px;
  height: 2px;
  background-color: rgb(255, 88, 0);
  position: absolute;
  top: 10px;
  left: 6px;
}

.waimai-title .waimai-count-plus:before,
.cart-btn .takeout-count-plus:before {
  content: '';
  display: inline-block;
  width: 10px;
  height: 2px;
  background-color: #fff;
  position: absolute;
  top: 10px;
  left: 6px;
}

.waimai-title .waimai-count-plus:after,
.cart-btn .takeout-count-plus:after {
  content: '';
  display: inline-block;
  height: 10px;
  width: 2px;
  background-color: #fff;
  position: absolute;
  left: 10px;
  top: 6px;
}

.disabledminusbtn {
  background-color: #eee !important;
  color: #ddd !important;
  cursor: not-allowed !important;
}

.disabledBtn {
  background-color: rgb(204, 204, 204) !important;
  cursor: not-allowed !important;
}

.goods-bottom-opt {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  border-top: 1px solid #eee;
  background-image: -webkit-linear-gradient(bottom, rgba(226, 227, 231, 0.33) 0%, rgba(248, 248, 248, 0.33) 100%);
  background-image: linear-gradient(to top, rgba(226, 227, 231, 0.33) 0%, rgba(248, 248, 248, 0.33) 100%);
  background-color: #fff;
  -webkit-user-select: none;
  user-select: none;
  font-size: 0;
  z-index: 1;
}

.app .goods-bottom-opt .shopping-cart-wrap {
  background-color: #ff7100;
  border-radius: 50%;
  color: #fff;
  height: 88rpx;
  width: 88rpx;
  line-height: 88rpx;
  margin-right: 20rpx;
  font-size: 48rpx;
  margin-left: 30rpx;
  margin-top: -20rpx;
}

.goods-bottom-opt .shopping-cart-wrap .icon-shoppingcart {
  margin: 0;
}

.takeout-goods-picture,
.takeout-model-choose {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.takeout-goods-picture>view {
  margin: -427rpx auto 0;
  width: 650rpx;
  background-color: #fff;
  border-radius: 5px;
  position: relative;
  top: 50%;
  overflow: hidden;
}

.takeout-model-choose>view {
  position: relative;
  top: 50%;
  margin: -175px auto 0;
  height: 750rpx;
  background-color: #fff;
  width: 300px;
  border-radius: 8px;
  overflow: hidden;
}

.takeout-goods-picture .takeout-pop-title {
  font-size: 16px;
  color: #4c4c4c;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-indent: 30rpx;
}

.takeout-goods-picture .takeout-pop-desc {
  font-size: 24rpx;
  color: #ff5800;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-indent: 30rpx;
}

.takeout-goods-picture .takeout-pop-price {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  font-size: 36rpx;
  color: #ff5800;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-indent: 30rpx;
}

.takeout-model-choose .takeout-title {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  padding-top: 20px;
  padding-bottom: 8px;
  text-align: center;
  color: #333;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.takeoutGoodsModelBtn {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 55px;
  width: 100%;
  background-color: #fbfbfb;
}

.takeoutGoodsModelBtn text {
  color: #ff7100;
  font-size: 18px;
  line-height: 50px;
  font-weight: 600;
}

.takeoutGoodsModelBtn view {
  height: 35px;
  float: right;
  background-color: #ff7100;
  color: #fff;
  line-height: 35px;
  width: 100px;
  margin-right: 10px;
  text-align: center;
  border-radius: 4px;
  margin-top: 10px;
}

.takeout-model-choose .model-btn text {
  border: 1px solid #aaa;
  color: #999;
  border-radius: 2px;
  padding: 3px 15px;
  line-height: 18px;
  height: 18px;
  display: inline-block;
  margin-right: 15px;
  margin-bottom: 5px;
  font-size: 14px;
}

.takeout-model-choose .model-btn text.active {
  color: #ff7100;
  border: 1px solid #ff7100;
  background-color: #fff4ed;
}

.takeout-shopping-carts-list {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 45px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 0;
}

.shopping-cart-list {
  border-bottom: 1px solid #eee;
}

.shopping-cart-list>view {
  display: inline-block;
  height: 60px;
  vertical-align: top;
}

.shopping-cart-list .cart-info {
  width: calc(100% - 154px);
}

.shopping-cart-list .cart-price {
  width: 76px;
  line-height: 60px;
  text-align: left;
  color: rgb(255, 88, 0);
  font-size: 14px;
  font-weight: 600;
}

.shopping-cart-list .cart-btn {
  width: 78px;
  text-align: center;
}

.shopping-cart-list .cart-btn>label,
.shopping-cart-list .cart-btn input {
  vertical-align: middle;
  margin-top: 18px;
  color: #333;
}

.takeout-shopping-carts-list>view {
  background-color: #fff;
  height: 246px;
  bottom: 0;
  position: absolute;
  left: 0;
  width: 100%;
}

.takeout-shopping-carts-list .chosen-title {
  line-height: 80rpx;
  height: 80rpx;
  background-color: #f7f7f7;
  color: #666;
  padding-left: 30rpx;
  border-bottom: 1px solid #eee;
  font-weight: 500;
  font-size: 30rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.shopping-cart-list .cart-title {
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.shopping-cart-list .cart-model {
  font-size: 11px;
  color: #999;
  height: 15px;
  line-height: 15px;
  margin-top: 5px;
}

.waimai-count {
  position: absolute;
  background: red;
  color: white;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  text-align: center;
  line-height: 36rpx;
  left: 68rpx;
  top: -5rpx;
  font-size: 20rpx;
  font-weight: 600;
  border: 2px solid #fff;
}

.shoppingcart-total-money {
  float: left;
  position: relative;
}

.shopping-cart-wrap {
  display: inline-block;
  position: relative;
  height: 40px;
}

.shopping-cart-wrap .icon-shoppingcart {
  display: inline-block;
  margin-left: 20px;
  margin-top: 5px;
  font-size: 29px;
}

.shopping-money-wrap {
  display: inline-block;
  position: relative;
  height: 88rpx;
  text-align: left;
  float: right;
  font-size: 26rpx;
  margin-left: 40rpx;
}

.shopping-money-wrap .shopping-money-price {
  color: rgb(255, 88, 0);
  font-size: 30rpx;
}

.sure-waimai-order {
  float: right;
  display: inline-block;
  height: 88rpx;
  padding: 0px 50rpx;
  line-height: 88rpx;
  font-size: 14px;
  text-align: center;
  color: #fff;
  background-color: rgb(255, 88, 0);
}

.comprehensive-score {
  display: inline-block;
  width: 238rpx;
  height: 58rpx;
  vertical-align: top;
  text-align: center;
  font-size: 44rpx;
  color: red;
  font-weight: 500;
  padding-top: 36rpx;
}

.other-score {
  display: inline-block;
  width: 378rpx;
  height: 92rpx;
  vertical-align: middle;
  padding: 36rpx;
  text-align: center;
}

.other-score text {
  color: #fc9e20;
  margin-right: 6rpx;
  font-size: 22rpx;
}

.getMoreAssess {
  background-color: #f3f3f3;
  padding: 20rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.app .franchisee-list {
  width: 100%;
  max-width: 100%;
  font-size: inherit;
  color: inherit;
  text-decoration: inherit;
  overflow-x: hidden;
  overflow-y: auto;
}

.app .franchisee-list .franchisee-title {
  padding: 23.4rpx;
  line-height: 39.4rpx;
  font-size: 32.76rpx;
  text-align: right;
  color: #666;
  background-color: #fff;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.app .franchisee-list .franchisee-location {
  float: left;
  width: 50%;
  text-align: left;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.app .franchisee-list .icon-location {
  font-size: 42.12rpx;
  vertical-align: sub;
}

.app .franchisee-list .franchisee-list-item {
  position: relative;
  height: auto !important;
  padding-left: 23.4rpx;
  margin-bottom: 23.4rpx;
  text-decoration: inherit;
  border-bottom: 1px solid #eee;
}

.app .franchisee-list .franchisee-list-item .franchisee-list-goods:before {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}

.app .franchisee-list .franchisee-list-item .inner-content {
  position: relative;
  display: inline-block;
  width: 100%;
  height: auto;
  vertical-align: middle;
}

.app .franchisee-list .franchisee-list-item .inner-content:before {
  content: "";
  display: none;
  position: absolute;
  left: -39px;
  top: -19px;
  border-top: 40px solid #999;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  transform: rotateZ(135deg) scale(0.8);
  -webkit-transform: rotateZ(135deg) scale(0.8);
}

.app .franchisee-list .franchisee-list-item.not-open .inner-content:before {
  display: block;
}

.app .franchisee-list .franchisee-list-item .not-open-tip {
  display: none;
}

.app .franchisee-list .franchisee-list-item.not-open .not-open-tip {
  display: block;
  position: absolute;
  left: -12px;
  top: -1px;
  z-index: 1;
  font-size: 12px;
  transform: rotateZ(-45deg) scale(0.9);
  -webkit-transform: rotateZ(-45deg) scale(0.9);
  text-align: center;
  color: #fff;
}

.app .franchisee-list .franchisee-list-item .cart-goods-num {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  line-height: 20px;
  background-color: #ff1616;
  border-radius: 50%;
  color: #fff;
  text-align: center;
}

.app .franchisee-list .franchisee-list-item .franchisee-list-goods:after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  background-color: #f00;
  vertical-align: middle;
}

.app .franchisee-list .franchisee-list-item.not-open {
  opacity: 0.5;
}

.app .franchisee-list .list-img {
  display: inline-block;
  width: 140.4rpx;
  height: 140.4rpx;
  vertical-align: middle;
}

.app .franchisee-list .title-container {
  display: inline-block;
  text-decoration: inherit;
  vertical-align: top;
}

.app .franchisee-list .title-container view {
  width: calc(100% - 12rpx);
  padding-left: 12rpx;
  word-break: break-all;
  text-decoration: inherit;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.app .franchisee-list .title-container .title,
.app .franchisee-list .title-container .descrition {
  text-decoration: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.app .franchisee-list .title-container .title {
  font-size: 32rpx;
}

.app .franchisee-list .title-container .description {
  margin-top: 11.7rpx;
  font-size: 32.76rpx;
  color: #aaa;
}

.app .franchisee-list .defaul {
  font-size: 24rpx;
  color: #ababab;
  margin-top: 16px;
}

.app .franchisee-list .distance {}

.app .discount-coupons {
  width: 100% !important;
  height: 100%;
  padding-left: 177rpx;
}

.app .discount-coupons .coupons-cash,
.app .discount-coupons .coupons-rebate {
  color: #ababab;
  padding: 4px 0;
  font-size: 24rpx;
}

.app .discount-coupons .coupons-cash label {
  width: 15px;
  height: 15px;
  line-height: 15px;
  display: inline-block;
  text-align: center;
  margin-right: 5px;
  background: #fb7e00;
  color: #fff;
}

.app .discount-coupons .coupons-rebate label {
  width: 15px;
  height: 15px;
  display: inline-block;
  line-height: 15px;
  text-align: center;
  margin-right: 5px;
  background: #f75351;
  color: #fff;
}

.app .community-list-item {
  padding-left: 10px;
  position: relative;
}

.app .community-list-item:before {
  /* 用于垂直居中的参照物 */
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  background-color: #f00;
  vertical-align: middle;
}

.app .community-list-item .inner-content {
  display: inline-block;
  vertical-align: middle;
  height: auto;
  position: relative;
  width: 100%;
  text-decoration: inherit;
  border-radius: inherit;
  white-space: nowrap;
  z-index: 1;
  overflow: hidden;
}

.app .community-list-item .list-img {
  display: inline-block;
  width: 140.3rpx;
  height: 140.3rpx;
  vertical-align: middle;
}

.app .community-list-item .title-container {
  display: inline-block;
  width: 561.6rpx;
  vertical-align: top;
}

.app .community-list-item .title-container>view {
  padding-left: 10px;
  margin-top: 4px;
}

.app .community-list-item .title-container .community-title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
  width: 340rpx;
}

.app .community-list-item .title-container .topic-num {
  margin-left: 20px;
  font-size: 12px;
  color: #a8a8a8;
}

.app .community-list-item .title-container .topic-num>span {
  margin-left: 5px;
}

.app .community-list-item .community-desc {
  font-size: 12px;
  color: #a8a8a8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  margin-top: 12px;
}

.like-animate {
  position: absolute;
  left: 50%;
  bottom: 17px;
  margin: -8px;
  color: red;
  backface-visibility: visible;
  -webkit-backface-visibility: visible;
  transform-origin: center center;
  -webkit-transform-origin: center center;
  animation: likeAnimate 0.5s 0s 1 ease-out normal;
  -webkit-animation: likeAnimate 0.5s 0s 1 ease-out normal;
}

@-webkit-keyframes likeAnimate {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0px);
  }

  60% {
    opacity: 1;
    -webkit-transform: translateY(-10px);
  }

  100% {
    opacity: 0;
    -webkit-transform: translateY(-15px);
  }
}

@keyframes likeAnimate {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0px);
  }

  60% {
    opacity: 1;
    -webkit-transform: translateY(-10px);
  }

  100% {
    opacity: 0;
    -webkit-transform: translateY(-15px);
  }
}

/*城市定位*/

.app .citylocation-choose {
  position: fixed;
  width: 100%;
  height: 100%;
  bottom: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.4);
}

.app .citylocation-choose .picker-btn {
  position: absolute;
  bottom: 300px;
  width: 100%;
  background-color: #fff;
}

.app .picker-btn text {
  width: 40px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  display: inline-block;
  color: #09bb07;
  padding: 10px 0 3px;
}

.app .picker-btn text:nth-child(2) {
  float: right;
}

.app .citylocation-choose picker-view {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 300px;
  text-align: center;
  background-color: #fff;
}

/*悬浮窗*/

.app .suspension {
  color: #fff;
  font-size: 20px;
  position: fixed !important;
  right: 0;
  bottom: 163.8rpx;
  z-index: 20;
}

.suspension-item {
  display: block;
  width: 81.9rpx;
  height: 81.9rpx;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
  margin-bottom: 2.34rpx;
}

.suspension-item.diy {
  display: flex;
  justify-content: center;
  align-items: center;
}

.suspension-item>icon {
  line-height: 81.9rpx;
  font-size: inherit;
}

.suspension-service {
  position: relative;
  width: 100%;
  height: 100%;
}

.suspension-service contact-button {
  width: 100%;
  height: 100%;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
}

.wxParse-inline {
  display: inline;
  margin: 0;
  padding: 0;
}

.wxParse-div {
  margin: 0;
  padding: 0;
}

.wxParse-h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.wxParse-h2 {
  font-size: 1.5em;
  margin: 0.75em 0;
}

.wxParse-h3 {
  font-size: 1.17em;
  margin: 0.83em 0;
}

.wxParse-h4 {
  margin: 1.12em 0;
}

.wxParse-h5 {
  font-size: 0.83em;
  margin: 1.5em 0;
}

.wxParse-h6 {
  font-size: 0.75em;
  margin: 1.67em 0;
}

.wxParse-h1 {
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 0.9em;
}

.wxParse-h2 {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0.34em;
}

.wxParse-h3 {
  font-weight: 400;
  font-size: 15px;
  margin-bottom: 0.34em;
}

.wxParse-h4 {
  font-weight: 400;
  font-size: 14px;
  margin-bottom: 0.24em;
}

.wxParse-h5 {
  font-weight: 400;
  font-size: 13px;
  margin-bottom: 0.14em;
}

.wxParse-h6 {
  font-weight: 400;
  font-size: 12px;
  margin-bottom: 0.04em;
}

.wxParse-h1,
.wxParse-h2,
.wxParse-h3,
.wxParse-h4,
.wxParse-h5,
.wxParse-h6,
.wxParse-b,
.wxParse-strong {
  font-weight: bolder;
}

.wxParse-i,
.wxParse-cite,
.wxParse-em,
.wxParse-var,
.wxParse-address {
  font-style: italic;
}

.wxParse-pre,
.wxParse-tt,
.wxParse-code,
.wxParse-kbd,
.wxParse-samp {
  font-family: monospace;
}

.wxParse-pre {
  white-space: pre;
}

.wxParse-big {
  font-size: 1.17em;
}

.wxParse-small,
.wxParse-sub,
.wxParse-sup {
  font-size: 0.83em;
}

.wxParse-sub {
  vertical-align: sub;
}

.wxParse-sup {
  vertical-align: super;
}

.wxParse-s,
.wxParse-strike,
.wxParse-del {
  text-decoration: line-through;
}

.wxParse-strong,
wxParse-s {
  display: inline;
}

.wxParse-a {
  color: deepskyblue;
  word-break: break-all;
  overflow: auto;
}

.wxParse-video {
  text-align: center;
  margin: 10px 0;
}

.wxParse-video-video {
  width: 100%;
}

.wxParse-img {
  background-color: transparent;
  overflow: hidden;
  width: 40px;
  height: 40px;
}

.wxParse-blockquote {
  margin: 0;
  padding: 10px 0 10px 5px;
  font-family: Courier, Calibri, "宋体";
  background: #f5f5f5;
  border-left: 3px solid #dbdbdb;
}

.wxParse-code,
.wxParse-wxxxcode-style {
  display: inline;
  background: #f5f5f5;
}

.wxParse-ul {
  margin: 20rpx 10rpx;
}

.wxParse-li,
.wxParse-li-inner {
  display: flex;
  align-items: baseline;
  margin: 10rpx 0;
}

.wxParse-li-text {
  align-items: center;
  line-height: 20px;
}

.wxParse-li-circle {
  display: inline-flex;
  width: 5px;
  height: 5px;
  background-color: #333;
  margin-right: 5px;
}

.wxParse-li-square {
  display: inline-flex;
  width: 10rpx;
  height: 10rpx;
  background-color: #333;
  margin-right: 5px;
}

.wxParse-li-ring {
  display: inline-flex;
  width: 10rpx;
  height: 10rpx;
  border: 2rpx solid #333;
  border-radius: 50%;
  background-color: #fff;
  margin-right: 5px;
}

.wxParse-u {
  text-decoration: underline;
}

.wxParse-hide {
  display: none;
}

.WxEmojiView {
  align-items: center;
}

.wxEmoji {
  width: 16px;
  height: 16px;
}

.wxParse-tr {
  display: flex;
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.wxParse-th,
.wxParse-td {
  flex: 1;
  padding: 5px;
  font-size: 28rpx;
  border-left: 1px solid #e0e0e0;
  word-break: break-all;
}

.wxParse-td:last {
  border-top: 1px solid #e0e0e0;
}

.wxParse-th {
  background: #f0f0f0;
  border-top: 1px solid #e0e0e0;
}

.wxParse-text {
  white-space: pre-wrap;
}

.wxParse-br {
  height: 22px;
}

.wxParse-p {
  min-height: 19px;
  white-space: pre-wrap;
}