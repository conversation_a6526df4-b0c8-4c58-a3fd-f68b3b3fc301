page {
  background-color: #f5f5f5;
  position: relative;
}

.nav {
  width: 100%;
  height: 50px;
  display: flex;
  flex-direction: row;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 20;
}

.default {
  line-height: 50px;
  text-align: center;
  flex: 1;
  color: #333;
  font-size: 14px;
}

.blue {
  line-height: 50px;
  text-align: center;
  color: #FF7E00;
  flex: 1;
  font-size: 14px;
  border-bottom: 2px solid #FF7E00;
}

scroll-view {
  display: block;
  width: 100%;
  position: absolute;
  top:0;
}

.show, .hidden {
  line-height: 50px;
}

.show {
  display: block;
}

.hidden {
  display: none;
}

/**订单状态**/

.order_state {
  width: 100%;
  background: #fff;
  margin-bottom: 5px;
}

.state_box {
  height: 34px;
  line-height: 34px;
  display: block;
  border-bottom: 1px dashed #ececec;
  padding: 0 10px;
  font-size: 13px;
}

.state_left {
  float: left;
  font-weight: bold;
}

.state_right {
  float: right;
  color: #fa6a85;
}

.conBox {
  padding: 0 10px;
  font-size: 13px;
  color: #666;
}

.conBox label {
  width: 100%;

  line-height: 30px;
  display: block;
}

.conBox text {
  color: #333;
  margin-left: 5px;
}

.phone_number {
  color: green !important;
  margin-left:20px !important;
  text-decoration: underline;
}

.conBox textarea {
  border: 1px solid #d8d8d8;
  height: 80px;
  background: #ececec;
  margin: 10px 0;
  line-height: 24px;
  width: 100%;
}

.conBox button {
  background: #ff7100;
  color: #fff;
  font-size: 14px;
  line-height: 44px;
  height: 44px;
  margin-bottom: 20px;
}

/* 最新状态 */

.new_state, .old_state {
  position: relative;
  border-left: 1px solid #ccc;
  width: 100%;
  height: 80px;
  font-size: 14px;
}

.new_state {
  color: #FF7E00;
}

.old_state {
  color: #666;
}

.new_state text, .old_state text {
  width: 8px;
  height:8px;
  border-radius: 50%;
  position: absolute;
}

.new_state text {
  background: #FF7E00;
  border: 3px solid #ff8997;
  left: -8px;
  top: 0px;
}

.old_state text {
  background: #666;
  left: -5px;
  top: 5px;
}

.new_state label, .old_state label {
  padding-left: 15px;
  display: block;
  line-height: 24px;
}

.new_state label:last-child, .old_state label:last-child {
  font-size: 12px;
}
