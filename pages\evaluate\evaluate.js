var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var wxMarkerData = [];
Page({
    data: {
        goods_pic: app.imageUrl + 'classify_bg.png',
        add_pic: app.imageUrl + 'evaluate/add_pic.png',
        close_btn: app.imageUrl + 'close.png',
        goodsList: [],
        orderId: "",
        iscjActivity: false,
        orderTotalMoney: 0
    },
    onLoad: function (options) {
        var that = this;
        var orderId = options.orderId;
        that.setData({
            orderId: orderId
        });
        that.queryOrderDetail(orderId);
    },
    queryOrderDetail: function (orderId) {
        var that = this;
        wx.request({
            header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/applet/buyOrder/queryOrderDetailByEvaluate',
            data: {
                       "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
                "loginId": app.getLoginId(),
                "userRole": app.getUserRole(),
                "orderId": orderId,
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId()
            },
            success: function (res) {
                var orderTotalMoney = res.data.orderTotalMoney;
                var orderList = res.data.orderList;
                that.setData({
                    goodsList: orderList,
                    orderTotalMoney: orderTotalMoney
                });
            }
        })
    },
    changeScoreBindTap: function (e) {
        var that = this;
        var id = e.currentTarget.dataset.id;
        var score = e.currentTarget.dataset.score;
        var orderList = that.data.goodsList;
        for (var i = 0; i < orderList.length; i++) {
            if (orderList[i].commodityId == id) {
                orderList[i].evaluateScore = score;
                break;
            }
        }
        that.setData({
            goodsList: orderList
        });
    },
    evaluateContentBindInput: function (e) {
        var that = this;
        var content = e.detail.value;
        var id = e.target.dataset.id;
        var orderList = that.data.goodsList;
        for (var i = 0; i < orderList.length; i++) {
            if (orderList[i].commodityId == id) {
                orderList[i].evaluateContent = content;
                break;
            }
        }
        that.setData({
            goodsList: orderList
        });
    },
    /**
     * 从手机中选择图片
     */
    chooseImageBindTap: function (e) {
        var that = this;
        var id = e.currentTarget.dataset.id;
        wx.chooseImage({
            count: 5,
            success: function (res) {
                var tempFilePaths = res.tempFilePaths;
                if (tempFilePaths != null && tempFilePaths.length > 0) {
                    for (var i = 0; i < tempFilePaths.length; i++) {
                        var orderList = that.data.goodsList;
                        for (var j = 0; j < orderList.length; j++) {
                            if (orderList[j].commodityId == id) {
                                var image_list = orderList[j].evaluateImageList;
                                if (image_list.length >= 5) {
                                    app.showModal({
                                        content: "评论图片最多可以上传5张"
                                    });
                                } else {
                                    that.uploadImageForFtp(tempFilePaths[i], id);
                                }
                            }
                        }
                    }
                }
            }
        })
    },
    /**
     * 上传图片到FTP服务器
     */
    uploadImageForFtp: function (imageUrl, id) {
        var that = this;
        wx.uploadFile({
            url: app.projectName + '/upload/uploadPic', //仅为示例，非真实的接口地址
            filePath: imageUrl,
            name: 'file',
            header: {
                "Content-Type": "multipart/form-data"
            },
            success: function (res) {
                var data = JSON.parse(res.data);
                if (data.code == 1) {
                    var url = "https://www.cn2b2c.com/image" + data.url;
                    var orderList = that.data.goodsList;
                    for (var i = 0; i < orderList.length; i++) {
                        if (orderList[i].commodityId == id) {
                            orderList[i].evaluateImageList.push(url);
                            break;
                        }
                    }
                    that.setData({
                        goodsList: orderList
                    });
                } else {
                    app.showModal({
                        content: data.message
                    });
                }
            }
        })
    },
    deleteEvaluateImage: function (e) {
        var that = this;
        var id = e.currentTarget.dataset.id;
        var imageUrl = e.currentTarget.dataset.src;
        var orderList = that.data.goodsList;
        for (var i = 0; i < orderList.length; i++) {
            if (orderList[i].commodityId == id) {
                var evaluateImageList = orderList[i].evaluateImageList;
                for (var j = 0; j < evaluateImageList.length; j++) {
                    if (evaluateImageList[j] == imageUrl) {
                        evaluateImageList.splice(j, 1);
                    }
                }
                orderList[i].evaluateImageList = evaluateImageList;
                break;
            }
        }
        that.setData({
            goodsList: orderList
        });
    },
    previewImage: function (e) {
        var that = this;
        var imageUrl = e.currentTarget.dataset.src;
        var imageList = [];
        imageList.push(imageUrl);
        wx.previewImage({
            current: imageUrl,
            urls: imageList
        })
    },
    submitEvaluateBindTap: function () {
        var that = this;
        var content = "";
        for (var i = 0; i < that.data.goodsList.length; i++) {
            if (that.data.goodsList[i].evaluateScore == '') {
                app.showModal({
                    content: "请评分后提交"
                });
                return false;
            }
            content += that.data.goodsList[i].evaluateContent;
        }
        if (content != "") {
            that.submitEvaluate();
        } else {
            that.submitEvaluate();
        }
    },
    submitEvaluate: function () {
        var that = this;
        wx.request({
            header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/applet/buyOrder/orderEvaluate',
            data: {
                       "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
                "loginId": app.getLoginId(),
                "userRole": app.getUserRole(),
                "userName": app.getLoginName(),
                "orderId": that.data.orderId,
                "parmarList": JSON.stringify(that.data.goodsList),
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId()
            },
            success: function (res) {
                var flag = res.data.flag;
                if (flag) {
                    app.turnToPage("/pages/evaluateSuccess/evaluateSuccess?orderTotalMoney=" + that.data.orderTotalMoney + "&eventType=2");
                } else {
                    app.showModal({
                        content: res.data.msg
                    });
                }
            }
        })
    }
})