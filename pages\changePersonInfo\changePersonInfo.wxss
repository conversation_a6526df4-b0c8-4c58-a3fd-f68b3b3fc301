page {
  background: #f5f5f5;
}

/**
.current_account {
  width: 100%;
  height: 44px;
  line-height: 44px;
  border-bottom: 1px solid #ececec;
  display: block;
  font-size: 14px;
  padding: 0 15px;
  font-weight: bold;
  background: #fff;
}**/

.account_head {
  /**border-bottom: 1px solid #ececec;**/
  padding:15px 0 10px 0;
  background: #fff;
  margin-bottom: 20px;
}

.head_box {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}

.head_box image {
  width: 100%;
  height: 100%;
}

.account_tips {
  display: block;
  text-align: center;
  width: 100%;
  height: 30px;
  line-height: 30px;
  color: #7f7f7f;
  font-size: 15px;
  margin-top: 10px;
}

.account_detail {
  background: #fff;
  padding-bottom: 60px;
}

.account_detail label {
  height: 44px;
  line-height: 44px;
  border-bottom: 1px solid #ececec;
  padding: 0 15px;
  font-size: 14px;
  color: #333;
  background: #fff;
  display: block;
}

.account_detail label:last-child {
  border-bottom: none;
}

.account_detail label text {
  float: left;
  width: 80px;
}

.account_detail label input {
  height: 45px;
  line-height: 45px;
}

.radio {
  float: left;
  padding: 0 0 !important;
  margin-right: 40px;
}

textarea {
  padding: 10px 15px;
  display: block;
  line-height: 20px;
  font-size: 14px;
  height: 60px;
  margin-bottom: 20px;
  width: auto;
  color: #333333;
}

.add_address {
  height: 36px;
  width: 100%;
  background: #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding: 10px 0;
}

.add_address button {
  margin: 0 10px;
  height: 100%;
  background: #FF7E00;
  color: #fff;
  font-size: 14px;
  line-height: 36px;
}
