var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    array: [{
      mode: 'aspectFit',
    }],
    edit: app.imageUrl + 'edit.png',
    remove: app.imageUrl + 'remove.png',
    addressList: [],
    isFromBack: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.showLoading({
      title: '正在加载，请稍后',
      mask: true
    })
    this.initAddress();
  },
  /**
   * 初始化收货地址
   */
  initAddress: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/ios/queryDeliveryAddress',
      data: {
        "loginId": app.getUserId(),
        "loginRole": 1,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
      },
      success: function (res) {
        that.setData({
          addressList: res.data.returnList
        });
        wx.hideLoading();
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  /**
   * 添加新地址
   */
  addNewAddressBindTap: function () {
    app.navigateToPage('/pages/newbuiltAddress/newbuiltAddress');
  },
  /**
   * 删除地址
   */
  deleteAddressBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    app.showModal({
      content: '确定删除此收货地址？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/ios/deleteDeliveryAddress',
          data: {
            "loginId": app.getUserId(),
            "loginRole": 1,
            "jsonStr": id,
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            that.initAddress();
          }
        })
      }
    })
  },
  /**
   * 设置默认地址
   */
  setDefaultAddressBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    app.showModal({
      content: '确定设置为默认收货地址？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/ios/updateDefaultAddress',
          data: {
            "loginId": app.getUserId(),
            "loginRole": 1,
            "addressId": id,
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            that.initAddress();
          }
        })
      }
    })
  },
  /**
   * 更新地址
   */
  updateAddressBindTap: function (e) {
    var id = e.currentTarget.dataset.id;
    app.navigateToPage('/pages/editAddress/editAddress?id=' + id);
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (this.data.isFromBack) {
      this.initAddress();
    } else {
      this.setData({
        isFromBack: true
      });
    }

  }
})