var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cardBean: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    var cardBean = JSON.parse(options.cardBean);
    that.setData({
      cardBean: cardBean
    });
  },
  boundVipCardBindTap: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "cardId": that.data.cardBean.id
      },
      url: app.projectName + '/vipCard/boundUnderTheLineVipCard',
      success: function(res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "领取成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function() {
              setTimeout(function() {
                wx.switchTab({
                  url: "/pages/accountManager/accountManager"
                });
              }, 1000);
            }
          })
        } else {
          app.showModal({
            title: '提示',
            content: "领取失败，稍后在试"
          });
        }
      }
    })
  }
})