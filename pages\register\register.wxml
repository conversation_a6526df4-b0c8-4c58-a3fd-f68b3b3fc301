<!--券弹出层--->
<view class="pop_bg" hidden='{{putCardHidden}}'>
</view>
<view class="couponWrap" hidden='{{putCardHidden}}'>
  <image src="{{giftBox}}" style="width:100%;" mode="widthFix"></image>
  <view scroll-y="true" class="coupon_inner">
    <block wx:key="unique" wx:for="{{storeCardList}}" wx:for-item="sCard" wx:for-index="cardIndex">
      <view class="onecircal clearfix">
        <view class="oneCoupon">
          <view class="couponImage">
            <block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
              <text class="numDiscount">{{sCard.discountAmount}}</text>
              <text class="titleDiscount">折</text>
            </block>
            <block wx:else>
              <text class="titleDiscount">￥</text>
              <text class="numDiscount">{{sCard.discountAmount}}</text>
            </block>
          </view>
        </view>
        <view class="couponProduct">
          <view class="productDetail">
            {{sCard.cardName}}
          </view>
          <view class="couponMoney">
            <block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
              <label class="couponGift">
                {{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"享受"+sCard.discountAmount+"折扣"}}
              </label>
            </block>
            <block wx:else>
              <label class="couponGift">
                {{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"优惠"+sCard.discountAmount+"元"}}
              </label>
            </block>
          </view>
        </view>
        <view class="couponRight" bindtap='queryCardBagBindTap'>
          去使用
        </view>
        <label class="circal_top"></label>
        <label class="circal_bottom"></label>
      </view>
    </block>
  </view>
  <view style="width:100%;height:30rpx;background:#fc6366;"></view>
  <icon class="close" style="margin-top:20rpx;margin-left:45%;" bindtap='closeCardBgBindTap' type="clear" size='34' color='#c7c7c7' />
</view>
<!--券弹出层--->
<view class="list-block">
  <view>
    <input type="number" bindinput="bindKeyInput" maxlength="11" placeholder='输入手机号码'></input>
  </view>
  <view>
    <input type="text" bindinput="bindUserNameInput" maxlength="8" placeholder='输入真实姓名'></input>
  </view>
  <view>
    <input type="password" bindinput='passwordInput' placeholder='设置登录密码'></input>
  </view>
  <view>
    <input type="password" bindinput='againPasswordInput' placeholder='再次输入密码'></input>
  </view>
  <view>
    <input class='sms_verification' bindinput='smsCodeInput' type="number" maxlength="6" placeholder='请输入验证码'></input>
    <label bindtap='smsBindTap' class='sms_btn'>{{secondDesc}}</label>
  </view>
</view>
<view class='user_agreement'>
  <label class='checkbox'>
    <checkbox checked='{{isDefault}}' bindtap='defaultBindTap'></checkbox>我已阅读并同意
    <label bindtap='openServerBindTap'>《用户注册协议》</label>
  </label>
</view>
<button class="confirm_btn" bindtap='registerBindTap'>
  注册
</button>
<label class='existing_account' bindtap='goToLogin'>已有账号></label>


<!--黑色背景-->
<view class='black_bg' hidden="{{serverHidden}}"></view>
<!--黑色背景-->

<!--协议弹出框-->
<scroll-view scroll-y class='protocol_box' hidden="{{serverHidden}}">
  <image class='close_btn' src='{{close_btn}}' bindtap='hideServerBindTap'></image>
  <label class='protocol_title'>{{storeName}}注册协议</label>
  <view class='protocolContent'>
    <label>尊敬的会员及消费者（以下简称“甲方”），在此特别提醒您在注册成为“{{storeName}}”会员之前，请认真阅读本《{{storeName}}会员服务协议》（以下简称“本协议”），确保您充分理解本协议中各条款。本协议适用于{{storeName}}供应链有限公司（以下简称“乙方”）提供的会员服务。</label>
    <label>第一条 接受条款</label>
    <label>1、甲方须审慎阅读并选择接受或不接受本协议，接受本协议所有条款方可注册、登录或使用本协议所涉服务，成为“{{storeName}}”会员。甲方的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。</label>
    <label>2、本协议可由乙方“{{storeName}}”随时更新，更新后的协议条款一旦公布即代替原来的协议条款，恕不再另行通知，甲方可在“{{storeName}}”中查阅最新版协议条款。在修改协议条款后，如果甲方不接受修改后的条款，请立即停止使用“{{storeName}}”提供的会员服务，甲方继续使用“{{storeName}}”提供的会员服务将被视为接受修改后的协议。</label>
    <label>第二条 注册</label>
    <label>1、服务使用对象</label>
    <label>甲方在完成注册程序或以其他乙方允许的方式实际使用乙方会员服务时，甲方应当是具备完全民事权利能力和与所从事的民事行为相适应的行为能力的自然人、法人或其他组织。若不具备前述主体资格，请勿使用本服务，否则乙方有权注销或永久冻结甲方的会员账户，并停止提供会员服务。</label>
    <label>2、注册义务</label>
    <label>如甲方在“{{storeName}}”成功注册成为“{{storeName}}”会员，代表甲方同意提供真实、准确、完整和反映甲方当前实际情况的资料。倘若上述资料有任何不真实、不准确、不完整或不能反映当前实际情况的地方，乙方有权暂停、注销或永久冻结甲方的会员账户，并停止提供会员服务，由此产生的任何损失完全由甲方承担。</label>
    <label>第三条 权利与义务</label>
    <label>乙方应维护网络的正常运行，若因必须的系统维护、升级测试、修理等原因，乙方有权在未事先通知的情况下，暂停提供会员服务或与会员服务相关的其他内容，并不对因上述原因致使服务延误而引起的任何直接或间接的损失负责。</label>
    <label>乙方系统软件升级更新后，甲方应及时升级更新软件，如因甲方未及时升级更新软件而产生的任何争议和损失完全由甲方承担。</label>
    <label>第四条 承诺与保证</label>
    <label>1、甲方拥有签订并履行本协议的权利。</label>
    <label>2、甲方保证在“{{storeName}}”上所提交的内容、资料等不含有任何违反国家有关法律、法规及中华人民共和国承认或加入的国际条约的内容,包括但不限于危害国家安全、淫秽色情、虚假、侮辱、诽谤、侵犯他人知识产权、人身权或其他合法权益以及有违社会公序良俗的内容或指向这些内容的链接。</label>
    <label>3、甲方不得私自以乙方名义对外进行任何形式的承诺，举办未经乙方允许的活动，如有损害乙方名誉、商业利益等一切有危害性的行为发生，所引起的一切法律纠纷、赔偿责任以及其他任何责任、义务等均由甲方自行承担，乙方有权取消该会员的资格，并保留进一步追究该行为法律责任的权利。</label>
    <label>4、乙方若发现任何“{{storeName}}”会员服务的管理账号出现非法使用或存在安全漏洞的情况，应立即通知甲方。</label>
    <label>5、甲方必须遵守与会员服务有关的各项服务条款，如有违反并足以影响到乙方声誉者，乙方有权拒绝向甲方提供“{{storeName}}”会员服务，并终止本协议，同时乙方有进一步追究甲方法律责任及损失赔偿的权利。</label>
    <label>第五条 所有权及其许可</label>
    <label>1、本合约的任何内容均不得视为乙方将其所有的或许可甲方使用的专利权及其它知识产权转让给甲方。除根据本协议条款使用“{{storeName}}”的产品和服务外，不授予甲方其它任何权利。任何使用“智远海淘”的商标及商业标识的行为必须事先得到乙方的书面许可。</label>
    <label>2、甲方所获知的乙方的商业秘密负有保密义务，未经乙方书面许可，甲方不得将其泄露给第三方，否则应承担违约责任并赔偿损失。</label>
    <label>第六条 重要声明</label>
    <label>乙方提供的服务在技术上存在一定的风险因素，即因某些技术上不能预见的问题或其他困难，有可能导致甲方某些数据损失或其他服务中断，因此，乙方对下述内容不作保证:</label>
    <label>1、“服务”不会中断和不带任何错误；</label>
    <label>2、通过使用“服务”而可能获取的结果将是完全准确或可信赖。</label>
    <label>甲方需要考虑到上述风险因素并自行承担全部责任。</label>
    <label>第七条 第三方投诉的解决</label>
    <label>因甲方的行为或活动导致第三方投诉或索赔而造成乙方损失的(包括但不限于乙方依法必须支付给第三方的赔偿款) ,由甲方承担全部赔偿责任。</label>
    <label>第八条 会员服务内容：</label>
    <label>1、乙方通过“{{storeName}}”不定期向会员举办美容专业知识讲座、美容产品介绍等活动；</label>
    <label>2、会员可以参与乙方在“{{storeName}}”上举办的优惠促销活动。</label>
    <label>第九条 法律适用</label>
    <label>本协议适用中华人民共和国法律。如与本协议有关的某一特定事项缺乏明确法律规定，则应参照通用的国际商业惯例或行业惯例执行。</label>
    <label>第十条 不可抗力</label>
    <label>对于因合理控制范围以外的原因，包括但不限于自然灾害、罢工或骚乱、物质短缺或定量配给、暴动、战争行为、政府行为、通讯或其他设施故障或严重伤亡事故等，致使服务延迟或未能履约的，乙方不对甲方承担任何责任。</label>
    <label>第十一条 免责条款</label>
    <label>1、在本协议规定的范围内，对下列情形，乙方不承担任何责任：</label>
    <label>（1）由于甲方的故意或过失导致甲方遭受损失的；</label>
    <label>（2）因不可抗力导致乙方不能履行其义务的。</label>
    <label>2、无论因何种原因导致乙方应当依法承担赔偿责任时，赔偿金的最高限额为甲方在“{{storeName}}”实际消费支付费用的二倍。</label>
    <label>第十二条 争议解决</label>
    <label>本协议的效力、解释及纠纷的解决，适用于中华人民共和国法律。若因本协议引起的或与本协议有关的任何纠纷或争议，首先应友好协商解决，协商不成的，应将纠纷或争议提交乙方住所地有管辖权的人民法院管辖。</label>
  </view>
</scroll-view>
<!--协议弹出框-->