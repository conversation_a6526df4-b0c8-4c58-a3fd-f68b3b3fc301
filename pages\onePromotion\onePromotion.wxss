/* pages/onePromotion/onePromotion.wxss */
page {
  background: #f5f5f5;
}

.rightWrap {
  width: 100%;
  /*padding:10px 3%;*/
  /*border-bottom:1px dashed #ccc;*/
  margin-bottom: 10rpx;
  background: #fff;
}

.rightMainPic {
  width: 60px;
  height: 60px;
  float: left;
}

.productAttr {
  position: relative;
  padding-left: 330rpx;
  height: 100%;
}

.specialMark {
  border-radius: 4px;
  font-size: 12px;
  position: absolute;
  padding: 1px 6px;
  right: 0px;
  top: 0px;
  background: #ff6600;
  color: #fff;
}

.productName {
  display: block;
  font-size: 30rpx;
  color: #333;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
  padding-top: 50rpx;
}

.productLine {
  width: 100%;
  float: left;
}

.productLine label {
  height: 20px;
  display: block;
  font-size: 12px;
  color: #bfbfbf;
}

.c_fl {
  float: left;
}

.c_fr {
  float: right;
}

.skuAttr {
  display: block;
  font-size: 13px;
  color: red;
}

.normalPrice {
  display: block;
  font-size: 13px;
  color: red;
}

.numWrap {
  height: 24px;
  line-height: 24px;
  float: right;
}

/*抛物线动画*/

.good_box {
  width: 18px;
  height: 18px;
  position: fixed;
  border-radius: 50%;
  overflow: hidden;
  left: 50%;
  top: 50%;
  z-index: +99;
  background: rgba(0, 133, 207, 1);
}

.minusIcon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #228ed8;
  text-align: center;
  line-height: 18px;
  color: #228ed8;
  float: left;
  font-size: 20px;
}

.addIcon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #228ed8;
  color: #fff;
  background: #228ed8;
  text-align: center;
  line-height: 18px;
  display: inline-block;
  float: left;
  font-size: 20px;
}

.buyNum {
  margin-left: 3px;
  margin-right: 3px;
  width: 50px;
  line-height: 24px;
  display: inline-block;
  text-align: center;
  float: left;
  background: #eee;
  border-radius: 4px;
  vertical-align: top;
  margin-top: -1px;
  height: 24px;
  color: #000;
  font-size: 12px;
}

.validTime {
  float: left;
  width: 56%;
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
  margin-top: 4px;
}

.soldOut {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: #000;
  z-index: 999;
  opacity: 0.3;
}

.soldOutIcon {
  z-index: 9999;
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 80px;
  height: 80px;
}

/*add CSS*/

.scroll_block {
  width: 80%;
  position: fixed;
  top: 35%;
  left: 10%;
  z-index: 1000;
  padding-bottom: 20px;
  border-radius: 10px;
  background: #fff;
}

.append_title {
  margin-top: 40px;
}

.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: #000;
  opacity: 0.6;
}

.confirmButton {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  width: 80%;
  background: #590f10;
  color: #fff;
  text-align: center;
  margin-left: 10%;
  margin-top: 15px;
  border-radius: 5px;
}

.numBox {
  border-radius: 10px;
  height: 30px;
  margin: 0 auto;
  border: 1px solid #ddd;
  width: 80%;
  padding-left: 20px;
}

.orderWrap {
  margin-top: 10px;
}

.limited_quantity {
  font-size: 14px;
  color: #666;
  display: block;
  width: 100%;
  text-align: center;
}

.oldPrice {
  color: #444;
  text-decoration: line-through;
  font-size: 26rpx;
}

.newPrice {
  color: #FF7E00;
  font-size: 32rpx;
}

.buy_Num {
  color: #FF7E00;
  float: right;
  font-size: 12px;
}

.promotionTitle {
  border-bottom: 1px solid #ececec;
  background: #fff;
  padding: 10px 15px;
  font-size: 14px;
}

.promotionDetail {
  text-align: center;
  padding: 10px 15px;
}

.promotionDetail text {
  color: #444;
}

.time_promot {
  display: block;
  padding: 2px 15px;
}

.end_promot {
  display: block;
  padding: 2px 15px 15px;
}

.hour_end {
  margin: 2px 4px;
  background: rgb(212, 19, 19);
  padding: 2px;
  color: #fff;
  border-radius: 5px;
}

.minus_end {
  margin: 0px 2px;
  background: #000;
  padding: 2px;
  color: #fff;
  border-radius: 5px;
}

.onSaleProduct {
  margin-top: 20rpx;
}

.infoWrap {
  background: #fff;
  font-size: 14px;
}

.settle_Info {
  position: fixed;
  bottom: 10px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #ff6600;
  text-align: center;
  z-index: 999;
}

.othercart {
  height: 30px;
  width: 30px;
  margin-top: 10px;
  top: 14px;
  left: 22%;
}

.cartNum {
  position: absolute;
  top: 4px;
  left: 40%;
  /*width:30px;
  height:30px;*/
  background: #fff;
  border-radius: 8px;
  font-size: 12px;
  color: red;
  padding: 2px 7px;
}