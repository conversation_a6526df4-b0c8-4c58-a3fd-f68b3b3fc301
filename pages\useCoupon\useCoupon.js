// pages/useCoupon/useCoupon.js
var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    goodsList: [],
    orderTotalMoney: 0,
    storeCard: [],
    orderMoney: 0,
    downArrow: app.imageUrl + 'downArrow.png',
    upArrow: app.imageUrl + 'upArrow.png',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      orderTotalMoney: options.orderTotalMoney,
      goodsList: options.goodsList,
      orderMoney: options.orderMoney
    });
    this.queryUseCardByStoreId();
  },
  /**
   * 不可使用的券
   */
  noUserCardBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var storeCard = that.data.storeCard;
    app.showModal({
      content: storeCard[index].unavailableCause
    });
  },
  queryUseCardByStoreId: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryUseCardByStoreId',
      data: {
        "storeId": app.getExtStoreId(),
        "companyId": app.getUserId(),
        "orderTotalMoney": that.data.orderTotalMoney,
        "goodsList": that.data.goodsList,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        

      },
      success: function (res) {
        var userCard = res.data.userCard;
        for (var i = 0; i < userCard.length; i++) {
          userCard[i]['ruleSwitch'] = false;
        }
        that.setData({
          storeCard: userCard
        });
      }
    })
  },
  /**
   * 选择可用的券进行使用
   */
  goToUserCardBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var storeCard = that.data.storeCard;
    var cardDesc = "";
    var totalMoney = 0;
    //当卡券类型为2的时候为折扣券，其它的为优惠券
    if (storeCard[index].cardType >= 4 && storeCard[index].cardType <= 6) {
      cardDesc = storeCard[index].discountAmount + "折";
    } else {
      cardDesc = "优惠" + storeCard[index].discountAmount + "元";
    }
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      cardDesc: cardDesc,
      cardMoney: storeCard[index].discountAmount,
      cardType: storeCard[index].cardType,
      cardNo: storeCard[index].cardNo
    });
    prevPage.calculationOrderTotal();
    app.turnBack();
  },
  /**
   * 不使用优惠券
   */
  notUseCardBindTap: function () {
    var that = this;
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      cardDesc: "未使用任何优惠券",
      orderTotalMoney: that.data.orderMoney,
      cardMoney: 0,
      cardType: -1,
      cardNo: ""
    });
    prevPage.calculationOrderTotal();
    app.turnBack();
  },
  // 点击使用规则
  ruleSwitchFun: function (e) {
    var that = this
    var StoreStoreCardData = this.data.storeCard;
    var ruleSwitchData = StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch;
    if (ruleSwitchData == false) {
      if (typeof (StoreStoreCardData[e.currentTarget.dataset.idx].rule) == "undefined") {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/retailCoupon/queryCardUseRule',
          data: {
            "cardId": e.currentTarget.dataset.cardid,
            "storeId": app.getExtStoreId(),
          },
          success: function (res) {
            StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch = true;

            StoreStoreCardData[e.currentTarget.dataset.idx].rule = res.data.cardRule;
            that.setData({
              storeCard: StoreStoreCardData
            })
          }
        })
      } else {
        StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch = true;
        that.setData({
          storeCard: StoreStoreCardData
        })
      }

    } else {
      StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch = false;
      that.setData({
        storeCard: StoreStoreCardData
      })
    }

  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})