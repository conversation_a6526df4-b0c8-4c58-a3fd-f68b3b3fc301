<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<wxs src="../../wxs/subutil.wxs" module="tools" />
<view style="background:url('{{result.collectConfig.bgImg}}'); background-size:100%">
	<view class="background">
		<!-- <view class="strategy" bindtap="toCollectStrategy">
			攻略
		</view> -->
	</view>
	<view class="colStatus" style="height:572rpx; margin-bottom: 20rpx;">
		<view class="notice_one" style="margin-bottom:30rpx">
			<view wx:if="{{result.collectConfig.collectNotice}}">
				<view class="notice_two">
					<image src="{{notice}}" style="width:100%;height:auto;" mode="widthFix"></image>
				</view>
				<view class="notice_three">
					<view class="example">
						<view class="marquee_box">
							<view class="marquee_text" style="{{orientation}}:{{marqueeDistance}}rpx;font-size: {{size}}rpx;">
								{{result.collectConfig.collectNotice}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="collect_one" style="background:url({{reward}});background-size: cover;">
			<view wx:if="{{result.collectList.length==0}}">
				<view class="collect_two">
					<image src="{{expression}}" style="width:132rpx;height:auto;" mode="widthFix"></image>
				</view>
				<view class="collect_three">
					当前还没有字</view>
				<view class="collect_four">
					快去做任务集字吧</view>
			</view>
			<view wx:elif="{{zigroup}}">
				<view style="height:38rpx;"></view>
				<view class="collect_five"
					style="background-image : url({{tiange}});background-size: cover;color: {{zigroup.color}};background-color:{{zigroup.bgColor}};">
					{{zigroup.word}}</view>
				<button open-type='share' data-zigroup="{{zigroup}}" wx:if="{{zigroup.num==0}}"
					class="collect_six">请好友送字</button>
				<button wx:elif="{{zigroup.num>0}}" bindtap="goShareTap" class="collect_six">送给好友</button>
			</view>
			<!--字已集满-->
			<view wx:elif="{{fullSign||fullState}}">
				<view class="collect_seven">
					恭喜你</view>
				<view class="collect_eight">
					集字成功</view>
				<button class="collect_nine" bindtap="toExchange">立即兑换</button>
				<template wx:if="{{state2}}" is="getExchange" data="{{...item}}" />
			</view>
			<!--有字但未选择-->
			<view wx:else>
				<view class="collect_ten">
					集满奖励
				</view>
				<view class="collect1_one">
					<image wx:if="{{result.collectConfig.prizeType==1}}" src="{{money}}" style="width:100%;height:auto"
						mode="widthFix"></image>
					<image wx:elif="{{result.collectConfig.prizeType==2}}" src="{{intergral}}" style="width:100%;height:auto"
						mode="widthFix"></image>
					<image wx:elif="{{result.collectConfig.prizeType==3}}" src="{{coupon}}" style="width:100%;height:auto"
						mode="widthFix"></image>
				</view>
				<view class="collect1_two">
					{{result.collectConfig.prizeName}}</view>
			</view>
		</view>
		<view class="collect1_three">
			<block wx:for="{{ziArray}}" wx:key="index">
				<view class="collect1_four"
					style="background-image : url({{tiange}});background-size: cover;color: {{item.color}};background-color:{{item.bgColor}};"
					bindtap="ziClick" data-zigroup="{{item}}">
					{{item.word}}<view wx:if="{{item.num}}" class="collect1_five">{{item.num}}</view>
				</view>
			</block>
		</view>
	</view>
	<view class="colStatus">
		<view class="collect1_six">
			— 做任务集字 —
    </view>
		<view class="collect1_seven">
			<view style="width:68rpx;height:100%;">
				<image src="{{sign}}" style="width:100%;height:auto;" mode="widthFix"></image>
			</view>
			<view class="collect1_eight">
				<view class="collect1_nine">
					集字签到</view>
				<view class="collect1_ten">
					每日签到，即可获取一个字</view>
			</view>
			<button wx:if="{{result.todaySingin==1}}" class="collect2_one" style="opacity: 0.3;">已签到</button>
			<button wx:else class="collect2_one" style="opacity: 1;" bindtap="toSign">去签到</button>
			<template wx:if="{{state1}}" is="getWord" data="{{...item}}" />
		</view>
    <block wx:for="{{supplyData}}" wx:key="index">
      <view class="collect1_seven">
        <view style="width:68rpx;height:100%;">
          <block wx:if="{{item.eventType == 1}}">
            <image src="{{r_consuption}}" style="width:100%;height:auto;" mode="widthFix"></image>
          </block>
          <block wx:if="{{item.eventType == 2}}">
            <image src="{{r_evaluate}}" style="width:100%;height:auto;" mode="widthFix"></image>
          </block>
          <block wx:if="{{item.eventType == 3}}">
            <image src="{{r_vip}}" style="width:100%;height:auto;" mode="widthFix"></image>
          </block>
          <block wx:if="{{item.eventType == 4}}">
            <image src="{{r_charge}}" style="width:100%;height:auto;" mode="widthFix"></image>
          </block>
          <block wx:if="{{item.eventType == 5}}">
            <image src="{{r_refill}}" style="width:100%;height:auto;" mode="widthFix"></image>
          </block>
        </view>
        <view class="collect1_eight">
          <block wx:if="{{item.eventType == 1}}">
            <view class="collect1_nine">
              消费
            </view>
            <view class="collect1_ten">
              购物下单，即可获取一个字
            </view>
          </block>
          <block wx:if="{{item.eventType == 2}}">
            <view class="collect1_nine">
              评论
            </view>
            <view class="collect1_ten">
              评论订单，即可获取一个字
            </view>
          </block>
          <block wx:if="{{item.eventType == 3}}">
            <view class="collect1_nine">
              成为会员
            </view>
            <view class="collect1_ten">
              注册成为会员，即可获取一个字
            </view>
          </block>
          <block wx:if="{{item.eventType == 4}}">
            <view class="collect1_nine">
              充值
            </view>
            <view class="collect1_ten">
             会员卡充值，即可获取一个字
            </view>
          </block>
          <block wx:if="{{item.eventType == 5}}">
            <view class="collect1_nine">
              完善资料
            </view>
            <view class="collect1_ten">
               完善资料，即可获取一个字
            </view>
          </block>
        </view>
        <block wx:if="{{item.eventType == 1}}">
          <button class="collect2_one" style="opacity: 1;" bindtap="toIndexClick">去购物</button>
        </block>
        <block wx:if="{{item.eventType == 2}}">
          <button class="collect2_one" style="opacity: 1;" bindtap="toOrderClick">去评论</button>
        </block>
         <block wx:if="{{item.eventType == 3}}">
          <button class="collect2_one" style="opacity: 1;" bindtap="toCardClick">去完成</button>
        </block>
         <block wx:if="{{item.eventType == 4}}">
          <button class="collect2_one" style="opacity: 1;" bindtap="toCardClick">去充值</button>
        </block>
         <block wx:if="{{item.eventType == 5}}">
          <button wx:if="{{isFill}}" class="collect2_one" bindtap="toProfileClick">去完善</button>
          <button wx:else class="collect2_one" style="opacity:0.3;">已完善</button>
        </block>
        <template wx:if="{{state1}}" is="getWord" data="{{...item}}" />
      </view>
      <!--<view class="s_w collect1_seven">
        <view>
          <image style="width:30rpx;height:30rpx;" mode="widthFix" src="{{item.imagesUrl}}"></image>
          <label>{{item.businessName}}</label>
        </view>
        <block wx:if="{{item.eventType == 1}}">
          <label bindtap="toIndexClick" class="act_btn">去购买</label>
        </block>
        <block wx:if="{{item.eventType == 2}}">
          <label bindtap="toOrderClick" class="act_btn">去评论</label>
        </block>
        <block wx:if="{{item.eventType == 3}}">
          <label bindtap="toCardClick" class="act_btn">成为会员</label>
        </block>
        <block wx:if="{{item.eventType == 4}}">
          <label bindtap="toCardClick" class="act_btn">去充值</label>
        </block>
        <block wx:if="{{item.eventType == 5}}">
          <label bindtap="toProfileClick" class="act_btn">去完善</label>
        </block>
      </view>-->
    </block>
	</view>
	<view
		class="collect2_three">
		<view
			class="collect2_two"
			bindtap="toCollectStrategy">
			活动规则
		</view>
		<view style="width:26rpx;"></view>
		<view
			class="collect2_two"
			bindtap="toRecord">
			赠送记录
		</view>
	</view>
</view>
<view style="background:#f5f5f5;">
  <view style="height:80rpx;line-height:80rpx;padding-left:25rpx;">推荐商品</view>
   <view class="pic_two">
        <block wx:key="unique" wx:for="{{indexGoodsList}}" wx:for-item="goods"
          wx:for-index="goodsIndex">
          <view class="commodity_box3" style="border-radius:20rpx">
            <image lazy-load='true'
              style="border-top-left-radius:20rpx;border-top-right-radius:20rpx;"
              bindtap="imageClick" data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
            <view class="goods_desc">
              <view class="desc_title">
                {{goods.commodityName}}
              </view>
              <view class="desc_price" style="display:flex;justify-content:space-between">
                <view class="price_l">
                  <label class="price_tag">￥</label>
                  <label class="price_inner"
                    hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                  <label class="price_inner">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                  <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                    <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0&&indexConfig.goodsBean.shopCartStyle != 3)?false:true}}"
                      class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                  </block>
                  <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                    <label  class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                  </block>
                  <block
                    wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0&& indexConfig.goodsBean.shopCartHidden==2}}">
                    <label>
                      <text class="promotionDesc">{{goods.promotionList[0].promotionName}}</text>
                    </label>
                  </block>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
</view>
<view class="black_bg" hidden="{{shareIsShow}}"></view>
<view class="shareBox" hidden="{{shareIsShow}}">
	<icon class="closeTemp" bindtap="closeShareBindTap" type="clear" size='26' color='#303337' />
	<view style="font-size:30rpx;line-height:80rpx;">将该字转赠给好友</view>
	<view>请注意：该字分享后将无法撤回</view>
	<view style="margin-top:40rpx">
		<button open-type="share" data-zigroup="{{zigroup}}" style="background:#fff;line-height:40rpx">
			<image src="{{wechatShareImg}}" style="width:80rpx;height:80rpx;"></image>
			<view style="text-align:center;font-size:28rpx">微信</view>
		</button>
	</view>
</view>