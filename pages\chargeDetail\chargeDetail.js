// pages/chargeDetail/chargeDetail.js
var app = getApp();
var TimeUtil = require('../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    offSet: 1,
    pageSize: 20,
    cardNo: "",
    searchLoading: false,
    consumptionShowList: [],
    payWayArray: ["", "充值", "消费", "退款"],
    payWayArrayNo: ["", "+", "-", "+"],
    date1: '',
    date2: '',
    rightMark: app.imageUrl+"rightMark.png"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var cardNo = options.cardNo;
    this.setData({
      cardNo: cardNo,
      date1: that.getLocalDate(),
      date2: that.getLocalDate()
    });
    this.queryVipCardConsumptionWater(cardNo);
  },
  consumptionDetailClick:function(){
    app.navigateToPage("/pages/consumptionDetail/consumptionDetail");
  },
  getLocalDate: function () {
    var timestamp = Date.parse(new Date());
    var date = new Date(timestamp);
    //获取年份  
    var Y = date.getFullYear();
    //获取月份  
    var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
    //获取当日日期 
    var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    return Y + "-" + M + "-" + D;
  },
  queryVipCardConsumptionWater: function (cardNo) {
    var that = this;
    wx.request({
      url: app.projectName + '/vipCard/queryVipCardConsumptionWaterNew',
      data: {
        "page": that.data.offSet,
        "pagesize": that.data.pageSize,
        "cardNo": cardNo,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "startTime": that.data.date1,
        "endTime": that.data.date2
      },
      success: function (res) {
        var consumptionList = res.data.consumptionList;
        var oldconsumptionList = that.data.consumptionShowList;
        if (oldconsumptionList != null && oldconsumptionList.length > 0) {
          consumptionList = oldconsumptionList.concat(consumptionList);
        }
        if (res.data.consumptionList == null || res.data.consumptionList.length == 0) {
          that.setData({
            searchLoading: false
          });
        } else {
          that.setData({
            searchLoading: true
          });
        }
        if (consumptionList != null && consumptionList.length > 0) {
          for (var i = 0; i < consumptionList.length; i++) {
            consumptionList[i].createTime = TimeUtil.getSmpFormatDateByLong(consumptionList[i].createTime, true);
          }
        }
        that.setData({
          consumptionShowList: consumptionList
        });
      }
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        offSet: that.data.offSet + 1
      });
      that.queryVipCardConsumptionWater(that.data.cardNo);
    }
  },
  bindDateChange1: function (e) {
    this.setData({
      date1: e.detail.value
    })
  },
  bindDateChange2: function (e) {
    this.setData({
      date2: e.detail.value
    })
  },
  //搜索
  timeSearchFun: function () {
    this.setData({
      offSet: 1,
      consumptionShowList: []
    });
    this.queryVipCardConsumptionWater(this.data.cardNo);
  }
})