/* pages/chooseCard/chooseCard.wxss */
.cardWrap{
  padding:40rpx 0;
  border-radius:10rpx;
  width:90%;
  margin:0 auto;
  margin-bottom:20rpx;
  position:relative;
  height:240rpx;
}
.cardWrap image{
  border-radius:10rpx;
  z-index:-1;
  position:absolute;
  width:100%;
  top:0;
  left:0;
  right:0;
  bottom:0;
}
.card_title{
  font-size:26rpx;
  margin-left:20rpx;
  text-align:center;
  color:#fff;
}
.card_title text{
  color:#fff;
  float:right;
  margin-right:20rpx;
}
.card_no{
  margin-top:20rpx;
  font-size:26rpx;
  text-align:left;
  margin-left:20rpx;
}
.amount_wrap{
  margin:40rpx 0;
  text-align:center;
  color:#fff;
}
.remain_amount{
  font-size:26rpx;
}
.remain_amount text{
  font-size:32rpx;
  font-weight:bold;
}
.remain_score{
  font-size:26rpx;
  margin-left:40rpx;
}
.remain_score text{
  font-size:32rpx;
  font-weight:bold;
}
.validDate{
  font-size:26rpx;
  margin-top:20rpx;
  margin-right:20rpx;
  text-align:right;
  color:#fff;
}
.card_id{
  text-align:left;
  padding-top:20rpx;
  padding-left:20rpx;
  color:#fff;
  font-size:24rpx;
}
.cardTip{
  font-weight:bold;
  text-align:center;
  font-size:32rpx;
  padding-bottom:40rpx;
}
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom:0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.scoreWrap{
  z-index:999;
  text-align:center;
  position:fixed;
  background:#f0f0f0;
  bottom:0;
  left:0;
  right:0;
}
.score_title{
  background:#fff;
  padding:30rpx 0;
  margin-bottom:30rpx;
}
.score_avail{
  text-align:left;
  text-indent:40rpx;
  background:#fff;
  padding:40rpx 0;
  border-bottom:1rpx solid #c6c7c8;
}
.score_switch{
  text-align:left;
  text-indent:40rpx;
  background:#fff;
  padding:40rpx 0;
}
.score_switch input{
  vertical-align:middle;
  display:inline-block;
}
.score_oper{
  background:#fff;
  margin-top:30rpx;
  height:100rpx;
  line-height:100rpx;
}
.score_confirm{
  display:inline-block;
  height:100rpx;
  text-align:center;
  width:50%;
  background:#FF7E00;
  color:#fff;
}
.score_cancel{
  display:inline-block;
  height:100rpx;
  text-align:center;
  width:50%;
}