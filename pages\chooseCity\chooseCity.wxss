/* pages/retail/chooseCity/chooseCity.wxss */
page {
  background: #F2F2F2;
}

.c_w {
  padding: 30rpx 20rpx;
}

.hotCity label {
  width: 200rpx;
  height: 50rpx;
  line-height: 50rpx;
}

.cityWrap {
  display: flex;
  flex-wrap: no-wrap;
}

.l_city {
  background: rgba(255, 126, 0, 0.16);
  width: 180rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 30rpx;
  color: #FF7700;
  margin: 0 20rpx;
  border-radius: 10rpx;
  border: 1px solid #FF7700;
  margin-bottom: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.l_city image {
  margin-right: 10rpx;
  width: 26rpx;
}

.cityWrap .oneCity {
  background: #fff;
  width: 180rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 30rpx;
  color: #525252;
  margin: 0 20rpx;
  text-align: center;
  border-radius: 10rpx;
}

.hotCity {
  color: #919398;
  font-size: 30rpx;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
}

.b_wrap {
  background-image: linear-gradient(#FE5632, #FEE8C2, #FFEDCF);
}

.storeWrap .oneStore {
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 28rpx 20rpx 28rpx 0;
}

.oneStore image {
  width: 70rpx;
}

.address_detail {
  flex-direction: column;
  display: flex;
  flex: 1;
}

.address_distance {
  width: 200rpx;
  text-align: right;
  font-size: 29rpx;
  color: #FF7E00;
}

.imageWrap {
  width: 120rpx;
  text-align: center;
}

.a_title {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.a_content {
  font-size: 26rpx;
  color: #B2B2B2;
}

.top_wrap {
  display: flex;
  align-items: center;
  padding: 40rpx 20rpx 30rpx 20rpx;
}

.top_wrap label {
  font-size: 30rpx;
  color: #fff;
  margin-left: 10rpx;
  margin-right: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 40rpx;
}

.icon_l {
  width: 40rpx;
  height: 44.6rpx;
}

.icon_r {
  width: 34rpx;
  height: 18.8rpx;
  margin-top: 8rpx;
}

.x_title {
  font-size: 64rpx;
  margin: 40rpx 0 20rpx;
  text-align: center;
  color: #FFF6C3;
}

.sub_title {
  font-size: 32rpx;
  margin: 20rpx 0 40rpx;
  text-align: center;
  color: #FFF6C3;
}