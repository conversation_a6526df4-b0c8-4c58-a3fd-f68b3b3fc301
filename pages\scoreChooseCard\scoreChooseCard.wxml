<!--强制登录-->
<view hidden="{{boundTelephoneHidden}}" class="bl_bg" style="z-index:1;"></view>
<view hidden="{{boundTelephoneHidden}}" style="position:absolute;z-index:999;top:20%;width:90%;background:#fff;margin-left:5%;border-radius:10rpx;">
	<!-- <view class="topWrap">
    <image src="{{defaultLogo}}">
    </image>
    <view>{{storeName}}</view>
  </view>
  <view style="text-align:center;font-size:30rpx;">您还未绑定手机号</view> -->
	<view class="topWrap">
		<view style="width:200rpx;margin:0 auto;">
			<open-data type="userAvatarUrl" style="border-radius:50%;"></open-data>
		</view>
		<view>
			<open-data type="userNickName"></open-data>
		</view>
	</view>
	<view style="width:80%;margin:0 auto">
		<view class="fillCode">
			<input placeholder='请输入手机号' type='number' bindinput="bindVipCardTelephoneBindInput" style="width:300rpx;" maxlength="11"></input>
			<text class="smsButton darkBlack" bindtap='smsBindTap'>{{secondDesc}}</text>
		</view>
		<view class="fillCode" style="margin-top:20rpx;">
			<input placeholder='请输入验证码' maxlength="6" type='number' bindinput="smsCodeBindInput"></input>
		</view>
	</view>
	<!-- <view style="text-align:center;font-size:30rpx;">您还未绑定手机号</view> -->
	<view style="text-align:center;color:#666;font-size:26rpx;margin-top:20rpx;">为让你更好的使用会员卡，请先绑定手机号</view>
	<view style="width:600rpx;margin:0 auto;">
		<button class='confirm_btn' style="background:#999;color:#fff;" bindtap='noBoundTelephoneBindTap'>暂不绑定</button>
		<button class='confirm_btn' style="background:#07c160;color:#fff;" bindtap="nowBoundTelephoneBindTap">立即绑定</button>
	</view>
</view>
<!--强制登录-->
<view style="margin-top:60rpx;">
	<block wx:if="{{vipCardTelephone.length>0}}">
		<view class="cardTip">选择支付会员卡</view>
		<block wx:key="unique" wx:for="{{cardList}}" wx:for-item="card" wx:for-index="index">
			<!--单张卡-->
			<view class="cardWrap" bindtap="chooseCardBindTap" data-index="{{index}}">
				<image src="{{card_bg}}" mode="widthFix"></image>
				<view class="card_title" style="text-align:center">{{companyName}}
				</view>
				<view class="card_id">No.{{card.cardId}}</view>
				<view class="amount_wrap">
					<label class="remain_amount">余额：
						<text>{{card.spareCash}}</text>
					</label>
					<label class="remain_score">积分：
						<text>{{card.integral}}</text>
					</label>
				</view>
				<block wx:if="{{card.source==1}}">
					<view class="validDate">有效期：{{card.validityEndDate}}</view>
				</block>
				<block wx:elif="{{card.source==0}}">
					<view class="validDate">有效期：长期</view>
				</block>
			</view>
			<!--单张卡-->
		</block>
	</block>
	<block wx:else>
		<view class="cardTip">请您先绑定手机号码</view>
		<view style="text-align:center;">
			<button style="background-color:#FF7E00;color:#fff;margin:0 auto;line-height:60rpx;height:60rpx;font-size:28rpx;border-radius:35rpx;width:300rpx;" class='confirm_btn' open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">获取手机号 </button>
		</view>
	</block>
</view>