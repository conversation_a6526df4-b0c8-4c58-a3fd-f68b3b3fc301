@import "../CouponTem/CouponTem.wxss";
page {
  background: #fff;
  font-size:0;
}
/*卡券**/
.cardImg {
  z-index: 11;
  width: 100%;
  position: fixed;
}

.coupon_inner {
  width: 100%;
  max-height: 500rpx;
  background: #fc6366;
  padding: 5rpx 0 40rpx;
  overflow-y: auto;
  margin-top: -25rpx;
}

.pop_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #000;
  opacity: 0.6;
}
/*新加的样式*/
.example {
  display: block;
  height: 60rpx;
  /*background: #fff;*/
  padding-top: 20rpx;
}

.marquee_box {
  position: relative;
  padding-left: 80rpx;
}

.marquee_box image {
  padding-left: 30rpx;
  padding-right: 30rpx;
  /*background: #fff;*/
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  width: 50rpx;
  height: 50rpx;
}

.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
  color: #FF7E00 !important;
}

/*加入购物车*/
.scroll_blo {
  width: 100%;
  position: fixed;
  bottom: 96rpx;
  left: 0;
  right:0;
  z-index: 99999;
  background:#fff;
  border-top-left-radius:30rpx;
  border-top-right-radius:30rpx;
  padding-bottom:60rpx;
  /*padding-bottom: 46px;*/
}

.addgoods_box {
  width: 100%;
  height: 60px;
  /* border-bottom: 1px solid #e5e5e5; */
  background: #fff;
}

.addgoods_pic {
  width: 86px;
  height: 86px;
  background: #fff;
  position: absolute;
  top: -43px;
  left: 15px;
  border: 1px solid #ccc;
  /* overflow: hidden; */
}

.addgoods_pic image {
  width: 100%;
  height: 100%;
}

.addgoods_title,
.addgoods_price {
  height: 20px;
  overflow: hidden;
  white-space: nowrap;
  padding-right: 30px;
  text-overflow: ellipsis;
  padding-top: 3px;
}

.addgoods_title {
  font-size: 14px;
  background: #fff;
  color: #000;
}

.addgoods_price {
  color: #FF7E00;
  font-size: 13px;
}

.goods_classify {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 10px 5px 10px;
}

.goods_classify label {
  margin-bottom: 10px;
  line-height: 28px;
  font-size: 14px;
}

.goods_classify view text {
  float: left;
  padding: 0 16px;
  color: #333;
  background: #ececec;
  font-size: 12px;
  margin-bottom: 8px;
  margin-right: 10px;
  line-height: 28px;
  border-radius: 5px;
}

.addgoods_number {
  width: 100%;
  background: #fff;
  padding-top: 15px;
  padding-bottom: 15px;
  /**border-bottom: 1px solid #e5e5e5;**/
}

.limited_quantity {
  font-size: 13px;
  float: left;
  padding-left: 15px;
  padding-top: 5px;
}

.stock {
  font-size: 13px;
  float: right;
  margin-right: 5px;
  margin-top: 4px;
}

.plus_minus {
  float: right;
  padding-right: 15px;
  margin-top: 3px;
}

.minus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 22px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 24px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_minus input {
  width: 50px;
  height: 26px;
  line-height: 10px;
  background: #e4e4e4;
  float: left;
  border: none;
  font-size: 13px;
  margin: 0 5px;
  text-align: center;
  color: #333;
}

.pay-add-to-shoppingcart {
  display: block;
  padding: 0px;
  font-size: 14px;
  line-height: 44px;
  height: 44px;
  border-radius: 0px;
  flex: 1;
  color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
}

.page-dialog-close {
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 10;
  display: block;
  width: 20px;
  height: 20px;
}

.code {
  width: 80%;
  margin-left: 5%;
}

.outCode {
  width: 55% !important;
}


@keyframes around {
  from {
    margin-left: 100%;
  }

  to {
    /* var接受传入的变量 */
    margin-left: var(--marqueeWidth--);
  }
}

.marquee_container {
  /*background-color: #fff;*/
  height: 40rpx;
  line-height: 40rpx;
  position: relative;
  width: 100%;
}

.a_text {
  color: #909090;
  font-size: 28rpx;
  /*display: inline-block;*/
  white-space: nowrap;
  overflow: hidden;
  animation: around var(--allTs--) infinite linear;
}

.activity_time {
  /*width: 100%;*/
  /* float: right; */
  /*background: #FF7E00;
  height: 70rpx;*/
  padding-left: 10px;
  text-align: center;
  color: #583512;
  vertical-align:middle;
}

.activity_time>view:first-child {
  color: #fff;
  text-align: center;
  width: 100%;
  display: block;
  font-size: 26rpx;
  background: none;
}
.activity_time text{
  padding:0 10rpx;
  color:#FF7E00;
  font-size:26rpx;
}
.activity_time .clock {
  font-size: 28rpx;
  background: #FF7E00;
  color: #fff;
  padding: 0 8rpx;
  border-radius: 8rpx;
  height: 42rpx;
  line-height: 42rpx;
}

/* 团购活动start */
.groupbuy {
  width: 750rpx;
  background-color: #fff;
  font-size: 28rpx;
  margin:10rpx 0;
}

.groupbuy .groupTop {
  height: 100rpx;
  line-height: 100rpx;
  padding: 0 20rpx;

}

.groupbuy .groupCenter {
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
}

.groupbuy .groupCenter .groupGoods {
  width: 225rpx;
  font-size: 28rpx;
}

.groupbuy .groupCenter .groupGoods .goodsName {
  color: #525252;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden
}

.personal_more {
  float: right !important;
  width: 14px !important;
  height: 14px !important;
  margin-top: 16px !important;
  margin-right: 0 !important;
}
/* 团购活动end */


/*搜索框模块*/
.s_box{
  display:flex;
  padding:20rpx 25rpx;
  background:#f2f2f2;
  align-items: center;
}
.s_box .s_inner{
  display:flex;
  flex:1;
  height:70rpx;
  border-radius:40rpx;
  background:#fff;
  border:1px solid #ddd;
  align-items: center;
}
.s_inner icon{
  margin:0 30rpx;
}
.s_inner text{
  font-size:28rpx;
  color:#666;
}
.s_box image{
  width:60rpx;
  margin-left:14rpx;
}
/*搜索框模块*/
/*中部导航模块*/
.navi_box{
   display:flex;
   align-items:center;
   justify-content: space-around;
}
.navi_box image{
  width:120rpx;
  height:120rpx;
}
.navi_box .navi_text{
  font-size:28rpx;
  text-align:center;
}
/*中部导航模块*/
/*活动模块*/
.promotionWrap{
  width:700rpx;
  margin:0 auto;
}
.promotionTop{
  display:flex;
  justify-content:space-between;
  font-size:29rpx;
  color:#666;
  padding:20rpx 0;
  justify-items: center;
}
.promotionWrap .oneGoods{
  display:flex;
  margin-bottom:20rpx;
  background:#fff;
  padding-right:20rpx;
}
.promotionWrap .oneGoods image{
  width:240rpx;
  height:240rpx;
}
.promotionWrap .oneGoods .goodsDetail{
  display:flex;
  flex-direction: column;
  padding:20rpx 0;
  justify-content: space-between;
}
.promotionWrap .oneGoods .one_b{
  display:flex;
  justify-content: space-between;
  align-items: center;
}
.promotionWrap .oneGoods .goodsText{
  padding:6rpx 18rpx;
  border-radius:6rpx;
  background:#fc6366;
  color:#fff;
  font-size:28rpx;
}
.goodsText{
  padding:6rpx 20rpx;
  border-radius:30rpx;
  background:#FF7E00;
  color:#fff;
  font-size:26rpx;
}
/*一排两个*/
.pic_two{
  display:flex;
  width:700rpx;
  margin:0 auto;
  flex-wrap: wrap;
}
.pic_two > view:nth-child(2n+1){
  margin-right:20rpx;
}
.pic_two  .pic_two_goods{
  width:340rpx;
  background:#fff;
  margin-bottom:20rpx;
}
.pic_two  .pic_two_goods image{
  width:340rpx;
  height:340rpx;
}
/*一排三个*/
.pic_three{
  display:flex;
  width:700rpx;
  margin:0 auto;
  flex-wrap: wrap;
}
.pic_three > view{
  margin-right:20rpx;
}
.pic_three > view:nth-child(3n){
  margin-right:0;
}
/*公用的*/
.goods_desc{
  padding:14rpx 10rpx;
}
.desc_title{
  font-size:28rpx;
  height:80rpx;
  overflow: hidden;
}
.desc_price{
  margin-top:20rpx;
}
.price_tag{
  color:#FF7E00;
  font-size:22rpx;
}
.price_inner{
  color:#FF7E00;
  font-size:32rpx;
}
.line_price{
  font-size:18rpx;
  text-decoration: line-through;
  color:#666;
  margin-left:6rpx;
}
.promotionDesc{
  display:inline-block;
  border-radius: 6rpx;
  padding: 0 10rpx;
  border: 1px solid #FF7E00;
  margin-left: 20rpx;
  font-size: 18rpx;
  color: #FF7E00;
  height:30rpx;
  line-height:30rpx;
}
/*直播模块*/
.cast_box{
  display:flex;
  margin:0 auto;
}
.cast_box image{
  margin-right:20rpx;
}
.cast_box .oneCast{
  flex:1;
}
.cast_box .oneCast image{
  width:100%;
}
/*转盘*/
.lottery_box{
  margin:0 auto;
}
/*优惠券模块*/
.coupon_box{
  display:flex;
  margin:0 auto;
}
.coupon_box view{
  margin-right:20rpx;
}
.coupon_box view:last-child{
  margin-right:0rpx;
}
.coupon_box .oneCoupon{
  flex:1;
}
.coupon_box .oneCoupon image{
  width:100%;
}
/*占位符*/
.place_box{
  background:transparent;
}
/*视频模块*/
.video_box{
  margin:0 auto;
}
.video_box video{
  width:100%;
} 
/*文本模块*/
.text_box{
  position:relative;
  margin:0 auto;
  padding:20rpx 0;
}
.text_box .moreText{
  position:absolute;
  right:40rpx;
  top:20rpx;
  font-size:26rpx;
  line-height:50rpx;
  color:#666;
}
/*商品模块*/
.commodity_box1{
  width:700rpx;
  margin:0 auto;
  background:#fff;
  margin-bottom:20rpx;
}
.commodity_box2{
  display:flex;
  width:700rpx;
  margin:0 auto;
  margin-bottom:20rpx;
  background:#fff;
}
.commodity_box2 image{
  width:220rpx;
  height:220rpx;
}
.commodity_box2 .box_one{
  flex:1;
  display:flex;
  padding-left:30rpx;
}
.commodity_box2 .goods_desc{
  display:flex;
  flex-direction: column;
  padding:20rpx 0;
  justify-content: space-between;  
}
.commodity_box3{
  width:340rpx;
  background:#fff;
  margin-bottom:20rpx;
}
.commodity_box3 image{
  width:340rpx;
  height:340rpx;
}
.commodity_box4{
  width:220rpx;
  background:#fff;
  margin-bottom:20rpx;
}
.commodity_box4 image{
    width:220rpx;
    height:220rpx;
}
.mode_slide {
  overflow: hidden;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.slide_wrap {
  width: 226rpx;
  display: inline-block;
  margin-left: 14rpx;
}

.slide_wrap image {
  width: 226rpx;
  height: 226rpx;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.mode_slide scroll-view {
  overflow: hidden;
}
/*图片导航模块*/
.picNav_box{
  display:flex;
  margin:0 auto;
}
.picNav_box .pic_box1{
  flex:1;
}
.pic_box1 image{
  width:100%;
}
.picNav_box .pic_box2{
  width:49%;
  margin-right:2%;
  height:400rpx;
}
.picNav_box .pic_box2 image{
  width:100%;
  height:100%;
}
.picNav_box .rightPic{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width:49%;
  height:400rpx;
}
.rightPic image{
  width:100%;
  height:190rpx;
}
.picNav_box .pic_box3{
  width:49%;
  height:400rpx;
}
.picNav_box .pic_box3 image{
  width:100%;
  height:100%;
}
.picNav_box .leftPic{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-right:2%;
  width:49%;
  height:400rpx;
}
.leftPic image{
  width:100%;
  height:190rpx;
}
/*公告模块*/
.annoucnePart{
  padding:0 25rpx;
  display:flex;
}
.annoucnePart image{
  width:50rpx;
}
.annoucnePart .announceDetail{
  flex:1;
  padding-left:25rpx;
}
/*sku被选中*/
.active_classify{
  background: #FFF8F9 !important;
  color: #FF7E00 !important;
  border: 1px solid #FF7E00;
  border-radius: 10rpx;
}
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
/*店铺导航*/
.location_store {
  line-height: 80rpx;
  height: 80rpx;
  
  /* position: fixed;
  top: 0;
  left: 0;
  right: 0; */
  z-index: 100;
}

.location_store image {
  width: 40rpx;
  vertical-align: middle;
  margin-left: 40rpx;
  margin-right: 16rpx;
}

.storeName {
  color: #999;
  font-size: 28rpx;
}

.storeChange {
  font-size: 28rpx;
  margin-right: 30rpx;
}
/*嵌套的template*/
.mark1{
  align-items:flex-end;
  position:absolute;
  bottom:0;
  left:0;
  right:0;
  display:flex;
}
.mark1_l{
  border:3rpx solid #db2f04;
  border-left:none;
  border-bottom:none;
  font-size:30rpx;
  z-index:9;
  border-radius:100rpx 100rpx 100rpx 0;
  width:100rpx;
  height:100rpx;
  background:#f5f5f5;
  color:#db2f04;
  text-align:center;
}
.mark1_l view{
  padding:10rpx;
  padding-left:20rpx;
}
.mark1_r{
  margin-left:-50rpx;
  padding-left:50rpx;
  display:flex;
  flex:1;
  height:50rpx;
  line-height:50rpx;
  font-size:30rpx;
  background:#db2f04;
  color:#fff; 
}
/*一排三个字号小一号*/
.commodity_box4 .mark1_l,.commodity_box2 .mark1_l,.slide_wrap .mark1_l,.oneGoods .mark1_l{
  border-radius:60rpx 60rpx 60rpx 0;
  width:60rpx;
  height:60rpx;
  font-size:18rpx;
}
.commodity_box4 .mark1_l view,.commodity_box2 .mark1_l view,.slide_wrap .mark1_l view,.oneGoods .mark1_l view{
  padding:4rpx;
  padding-left:10rpx;
}
.commodity_box4 .mark1_r,.commodity_box2 .mark1_r,.slide_wrap .mark1_r,.oneGoods .mark1_r{
  height:40rpx;
  line-height:40rpx;
  font-size:18rpx;
}
/**/
.mark2_1 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 0rpx;
  left: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  z-index:10;

}

.mark2_2 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 0rpx;
  left: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  z-index:10;
}

.mark2_3 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  z-index:10;
}

.mark2_4 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 0rpx;
  right: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  z-index:10;
}

.mark_1 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 30rpx;
  left: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.mark_2 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 30rpx;
  left: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.mark_3 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 30rpx;
  right: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.mark_4 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 30rpx;
  right: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.showTag{
  z-index:99999;position:absolute;background:#ddd;top:0;left:0;right:0;bottom:0;opacity:0.7;
}
.tag_content{
  /*display:flex;
  flex-wrap:wrap;
  align-items:center;
  justify-content:center;*/
  z-index:99999;
  position:absolute;
  top:10%;
  left:5%;
  width:90%;
  background:#fff;
  border-radius:20rpx;
  padding:20rpx 0;
}
.tag_content::after {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-bottom-color: #fff;
  top: -14px;
  left: var(--topLeft);
}
.tagInner{
  display:flex;
  justify-content: space-around;
  align-items: center;
  padding:0 10rpx;
}
.tagInner>view{
  align-items: center;
  flex:1;
  padding:12rpx 0rpx;
  background:#f6f6f6;
  margin:10rpx;
  text-align:center;
  font-size:24rpx;
  border-radius:10rpx;
}
.active_tag{
  background:#FFF8F9;
  border:1px solid #ff6600;
  border-radius:10rpx;
}
.g_t{
  font-size:22rpx;
  padding:2rpx 8rpx;
  border-radius:6rpx;
  color:#ff6600;
  border:1px solid #ff6600;
}
.box {
  position: fixed;
  right: 0;
  z-index: 999;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  flex-direction: column;
  width: 600rpx;
}
.arrow {
  width: 0;
  height: 0;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.75) transparent;
}
.body {
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 10px;
}
.tips {
  flex: 1;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
}
.logo {
  height: 42px;
  width: 42px;
  border-radius: 8px;
  margin-right: 10px;
}