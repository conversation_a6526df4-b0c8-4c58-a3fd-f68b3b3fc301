<view>
  <image class="shareBg" src="{{discountImg}}" mode="widthFix"></image>
  <view class="money">
    <view>
      <block wx:if="{{cardBean.cardType>=4&&cardBean.cardType<=6}}">
        <text style="font-size:40rpx">折</text>
      </block>
      <block wx:else>
        <text style="font-size:40rpx">￥</text>
      </block>
      <text style="font-size:70rpx;margin-right:10rpx">{{cardBean.discountAmount}}</text>优惠券
    </view>
    <view style="width:80rpx;font-size:40rpx;color:#FFA222;margin-right:144rpx;">立即领取</view>
  </view>
  <view class="condition">
    <block wx:if="{{cardBean.cardType>=4&&cardBean.cardType<=6}}">
      {{cardBean.fullAmount==0?"无门槛"+cardBean.discountAmount+"折扣":"满"+cardBean.fullAmount+"享受"+cardBean.discountAmount+"折扣"}}
    </block>
    <block wx:else>
      {{cardBean.fullAmount==0?"无门槛优惠"+cardBean.discountAmount+"元":"满"+cardBean.fullAmount+"优惠"+cardBean.discountAmount+"元"}}
    </block>
  </view>
  <block wx:if="{{state==0}}">
    <view class="submitBtn" bindtap="nowReceiveCardBindTap">立即领取</view>
  </block>
  <block wx:elif="{{state==1}}">
    <view class="submitBtn" style="opacity: 0.5;">券已被领完</view>
  </block>
  <block wx:elif="{{state==2}}">
    <view style="margin: 78rpx auto 0;font-size:36rpx;color:#525252;text-align:center;height: 100rpx;">优惠券已放入卡包<text
        bindtap="goCardTap" style="color:#FF7E00;">查看></text></view>
  </block>
  <view class="rule">
    <view style="text-align:center;">活动规则</view>
    <view style="font-size:26rpx;height:240rpx;overflow:auto">{{cardBean.limitContent}}</view>
  </view>
</view>