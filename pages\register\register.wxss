page {
  background: #f5f5f5;
  height: 100%;
  position: relative;
}

/*卡券**/

.cardImg {
  z-index: 11;
  width: 100%;
  position: fixed;
}
.coupon_inner{
  width:100%;
  max-height:500rpx;
  background:#fc6366;
  padding:5rpx 0 40rpx;
  overflow-y:auto;
  margin-top:-25rpx;
}

.pop_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.couponWrap{
  padding-bottom:30px;
  margin:0 auto;
  width:90%;
  position:fixed;
  top:14%;
  overflow-y:auto;
  left:5%;
  z-index:9999;
}

.circal_top {
  display: inline-block;
  width: 12px;
  height: 6px;
  position: absolute;
  top: -1px;
  left: calc(25% - 6px);
  background: #fc6366;
  border-radius: 0 0 6px 6px;
  border-top: none;
}

.circal_bottom {
  display: inline-block;
  width: 12px;
  height: 6px;
  position: absolute;
  bottom: -1px;
  left: calc(25% - 6px);
  background: #fc6366;
  border-radius: 6px 6px 0 0;
  border-bottom: none;
}

.couponTitle {
  color: #fff;
  text-align: center;
  height: 60px;
  line-height: 80px;
}

.onecircal {
  /*height: 80px;*/
  margin: 15px 20px;
  background: #fff;
  position: relative;
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.oneCoupon {
  height: 80px;
  float: left;
  border-right: 1rpx dashed #c7c7c7;
  width: 25%;
  line-height: 50px;
  margin: 0 auto;
}

.couponImage {
  padding: 15px 0px 0px 10px;
  display: inline-block;
  width: 80%;
  margin: 0 auto;
  text-align: center;
}

.imageShow {
  width: 50px;
  height: 50px;
}

.couponProduct {
  padding-top: 15px;
  padding-left: 10px;
  display: inline-block;
  vertical-align: top;
  overflow: hidden;
  width: 45%;
  float: left;
}

.productDetail {
  font-size: 13px;
  color: #444;
  height: 24px;
  overflow: hidden;
}

.couponMoney {
  margin-top: 5px;
  font-size: 13px;
  color: #fb6165;
  height: 24px;
  overflow: hidden;
}

.couponGift {
  font-size: 14px;
  color: #666;
}

.couponLimit {
  color: #666;
  font-size: 12px;
}

.lineNum {
  color: #444;
  font-size: 12px;
  text-decoration: line-through;
}

.couponRight {
  float: left;
  margin-top: 30px;
  width: 20%;
  padding: 10rpx 6rpx;
  border-radius: 30rpx;
  color: #fff;
  background: #FF7E00;
  text-align: center;
  font-size: 26rpx;
}

.couponText {
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  background: #fb6165;
  color: #fff;
  margin-left: 12px;
}

.coupon_append {
  color: #999;
  background: none;
  /*color:#fff;*/
}

.couponLine {
  font-size: 12px;
  color: #666;
  text-decoration: line-through;
}

.numDiscount {
  font-size: 26px;
  color: red;
}

.titleDiscount {
  font-size: 12px;
  color: red;
}

/**卡券结束*/

.list-block {
  width: 100%;
  height: 225px; /** height: 178px;**/
  background: #fff;
}

.list-block view {
  margin-left: 30px;
  height: 44px;
  border-bottom: 1px solid #ececec;
}

.list-block view:last-child {
  border-bottom: none;
}

.list-block view input {
  line-height: 44px;
  height: 44px;
  font-size: 15px;
}

.sms_verification {
  width: 55%;
  float: left;
}

.sms_btn {
  width: 30%;
  height: 30px;
  margin-top: 6px;
  line-height: 30px;
  border: 1px solid #FF7E00;
  color: #FF7E00;
  font-size: 12px;
  text-align: center;
  display: block;
  margin-right: 15px;
  position: absolute;
  right: 0;
}

.user_agreement {
  width: 94%;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
  height: 40px;
  line-height: 40px;
  font-size: 13px;
}

.checkbox {
  color: #333;
  line-height: 26px;
}

.checkbox label {
  color: #FF7E00;
}

.confirm_btn, .confirm_btn2 {
  width: 92%;
  margin: 0 auto;
  height: 48px;
  line-height: 48px;
  font-size: 15px;
  border: none !important;
}

.confirm_btn {
  color: #fff;
  background: #FF7E00;
}

.confirm_btn2 {
  color: #333;
  background: #d9d9d9;
}

.existing_account {
  text-align: center;
  color: #FF7E00;
  display: block;
  margin-top: 10px;
  font-size: 14px;
  text-decoration: underline;
}

/**黑色背景**/

.black_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

/**协议弹框**/

.protocol_box {
  width: 80%;
  height: 80%;
  background: #fff;
  border-radius: 8px;
  position: absolute;
  top: 10%;
  left: 10%;
  z-index: 20;
}

.close_btn {
  width: 15px;
  height: 15px;
  position: fixed;
  top: 10%;
  right: 10%;
  z-index: 22;
  background: #FF7E00;
  padding: 8px 15px;
  border-radius: 0 8px 0 0;
}

.protocol_title {
  display: block;
  text-align: center;
  line-height: 36px;
  font-weight: bold;
  color: #333;
  font-size: 15px;
  position: fixed;
  top: 10%;
  width: 80%;
  background: #fff;
  border-radius: 8px 8px 0 0;
}

.protocolContent {
  padding-top: 36px;
}

.protocolContent label {
  width: 90%;
  margin: 5px 5%;
  display: block;
  text-indent: 2em;
  line-height: 22px;
  color: #666;
  font-size: 13px;
}
