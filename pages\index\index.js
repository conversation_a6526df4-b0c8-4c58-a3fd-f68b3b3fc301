var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
var wxMarkerData = [];
var mstimeoutId;
var s_mstimeoutId;
Page({
  data: {
    img1: app.imageUrl + 'tongyong.png',
    img2: app.imageUrl + 'xinren.png',
    img3: app.imageUrl + 'integral.png',
    img4: app.imageUrl + 'xianjin.png',
    // img5: app.imageUrl + 'youhui1.png',
    // img5: app.imageUrl + 'PopupBackground.png',
    img5: app.imageUrl + 'background.png',
    img6: app.imageUrl + 'shengri.png',
    img7: app.imageUrl + 'zhifu.png',
    img8: app.imageUrl + 'hongbao.png',
    img9: app.imageUrl + 'hongbao2.png',
    img10: app.imageUrl + 'goVipCard.png',
    img11: app.imageUrl + 'popup1.png',
    goodsList: [],
    imgUrls: [
      app.imageUrl + 'activity/activity.png'
    ],
    bottom_logo: app.imageUrl + 'bottom.png',
    announce: app.imageUrl + 'announcement.png',
    three_icon: app.imageUrl + 'three_icon.png',
    newvip: app.imageUrl + 'icon1.png',
    material: app.imageUrl + 'icon2.png',
    scan: app.imageUrl + 'newscan.png',
    vipCode: app.imageUrl + 'newvipCode.png',
    brand: app.imageUrl + 'icon4.png',
    vip: app.imageUrl + 'icon5.png',
    item: app.imageUrl + 'activity/activity.png',
    newgoods_banner: app.imageUrl + 'activity/newgoods_banner.png',
    limit_banner: app.imageUrl + 'activity/limit_banner.png',
    fire: app.imageUrl + 'activity/fire.png',
    goods_mfy: app.imageUrl + 'production/mfy.png',
    goods_hlmm: app.imageUrl + 'production/hlmm.png',
    more: app.imageUrl + 'more.png',
    index_bg: app.imageUrl + 'index/index_bg.png',
    test_bg: '../../image/test11.jpg',
    event: app.imageUrl + 'event.png',
    autoplay: true, //是否自动切换
    //interval: 3000, //自动切换时间间隔
    duration: 500, //滑动动画时长
    circular: true, //是否采用衔接滑动
    tj_indicatorDots: true, //是否显示面板指示点
    xsqg_indicatorDots: true, //是否显示面板指示点
    xpss_indicatorDots: true, //是否显示面板指示点
    hgq_indicatorDots: true, //是否显示面板指示点
    indicatorColor: "rgba(0, 0, 0, .3)", //指示点颜色
    indicatorActiveColor: "#fa6a85", //当前选中的指示点颜色
    mode: 'widthFix',
    isFromBack: false,
    localCity: "", //当前地理位置
    winHeight: "", //窗口高度
    currentTab: 0, //预设当前项的值
    scrollLeft: 0, //tab标题的滚动条位置
    imgheights: [],
    current: 0,
    imgwidth: 750,
    xsqgGoods: [],
    hgqGoods: [],
    contentHidden: true,
    navigateList: [], //中部导航图标
    headnavList: [], //头部导航图标
    navigateNum: 0,
    navigateW: "20%",
    cardBanner: [],
    indexBanner: [],
    appletPromotionList: [],
    appletPromotionEvent: [],
    filterName: '',
    showGoodsList: [],
    isShowClassify: true,
    eventList: [],
    flag: false,
    shopperGift: app.imageUrl + 'shopperGift.png',
    cancelMark: app.imageUrl + 'cancelMark.png',
    vipGift: app.imageUrl + 'vipGift.png',
    vip_shopperGift: app.imageUrl + 'vipshopperGift.png',
    cardImageSrc: "",
    putCardHidden: true,
    giftBox: app.imageUrl + 'giftBox.png',
    online_service: app.imageUrl + 'online_service.png',
    indexListConfig: [],
    announctList: [],
    cardList: [],
    goodsTypeList: [],
    categoryIdList: [],
    commodityList: [],
    myAddress: app.imageUrl + 'locate.png',
    timeId: null,
    latitude: 0,
    longitude: 0,
    isShowStoreHidden: true,
    storeName: "",
    localUserAddress: "",
    localUserCity: "",
    addToShoppingCartHidden: true,
    liveImg: app.imageUrl + 'liveImg.png',
    isOnlineService: false,
    isOnLive: true,
    content_t: '', //内容
    size: 14, //宽度即文字大小
    marqueeW: 0,
    moveTimes: 12, //一屏内容滚动时间为8s
    allT: "0s",
    pickOrderStoreId: '',
    storeAddress: '',
    promotionIndex: 0,
    promotionJson: [],
    exchange_icon: app.imageUrl + 'exchange_icon.png',
    countDownList: [],
    countStartDownList: [],
    actStartTimeList: [],
    actEndTimeList: [],
    mainLeftColor: '',
    mainRightColor: '',
    isVipCardSwitch: false,
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
    s1: 0,
    s2: 0,
    s3: 0,
    pageArray: ["", "", "", "", "/pages/index/index", "/pages/goods_classify/goods_classify", "/pages/shopCart/shopCart", "/pages/accountManager/accountManager", "/pages/indexThree/indexThree", "/pages/person_coupon/person_coupon", "/pages/scoreMall/scoreMall", "/pages/onlineCast/onlineCast", "/pages/turntableActivity/turntableActivity", "/pages/vipCard/vipCard", "/pages/sign/sign", "/pages/couponCenter/couponCenter", "/pages/collect/collect","","/pages/giftCardExchange/giftCardExchange"],
    bg_color: "#ffffff",
    storeCardList: [],
    shop_cart1: app.imageUrl + 'shop_cart1.png',
    shop_cart2: app.imageUrl + 'shop_cart2.png',
    skuId: '',
    groupBuyUserId: 0,
    disLikeGoods: app.imageUrl + 'disLikeGoods.png',
    casualGoods: app.imageUrl + 'casualGoods.png',
    popularGoods: app.imageUrl + 'popularGoods.png',
    cheapGoods: app.imageUrl + 'cheapGoods.png',
    tagImg5: app.imageUrl + 'tagImg5.png',
    tagImg6: app.imageUrl + 'tagImg6.png',
    tagImg7: app.imageUrl + 'tagImg7.png',
    tagImg8: app.imageUrl + 'tagImg8.png',
    c_disLikeGoods: app.imageUrl + 'c_disLikeGoods.png',
    c_casualGoods: app.imageUrl + 'c_casualGoods.png',
    c_popularGoods: app.imageUrl + 'c_popularGoods.png',
    c_cheapGoods: app.imageUrl + 'c_cheapGoods.png',
    c_tagImg5: app.imageUrl + 'c_tagImg5.png',
    c_tagImg6: app.imageUrl + 'c_tagImg6.png',
    c_tagImg7: app.imageUrl + 'c_tagImg7.png',
    c_tagImg8: app.imageUrl + 'c_tagImg8.png',
    tagList: [],
    topLeft: '',
    topPage: '',
    tagHidden: true,
    currentTag: 0,
    exchangeTip: app.imageUrl + 'exchangeTip.png',
    scoreExchangeOrderGoodsHidden: true
  },
    /*getTagPress: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var tag = e.currentTarget.dataset.tag;
    that.setData({
      topLeft: e.detail.x,
      topPage: e.detail.y,
      tagHidden: false,
      tagCommodityId: id,
      currentTag: tag
    })
  },*/
  clientSignCommodityBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var code = e.currentTarget.dataset.code;
    var tagCommodityId = that.data.tagCommodityId;
    app.clientSignCommodity(tagCommodityId, code).then(res => {
      if (res) {
        that.querynewIndexTemplate(app.getExtCompanyId(), app.getExtStoreId());
      }
      that.setData({
        tagHidden: true
      })
    });
  },
  hiddenTagHiddenBindTap: function () {
    this.setData({
      tagHidden: true
    })
  },
  switchSelectStoreBindTap: function () {
    app.navigateToPage("/pages/chooseCity/chooseCity");
  },
  goCouponCenterBind: function () {
    app.navigateToPage('/pages/couponCenter/couponCenter');
  },
  commonJumpContent: function (relationType, content, type) {
    var that = this;
    if (relationType == 1) {/*关联的是某一个分类*/
      var id = "";
      if (type == 1) {
        id = content.relationContent.split("@")[0];
      }
      else if (type == 2) { /*处理的文本的跳转*/
        id = content.moreRelationContent.split("@")[0];
      }
      if (id != null && id.length > 0) {
        app.navigateToPage("/pages/secondCategory/secondCategory?categoryId=" + id);
      }
    }
    else if (relationType == 2) {/*关联的是商品或者多个商品*/
      var goodsEntity = content.wechatAppletBlockGoodsInfoEntity;
      if (goodsEntity != null && goodsEntity.length > 0) {
        if (goodsEntity.length == 1) {
          app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsEntity[0].goodsId);
        } else {
          var goodsJson = [];
          for (var i = 0; i < goodsEntity.length; i++) {
            var oneGoods = {};
            oneGoods["goodsId"] = goodsEntity[i].goodsId;
            oneGoods["sort"] = goodsEntity[i].sort;
            goodsJson.push(oneGoods);
          }
          app.navigateToPage("/pages/moreGoods/moreGoods?idJson=" + JSON.stringify(goodsJson));
        }
      }

    }
    else if (relationType == 3) {/*关联的是活动页面*/
      var id = "";
      if (type == 1) {
        id = content.relationContent.split("@")[0];
      }
      else if (type == 2) { /*处理的文本的跳转*/
        id = content.moreRelationContent.split("@")[0];
      }
      if (id != null && id.length > 0) {
        app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
      }
    }
    if (relationType > 3) { /*跳到对应页面*/
      if (relationType == 4) {
        wx.switchTab({
          url: "/pages/index/index"
        });
      }
      if (relationType == 5) {
        wx.switchTab({
          url: "/pages/goods_classify/goods_classify"
        });
      }
      else if (relationType == 6) {
        wx.switchTab({
          url: "/pages/shopCart/shopCart"
        });
      }
      else if (relationType == 7) {
        wx.switchTab({
          url: "/pages/accountManager/accountManager"
        });
      }
      else if (relationType == 17) {
        app.reLaunchToPage("/pages/find/find");
      }
      else if(relationType == 19){
        app.navigateToPage("/pages/sign/sign");
      }
      else if (relationType == 100) {/*代表跳转到图文素材页面*/
        var id = "";
        if (type == 1) {
          id = content.relationContent.split("@")[0];
        }
        else if (type == 2) { /*处理的文本的跳转*/
          id = content.moreRelationContent.split("@")[0];
        }
        app.navigateToPage("/pages/picMatter/picMatter?id=" + id);
      }
      else if(relationType == 12){
        id = content.relationContent.split("@")[0];
        app.navigateToPage("/pages/turntableActivity/turntableActivity?id="+id);
      }
      else {
        app.navigateToPage(that.data.pageArray[relationType]);
      }
    }
  },
  jumpPageBindTap: function (e) {
    var that = this;
    var content = e.currentTarget.dataset.content;
    var relationType = content.relationType;
    that.commonJumpContent(relationType, content, 1);
  },
  /*文本的跳转*/
  textClickJump: function (e) {
    var that = this;
    var content = e.currentTarget.dataset.content;
    var relationType = content.moreRelationType;
    if (content.moreWay == 0) {/*代表有展示更多*/
      that.commonJumpContent(relationType, content, 2);
    }
  },
  /*添加商品到购物车*/
  addCartClick: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/appletGoodsDetail',
      data: {
        "commodityId": id,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        that.setData({
          addToShoppingCartHidden: false,
          commodityUnitOtDefault: res.data.commodityUnitOtDefault,
          commodityUnitOmDefault: res.data.commodityUnitOmDefault,
          cutPriceOM: res.data.cutPriceOM,
          goodsOMUnit: res.data.goodsOMUnit,
          commodityVirtualStoreOM: res.data.commodityVirtualStoreOM,
          commodityMultiple: res.data.commodityMultiple,
          commodityVirtualStore: res.data.commodityVirtualStore,
          goodsPriceOM: res.data.goodsPriceOM,
          goodsOtUnit: res.data.goodsOtUnit,
          goodsPrice: res.data.goodsPrice,
          goodsDetail: res.data.list,
          showskuAllAttrList: res.data.skuList,
          cutPrice: res.data.cutPrice,
          showGoodsPrice: res.data.goodsPrice,
          showViewGoodsPrice: res.data.goodsPrice,
          buyOtNum: 1,
          buyOmNum: 0,
          commodityId: id
        });
        that.getGoodsDetailEvaluate(app.getExtStoreId(), app.getExtCompanyId());
      }
    })

  },
  hiddeAddToShoppingCart: function () {
    var that = this;
    that.setData({
      addToShoppingCartHidden: true,
      skuId: ''
    })
  },
  otMinusNumBindTap: function () {
    var that = this;
    if (that.data.buyOtNum > 0) {
      that.setData({
        buyOtNum: that.data.buyOtNum - 1
      });
    }
  },
  otPlusNumBindTap: function () {
    var that = this;
    that.setData({
      buyOtNum: parseInt(that.data.buyOtNum) + 1
    });
  },
  otNumBindInput: function (e) {
    var that = this;
    that.setData({
      buyOtNum: e.detail.value >= 0 ? e.detail.value : 0
    });
  },
  omMinusNumBindTap: function () {
    var that = this;
    if (that.data.buyOmNum > 0) {
      that.setData({
        buyOmNum: that.data.buyOmNum - 1
      });
    }
  },
  omPlusNumBindTap: function () {
    var that = this;
    that.setData({
      buyOmNum: that.data.buyOmNum + 1
    });
  },
  omNumBindInput: function (e) {
    var that = this;
    that.setData({
      buyOmNum: e.detail.value >= 0 ? e.detail.value : 0
    });
  },
  changeSKUBindTap: function (e) {
    var that = this;
    var name = e.target.dataset.name;
    var childname = e.target.dataset.childname;
    var updateSkuList = that.data.showskuAllAttrList;
    for (var i = 0; i < updateSkuList.length; i++) {
      if (name == updateSkuList[i].skuAttrName) {
        for (var j = 0; j < updateSkuList[i].skuAttrValueList.length; j++) {
          if (childname == updateSkuList[i].skuAttrValueList[j].skuAttrName) {
            updateSkuList[i].skuAttrValueList[j].isSelect = true;
          } else {
            updateSkuList[i].skuAttrValueList[j].isSelect = false;
          }
        }
      }
    }
    that.setData({
      showskuAllAttrList: updateSkuList
    });
    that.bachGoodsPrice();
  },
  bachGoodsPrice: function () {
    var that = this;
    that.setData({
      skuId: ''
    })
    var selectSKUArray = [];
    var skuList = that.data.showskuAllAttrList;
    for (var i = 0; i < skuList.length; i++) {
      for (var j = 0; j < skuList[i].skuAttrValueList.length; j++) {
        if (skuList[i].skuAttrValueList[j].isSelect) {
          var skuBean = {};
          skuBean.name = skuList[i].skuAttrName;
          skuBean.value = skuList[i].skuAttrValueList[j].skuAttrName;
          selectSKUArray.push(skuBean);
        }
      }
    }

    var skuGoodsPrice = "";
    var skuGroup = that.data.goodsDetail.skuList;
    for (var i = 0; i < skuGroup.length; i++) {
      var skuAttrList = skuGroup[i].skuAttrList;
      var count = 0;
      for (var j = 0; j < skuAttrList.length; j++) {
        var skuName = skuAttrList[j].skuName;
        var skuValue = skuAttrList[j].skuValue;
        var flag = false;
        for (var z = 0; z < selectSKUArray.length; z++) {
          var skuBean = selectSKUArray[z];
          if (skuName == skuBean.name && skuValue == skuBean.value) {
            flag = true;
            break;
          }
        }
        if (flag) {
          count++;
        } else {
          count--;
        }
      }
      if (count == selectSKUArray.length) {
        if (skuGroup[i].skuPrice != null && skuGroup[i].skuPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPrice;
        }
        if (skuGroup[i].skuPromotionPrice != null && skuGroup[i].skuPromotionPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPromotionPrice;
        }
        if (skuGroup[i].regionPriceList != null && skuGroup[i].regionPriceList.length > 0) {
          var goodsCount = that.data.buyCount + (that.data.buyOmCount * that.data.commodityMultiple);
          for (var k = 0; k < skuGroup[i].regionPriceList.length; k++) {
            var beginRegion = skuGroup[i].regionPriceList[k].beginRegion;
            var endRegion = skuGroup[i].regionPriceList[k].endRegion;
            var commoditySalePrice = skuGroup[i].regionPriceList[k].commoditySalePrice;
            if (goodsCount >= beginRegion && goodsCount <= endRegion) {
              skuGoodsPrice = commoditySalePrice;
              break;
            }
          }
        }
        var selectSkuStr = "";
        for (var m = 0; m < skuGroup[i].skuAttrList.length; m++) {
          selectSkuStr += skuGroup[i].skuAttrList[m].skuName + ":" + skuGroup[i].skuAttrList[m].skuValue + ";";
        }
        selectSkuStr = selectSkuStr.substring(0, selectSkuStr.length - 1);
        that.setData({
          selectSkuStr: selectSkuStr,
          showGoodsPrice: skuGoodsPrice,
          skuId: skuGroup[i].skuId,
          commodityVirtualStore: skuGroup[i].skuInventory
        });
        console.log(that.data.showGoodsPrice);
        break;
      }
    }
    if (that.data.skuId == '' && selectSKUArray.length == skuList.length && skuList.length != 0) {
      app.showModal({
        title: '提示',
        content: "您选择的分类暂无货"
      });
    }
  },
  /*商品添加到购物车*/
  addShoppingCartBindTap: function () {
    var that = this;
    var updateSkuList = that.data.showskuAllAttrList;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (updateSkuList != null && updateSkuList.length > 0) {
      if (that.data.skuId == "" || that.data.skuId == null) {
        app.showModal({
          title: '提示',
          content: "请选择规格"
        });
        return;
      }
    }
    var otBuyNum = that.data.buyOtNum;
    var omBuyNum = that.data.buyOmNum;
    if (otBuyNum + omBuyNum == 0) {
      app.showModal({
        title: '提示',
        content: "购买数量不能为0"
      });
      return;
    }
    if (!that.checkStock()) {
      return;
    };
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/addRetailShppingCart',
      data: {
        "a_num": otBuyNum,
        "omNum": omBuyNum,
        "a_id": that.data.commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "skuId": that.data.skuId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "recommendUserId": app.recommendData.recommendUserId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          that.setData({
            skuId: '',
            addToShoppingCartHidden: true,
            buyOtNum: 0,
            buyOmNum: 0
          })
          app.countRetailCartTotal();
          setTimeout(function () {
            if (typeof that.getTabBar === 'function' &&
              that.getTabBar()) {
              that.getTabBar().setData({
                shopCartNum: wx.getStorageSync('shopCartNum')
              })
            }
          }, 500)

          wx.showToast({
            title: '添加成功',
            duration: 1000
          })
        } else {
          wx.showToast({
            title: '添加失败',
            duration: 1000
          })
        }
      }
    })
  },
  /*立即购买*/
  nowPayClick: function (e) {
    var that = this;
    if (!that.checkStock()) {
      return;
    };
    var goodsDetail = that.data.goodsDetail;
    var unitList = goodsDetail.unitList;
    var commodityMoq = 0;
    var commodityWeightUnit = "";
    var commodityMultiple = 0;
    if (unitList.length == 1) {
      commodityMoq = goodsDetail.unitList[0].commodityMoq;
      commodityWeightUnit = goodsDetail.unitList[0].commodityWeightUnit;
    } else if (unitList.length == 2) {
      for (var i = 0; i < unitList.length; i++) {
        if (unitList[i].commodityWeightType == "OT") {
          commodityMoq = unitList[i].commodityMoq;
          commodityWeightUnit = unitList[i].commodityWeightUnit;
        } else if (unitList[i].commodityWeightType == "OM") {
          commodityMultiple = unitList[i].commodityMultiple;
        }
      }
    }
    if (parseInt(commodityMoq) > 0) {
      if (that.data.buyOtNum + (that.data.buyOmNum * commodityMultiple) < parseInt(commodityMoq)) {
        app.showModal({
          title: '提示',
          content: "商品购买数量必须满足" + commodityMoq + commodityWeightUnit + "!"
        });
        return;
      }
    }
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId);
    }
    else {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId(),
          "phone": app.getTelephone()
        },
        url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
        success: function (res) {
          var cardList = res.data.show_vipCardList;
          if (cardList != null && cardList.length > 0) {
            if (cardList.length == 1) { //一张会员卡自动选择
              var cardBean = cardList[0];
              app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&vipCardNo=" + cardBean.cardId + '&vipCardCode=' + cardBean.vipCardCode);
            } else if (cardList.length > 1) {
              //多张会员卡进行选择
              app.navigateToPage("/pages/chooseCard/chooseCard?payType=2&goodsId=" + commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId);
            }
          } else {
            //没有会员卡
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId);
          }
        }
      })
    }
  },
  /**
  * 发起拼团
  */
  nowGroupBuyBindTap: function (e) {
    var that = this;
    var groupBuyUserId = e.currentTarget.dataset.id;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (!that.checkStock()) {
      return;
    };
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          var skuList = that.data.showskuAllAttrList;
          if (skuList != null && skuList.length > 0) {
            if (that.data.skuId == "" || that.data.skuId == null) {
              app.showModal({
                title: '提示',
                content: "请选择规格"
              });
              return;
            }
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&groupBuyUserId=" + groupBuyUserId + "&joinPromotion=2" + "&isTuanGou=1");
          }
          else {
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + that.data.commodityId + "&storeId=" + app.getExtStoreId() + "&buyNum=" + that.data.buyOtNum + "&buyOmNum=" + that.data.buyOmNum + "&skuId=" + that.data.skuId + "&groupBuyUserId=" + groupBuyUserId + "&joinPromotion=2" + "&isTuanGou=1");
          }
        }
      }
    })
  },
  /**
  * 查询商品库存是否开启
  */
  getGoodsDetailEvaluate: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/getGoodsDetailEvaluate',
      data: {
        "commodityId": that.data.commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      success: function (res) {
        that.setData({
          salesVolume: res.data.salesVolume,
          overallStock: res.data.overallStock,
          stockBean: res.data.stockBean,
          isOnlineService: res.data.isOnlineService,
          fullCut: res.data.fullCut,
          orderEvaluateList: res.data.orderEvaluateList,
          shopCartNum: res.data.shopCartNum,
        });
      }
    })
  },
  /*控制库存时,检查商品是否超过了库存数量*/
  checkStock: function () {
    console.log('22222222222222222222222222');
    var that = this;
    var openStock = that.data.stockBean.openStock;
    var overallStock = that.data.overallStock;
    var buyNum = that.data.buyOtNum + (that.data.buyOmNum * that.data.commodityMultiple);
    var commodityVirtualStore = that.data.commodityVirtualStore;
    var flag = true;
    if (openStock || overallStock == 1) {
      if (parseInt(buyNum) > commodityVirtualStore) {
        app.showModal({
          title: '提示',
          content: "购买数量超过库存"
        });
        flag = false;
      }
    }
    return flag;
  },
  /*大转盘跳转*/
  lotteryClickBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var busiId = e.currentTarget.dataset.busid;
    app.navigateToPage('/pages/turntableActivity/turntableActivity?id='+busiId);
    /*wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var vipCardList = res.data.show_vipCardList
        if (vipCardList.length > 0) {
          app.navigateToPage('/pages/turntableActivity/turntableActivity?sceneType=3&vipCardNo=' + vipCardList[0].cardId);
        } else if (vipCardList.length == 0) {
          app.navigateToPage('/pages/vipCard/vipCard');
          wx.showToast({
            title: '暂无会员卡，先申请一张吧',
            icon: 'none',
            duration: 2000,
            mask: true
          })
        }

      }
    })*/

  },


  /*直播关联跳转*/
  castClickBindTap: function (e) {
    var that = this;
    var content = e.currentTarget.dataset.content;
    var jumpType = content.jumpType;
    if (jumpType == 1) {
      app.navigateToPage("/pages/onlineCast/onlineCast");
    }
  },
  /*优惠券的跳转*/
  couponClickBindTap: function (e) {
    var that = this;
    var content = e.currentTarget.dataset.content;
    var receiveType = content.receiveType;
    if (receiveType == 1) {/*代表跳转页面领取*/
      var cardType = content.relationType - 1;
      app.navigateToPage('/pages/couponTypeCenter/couponTypeCenter?cardType=' + cardType);
    }
    else if (receiveType == 2) {/*直接领取*/
      var cardId = content.relationContent.split("@")[0];
      var type = content.relationContent.split("&")[1];
      if (that.data.storeCardList.length > 0) {
        that.userGetCardBindTap(cardId, type);
      }
      else {

      }
    }
  },
  /**
 * 用户可领取的优惠券
 */
  showCouponBind: function (companyId, storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryCardByStoreId',
      data: {
        "sceneType": 1,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId,
        "is_free": 0
      },
      success: function (res) {
        wx.hideLoading();
        var cardList = res.data.returnList;
        if (cardList != null && cardList.length > 0) {
          for (var i = 0; i < cardList.length; i++) {
            cardList[i]['ruleSwitch'] = false;
            if (typeof (cardList[i]['supplierSettingReceiveStartTime']) != 'undefined') {
              var startTime = cardList[i]['supplierSettingReceiveStartTime'].split("")
              cardList[i]['supplierSettingReceiveStartTime'] = startTime[5] + startTime[6] + '-' + startTime[8] + startTime[9] + ' ' + startTime[11] + startTime[12] + ':' + startTime[14] + startTime[15]
            }

          }
          that.setData({
            hiddenCoupon: false,
            storeCardList: cardList
          });
        } else {
        }
      }
    })
  },
  /**
   * 免费领取券
   */
  userGetCardBindTap: function (cardId, type) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateId',
      data: {
        "companyId": app.getExtCompanyId(),
        "typeId": "1"
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              wx.showLoading({
                title: '卡券领取中...',
                mask: true
              })
              that.checkCardIsHave(cardId, type);
            }
          })
        } else {
          wx.showLoading({
            title: '卡券领取中...',
            mask: true
          })
          that.checkCardIsHave(cardId, type);
        }
      }
    })
  },
  /**
   * 检查卡券库存是否充足
   */
  checkCardIsHave: function (cardId, type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/checkCardIsHave',
      data: {
        "cardId": cardId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          var storeCardList = that.data.storeCardList;
          for (var i = 0; i < storeCardList.length; i++) {
            if (storeCardList[i].cardId == cardId) {
              if (storeCardList[i].type != 4) {
                that.freeGetCardBindTap(cardId, type);
              } else {
                that.nowPayCardBindTap(cardId);
              }
              break;
            }
          }
        } else {
          that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
          wx.hideLoading();
          that.setData({
            couponOut: false,
          });
        }
      }
    })
  },
  /**
  * 免费领取卡券
  */
  freeGetCardBindTap: function (cardId, type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/addUserCard',
      data: {
        "cardId": cardId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userName": app.getLoginName(),
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var randomAmount = res.data.randomAmount;
        if (flag) {
          wx.showModal({
            title: '提示',
            content: '领取成功，是否立即使用',
            success(res) {
              if (res.confirm) {
                app.navigateToPage("/pages/person_coupon/person_coupon");
              }
            }
          })
          that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
        } else {
          // wx.showToast({
          //   title: '领取失败',
          //   duration: 1500
          // })
          wx.showModal({
            title: '提示',
            content: '领取成功，是否立即使用',
            success(res) {
              if (res.confirm) {
                app.navigateToPage("/pages/person_coupon/person_coupon");
              }
            }
          })
        }
      }
    })
  },
  /***
 * 去支付获取卡券
 */
  nowPayCardBindTap: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/buyCard',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "cardId": cardId,
        "openId": app.getOpenId(),
        "localStoreId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "companyName": app.getLoginName()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        if (flag) {
          var param = res.data;
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              wx.showToast({
                title: '支付成功',
                duration: 1500
              })
              that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
            },
            fail: function (res) {
              if (res.errMsg === 'requestPayment:fail cancel') {
                app.showModal({
                  content: '取消支付'
                })
              } else {
                app.showModal({
                  content: '支付失败'
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '领取失败',
            duration: 1500
          })
        }
      }
    })
  },
  imageLoad: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight
    var imgheights = this.data.imgheights
    //把每一张图片的高度记录到数组里  
    imgheights.push(imgheight)
    this.setData({
      imgheights: imgheights,
      current: 0
    })
  },
  imageLoadAll: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight;
    var allimgheights = imgheight;
    //把每一张图片的高度记录到数组里  
    this.setData({
      allimgheights: allimgheights
    })
  },
  // 滚动切换标签样式
  switchTab: function (e) {
    this.setData({
      currentTab: e.detail.current
    });
    this.checkCor();
  },
  // 点击标题切换当前页时改变样式
  swichNav: function (e) {
    var cur = e.target.dataset.current;
    if (this.data.currentTab == cur) {
      return false;
    } else {
      this.setData({
        currentTab: cur
      })
    }
  },
  /**
   * 更多分类点击事件
   */
  moreTypeBindTap: function () {
    wx.switchTab({
      url: "/pages/goods_classify/goods_classify"
    });
  },
  /**
   * 首页商品分类点击事件
   */
  goCatogroyTap: function (e) {
    app.classifyData.classifyId = e.currentTarget.dataset.id;
    wx.switchTab({
      url: "/pages/goods_classify/goods_classify"
    });
  },
  /**
   * 扫码识别商品
   */
  sweepCodeBindTap: function () {
    var that = this;
    wx.scanCode({
      success(res) {
        var filterName = res.result;
        console.log("我是扫码"+filterName);
        app.navigateToPage('/pages/searchPage/searchPage?filterName=' + filterName);
      }
    })
  },
  //判断当前滚动超过一屏时，设置tab标题滚动条。
  checkCor: function () {
    if (this.data.currentTab > 4) {
      this.setData({
        scrollLeft: 300
      })
    } else {
      this.setData({
        scrollLeft: 0
      })
    }
  },
  isCollectClick:function(){
    app.globalData.isCollect = 1;
    this.setData({
      SHOW_TOP:false
    })
  },
  onLoad: function (options) {
    console.log(app.globalData.isCollect)
    let rect = wx.getMenuButtonBoundingClientRect ? wx.getMenuButtonBoundingClientRect() : null
      
      let {screenWidth} = wx.getSystemInfoSync()
      this.setData({
        navbarHeight: rect.bottom,
        arrowR: screenWidth - rect.right + rect.width*3/4 - 5,
        bodyR: screenWidth - rect.right
      })
      this.startTimer = setTimeout(() => {
        this.setData({
          SHOW_TOP: true
        })
      }, this.data.delay * 1000)
      this.duraTimer = setTimeout(() => {
        this.shrink();
      }, (this.data.duration + this.data.delay) * 1000)
    var that = this;
    that.setData({
      currentTab: that.data.currentTab
    });
    //  高度自适应
    wx.getSystemInfo({
      success: function (res) {
        var environment = res.environment;
        if (environment && environment == "wxwork") {//企业微信进入

        } else {//微信进入

        }
        var clientHeight = res.windowHeight,
          clientWidth = res.windowWidth,
          rpxR = 750 / clientWidth;
        var calc = clientHeight * rpxR;
        that.setData({
          winHeight: calc
        });
      }
    });
    that.initPage(options);
    app.init_getExtMessage().then(res => {
      that.queryNearestStoreInfo(res.storeId)
    });
  },
  initPage: function (options) {
    var that = this;
    app.init_getExtMessage().then(res => {
      app.getSupplierSetting(res.companyId);
      that.queryNewIndexTemplate(res.companyId, res.storeId)
        .then(that.queryIndexPutCardInfo(res.storeId, res.companyId))
        .then(app.countRetailCartTotal());
    });
    var selectStoreInfoKey = wx.getStorageSync("selectStoreInfoKey");
    if (selectStoreInfoKey != "" && selectStoreInfoKey != undefined) {
      var storeName = selectStoreInfoKey.storeName;
      that.setData({
        storeName: storeName
      });
    }
    that.queryS();
  },
  goLiveCast: function () {
    app.navigateToPage("/pages/onlineCast/onlineCast");
  },
  /**
   * 查询距离最近的店铺信息
   */
  queryNearestStoreInfo: function (storeId) {
    var that = this;
    let vm = this;
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取门店地理位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             vm.queryStoreInfo(0, 0);

    //             } else if (res.confirm) {
    //               wx.openSetting({
    //                 success: function (dataAu) {
    //                   if (dataAu.authSetting["scope.userLocation"] == true) {
    //                     wx.showToast({
    //                       title: '授权成功',
    //                       icon: 'success',
    //                       duration: 1000
    //                     })
    //                     //再次授权，调用wx.getLocation的API
    //                     vm.getLocation();
    //                   } else {

    //                     wx.showToast({
    //                       title: '授权失败',
    //                       icon: 'none',
    //                       duration: 1000
    //                     })
    //                     vm.queryStoreInfo(0, 0);
    //                   }
    //                 }
    //               })
    //             }
    //           },
    //           fail: function () {

    //           vm.queryStoreInfo(0, 0);
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     } else {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     }
    //   }
    // })
    /*var selectStoreInfoKey = wx.getStorageSync("selectStoreInfoKey");
    if (selectStoreInfoKey != "" && selectStoreInfoKey != undefined) {
      var storeName = selectStoreInfoKey.storeName;
      that.setData({
        storeName: storeName
      });
    } else {

    }*/
  },
  /*封装倒计时模块的结束时间戳*/
  formatCountDownTime: function (startDate, startTime) {
    var that = this;
    var startDate = startDate.replace(/-/g, "/");
    return new Date(startDate + ' ' + startTime).getTime();
  },
  /*封装开始和结束时间的时间戳*/
  formatCountDown: function (startDate, startTime) {
    var that = this;
    var date = new Date(startDate);
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    return new Date([year, month, day].map(that.formatNumber).join('/') + ' ' + startTime + ':00').getTime();
  },
  formatNumber: function (n) {
    n = n.toString()
    return n[1] ? n : '0' + n
  },
  /**
 * 查询首页新的模版配置信息
 */
  queryNewIndexTemplate: function (companyId, storeId) {
    var that = this;
    return new Promise(function () {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/newIndexTemplate/queryWechatAppletIndexShowContent',
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": storeId,
          "companyId": companyId,
          "identity": 1
        },
        success: function (res) {
          wx.hideLoading();
          var indexListConfig = res.data.indexList;
          var announctList = [];
          var cardList = [];
          var goodsTypeList = [];
          var categoryIdList = [];
          var commodityList = [];
          let promotionList = [];
          let startTimeList = [];
          let endTimeList = [];
          var screenW = wx.getSystemInfoSync().windowWidth; //获取屏幕宽度
          allT = allT < 8 ? 8 : allT; //不够一平-----最小滚动一平时间
          if (indexListConfig != null && indexListConfig.length > 0) {
            for (var i = 0; i < indexListConfig.length; i++) {
              var floorType = indexListConfig[i].templateType;
              if (floorType == 2) {
                if (indexListConfig[i].noticeBean != null) {
                  announctList = indexListConfig[i].noticeBean.wehcatAppletIndexNoticeContentEntities;
                  if (announctList != null && announctList.length > 0) {
                    var content_t = announctList[0].noticeContent;
                    var contentW = content_t.length * that.data.size; //获取文本宽度（大概宽度）
                    var allT = (contentW / screenW) * that.data.moveTimes; //文字很长时计算有几屏
                    that.setData({
                      content_t: content_t,
                      marqueeW: -contentW + "px",
                      allT: allT + "s"
                    })
                  }
                }
              } else if (floorType == 4) {
                cardList = res.data.cardList;
              } else if (floorType == 6) {
                goodsTypeList = res.data.goodsTypeList;
              } else if (floorType == 7) {
                categoryIdList = res.data.categoryIdList;
                commodityList = res.data.commodityList;
              }
              else if (floorType == 9) {
                that.showCouponBind(companyId, storeId)
              }
              else if (floorType == 10 || floorType == 11 || floorType == 12 || floorType == 18) {


                // 将活动的结束时间参数提成一个单独的数组，方便操作
                /*封装开始时间*/

                /* if (indexListConfig[i].wechatAppletGoodsList != null && indexListConfig[i].wechatAppletGoodsList.length > 0 ){
                   promotionList.push({actEndTime: indexListConfig[i].wechatAppletGoodsList[0].promotionEndDate })
                 }
                 promotionList.forEach(o => {endTimeList.push(o.actEndTime) })*/
                if (indexListConfig[i].promotionSaleBean != null) {
                  var oneTime = null;
                  oneTime = {
                    s: that.formatCountDown(indexListConfig[i].promotionSaleBean.promotionStartDate, indexListConfig[i].promotionSaleBean.promotionStartTime),
                    e: that.formatCountDown(indexListConfig[i].promotionSaleBean.promotionEndDate, indexListConfig[i].promotionSaleBean.promotionEndTime)
                  }
                  endTimeList.push(oneTime);
                }
                if (indexListConfig[i].countDownBean != null) {
                  var oneTime = null;
                  oneTime = {
                    s: that.formatCountDownTime(indexListConfig[i].countDownBean.startTimeYmd, indexListConfig[i].countDownBean.startTimeHms),
                    e: that.formatCountDownTime(indexListConfig[i].countDownBean.endTimeYmd, indexListConfig[i].countDownBean.endTimeHms),
                  }
                  endTimeList.push(oneTime);
                }

                that.setData({
                  //actStartTimeList:startTimeList,
                  actEndTimeList: endTimeList
                });
                ///that.countStartDown();
                that.countDown();

              } else if (floorType == 15) {/*全局头部和背景颜色模块*/
                var top_color = "#fffff";
                var bg_color = "#fffff";
                if (indexListConfig[i].overallBean.bgm != null && indexListConfig[i].overallBean.bgm.length > 0) {
                  /*wx.playBackgroundAudio({
                    dataUrl: indexListConfig[i].overallBean.bgm,
                    title: '',
                    coverImgUrl: ''
                  })*/
                  const backgroundAudioManager = wx.getBackgroundAudioManager()
                  // 设置了 src 之后会自动播放
                  backgroundAudioManager.title = '音乐';
                  backgroundAudioManager.epname = '音乐';
                  backgroundAudioManager.singer = '音乐';
                  backgroundAudioManager.src = indexListConfig[i].overallBean.bgm;
                  console.log(backgroundAudioManager);
                  backgroundAudioManager.play();
                }
                if (indexListConfig[i].overallBean != '') {
                  var overBean = indexListConfig[i].overallBean;
                  top_color = overBean.topColor;
                  bg_color = overBean.overallBagColor;
                };
                that.setData({
                  bg_color: bg_color
                })
                if (top_color == "#ffffff") {
                  wx.setNavigationBarColor({
                    frontColor: '#000000',
                    backgroundColor: top_color
                  })
                }
                else {
                  wx.setNavigationBarColor({
                    frontColor: '#ffffff',
                    backgroundColor: top_color
                  })
                }
              }
              else if (floorType == 19) {
                app.globalData.bottomBean = indexListConfig[i].bottomBean;
                that.getTabBar().setData({
                  list: indexListConfig[i].bottomBean.wechatAppletIndexBottomContentEntity,
                  selectedColor: indexListConfig[i].bottomBean.selectedColor,//字选中后的颜色
                  color: indexListConfig[i].bottomBean.color,//字选中前的颜色,
                  backContent: indexListConfig[i].bottomBean.backContent,
                  backgroundStyle: indexListConfig[i].bottomBean.backgroundStyle
                })
              }
            }

          }
          that.setData({
            isOnlineService: res.data.isOnlineService,
            indexListConfig: indexListConfig,
            announctList: announctList,
            cardList: cardList,
            goodsTypeList: goodsTypeList,
            categoryIdList: categoryIdList,
            commodityList: commodityList
          });
        },
        fail: function () {
          wx.hideLoading();
        }
      })
    })
  },
  /**
   * 查询首页模版配置信息
   */
  queryIndexTemplate: function (companyId, storeId) {
    var that = this;

    return new Promise(function () {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/newIndexTemplate/queryWechatAppletIndexShowContent',
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": storeId,
          "companyId": companyId,
          "identity": 1
        },
        success: function (res) {
          wx.hideLoading();
          var indexListConfig = res.data.indexListConfig;
          var announctList = [];
          var cardList = [];
          var goodsTypeList = [];
          var categoryIdList = [];
          var commodityList = [];
          let promotionList = []
          var screenW = wx.getSystemInfoSync().windowWidth; //获取屏幕宽度
          allT = allT < 8 ? 8 : allT; //不够一平-----最小滚动一平时间
          if (indexListConfig != null && indexListConfig.length > 0) {
            for (var i = 0; i < indexListConfig.length; i++) {
              var floorType = indexListConfig[i].floorType;
              if (floorType == 2) {
                announctList = res.data.announctList;
                if (announctList != null && announctList.length > 0) {
                  var content_t = announctList[0].contentTitle + announctList[0].contentText;
                  var contentW = content_t.length * that.data.size; //获取文本宽度（大概宽度）
                  var allT = (contentW / screenW) * that.data.moveTimes; //文字很长时计算有几屏
                  that.setData({
                    content_t: content_t,
                    marqueeW: -contentW + "px",
                    allT: allT + "s"
                  })
                }
              } else if (floorType == 4) {
                cardList = res.data.cardList;
              } else if (floorType == 6) {
                goodsTypeList = res.data.goodsTypeList;
              } else if (floorType == 7) {
                categoryIdList = res.data.categoryIdList;
                commodityList = res.data.commodityList;
              } else if (floorType == 8) {

                for (var m = 0; m < indexListConfig[i].goodsTemplateList.length; m++) {
                  promotionList.push({ actEndTime: indexListConfig[i].goodsTemplateList[m].promotionEndDate })
                }
                let endTimeList = [];
                // 将活动的结束时间参数提成一个单独的数组，方便操作

                promotionList.forEach(o => { endTimeList.push(o.actEndTime) })
                that.setData({ actEndTimeList: endTimeList });
                // 执行倒计时函数
                // that.countDown();


              } else if (floorType == 12) {

                /*if (indexListConfig[i].showContent != '') {
                  var showContent = indexListConfig[i].showContent.split('@');
                  if (showContent[0] == '#ffffff' && showContent[1] == '#ffffff') {
                    wx.setNavigationBarColor({
                      frontColor: '#000000', // 必写项
                      backgroundColor: '#ffffff', // 传递的颜色值
                    })
                    that.setData({
                      mainLeftColor: '#ffffff',
                      mainRightColor: '#ffffff',
                      myAddress: app.imageUrl + 'locate.png',
                      scan: app.imageUrl + 'newscan.png',
                      vipCode: app.imageUrl + 'newvipCode.png'
                    })
                  } else {
                    wx.setNavigationBarColor({
                      frontColor: '#ffffff', // 必写项
                      backgroundColor: showContent[0], // 传递的颜色值
                    })
                    that.setData({
                      mainLeftColor: showContent[0],
                      mainRightColor: showContent[1],
                      myAddress: app.imageUrl + 'locate_white.png',
                      scan: app.imageUrl + 'newscan_white.png',
                      vipCode: app.imageUrl + 'newvipCode_white.png'
                    })
                  }

                }*/

              }
            }

          }
          that.setData({
            isOnlineService: res.data.isOnlineService,
            indexListConfig: indexListConfig,
            announctList: announctList,
            cardList: cardList,
            goodsTypeList: goodsTypeList,
            categoryIdList: categoryIdList,
            commodityList: commodityList
          });
        },
        fail: function () {
          wx.hideLoading();
        }
      })
    })
  },
  timeFormat(param) {//小于10的格式化函数
    return param < 10 ? '0' + param : param;
  },
  countDown() {//倒计时函数
    // 获取当前时间，同时得到活动结束时间数组
    var that = this;
    let newTime = new Date().getTime();
    let endTimeList = this.data.actEndTimeList;
    let countDownArr = [];
    // 对结束时间进行处理渲染到页面
    endTimeList.forEach(o => {
      let endTime = o.e;
      let startTime = o.s
      let obj = null;
      // 如果活动未结束，对时间进行处理
      if (startTime - Date.parse(new Date()) > 0) {
        let time = (startTime - newTime) / 1000;
        // 获取天、时、分、秒
        let day = parseInt(time / (60 * 60 * 24));
        let hou = parseInt(time % (60 * 60 * 24) / 3600);
        let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
        let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
        obj = {
          day: this.timeFormat(day),
          hou: this.timeFormat(hou),
          min: this.timeFormat(min),
          sec: this.timeFormat(sec),
          actEndTime: startTime
        }
      } else if (endTime - Date.parse(new Date()) <= 0) {
        obj = {
          day: '00',
          hou: '00',
          min: '00',
          sec: '00',
          actEndTime: '0'
        }
      }
      else {
        let time = (endTime - newTime) / 1000;
        // 获取天、时、分、秒
        let day = parseInt(time / (60 * 60 * 24));
        let hou = parseInt(time % (60 * 60 * 24) / 3600);
        let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
        let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
        obj = {
          day: this.timeFormat(day),
          hou: this.timeFormat(hou),
          min: this.timeFormat(min),
          sec: this.timeFormat(sec),
          actEndTime: endTime
        }
      }
      countDownArr.push(obj);
    })
    // 渲染，然后每隔一秒执行一次倒计时函数

    var hash = {};    //去重
    countDownArr = countDownArr.reduce(function (item, next) {
      hash[next.actEndTime] ? '' : hash[next.actEndTime] = true && item.push(next);
      return item
    }, [])
    this.setData({ countDownList: countDownArr });
    clearTimeout(mstimeoutId)
    mstimeoutId = setTimeout(this.countDown, 1000);
  },

  /**
   * 查询商家是否推送卡券给用户
   */
  queryIndexPutCardInfo: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newPushCard/queryIndexPutCardInfo',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "cardMarketingType": "4,5",
        "companyId": companyId
      },
      success: function (res) {
        var indexFlag = res.data.indexFlag;
        if (indexFlag) {
          var storeCardList = res.data.storeCardList;
          that.setData({
            indexFlag: indexFlag,
            sendStoreCardList: storeCardList,
            putCardHidden: false
          });
        } else {
          that.setData({
            indexFlag: indexFlag,
            putCardHidden: true
          });
        }
      }
    })
  },
  queryCardBagBindTap: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
    } else {
      app.navigateToPage('/pages/person_coupon/person_coupon');
    }

  },
  closeCardBgBindTap: function () {
    this.setData({
      putCardHidden: true,
      indexFlag: false
    });
  },
  /**
   * 关闭
   * */
  cancelButtonBindTap: function () {
    this.setData({
      contentHidden: true
    });
  },
  showAnnounceBindTap: function (e) {
    var index = e.currentTarget.dataset.index;
    var announctList = this.data.announctList;
    this.setData({
      announceTitle: announctList[index].contentTitle,
      announceContent: announctList[index].contentText,
      contentHidden: false
    });
  },
  /**
   * 首页banner图点击事件
   */
  bannerBindTap: function (e) {
    var id = e.target.dataset.id;
    var type = e.target.dataset.type;
    var click = e.target.dataset.click;
    if (click == 2) {
      if (type == 1) { //关联商品
        var pid = e.target.dataset.pid;
        var idArray = id.split(",");
        if (idArray.length == 1) {
          app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + id);
        } else {
          app.navigateToPage('/pages/moreGoods/moreGoods?type=3&id=' + pid);
        }
      } else if (type == 2) { //关联活动
        app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
      } else if (type == 3) { //关联积分商城
        app.navigateToPage('/pages/scoreMall/scoreMall');
      } else if (type == 6) { //关联转盘
        app.navigateToPage('/pages/turntableActivity/turntableActivity?sceneType=3');
      }
    }
  },
  /**
   * 跳转到商品详情(点击image)
   */
  imageClick: function (e) {
    var goodsId = e.target.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 跳转到商品详情(点击view)
   */
  imageClick1: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 点击搜索框跳转
   */
  searchBindFocus: function () {
    app.navigateToPage('/pages/searchPage/searchPage');
  },
  onHide: function () {
    var that = this;
    clearTimeout(that.data.timeId);
    clearTimeout(mstimeoutId)
    if (that.selectComponent("#slideItem") != null) {
      that.selectComponent("#slideItem").stopSlide();
    }
  },
  // 下拉刷新
  // onPullDownRefresh: function () {
  //   var that = this;
  //   this.initPage()
  // },
  onShow: function (options) {
    setTimeout(function(){
      if(app.getodbtoken() == null || app.getodbtoken().length<1 || !app.isLogin()){
        console.log("我是在onShow中执行的方法")
        app.init_userOpenId();
      };
    },4000)
    if(app.globalData.isCollect > 0){
      this.setData({
       SHOW_TOP:false
      })
     }
     else{
       this.setData({
         SHOW_TOP:true
        })
     }
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4,
        shopCartNum: wx.getStorageSync('shopCartNum'), //字选中前的颜色
        list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,//字选中前的颜色
        color: app.globalData.bottomBean.color,
        selectedColor: app.globalData.bottomBean.selectedColor,
        backContent: app.globalData.bottomBean.backContent,
        backgroundStyle: app.globalData.bottomBean.backgroundStyle
      })
    }
    var that = this;
    this.initPage(options);
    this.setData({
      currentTab: this.data.currentTab
    });
  },
  /**
   * 首页中部导航点击事件
   */
  indexNavigateBindTap: function (e) {
    var id = e.currentTarget.dataset.id;
    var type = e.currentTarget.dataset.type;
    var name = e.currentTarget.dataset.name;
    if (type == 1) { //文字图片素材页面
      app.navigateToPage('/pages/imageDesc/imageDesc?id=' + id + "&name=" + name);
    } else if (type == 2) { //活动商品页面onePromotion
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
          "id": id
        },
        url: app.projectName + '/indexTemplate/queryNavigationDetail',
        success: function (res) {
          var navigationEntity = res.data.navigationEntity;
          if (navigationEntity != null) {
            var content = navigationEntity.content;
            var promotionId = "";
            if (content.indexOf("@") > -1) {
              promotionId = content.substring(0, content.indexOf("@"));
            } else {
              promotionId = content;
            }
            app.navigateToPage('/pages/onePromotion/onePromotion?id=' + promotionId);
          }
        }
      })
    }
  },
  /**
   * 首页活动版块点击事件
   */
  indexMoveBlockBindTap: function (e) {
    var relation = e.target.dataset.relation;
    var type = e.target.dataset.type;
    var content = e.target.dataset.content;
    var pK_id = e.target.dataset.id;
    if (relation == 1) { //关联
      var id = "";
      if (content != null && content.length > 0) {
        if (content.indexOf("@") > -1) {
          id = content.substring(0, content.indexOf("@"));
        } else {
          id = content;
        }
        if (type == 1) { //关联商品
          var idArray = id.split(",");
          if (idArray.length == 1) {
            app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + id);
          } else {
            app.navigateToPage('/pages/moreGoods/moreGoods?type=2&id=' + pK_id);
          }
        } else if (type == 2) { //关联活动
          app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
        } else if (type == 3) { //关联积分商城
          app.navigateToPage('/pages/scoreMall/scoreMall');
        } else if (type == 4) { //关联卡券信息
          app.navigateToPage('/pages/couponTypeCenter/couponTypeCenter?cardType=' + content);
        } else if (type == 5) { //关联直播间
          app.navigateToPage("/pages/onlineCast/onlineCast");
        } else if (type == 6) { //关联转盘
          app.navigateToPage("/pages/turntableActivity/turntableActivity?sceneType=3");
        }
      }
    }
  },
  /**
   * 商品模块更多点击事件
   */
  goodsMoreBindTap: function (e) {
    var id = e.currentTarget.dataset.id;
    app.navigateToPage('/pages/moreGoods/moreGoods?type=1&id=' + id);
  },
  /**
   * 查询当前会员是否有会员卡，如果有则跳转到会员卡页面，如果没有跳转到个人中心页面
   */
  goToVipCardBindTap: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      app.navigateToPage('/pages/vipCard/vipCard');
    } else {
      that.getMyVipCardInfo();
    }
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          var cardNo = cardList[0].cardId;
          app.navigateToPage('/pages/myvipCard/myvipCard?cardNo=' + cardNo);
        } else {
          app.navigateToPage('/pages/vipCard/vipCard');
        }
      }
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    app.commonShareApplet(res);
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  getLocation: function () {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // wx.getLocation({
    //   type: 'gcj02',
    //   altitude: true,
    //   complete: function (res) {
    //     if (res.errMsg == "getLocation:ok") {
    //       var latitude = res.latitude;
    //       var longitude = res.longitude;
    //       qqmapsdk.reverseGeocoder({
    //         location: {
    //           latitude: latitude,
    //           longitude: longitude
    //         },
    //         success: function (res) { //成功后的回调
    //           var res = res.result;
    //           that.setData({
    //             localUserAddress: res.address,
    //             localUserCity: res.address_component.city
    //           })
    //           qqmapsdk.geocoder({
    //             //获取表单传入地址
    //             address: res.address, //地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
    //             success: function (res) { //成功后的回调
    //               var res = res.result;
    //               var latitude = res.location.lat;
    //               var longitude = res.location.lng;
    //               that.setData({
    //                 latitude: latitude,
    //                 longitude: longitude
    //               });
    //               that.queryStoreInfo(latitude, longitude);
    //             },
    //             fail: function (error) {
    //               console.error(error);
    //             },
    //             complete: function (res) {
    //             }
    //           })
    //         },
    //         fail: function (error) {
    //           console.error(error);
    //         },
    //         complete: function (res) {
    //         }
    //       })
    //     } else {
    //       that.setData({
    //         latitude: 0,
    //         longitude: 0
    //       });
    //       that.queryStoreInfo(latitude, longitude);
    //     }
    //   }
    // })
  },
queryS:function(){
  var that = this;
  wx.request({
    header: {
      'content-type': 'application/x-www-form-urlencoded' // 默认值
    },
    method: "POST",
    url: app.projectName + '/applet/queryLoginBreakStoreInfo',
    data: {
      "companyId": app.getExtCompanyId()
    },
    success: function (res) {
      wx.hideLoading();
      var returnStoreList = res.data.storeList;
      that.setData({
        rStoreList: returnStoreList,
      })
    }
  })
},
  /**
   * 查询店铺信息
   */
  queryStoreInfo: function (latitude, longitude) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryLoginBreakStoreInfo',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var returnStoreList = res.data.storeList;
        if (returnStoreList != null && returnStoreList.length > 0) {
          for (var i = 0; i < returnStoreList.length; i++) {
            returnStoreList[i].storeDistance = that.distance(latitude, longitude, returnStoreList[i].latitude, returnStoreList[i].longitude);
          }
          //按照距离排序
          returnStoreList = returnStoreList.sort((el1, el2) =>
            el1.storeDistance - el2.storeDistance
          );
        }
        that.setData({
          returnStoreList: returnStoreList,
          storeName: returnStoreList[0].storeName,
        })
        var extObj = {
          "storeId": returnStoreList[0].id,
          "storeName": returnStoreList[0].storeName,
          "storeAddress": returnStoreList[0].province + returnStoreList[0].city + returnStoreList[0].area + returnStoreList[0].storeAddress
        };
        wx.removeStorageSync("selectStoreInfoKey");
        app.setStorage({
          key: 'selectStoreInfoKey',
          data: extObj
        });
      }
    })
  },
  /**
* 由经纬度计算两点之间的距离，la为latitude缩写，lo为longitude
* @param {*} 第一个坐标点的纬度 
* @param {*} 第一个坐标点的经度 
* @param {*} 第二个坐标点的纬度 
* @param {*} 第二个坐标点的经度 
* @return (int)s   返回距离(单位千米或公里)
*/
  distance: function (la1, lo1, la2, lo2) {
    var La1 = la1 * Math.PI / 180.0;
    var La2 = la2 * Math.PI / 180.0;
    var La3 = La1 - La2;
    var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    s = s.toFixed(2);
    return s;
  },
  closeVipCardBindTap: function () {
    this.setData({
      isVipCardSwitch: false
    });
  },
  goVipCard: function () {
    app.navigateToPage('/pages/vipCard/vipCard');
  }
})