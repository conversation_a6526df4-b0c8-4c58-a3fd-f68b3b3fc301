<view style="padding:40rpx 0;">

  <block wx:key="unique" wx:for="{{giveList}}" wx:for-item="give" wx:for-index="idx">
    <view style="margin:20rpx">
      <view class="couponBox">
        <view class="jagged">
          <view
            style="display:flex;justify-content: space-between;border-bottom:1px dashed #FFFFFF;padding:20rpx 10rpx 0 20rpx">
            <view style="width:260rpx;">
              <view style="font-size:38rpx;">
                {{give.cardMarketingName}}
              </view>
              <view style="font-size:20rpx;margin:10rpx 0;">{{give.activityTime}}</view>
            </view>
            <view style="font-size:38rpx;padding-top: 20rpx;" hidden="{{give.consumptionAmount>0?false:true}}">
              ￥
              <text style="font-size:60rpx;font-weight:bold;">{{give.consumptionAmount}}</text>
            </view>
          </view>
          <view class="ruleBox" bindtap="marketRuleSwitchFun" data-idx="{{idx}}">
            <view>券包详情
              <image src="{{give.ruleSwitch==true?upArrow:downArrow}}"></image>
            </view>
            <view class="overflowMore" style="max-height:120rpx;line-height:40rpx" hidden='{{!give.ruleSwitch}}'>
              {{give.cardMarketingDesc}}</view>
          </view>
        </view>
        <view class="couponRight">
          <block wx:if="{{give.consumptionAmount>0}}">
            <view class="lType" data-idx="{{idx}}" bindtap="buyMarketCardBindTap">
              点击购买</view>
          </block>
          <block wx:else>
            <view class="lType" data-idx="{{idx}}" bindtap="freeReceiveCardBindTap">
              点击领取</view>
          </block>
        </view>
      </view>
    </view>
  </block>

  <block wx:key="unique" wx:for="{{storeCardList}}" wx:for-item="card" wx:for-index="idx">
    <block wx:if="{{card.type==1||card.type==2||card.type==4}}">
      <view style="margin:20rpx">
        <view class="couponBox">
          <view class="jagged">
            <view
              style="display:flex;justify-content: space-between;border-bottom:1px dashed #FFFFFF;padding:20rpx 10rpx 0 20rpx">
              <view style="width:260rpx;">
                <view style="font-size:38rpx;">{{card.cardName}}
                </view>
                <view style="font-size:24rpx;">{{card.discountDesc}}</view>
                <view style="font-size:20rpx;margin:10rpx 0;">
                  {{card.supplierSettingReceiveStartTime}}~{{card.supplierSettingReceiveEndTime}}</view>
              </view>
              <view style="font-size:38rpx;padding-top: 20rpx;">
                <block wx:if="{{card.type==4}}">
                  <block wx:if="{{card.payWay==1}}">
                    {{card.discountName}}
                    <text style="font-size:60rpx;font-weight:bold;">{{card.buyMoney}}</text>
                  </block>
                  <block wx:elif="{{card.payWay==2}}">
                    <text style="font-size:50rpx;font-weight:bold;">{{card.buyScore}}</text>
                    <view style="text-align:right;font-size:28rpx;margin-top:-12rpx;">积分</view>
                  </block>
                  <block wx:elif="{{card.payWay==3}}">
                    {{card.discountName}}
                    <text style="font-size:60rpx;font-weight:bold;">{{card.buyMoney}}</text>
                    <view style="text-align:right;font-size:28rpx;margin-top:-12rpx;">+{{card.buyScore}}积分</view>
                  </block>
                </block>
                <block wx:else>
                  {{card.discountName}}
                  <text style="font-size:60rpx;font-weight:bold;">{{card.discountMoney}}</text>
                </block>
              </view>
            </view>
            <view class="ruleBox" bindtap="ruleSwitchFun" data-idx="{{idx}}" data-cardId="{{card.cardId}}">
              <view>使用规则
                <image src="{{card.ruleSwitch==true?upArrow:downArrow}}"></image>
              </view>
              <view class="overflowMore" style="max-height:120rpx;line-height:40rpx" hidden='{{!card.ruleSwitch}}'>
                {{card.rule}}</view>

            </view>
          </view>
          <view class="couponRight">
            <block wx:if="{{card.type==4}}">
              <block wx:if="{{card.have}}">
                <view class="lType" style="width:154rpx;margin:80rpx auto;">已兑换</view>
              </block>
              <block wx:else>
                <block wx:if="{{card.receiveState==1}}">
                  <!-- 未到领取时间 -->
                  <block wx:if="{{card.userReceiveState==0}}">
                    <view class="noType" style="color:#EC1B2E;font-size: 32rpx;font-weight: bold;">待购买</view>
                    <view style="color:#EC1B2E;font-size: 32rpx;text-align:center;font-weight: bold;">
                      {{card.supplierSettingReceiveStartTime}}</view>
                  </block>
                  <block wx:else>
                    <block wx:if="{{card.payWay==1}}">
                      <view class="lType" data-id="{{card.cardId}}" data-type="{{card.type}}"
                        bindtap="userGetCardBindTap">点击购买</view>
                    </block>
                    <block wx:if="{{card.payWay==2}}">
                      <view class="lType" data-id="{{card.cardId}}" data-type="{{card.type}}"
                        bindtap="scoreBuyCardBindTap">点击购买</view>
                    </block>
                    <block wx:if="{{card.payWay==3}}">
                      <view class="lType" data-id="{{card.cardId}}" data-type="{{card.type}}"
                        bindtap="scoreAddAmountBuyCardBindTap">点击购买</view>
                    </block>
                  </block>
                </block>
                <block wx:else>
                  <view class="lType" style="width:154rpx;margin:80rpx auto;color:#6e6e6c">已售空</view>
                </block>
              </block>
            </block>
            <block wx:else>
              <block wx:if="{{card.have}}">
                <view class="lType" style="width:154rpx;margin:80rpx auto;">已领取</view>
              </block>
              <block wx:else>
                <block wx:if="{{card.receiveState==1}}">
                  <!-- 未到领取时间 -->
                  <block wx:if="{{card.userReceiveState==0}}">
                    <view class="noType" style="color:#EC1B2E;font-size: 32rpx;font-weight: bold;">待领取</view>
                    <view style="color:#EC1B2E;font-size: 32rpx;text-align:center;font-weight: bold;">
                      {{card.supplierSettingReceiveStartTime}}</view>
                  </block>
                  <block wx:else>
                    <view class="lType" data-id="{{card.cardId}}" data-type="{{card.type}}"
                      bindtap="userGetCardBindTap">点击领取</view>
                  </block>

                </block>
                <block wx:else>
                  <view class="lType" style="width:154rpx;margin:80rpx auto;color:#6e6e6c">已抢光</view>
                </block>
              </block>
            </block>
          </view>
        </view>
      </view>
    </block>
    <!--单张券-->
    <block wx:if="{{card.type==3}}">
      <!--红包券-->
      <view style="width:710rpx;height:300rpx;margin:20rpx auto;position:relative">
        <!-- 已领完 -->
        <block wx:if="{{card.have}}">
          <view data-fullamount="{{card.fullAmount}}" data-lunckyamount="{{card.redPacket}}" data-id="{{card.cardId}}"
            bindtap="luckyCardBindTap">
            <view style="color:#FFED8E;height:140rpx;position:absolute;top:70rpx;left:200rpx;display:flex">
              <text style="font-size:38rpx;display:block;line-height:180rpx;">￥</text>
              <text
                style="font-size:120rpx;line-height:140rpx;display:block;margin-left:10rpx;">{{card.redPacket}}</text>
              <text style="font-size:24rpx;line-height:140rpx;display:block;margin-left:20rpx;width:200rpx;"
                class="coupon-overflow">{{card.cardDesc}}</text>
            </view>
            <image src="{{yichai}}" style="width:100%" mode="widthFix"></image>
          </view>

        </block>
        <!--红包拆字样式-->
        <block wx:else>
          <block wx:if="{{card.receiveState==1}}">
            <view data-id="{{card.cardId}}" data-type="{{card.type}}" bindtap="userGetCardBindTap">
              <text style="position:absolute;top:120rpx;left:220rpx;font-size:42rpx;color:#FFED8E;">点击拆拼手气红包</text>
              <image src="{{weichai}}" style="width:100%" mode="widthFix"></image>
            </view>
          </block>
          <block wx:else>
            <!--红包已抢光-->
            <view data-id="{{card.cardId}}" bindtap="lootAllRedPacketBindTap">
              <text style="position:absolute;top:120rpx;left:220rpx;font-size:42rpx;color:#FFED8E;">已抢光</text>
              <image src="{{yichai}}" style="width:100%" mode="widthFix"></image>
            </view>
          </block>
        </block>
      </view>
      <!--红包券-->
    </block>
  </block>





  <!--红包券被领完弹出层-->
  <view hidden="{{couponOut}}">
    <image src="{{couponOut_bg}}" class="coin_back"></image>
    <view class="deleteIcon">
      <icon class="page-dialog-close" type="clear" size='30' color='#fff' bindtap='couponOutBindTap' />
    </view>
  </view>

  <!--红包券被领完弹出层-->
  <!-- <image hidden="{{hiddenCoin}}" src="{{coin_bg}}" class="coin_back"></image> -->
  <image hidden="{{hiddenCoin}}" src="{{hongbao}}" class="couponImg"></image>
  <!-- 黑色背景 -->
  <view class='black_bg' hidden='{{hiddenCoin}}'></view>
  <!--领取成功的背景-->
  <!--领取红包成功的详情-->
  <view class="pop_coin" hidden="{{hiddenLucky}}">
    <view class="coin_info" hidden="{{redPacketHidden}}" style="text-align:center;">
      <view style="font-size:40rpx;color:#EA2A15;font-weight:bold;">消费红包券</view>
      <view style="color:#EA2A15;">
        <text style="font-size:110rpx;">{{random}}</text>
        <text style="font-size:54rpx;">元</text>
      </view>
      <view style="color:#EA2A15;">
        <block wx:if="{{full_Amount>0}}">
          <text class="fullAmount">满￥{{full_Amount}}元可用</text>
        </block>
        <block wx:else>
          <text class="fullAmount">无门槛使用</text>
        </block>
      </view>
    </view>
    <view class="useNow" hidden="{{redPacketHidden}}" bindtap="useCoinCoupon">立即使用</view>
    <view hidden="{{!redPacketHidden}}"
      style="height:340rpx;line-height:248rpx;font-size:80rpx;color:#6e6e6c;text-align:center">已抢光</view>
    <view hidden="{{otherCoupon.length>0?false:true}}">
      <view class="luckyShow" style="color:#FFED8E">看他人手气如何</view>
      <view class="lunckyWrap">
        <block wx:key="unique" wx:for="{{otherCoupon}}" wx:for-item="sCoupon" wx:for-index="cardIndex">
          <view class="clearfix" style="margin-bottom:20rpx;">
            <view class="lunckyName">
              <text>{{sCoupon.userName}}</text>
              <text>
                {{sCoupon.receiveTime}}
              </text>
            </view>
            <view class="lunckyAmount" style="color:#FFED8E">{{sCoupon.discountAmount}}元</view>
          </view>
        </block>
        <view bindtap='couponScrollBind' style="font-size:28rpx;margin:20rpx 0;text-align:center;color:#fff;"
          hidden='{{moreLuckyHidden}}'>查看更多>></view>
      </view>
    </view>
    <view class="deleteIcon" style="top:164rpx">
      <icon class="page-dialog-close" type="clear" size='30' color='#fff' bindtap='cancelButtonBindTap' />
    </view>
  </view>
  <!--领取红包成功的详情-->
</view>

<!-- <view style="padding:20rpx">
	<view class="couponBox">
		<view class="jagged">
			<view style="display:flex;border-bottom:1px dashed #FFFFFF;padding:40rpx 40rpx 0 40rpx">
				<view style="width:300rpx;">
					<view style="font-size:38rpx;margin-top:10rpx">优惠券</view>
					<view style="font-size:24rpx;">无门槛使用</view>
					<view style="font-size:20rpx;margin-top:10rpx;">时间</view>
				</view>
				<view style="font-size:38rpx;">￥<text style="font-size:120rpx">50</text></view>
			</view>
			<view class="ruleBox">使用规则<image src="{{downArrow}}"></image>
			</view>
		</view>
		<view class="couponRight">
			<view>点击领取</view>
		</view>
	</view>
</view> -->