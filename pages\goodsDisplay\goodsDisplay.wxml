<!-- 单个商品 -->
<block wx:key="unique" wx:for="{{commodityList}}" wx:for-item="goods">
  <view style="width:100%; height:140px; margin-bottom:10px;" bindtap='goodsDetailBindTap'
    data-commodityId='{{goods.commodityId}}' data-storeId='{{goods.commoditySupplierId}}'>
    <image src='{{goods.commodityMainPic}}' style='width:140px; height:140px;float:left;'></image>
    <view style='padding:5px 5px 0 150px;height:135px;'>
      <label
        style='width:100%; height:80px;display:block;font-size:15px;line-height:24px; color:#222;'>{{goods.commodityName}}
      </label>
      <label class='clearfix'
        style='width:100%; height:39px;margin-top:15px;display:block; border-bottom:1px solid #efefef;'>

        <block wx:if="{{goods.skuList.length>0}}">
          <label
            style='color:red; font-size:17px;line-height:40px;display:inline-block;'>￥{{goods.skuList[0].skuPrice}}</label>
        </block>
        <block wx:else>
          <block wx:key="unique" wx:for="{{goods.unitList}}" wx:for-item="unit">
            <block wx:if="{{unit.commodityWeightType=='OT'}}">
              <label style='color:red; font-size:17px;line-height:40px;display:inline-block;'>
                ￥{{unit.commodityBatchPrice}}
              </label>
              <label style='color:#999; font-size:12px; margin-left:15px;display:inline-block;line-height:40px;'>
                {{unit.commoditySaleTotal}}人付款
              </label>
            </block>
          </block>
        </block>

      </label>

    </view>
  </view>
</block>
<!-- 单个商品 -->