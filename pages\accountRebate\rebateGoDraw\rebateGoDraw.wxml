<wxs src="../../../wxs/subutil.wxs" module="tools" />
<view>
  <view class="draw_w">
    <view class="draw_all">￥{{tools.sub.formatAmount(validRebateMoney,2)}}</view>
    <view class="draw_text">可提现金额</view>
    <view class="draw_title">提现金额</view>
    <view class="withdraw_m">
      <label>￥</label>
      <input type="digit" class="fill_m" value="{{withdrawQuota}}" placeholder="最低可提现{{minExtractMoney}}元"
        bindinput="withdrawQuotaBindInput" />
      <label class="fill_all" bindtap="allDrawBindTap">全部</label>
    </view>
    <radio-group class="radio-group" bindchange="radioChange">
      <view class="bankInfo">
        <radio class="radio" value="1" checked>
        </radio>
        <image src="{{unionPay}}" style="width:46rpx;margin:0 18rpx;" mode="widthFix"></image>
        <label>银行卡</label>
        <label class="bankno">{{bankNo}}</label>
        <label class="bank_c" bindtap="updateBankInfo">{{bankBtnText}}</label>
      </view>
      <view class="bankInfo" style="margin-top:30rpx;">
        <radio class="radio" value="2">
        </radio>
        <image src="{{aliPay}}" style="width:46rpx;margin:0 18rpx;" mode="widthFix"></image>
        <label>支付宝</label>
        <label class="bankno">{{zfbAccount}}</label>
        <label class="bank_c" bindtap="updateAlipayInfo">{{zfbBtnText}}</label>
      </view>
    </radio-group>
  </view>
  <!--金额大于2元提交申请class添加applySure属性-->
  <view class="withdrawApply {{isSend?'applySure':''}}" bindtap="userApplyExtractBindTap">
    提交申请
  </view>
</view>