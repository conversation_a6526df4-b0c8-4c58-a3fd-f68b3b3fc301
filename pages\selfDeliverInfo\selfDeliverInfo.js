// pages/selfDeliverInfo/selfDeliverInfo.js
var app = getApp();
var TimeUtil = require('../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderBean: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var orderId = options.orderId;
    that.queryOrderDetailByOrderId(orderId);
  },
  makePhone:function(e){
    var phone = e.currentTarget.dataset.orderphone;
    wx.makePhoneCall({
      phoneNumber: phone
    })
  },
  queryOrderDetailByOrderId: function (orderId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/queryOrderDetailByOrderId',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "orderId": orderId
      },
      success: function (res) {
        var orderBean = res.data.orderBean;
        if (orderBean != null) {
          orderBean.orderGenerateDate = TimeUtil.getSmpFormatDateByLong(orderBean.orderGenerateDate, true);
          orderBean.orderPayTime = TimeUtil.getSmpFormatDateByLong(orderBean.orderPayTime, true);
          orderBean.pickActualArriveTimeBegin = TimeUtil.getSmpFormatDateByLong(orderBean.pickActualArriveTimeBegin, true);
          orderBean.pickActualArriveTimeEnd = TimeUtil.getSmpFormatDateByLong(orderBean.pickActualArriveTimeEnd, true);
          orderBean.orderDeliver[0].estimateArrTime = TimeUtil.getSmpFormatDateByLong(orderBean.orderDeliver[0].estimateArrTime, true);

          var payList = orderBean.payList;
          var actualPayment = 0.0;
          var scorePayment = 0.0;
          var couponPayment = 0.0;
          var banlancePayment = 0.0;
          var payTime = "";
          if (payList != null && payList.length > 0) {
            for (var i = 0; i < payList.length; i++) {
              if (payList[i].payType == "WECHAT_PAY" && payList[i].payStatus == "PAY_SUCCESS") {
                actualPayment += parseFloat(payList[i].payMoney);
                payTime = TimeUtil.getSmpFormatDateByLong(payList[i].operateTime, true);
              }
              if (payList[i].payType == "ALIPAY" && payList[i].payStatus == "PAY_SUCCESS") {
                actualPayment += parseFloat(payList[i].payMoney);
                payTime = TimeUtil.getSmpFormatDateByLong(payList[i].operateTime, true);
              }
              if (payList[i].payType == "MEMEBER_CARD_ACCUMULATION_PAY" && payList[i].payStatus == "PAY_SUCCESS") {
                scorePayment += parseFloat(payList[i].payMoney);
              }
              if (payList[i].payType == "MEMBER_CARD_BANLANCE_PAY" && payList[i].payStatus == "PAY_SUCCESS") {
                banlancePayment += parseFloat(payList[i].payMoney);
              }
              if (payList[i].payType == "CouponPay" && payList[i].payStatus == "PAY_SUCCESS") {
                couponPayment += parseFloat(payList[i].payMoney);
              }
            }
          }
          that.setData({
            payTime: payTime,
            couponPayment: couponPayment,
            actualPayment: actualPayment,
            scorePayment: scorePayment,
            banlancePayment: banlancePayment,
            orderBean: orderBean
          });
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})