// pages/acceptance/acceptance.js
var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    return_apply: app.imageUrl + "accountManager/return_kefuDo.png",
    array: ["圆通", "申通", "韵达", "中通", "顺丰", "天天", "EMS", "宅急送", "优速", "全峰", "包裹", "汇通", "德邦", "邮政包裹", "安能快递"],
    objectArray: [{
      id: 1,
      name: '圆通'
    }, {
      id: 2,
      name: '申通'
    }, {
      id: 3,
      name: '韵达'
    }, {
      id: 4,
      name: '中通'
    }, {
      id: 5,
      name: '顺丰'
    }, {
      id: 6,
      name: '天天'
    }, {
      id: 7,
      name: 'EMS'
    }, {
      id: 8,
      name: '宅急送'
    }, {
      id: 9,
      name: '优速'
    }, {
      id: 10,
      name: '全峰'
    }, {
      id: 11,
      name: '包裹'
    }, {
      id: 12,
      name: '汇通'
    }, {
      id: 53,
      name: '德邦'
    }, {
      id: 35,
      name: '邮政包裹'
    }, {
      id: 54,
      name: '安能快递'
    }],
    index: 0,
    logisticNo: "",
    rejectedId: "",
    rejectedDetailList: []
  },
  bindPickerChange: function (e) {
    this.setData({
      index: e.detail.value
    })
  },
  logisticNoBindInput: function (e) {
    this.setData({
      logisticNo: e.detail.value
    })
  },
  onLoad: function (options) {
    var rejectedId = options.rejectedId;
    var rejectedDetailList = JSON.parse(options.rejectedDetailList);
    this.setData({
      orderRejectedBean: rejectedDetailList,
      rejectedDetailList: rejectedDetailList.rejectedDetailList,
      rejectedId: rejectedId
    });
  },
  customerAcceptanceBindTap: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/returnGoods/deliveRejectedRetailOrder',
      data: {
        "rejectedId": that.data.rejectedId,
        "rejectedDetailList": JSON.stringify(that.data.rejectedDetailList),
        "logisticNo": that.data.logisticNo,
        "deliverCompanyCode": that.data.index,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "寄货成功",
            icon: 'success',
            duration: 1500,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1500);
            }
          })
        } else {
          wx.showToast({
            title: '寄货失败',
            duration: 1500
          })
        }
      }
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})