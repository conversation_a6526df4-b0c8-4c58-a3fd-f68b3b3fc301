const app = getApp();
Page({

    /**
     * 页面的初始数据
     */
    data: {

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this;
        var data = {};
        data["tm"] = "/luckdraw/retailClient/outlines";
        data["storeId"] = app.getExtStoreId();
        data["userId"] = app.getUserId(),
        data["loginId"] = app.getLoginId(),
        data["userRole"] = app.getUserRole(),
        /*获取当前抽奖活动的奖品*/
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: 'http://192.168.10.180:8080/common.external.service/simpleRouter/stickness',
          data: data,
          success: function (res) {
            wx.hideLoading();
            if (res.data.code == 1) {
              var resData = JSON.parse(res.data.data);
              var resultList = resData.resultList;
              if(resultList != null && resultList.length>0){
                  that.setData({
                     rightsList:resultList
                  })
              }
              else{
                wx.showLoading({
                    title: '您目前没有任何权益',
                    mask: true
                  })
                  app.navigateToPage("/pages/accountManager/accountManager");
              }
            } 
          },
          fail: function () {
            wx.hideLoading();
          }
        })
    },
    goDrawClick:function(e){
        var drawId = e.currentTarget.dataset.drawid;
        app.navigateToPage("/pages/turntableActivity/turntableActivity?drawId=" + drawId);
    }
   
})