<view style='width:100%;'>
  <!--单个订单-->
  <block wx:key="unique" wx:for="{{returnGoodsList}}" wx:for-item="order">
    <view class='single_order'>
      <label class='order_top'>
        <text>单号:{{order.rejectedNo}}</text>
        <text class='order_time'>{{order.rejectedGenerateDate}}</text>
      </label>

      <block wx:key="unique" wx:for="{{order.rejectedDetailList}}" wx:for-item="goods">
        <view class='goods_detail'>
          <image class='goods_pic' src='{{goods.commodityMainPic}}'></image>
          <view class='right_detail'>
            <label class='right_top'>
              <text class='goods_name'>{{goods.commodityName}}</text>
              <text class='package_number'>￥{{goods.commoditySaleOtPrice}}</text>
            </label>
            <label class='right_bottom'>
              <text class='goods_tips'>{{goods.commoditySpec}}</text>
              <text class='goods_number'>X{{goods.commodityOtNum}}</text>
            </label>
          </view>
        </view>
      </block>

      <view class='detail_box'>
        <text class='goods_static'>{{orderStatusArray[order.rejectedStatus]}}</text>
        <block wx:if="{{order.rejectedStatus==4||order.rejectedStatus==5||order.rejectedStatus==6}}">
          <button data-id="{{order.rejectedId}}" bindtap='goToSendGoodsBindTap'>去寄货</button>
        </block>
        <block wx:if="{{order.rejectedStatus==1||order.rejectedStatus==4||order.rejectedStatus==5}}">
          <button data-id="{{order.rejectedId}}" bindtap="buyerCancleRejectedOrderBindTap">取消申请</button>
        </block>
      </view>
    </view>
  </block>
  <!--单个订单-->
</view>