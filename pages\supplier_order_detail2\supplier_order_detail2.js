var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    logisticsList: [],
    logisticNo:'',
    logisticName: '',
    deliverWay:9
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    wx.showLoading({
      title: '正在加载数据。。。',
      mask: true
    })
    var orderId = options.orderId;
    var logisticNo = options.logisticNo;
    var logisticName = options.logisticName;
    var receivePhoneNum = options.receivePhoneNum;
    var deliverWay = options.deliverWay;
    var totalM = options.totalmoney;
    that.setData({
      logisticNo: logisticNo,
      logisticName: logisticName,
      deliverWay:deliverWay,
      totalM:totalM
    })
    if(deliverWay == 9){
      that.getInstantDeliver(orderId);
      wx.hideLoading();
    }
    else{
      that.getLogisticMessage(logisticNo,logisticName,receivePhoneNum)
    }
    

  },
  makePhoneCall:function(e){
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
  getThirdLocation:function(){
    var that = this;
    /*wx.openLocation({
      latitude: Number(31.970554),
      longitude: Number(118.70352),
      name: "cs2",
      address: "测试1",
      scale: 5
    })*/
  },
  getInstantDeliver:function(id){
      var that = this;
      var data = {};
      data["tm"] = "/orderRepo/busiInstantList";
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      data["orderId"] = id;
      /*获取即时配送的信息*/
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/openservice',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
            var rData = JSON.parse(res.data.data);
            if(rData != null && rData.length>0){
                var orderStatus = rData[rData.length-1].orderStatus;
                var rideName = rData[rData.length-1].rideName;
                var riderPhone = rData[rData.length-1].riderPhone;
                var rideTime = (rData[rData.length-1].reachTime/60).toFixed(0);
                var riderLat = rData[rData.length-1].riderLat;
                var riderLng = rData[rData.length-1].riderLng;
                that.setData({
                  rData:rData[rData.length-1],
                  orderStatus:orderStatus,
                  rideName:rideName,
                  riderPhone:riderPhone?riderPhone:'',
                  rideTime:rideTime,
                })
            }
            else{
              app.showModal({
                content: res.data.msg
              });
            }

          } else {
            app.showModal({
              content: res.data.msg
            });
          }
        },
        fail: function () {
          wx.hideLoading();
        }
      })
  },
  /*getMap:function(){
      this.mapCtx = wx.createMapContext('mapId')
      this.mapCtx.on('markerClusterClick', res =>{
        console.log('markerClusterClick', res)
      })
      // 使用默认聚合效果时可注释下一句
      this.bindEvent()
  },*/
  getLogisticMessage: function(logisticNo,logisticName,receivePhoneNum) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: 'https://www.cn2b2c.com/applet.message/template/getLogisticsMessage',
      data: {
        "logisticsNo": logisticNo,
        "logisticsName": logisticName,
        "receivePhoneNum":receivePhoneNum
      },
      success: function(res) {
        wx.hideLoading();
        var logisticsList = res.data.logisticsList;
        if (logisticsList != null && logisticsList.length > 0) {
          that.setData({
            logisticsList: logisticsList
          });
        } else {
          app.showModal({
            content: '暂无物流信息'
          });
        }
      },
      fail: function() {
        wx.hideLoading();
      }
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.storeName,
      path: '/pages/index/index?recommendId=' + app.getUserId() + '&recommendAccount=' + app.getLoginAccount() + '&recommendName=' + app.getLoginName() + '&identity=' + app.getIdentity(),
      imageUrl: app.shareImageUrl,
      success: function(res) {
        // 转发成功
      },
      fail: function(res) {
        // 转发失败
      }
    }
  }
})