const app = getApp();
var TimeUtil = require('../../utils/util.js');
Page({


  /**
   * 页面的初始数据
   */
  data: {
    storeCardList: [],
    drawState: true,
    coupon_bg: app.imageUrl + 'coupon_bg.png',
    coin_bg: app.imageUrl + 'coin.png',
    couponOut_bg: app.imageUrl + 'couponOut_bg.png',
    usedcoupon_bg: app.imageUrl + 'usedcoupon_bg.jpg',
    isHave: app.imageUrl + 'isHave.png',
    hiddenCoin: true,
    full_Amount: 0,
    pageSize: 3,
    currentPage: 1,
    otherCoupon: [],
    couponOut: true,
    card_Id: '',
    hiddenLucky: true,
    hiddenPacket: true,
    random: 0,
    moreLuckyHidden: false,
    luckyCardId: "",
    payMark: app.imageUrl + 'payMark.png',
    redPacketHidden: false,
    couponGone: app.imageUrl + 'couponGone.jpg',
    downArrow: app.imageUrl + 'downArrow.png',
    upArrow: app.imageUrl + 'upArrow.png',
    hongbao: app.imageUrl + 'hongbao.png',
    weichai: app.imageUrl + 'weichai.png',
    yichai: app.imageUrl + 'yichai.png'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    app.init_getExtMessage().then(res => {
      that.showCouponBind(res.companyId, res.storeId);
      that.queryCrmGiveCardMarketingProgram(res.companyId, res.storeId);
    });
  },
  /**
   * 查询卡券礼包
   */
  queryCrmGiveCardMarketingProgram: function (companyId, storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryCrmGiveCardMarketingProgram',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId,
      },
      success: function (res) {
        var giveList = res.data.giveList;
        if (giveList != null && giveList.length > 0) {
          giveList.forEach(e => {
            e['ruleSwitch'] = false;
          });
        }
        that.setData({
          giveList: giveList
        })
      }
    })
  },
  /**
   * 卡券礼包点击活动详情
   */
  marketRuleSwitchFun: function (e) {
    var that = this;
    var giveList = that.data.giveList;
    var idx = e.currentTarget.dataset.idx;
    var cardInfo = giveList[idx].cardInfo;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryMarketingProgramCardInfo',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "cardInfo": cardInfo
      },
      success: function (res) {
        var desc = res.data.desc;
        if (desc.length > 0) {
          giveList[idx].cardMarketingDesc = "";
          giveList[idx].cardMarketingDesc = giveList[idx].cardMarketingDesc + ";" + desc;
          giveList[idx].ruleSwitch = !giveList[idx].ruleSwitch;
          that.setData({
            giveList: giveList
          });
        }
        that.setData({
          giveList: giveList
        })
      }
    })
  },
  /**
   * 购买卡券礼包
   */
  buyMarketCardBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var idx = e.currentTarget.dataset.idx;
    var id = that.data.giveList[idx].id;
    wx.showLoading({
      title: '购买中...',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/buyMarketCardBindTap',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "openId": app.getOpenId(),
        "companyId": app.getExtCompanyId(),
        "id": id
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          var param = res.data;
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              wx.showToast({
                title: "卡券购买成功",
                icon: 'success',
                duration: 1500,
                mask: true
              })
            },
            fail: function (res) {
              //用户取消付款或支付失败，退还会员卡积分
              wx.showToast({
                title: "取消支付",
                icon: 'none',
                duration: 1500,
                mask: true
              })
            }
          })
        } else {
          wx.showToast({
            title: message,
            icon: 'none',
            duration: 1500,
            mask: true
          })
        }
      }
    })
  },
  /**
   * 免费领取卡券礼包
   * @param {*} e 
   */
  freeReceiveCardBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var idx = e.currentTarget.dataset.idx;
    var id = that.data.giveList[idx].id;
    wx.showLoading({
      title: '领取中...',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/freeReceiveCardBindTap',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "id": id
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        if (flag) {
          app.showModal({
            content: "领取成功",
            icon: "success",
            mask: true
          });
        } else {
          app.showModal({
            content: res.data.message == "" ? "领取失败" : res.data.message,
            icon: "none",
            mask: true
          });
        }
      }
    })
  },
  /**
   * 用户跳转到首页
   */
  useCoinCoupon: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
   * 用户可领取的优惠券
   */
  showCouponBind: function (companyId, storeId) {
    var that = this;
    // wx.showLoading({
    //   title: '数据加载中...',
    //   mask: true
    // })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryCardByStoreId',
      data: {
        "sceneType": 1,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId,
        "is_free": 0
      },
      success: function (res) {
        wx.hideLoading();
        var cardList = res.data.returnList;
        if (cardList != null && cardList.length > 0) {
          for (var i = 0; i < cardList.length; i++) {
            cardList[i]['ruleSwitch'] = false;
          }
          that.setData({
            hiddenCoupon: false,
            storeCardList: cardList
          });
        } else {
          wx.showToast({
            title: "暂无卡券可领取",
            icon: 'none',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 2000);
            }
          })
        }
      }
    })
  },
  /**
   * 免费领取券
   */
  userGetCardBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateId',
      data: {
        "companyId": app.getExtCompanyId(),
        "typeId": "1"
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              wx.showLoading({
                title: '卡券领取中...',
                mask: true
              })
              var cardId = e.currentTarget.dataset.id;
              var type = e.currentTarget.dataset.type;
              that.setData({
                card_Id: cardId
              })
              that.checkCardIsHave(cardId, type);
            }
          })
        } else {
          wx.showLoading({
            title: '卡券领取中...',
            mask: true
          })
          var cardId = e.currentTarget.dataset.id;
          var type = e.currentTarget.dataset.type;
          that.setData({
            card_Id: cardId
          })
          that.checkCardIsHave(cardId, type);
        }
      }
    })
  },
  /**
   * 积分兑换卡券
   */
  scoreBuyCardBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateId',
      data: {
        "companyId": app.getExtCompanyId(),
        "typeId": "1"
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              wx.showLoading({
                title: '卡券领取中...',
                mask: true
              })
              var cardId = e.currentTarget.dataset.id;
              that.scoreBuyCard(cardId);
            }
          })
        } else {
          wx.showLoading({
            title: '卡券领取中...',
            mask: true
          })
          var cardId = e.currentTarget.dataset.id;
          that.scoreBuyCard(cardId);
        }
      }
    })
  },
  /**
   * 积分购买卡券
   */
  scoreBuyCard: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/scoreBuyCard',
      data: {
        "cardId": cardId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userName": app.getLoginName(),
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "openId": app.getOpenId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          wx.showToast({
            title: "卡券兑换成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
              }, 1000);
            }
          })
        } else {
          app.showModal({
            content: message,
            icon: "none",
            mask: true
          });
        }
      }
    })
  },
  /**
   * 积分+现金兑换卡券
   */
  scoreAddAmountBuyCardBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateId',
      data: {
        "companyId": app.getExtCompanyId(),
        "typeId": "1"
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              wx.showLoading({
                title: '卡券领取中...',
                mask: true
              })
              var cardId = e.currentTarget.dataset.id;
              that.scoreAddAmountBuyCard(cardId);
            }
          })
        } else {
          wx.showLoading({
            title: '卡券领取中...',
            mask: true
          })
          var cardId = e.currentTarget.dataset.id;
          that.scoreAddAmountBuyCard(cardId);
        }
      }
    })
  },
  /**
   * 积分+现金兑换卡券
   */
  scoreAddAmountBuyCard: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/scoreAddAmountBuyCard',
      data: {
        "userName": app.getLoginName(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "cardId": cardId,
        "openId": app.getOpenId(),
        "companyId": app.getExtCompanyId(),
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          var param = res.data;
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              wx.showToast({
                title: "卡券兑换成功",
                icon: 'success',
                duration: 1000,
                mask: true,
                success: function () {
                  setTimeout(function () {
                    that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
                  }, 1000);
                }
              })
            },
            fail: function (res) {
              //用户取消付款或支付失败，退还会员卡积分
              that.returnUserVipCardScore(param.orderNo);
            }
          })
        } else {
          wx.showToast({
            title: message,
            icon: "none",
            mask: true
          })
        }
      }
    })
  },
  /**
   * 用户取消支付退还会员卡积分
   */
  returnUserVipCardScore: function (orderNo) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/returnUserVipCardScore',
      data: {
        "orderNo": orderNo,
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {

      }
    })
  },
  /**
   * 检查卡券库存是否充足
   */
  checkCardIsHave: function (cardId, type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/checkCardIsHave',
      data: {
        "cardId": cardId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          var storeCardList = that.data.storeCardList;
          for (var i = 0; i < storeCardList.length; i++) {
            if (storeCardList[i].cardId == cardId) {
              if (storeCardList[i].type != 4) {
                that.freeGetCardBindTap(cardId, type);
              } else {
                that.nowPayCardBindTap(cardId);
              }
              break;
            }
          }
        } else {
          that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
          wx.hideLoading();
          that.setData({
            couponOut: false,
          });
        }
      }
    })
  },
  /**
   * 手气券弹出层关闭
   **/
  cancelButtonBindTap: function () {
    var that = this;
    //that.showCouponBind();
    that.setData({
      hiddenCoin: true,
      hiddenPacket: true,
      hiddenLucky: true,
    })
  },
  couponOutBindTap: function () {
    var that = this;
    that.setData({
      couponOut: true,
    })
  },
  /**
   * 免费领取卡券
   */
  freeGetCardBindTap: function (cardId, type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/addUserCard',
      data: {
        "cardId": cardId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userName": app.getLoginName(),
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var randomAmount = res.data.randomAmount;
        if (flag) {
          wx.showModal({
            title: '提示',
            content: '领取成功，是否立即使用',
            success(res) {
              if (res.confirm) {
                app.navigateToPage("/pages/person_coupon/person_coupon");
              } else {
                if (type == 3) {
                  that.queryOtherCoupon(cardId);
                  that.setData({
                    hiddenCoin: false,
                    hiddenLucky: false,
                    random: randomAmount,
                  })
                }
              }
              that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
            }
          })
        } else {
          wx.showToast({
            title: '领取失败',
            duration: 1500
          })
        }
      }
    })
  },
  /***
   * 获取他人领取的红包
   */
  queryOtherCoupon: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryUserReceiveDetail',
      data: {
        "cardId": cardId,
        "page": that.data.currentPage,
        "pagesize": that.data.pageSize,
      },
      success: function (res) {
        var new_otherCoupon = res.data.receiveList;
        var old_otherCoupon = that.data.otherCoupon;
        if (new_otherCoupon != null && new_otherCoupon.length > 0) {
          new_otherCoupon = old_otherCoupon.concat(new_otherCoupon);
        } else {
          new_otherCoupon = old_otherCoupon;
        }
        if (res.data.receiveList == null || res.data.receiveList.length == 0) {
          that.setData({
            moreLuckyHidden: true
          });
        } else {
          if (res.data.receiveList.length < 3) {
            that.setData({
              moreLuckyHidden: true
            });
          } else {
            that.setData({
              moreLuckyHidden: false
            });
          }
        }
        that.setData({
          otherCoupon: new_otherCoupon
        })
      }
    })
  },
  /**
   *  获取更多的其他人手气
   */
  couponScrollBind: function () {
    var that = this;
    var luckyCardId = that.data.luckyCardId;
    that.setData({
      currentPage: that.data.currentPage + 1
    })
    that.queryOtherCoupon(luckyCardId);
  },
  /***
   * 已领取查询红包券详情
   */
  luckyCardBindTap: function (e) {
    var that = this;
    var cardId = e.currentTarget.dataset.id;
    var random_amount = e.currentTarget.dataset.lunckyamount;
    var full_Amount = e.currentTarget.dataset.fullamount;
    that.setData({
      otherCoupon: [],
      currentPage: 1,
      hiddenLucky: false,
      hiddenPacket: false,
      hiddenCoin: false,
      redPacketHidden: false,
      random: random_amount,
      luckyCardId: cardId,
      full_Amount: full_Amount,
      coin_bg: app.imageUrl + "coin.png"
    })
    that.queryOtherCoupon(cardId);
  },
  /**
   * 已抢光红包券查询领取详情
   * @param {} e 
   */
  lootAllRedPacketBindTap: function (e) {
    var that = this;
    var cardId = e.currentTarget.dataset.id;
    that.setData({
      otherCoupon: [],
      currentPage: 1,
      redPacketHidden: true,
      hiddenLucky: false,
      hiddenPacket: false,
      hiddenCoin: false,
      luckyCardId: cardId,
      coin_bg: app.imageUrl + "coinGone.png"
    })
    that.queryOtherCoupon(cardId);
  },
  /***
   * 去支付获取卡券
   */
  nowPayCardBindTap: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/buyCard',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "cardId": cardId,
        "openId": app.getOpenId(),
        "localStoreId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "companyName": app.getLoginName()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        if (flag) {
          var param = res.data;
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              wx.showToast({
                title: '支付成功',
                duration: 1500
              })
              that.showCouponBind(app.getExtCompanyId(), app.getExtStoreId());
            },
            fail: function (res) {
              if (res.errMsg === 'requestPayment:fail cancel') {
                app.showModal({
                  content: '取消支付'
                })
              } else {
                app.showModal({
                  content: '支付失败'
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '领取失败',
            duration: 1500
          })
        }
      }
    })
  },
  // 点击使用规则
  ruleSwitchFun: function (e) {
    var that = this;
    var StoreCardListData = this.data.storeCardList;
    var ruleSwitchData = StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch;

    if (ruleSwitchData == false) {
      if (typeof (StoreCardListData[e.currentTarget.dataset.idx].rule) == "undefined") {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/retailCoupon/queryReceiveCardUseRule',
          data: {
            "cardId": e.currentTarget.dataset.cardid,
            "storeId": app.getExtStoreId(),
          },
          success: function (res) {
            StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch = true;

            StoreCardListData[e.currentTarget.dataset.idx].rule = res.data.cardRule;
            that.setData({
              storeCardList: StoreCardListData
            })

          }
        })
      } else {
        StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch = true;
        that.setData({
          storeCardList: StoreCardListData
        })
      }
    } else {
      StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch = false;
      that.setData({
        storeCardList: StoreCardListData
      })
    }

  }
})