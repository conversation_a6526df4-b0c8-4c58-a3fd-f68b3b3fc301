var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {

    parameter: "",
    page: 1,
    pagesize: 10,
    searchLoading: false,
    agentList: []
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    var type = options.type;
    this.setData({
      defaultLogo: app.getExtStoreImage()
    })
    if (type == 1) {
      wx.setNavigationBarTitle({
        title: "我的上级信息"
      });
      this.queryMySuperior();
    } else if (type == 2) {
      wx.setNavigationBarTitle({
        title: "直属一级"
      });
      this.queryDirectlyUnderUser();
    }
  },
  makePhoneBindTap: function(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
  parameterBindInput: function(e) {
    this.setData({
      parameter: e.detail.value
    })
  },
  /**
   * 查询我的上级
   */
  queryMySuperior: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/relevantTeam/queryMySuperior',
      success: function(res) {
        var new_agentList = res.data.agentList;
        that.setData({
          agentList: new_agentList
        });
        wx.hideLoading();
      }
    })
  },
  /**
   * 按条件查询
   */
  queryDirectlyUnderUserSearch: function() {
    var that = this;
    if (that.data.parameter == "" || that.data.parameter.length == 0) {
      that.setData({
        agentList: [],
        page: 1,
        pagesize: 10
      });
      that.queryDirectlyUnderUser();
      return;
    }
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "type": 1,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "parameter": that.data.parameter,
        "page": 1,
        "pagesize": 10
      },
      url: app.projectName + '/relevantTeam/queryDirectlyUnderUser',
      success: function(res) {
        var new_agentList = res.data.agentList;
        that.setData({
          agentList: new_agentList
        });
        wx.hideLoading();
      }
    })
  },
  /**
   * 查询直属一级和直属二级人员信息
   */
  queryDirectlyUnderUser: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "type": 1,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "parameter": that.data.parameter,
        "page": that.data.page,
        "pagesize": that.data.pagesize
      },
      url: app.projectName + '/relevantTeam/queryDirectlyUnderUser',
      success: function(res) {
        var new_agentList = res.data.agentList;
        var old_eagentList = that.data.agentList;
        if (old_eagentList != null && old_eagentList.length > 0) {
          new_agentList = old_eagentList.concat(new_agentList);
        }
        if (res.data.agentList == null || res.data.agentList.length == 0) {
          that.setData({
            searchLoading: false
          });
        } else {
          that.setData({
            searchLoading: true
          });
        }
        that.setData({
          agentList: new_agentList
        });
        wx.hideLoading();
      }
    })
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    this.queryDirectlyUnderUser();
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        page: that.data.page + 1
      });
      that.queryDirectlyUnderUser();
    }
  }
})