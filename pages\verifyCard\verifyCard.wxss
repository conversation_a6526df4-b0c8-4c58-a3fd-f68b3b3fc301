/* pages/verifyCard/verifyCard.wxss */
.useTime{
  padding-left:10rpx;
  border-left:6rpx solid #FF7E00;
  margin-bottom:10rpx;
  margin-left:10px;
}
.useTime label{
  text-indent:6rpx;
}
.expirateTime{
  padding-left:20rpx;
}
.useArea{
  padding-left:20rpx;
}

/* 新的样式 */
page{
  background: #F0F0F0;
}
.useContent{
  background-color: #fff;
  width: 670rpx;
  margin: 20rpx auto;
  border-radius: 8rpx;
  padding:20rpx
}
.couponTop{
  min-height: 200rpx;
  display: flex;
}
.couponLeft{
  position: relative;
  width: 170rpx;
  height: 170rpx;
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}
.couponLeft image{
  width: 170rpx;
  height: 170rpx;
}
.couponLeft view{
  width: 170rpx;
}
.couponRight{
  font-size: 28rpx;
  color:#333333;
  margin-left: 20rpx;
}
.ruleImg{
  width: 24rpx;
  height: 12rpx;
  margin-left:10rpx;
}
.subBtn{
  width: 665rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  text-align: center;
  background: #FF7E00;
  color:#fff;
  margin:40rpx auto 0;
}