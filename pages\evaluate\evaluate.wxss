page {
  background: #f4f4f4;
}

.single_goods {
  width: 100%;
  margin-top: 10px;
  background: #fff;
}

.goods_box {
  width: 94%;
  height: 90px;
  margin: 0 3%;
}

.goods_pic {
  height: 70px;
  width: 70px;
  margin-top: 10px;
  float: left;
}

.right_detail {
  padding-left: 85px;
  height: 100%;
}

.goods_name {
  display: block;
  color: #282828;
  line-height: 30px;
  padding-top: 15px;
  font-size: 15px;
}

.goods_tips {
  display: block;
  color: #747476;
  font-size: 14px;
  margin-top: 5px;
}

.comment1-description {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
}

.comment1-description1 {
  font-family: PingFangSC-Regular;
  font-size: 15px;
  color: #333;
  margin-left: 3%;
  line-height: 25px;
}

.star-pos {
  /**position: absolute;
  right: 0px;**/
  margin-left: 20px;
  display: flex;
  flex-direction: row;
}

.comment1-description .star-pos .hideStar {
  z-index: 2;
  background-image: url("https://www.cn2b2c.com/gsf/img/wa/evaluate/star1.png");
  width: 25px;
  height: 25px;
  background-size: 22px;
  background-position: center center;
  background-repeat: no-repeat;
}

.starsM {
  z-index: 1;
  width: 25px;
  height: 25px;
  background-size: 22px;
  background-image: url("https://www.cn2b2c.com/gsf/img/wa/evaluate/star2.png");
  background-position: center center;
  background-repeat: no-repeat;
}

textarea {
  width: 94%;
  margin: 10px 3%;
  height: 80px;
  font-size: 14px;
  line-height: 24px;
}

.addPic {
  width: 94%;
  margin: 0 3%;
  /**height: 70px;**/
  padding: 5px 0;
  border-bottom: 1px solid #e0e0e0;
}

.add_pic {
  width: 60px;
  height: 60px;
  /**float: left;margin-right: 8px;**/
}

.button_box {
  width: 100%;
  height: 50px;
  background: #fff;
  line-height: 50px;
  text-align: right;
}

.button_box button {
  width: 84px;
  height: 34px;
  display: inline-block;
  font-size: 13px;
  color: #3b3b3b;
  border: 1px solid #8b8b8b;
  margin-top: 8px;
  margin-right: 3%;
  padding-left: 10px;
  padding-right: 10px;
  background: #fff;
}
