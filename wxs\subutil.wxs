var sub = {
    /**
  * 
  * @param 根据已抢数量格式话距离
  * @param sub_len 
  */
  formatDistance: function (num, total) {
    return ((1 - num / total) * 100) + "%";
  },
  toEvaluate:function(content){
    return decodeURIComponent(content);
  },
  /**
  * 
  * @param 根据已抢数量格式话距离
  * @param sub_len 
  */
  formatDistance: function (num, total) {
    return ((1 - num / total) * 100) + "%";
  },
  /**
  * 
  * @param 获取当前时间
  * @param sub_len 
  */
  formatcurrentTime: function () {
    return getDate().getTime();
  },
  /**
  * 
  * @param 转换当前时间
  * @param sub_len 
  */
  formatMill:function(timeS){
    var date = getDate(timeS);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    return [year, month, day].map(sub.formatNumber).join('/');
  },
    /**
  * 
  * @param 转换当前时间到秒
  * @param sub_len 
  */
  formatMillToS:function(timeS){
    var date = getDate(timeS);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    var sec = date.getSeconds();
    return [year, month, day].map(sub.formatNumber).join('/')+' '+[hour, minute, sec].map(sub.formatNumber).join(':');
  },
  formatMillToDate:function(timeS){
    var date = getDate(timeS);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    var sec = date.getSeconds();
    return [month, day].map(sub.formatNumber).join('-')
  },
  formatMillToSecond:function(timeS){
    var date = getDate(timeS);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    var sec = date.getSeconds();
    return [hour, minute, sec].map(sub.formatNumber).join(':');
  },
  /**
  * 
  * @param 格式话倒计时组件的毫秒数
  * @param sub_len 
  */
  formatCountDownTime: function (startDate, startTime) {
    var date = getDate(startDate);
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    return getDate([year, month, day].map(sub.formatNumber).join('/') + ' ' + startTime).getTime();
  },
  /**
  * 
  * @param 格式话倒计时的毫秒数
  * @param sub_len 
  */
  formatCountDown: function (startDate, startTime) {
    var date = getDate(startDate);
    var year = date.getFullYear()
    var month = date.getMonth() + 1 
    var day = date.getDate()
    return getDate([year, month, day].map(sub.formatNumber).join('/') + ' ' + startTime + ':00').getTime();
  },
  formatNumber: function (n) {
    n = n.toString()
    return n[1] ? n : '0' + n
  },
  /**
  * 
  * @param 计算图片模块的边距
  * @param sub_len 
  */
  formatWidth: function (s1, s2, s3) {
    if (s1 == 0) {/*有页边距*/
      return (700 - s2 * (s3 - 1)) / s3 + "rpx";
    }
    else {/*无页边距*/
      return (750 - s2 * (s3 - 1)) / s3 + "rpx";
    }
  },

  /**
   * 
   * @param val 格式化时间
   * @param sub_len 
   */
  formatDate: function (val, sub_len) {
    if (val.length == 0 || val == undefined) {
      return;
    }
    return val.substring(0, sub_len).replace("-", ".");
  },
  /**
   * 格式化金额
   * @param amount 金额
   * @param len  保留长度
   */
  formatAmount: function (input_amount, len) {
    var outAmount = parseFloat(input_amount);
    if (outAmount > 0) {
      return outAmount.toFixed(2);
    }
    return 0;
  }
};

module.exports.sub = sub;