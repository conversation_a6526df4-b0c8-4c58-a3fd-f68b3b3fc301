page {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 关键字展示 */

.keyword_show {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  background: #fff;
  width: 100%;
  height: 100%;
}

.keyword_show text {
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
  font-size: 13px;
  color: #333;
  border-bottom: 1px solid #ececec;
  display: block;
}
.keyword_show text:last-child{border-bottom:none;}

.search_box {
  height: 32px;
  padding: 10px;
  background: #ccc;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 20;
}

.search_box input {
  background: #fff;
  padding: 0 5px;
  height: 100%;
  border-radius: 3px;
  font-size: 13px;
  line-height: 32px;
}

scroll-view {
  width: 100%;
  position: absolute;
  top: 52px;
  bottom: 0;
}

.scrollView1 {
  width: 100%;
  position: absolute;
  top: 92px;
  bottom: 0;
}

/* 热门城市 */

.hot_city {
  padding: 0 10px;
  margin-bottom: 10px;
}

.hot_city label {
  display: block;
  width: 100%;
  font-size: 14px;
  line-height: 32px;
  height: 32px;
  border-bottom: 1px solid #ececec;
}

.hot_city view {
  margin-top: 10px;
}

.hot_city view text {
  width: auto;
  margin-right: 4%;
  text-align: center;
  line-height: 36px;
  border: 1px solid #ccc;
  display: block;
  float: left;
  font-size: 15px;
  margin-bottom: 14px;
  padding: 0 14px;
}

.hot_city view  text:nth-child(3n) {
  margin-right: 0;
}

/* 顺序排列 */

.order_city {
  width: 100%;
}

.order_city label {
  padding-left: 10px;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  background: #ccc;
  display: block;
}

.single_city text {
  padding-left: 10px;
  color: #333;
  height: 36px;
  line-height: 36px;
  border-bottom: 1px solid #ececec;
  display: block;
}

.single_city text:last-child {
  border-bottom: none;
}

/* 首字母快速查找 */

.quick_look {
  top: 52px;
  background: #000;
  width: 26px;
  opacity: 0.65;
  height: 100%;
  position: fixed;
  right: 0;
}

.quick_look text {
  width: 100%;
  height: 3.7%;
  color: #999;
  display: block;
  font-size: 12px;
  vertical-align: middle;
  text-align: center;
}
