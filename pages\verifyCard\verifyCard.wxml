
<view class="useContent">
	<view class="couponTop">
		<view class="couponLeft">
			<image src="{{hongbaoImg}}"></image>
			<view style="position: absolute;top:72rpx;">
				<block wx:if="{{cardBean.cardType>=4&&cardBean.cardType<=6}}">
					<text style="font-size:44rpx;">{{cardBean.discountAmount}}</text>
					<text>折</text>
				</block>
				<block wx:else>
					<text>￥</text>
					<text style="font-size:44rpx;">{{cardBean.discountAmount}}</text>
				</block>
			</view>
		</view>
		<view class="couponRight">
			<view style="font-weight:bold;line-height:50rpx">
				{{cardBean.cardName}}
			</view>
			<view style="color:#919398;max-height:80rpx;">
				<block wx:if="{{cardBean.usageScenarios==1}}">适用场景：仅线上使用</block>
				<block wx:elif="{{cardBean.usageScenarios==2}}">适用场景：仅线下门店使用</block>
				<block wx:else>适用场景：线上线下均可使用</block>
			</view>
			<view style="color:#919398;">有效时间:{{cardBean.cardStartTime}}-{{cardBean.cardEndTime}}</view>
			<view style="color:#919398;">领取时间:{{cardBean.receiveTime}}</view>
			<view style="color:#919398;" bindtap="ruleSwitchFun" data-cardId="{{cardBean.id}}">
				使用规则<image class="ruleImg" src="{{cardBean.ruleSwitch==true?upArrow_grey:downArrow_grey}}"></image>
			</view>
			<view style="line-height:40rpx" hidden='{{!cardBean.ruleSwitch}}'>
				<block wx:if="{{cardBean.cardType>=4&&cardBean.cardType<=6}}">
					{{cardBean.fullAmount==0?"无门槛"+cardBean.discountAmount+"折扣":"满"+cardBean.fullAmount+"享受"+cardBean.discountAmount+"折扣"}}
				</block>
				<block wx:else>
					{{cardBean.fullAmount==0?"无门槛优惠"+cardBean.discountAmount+"元":"满"+cardBean.fullAmount+"优惠"+cardBean.discountAmount+"元"}}
				</block>
				;{{cardBean.rule}}
			</view>
		</view>
	</view>
	<image src="data:image/png;base64,{{cardBean.barCode}}" style="width:100%;margin-top:50rpx;" mode="widthFix"></image>
	<image src="data:image/png;base64,{{cardBean.qrCode}}" style="width:100%;" mode="widthFix"></image>
	<view style="text-align:center;color:#333333;line-height: 60rpx;">{{cardBean.cardNo}}</view>
	<view style="text-align:center;color:#333333;line-height: 60rpx;">向商家出示此码使用优惠券</view>
</view>
<view class="subBtn" bindtap="goIndexBindTap">去商城选购</view>