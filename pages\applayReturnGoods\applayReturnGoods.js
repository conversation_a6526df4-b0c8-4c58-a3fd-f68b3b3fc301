var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var TimeUtil = require('../../utils/util.js');
var wxMarkerData = [];
Page({
  data: {
    goods_pic: app.imageUrl + 'classify_bg.png',
    isFromBack: false,
    orderStatus: ["", "待审核", "待付款", "待发货", "待收货", "订单取消", "订单取消",
      "订单待评价", "订单完成", "订单关闭", "部分发货", "部分付款", "待商家接单", "", "", "", "待成团", "待买家确认"
    ],
    //订单状态Id 1：待审核 2：待付款 3：待发货 4：待收货 5：零售商取消 6：供货商取消 7：订单待评价 8：订单完成 9：订单关闭 10：部分发货 11：部分付款 12：待商家接单 
    offSet: 1,
    searchLoading: true,
    isBack: false,
    orderList: [],
    orderStatusArray: ["", "部分退货处理中", "整单退货处理中", "退货中", "待退款", "退款完成"],
    goodsPurchaseArray: ["", "", "删除", "申请退货", "退货中", "退货完成"]
  },
  /**
   * 退货
   */
  returnGoodsBindTap: function (e) {
    var orderId = e.target.dataset.orderid;
    app.navigateToPage('/pages/returnGoods2/returnGoods2?orderId=' + orderId);
  },
  onLoad: function (options) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    that.queryRejectedOrderList();
  },
  /**
   * 查询可退货的订单信息
   */
  queryRejectedOrderList: function () {
    var that = this;
    wx.request({
      url: app.projectName + '/returnGoods/allRejectedOrder',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "offSet": that.data.offSet,
        "pageSize": 10,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var orderList = res.data.orderList;
        if (orderList != null && orderList.length > 0) {
          var oldOrderList = that.data.orderList;
          if (oldOrderList != null && oldOrderList.length > 0) {
            orderList = oldOrderList.concat(orderList);
          }
          for (var i = 0; i < orderList.length; i++) {
            var isShowReturnGoods = false;
            var goodsState1 = false; //商品状态有没有正常的
            orderList[i].orderGenerateDate = TimeUtil.getSmpFormatDateByLong(orderList[i].orderGenerateDate, true);
            var orderDetail = orderList[i].orderDetail;
            for (var j = 0; j < orderDetail.length; j++) {
              //1:正常 2:删除,默认正常3.申请退货4.退货中5.退货完成
              var commodityPurchaseStatus = orderDetail[j].commodityPurchaseStatus;
              if (commodityPurchaseStatus == 3 || commodityPurchaseStatus == 4) {
                isShowReturnGoods = true;
              }
              if (commodityPurchaseStatus == 1 || commodityPurchaseStatus == 6) {
                goodsState1 = true;
              }
            }
            if (!isShowReturnGoods) {
              if (goodsState1) {
                isShowReturnGoods = false;
              } else {
                isShowReturnGoods = true;
              }
            }
            orderList[i].showReturnBtn = isShowReturnGoods;
          }
          that.setData({
            orderList: orderList,
            searchLoading: true,
          });
        }
      }
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        offSet: that.data.offSet + 1
      });
      that.queryRejectedOrderList();
    }
  },
  onShow: function () {
    var that = this;
    if (that.data.isBack) {
      that.setData({
        offSet: 1,
        orderList: []
      });
      that.queryRejectedOrderList();
    } else {
      that.setData({
        isBack: true
      })
    }
  }
})