var app = getApp()

Page({
  data: {
    add_pic: app.imageUrl + 'evaluate/add_pic.png',
    return_apply: app.imageUrl + "accountManager/return_apply.png",
    close_btn: app.imageUrl + 'close.png',
    array: ['拍错了，重新购买', '对商品质量不放心', '买多了', '不想买了'],
    objectArray: [{
        id: 0,
        name: '拍错了，重新购买'
      },
      {
        id: 1,
        name: '对商品质量不放心'
      },
      {
        id: 2,
        name: '买多了'
      },
      {
        id: 3,
        name: '不想买了'
      }
    ],
    returnGoodsPic: [], //退货图片
    returnGoodsDesc: "", //退货描述信息
    returnReason: "", //退货原因
    index: 0,
    goodsList: [],
  },
  onLoad: function (options) {
    var goodsList = JSON.parse(options.goodsList);
    this.setData({
      goodsList: goodsList
    });
  },
  /**
   * 从手机中选择图片
   */
  chooseImageBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    wx.chooseImage({
      count: 5,
      success: function (res) {
        var tempFilePaths = res.tempFilePaths;
        if (tempFilePaths != null && tempFilePaths.length > 0) {
          for (var i = 0; i < tempFilePaths.length; i++) {
            if (that.data.returnGoodsPic.length >= 5) {
              app.showModal({
                content: "图片最多可以上传5张"
              });
            } else {
              that.uploadImageForFtp(tempFilePaths[i]);
            }
          }
        }
      }
    });
  },
  /**
   * 上传图片到FTP服务器
   */
  uploadImageForFtp: function (imageUrl) {
    var that = this;
    wx.uploadFile({
      url: app.projectName + '/upload/uploadPic', //仅为示例，非真实的接口地址
      filePath: imageUrl,
      name: 'file',
      header: {
        "Content-Type": "multipart/form-data"
      },
      success: function (res) {
        var data = JSON.parse(res.data);
        if (data.code == 1) {
          var url = "https://www.cn2b2c.com/image" + data.url;
          that.data.returnGoodsPic.push(url);
          that.setData({
            returnGoodsPic: that.data.returnGoodsPic
          });
        } else {
          app.showModal({
            content: data.message
          });
        }
      }
    })
  },
  /**
   * 删除图片
   */
  deleteEvaluateImage: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    that.data.returnGoodsPic.splice(index, 1);
    that.setData({
      returnGoodsPic: that.data.returnGoodsPic
    });
  },
  /**
   * 预览图片
   */
  previewImage: function (e) {
    var that = this;
    var imageUrl = e.currentTarget.dataset.src;
    var imageList = [];
    imageList.push(imageUrl);
    wx.previewImage({
      current: imageUrl,
      urls: imageList
    })
  },
  /**
   * 退货原因
   */
  bindPickerChange: function (e) {
    var returnReason = this.data.objectArray[e.detail.value].name;
    this.setData({
      returnReason: returnReason
    })
  },
  /**
   * 下一步
   */
  /*newApplayBindTap: function () {
    var goodsList = this.data.goodsList;
    var returnReason = this.data.returnReason;
    var returnGoodsDesc = this.data.returnGoodsDesc;
    var returnGoodsPic = this.data.returnGoodsPic;
    var parmar = "goodsList=" + JSON.stringify(goodsList) + "&returnReason=" + returnReason + "&returnGoodsDesc=" + returnGoodsDesc + "&returnGoodsPic=" + returnGoodsPic;
    app.navigateToPage('/pages/deliveredInformation/deliveredInformation?' + parmar);
  },*/
  /**
   * 提交申请退货
   */
  newApplayBindTap: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/returnGoods/applayReturnGoods',
      data: {
        "goodsList": JSON.stringify(that.data.goodsList),
        "returnReason": that.data.returnReason,
        "returnGoodsDesc": that.data.returnGoodsDesc,
        "returnGoodsPic": that.data.returnGoodsPic,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "申请退货成功,请等待商家处理",
            icon: 'none',
            duration: 1500,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnToPage("/pages/customerService/customerService");
              }, 1500);
            }
          })
        } else {
          wx.showToast({
            title: '申请退货失败',
            icon: 'none',
            duration: 1500
          })
        }
      }
    })
  },
  /**
   * 退货描述
   */
  returnGoodsDescBindInput: function (e) {
    this.setData({
      returnGoodsDesc: e.detail.value
    })
  }
})