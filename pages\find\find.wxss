@import '../popupTemplate/popupTemplate.wxss';

page {
  background: #F2F2F2;
}

.find_one {
  width: 695rpx;
  margin: 0px auto;
  /* border-radius: 32rpx 32rpx 0rpx 0rpx;
  background: linear-gradient(180deg, #F66404 0%,#F64611 100%); */
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120rpx;
}

.find_two {
  width: 650rpx;
  margin: 0px auto;
  display: flex;
  align-items: center;
}

.find_three {
  line-height: 50rpx;
  width: 50%;
  color: #fff;
  font-size: 24rpx;
}

.find_four {
  /* border: 2rpx solid #F64611; */
  /* border-top: 2rpx solid #F64611; */
  width: 707rpx;
  margin: 0px auto;
  background: #fff
}

.find_five {
  border: 0rpx solid green;
  width: 100%;
  margin: 0px auto;
  margin-bottom: 10rpx;
}

.find_six {
  width: 100%;
  height: 36rpx;
  padding-left: 24rpx;
}

.find_seven {
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 36rpx;
  color: #323334;
}

.find_eight {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 36rpx;
  color: #919398;
}

.find_nine {
  width: 72rpx;
  height: auto;
  border-radius: 50%
}

.find_ten {
  width: 100%;
  height: 72rpx;
}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_six {}

.find_content {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #525252;
}

.find_label {
  height: 50rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  color: #fff;
  text-align: center;
  padding: 5rpx 20rpx 5rpx 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.textFour_box {
  width: 100%;
  line-height: 50rpx;
  text-align: justify;
}

.text_toggloe_box {
  display: -webkit-box;
  display: -webkit-flex;
  flex-direction: row;
  align-items: center;
  margin: 10rpx 0;
}

.text_toggle_text {
  font-weight: bold;
  color: #4A79E5;
  font-size: 30rpx;
  line-height: 32rpx;
  margin-right: 10rpx;
}

.three {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.tab {
  width: 100%;
  height: 100%;
  white-space: nowrap;
}

/* .tab-item{
 display: inline-block;
 width: 50%;
 height: 95%;
 text-align: center;
 font-size: 14px;
 color: #8f9193;
} */
.tab-item {
  margin-right: 24rpx;
  background: #F6F7F9;
  display: inline-block;
  height: 100%;
  font-size: 28rpx;
  width: 492rpx;
  background: #F6F7F9;
  opacity: 1;
  border-radius: 6rpx;
}


/*九宫格*/
.gallery {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}

/*每张图片所占容器*/
.item {
  position: relative;
  margin: 8rpx;
}

/*删除按钮*/
.delete {
  position: absolute;
  height: 20px;
  bottom: 0;
  width: 100%;
  background: #ccc;
  opacity: .8;
}

.delete image {
  position: absolute;
  width: 20px;
  height: 20px;
}

.comments {
  display: flex;
  overflow: hidden;
  padding: 20rpx 20rpx 20rpx 20rpx;
}

.noblueColor {
  margin-left: 16rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #525252;
}

.blueColor {
  margin-left: 16rpx;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #909090;
}

.zan {
  overflow: hidden;
  align-items: center;
  border-bottom: 1rpx solid #e6e6e6;
  padding: 8rpx 6rpx 8rpx 6rpx;
  border-radius: 6rpx;
}


.love-icon {
  float: left;
  margin-left: 16rpx;
  margin-right: 2rpx;
  width: 64rpx;
  height: 64rpx;
  margin-top: 4rpx;
}

/**没有订单**/

.order_none {
  width: 160px;
  margin: 20px auto;
  display: block;
}