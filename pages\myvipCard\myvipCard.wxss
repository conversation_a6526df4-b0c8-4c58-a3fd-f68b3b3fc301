/* pages/myvipCard/myvipCard.wxss */
page{
  background:#F0F0F0;
}
.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}
.head_box {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}
.payWrap{
  background:#fff;
  width:700rpx;
  margin:0 auto;
  border-radius:20rpx;
}
.payInner{
  font-size:28rpx;
  color:#666;
  width:700rpx;
  margin:0 auto;
  padding:30rpx;
}
.goPay{
  width:600rpx;
  margin-left:20rpx;
  padding:20rpx 0;
  border:1px solid #FF7E00;
  text-align:center;
  border-radius:10rpx;
  font-size:32rpx;
  color:#FF7E00;
}
/*add*/
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom:0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.cardWrap{
  padding:40rpx 0;
  border-radius:10rpx;
  width:90%;
  margin:0 auto;
  margin-bottom:20rpx;
  position:relative;
  height:240rpx;
}
.cardWrap image{
  border-radius:10rpx;
  z-index:-1;
  position:absolute;
  width:100%;
  top:0;
  left:0;
  right:0;
  bottom:0;
}
.card_title{
  font-size:26rpx;
  margin-left:20rpx;
  text-align:center;
  color:#fff;
}
.card_title text{
  color:#fff;
  float:right;
  margin-right:20rpx;
}
.card_no{
  margin-top:20rpx;
  font-size:26rpx;
  text-align:left;
  margin-left:20rpx;
}
.amount_wrap{
  margin:40rpx 0;
  text-align:center;
  color:#fff;
}
.remain_amount{
  font-size:26rpx;
}
.remain_amount text{
  font-size:32rpx;
  font-weight:bold;
}
.remain_score{
  font-size:26rpx;
  margin-left:40rpx;
}
.remain_score text{
  font-size:32rpx;
  font-weight:bold;
}
.validDate{
  font-size:26rpx;
  margin-top:20rpx;
  margin-right:20rpx;
  text-align:right;
  color:#fff;
}
.card_id{
  text-align:left;
  padding-top:20rpx;
  padding-left:20rpx;
  color:#fff;
  font-size:24rpx;
}
.cardTip{
  font-weight:bold;
  text-align:center;
  font-size:32rpx;
  padding-bottom:40rpx;
}
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom:0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.scoreWrap{
  z-index:999;
  text-align:center;
  position:fixed;
  background:#fff;
  top:20%;
  width:80%;
  left:7%;
  border-radius:20rpx;
}
.score_title{
  background:#fff;
  padding:20rpx 0;
  /*margin-bottom:30rpx;*/
}
.score_avail{
  text-align:left;
  text-indent:40rpx;
  background:#fff;
  padding:30rpx 0;
}
.score_switch{
  text-align:left;
  background:#fff;
  padding:30rpx 0;
  padding-left:40rpx;
}
.score_switch input{
  vertical-align:middle;
  display:inline-block;
  border-bottom:1rpx solid #c6c7c8;
}
.score_oper{
  background:#fff;
  margin-top:30rpx;
  height:80rpx;
  line-height:80rpx;
}
.score_confirm{
  display:inline-block;
  height:80rpx;
  text-align:center;
  width:40%;
  background:#FF7E00;
  color:#fff;
  border-radius:10rpx;
}
.score_cancel{
  display:inline-block;
  height:80rpx;
  text-align:center;
  width:40%;
  margin-left:10%;
  border-radius:10rpx;
}
.newPay{
  width:600rpx !important;
}
.submitbtn{
  background-color: #FF7E00;
  width: 665rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  color:#fff;
  text-align: center;
  font-size:32rpx;
  margin:0 auto 10px;

}
.goCouponBtn{
  border:1px solid #FF7E00;
  width: 665rpx;
  height: 84rpx;
  line-height: 84rpx;
  border-radius: 42rpx;
  color:#FF7E00;
  text-align: center;
  font-size:32rpx;
  margin:0 auto 10px;
}

/* 新的样式 */
.vipCardTop{
  width: 710rpx;
  height: 300rpx;
  border-radius: 16rpx;
  position: relative;
  margin:20rpx auto;
}
.rechargeBtn{
  width: 156rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 28rpx;
  background-color: #82A78F;
  color: #fff;
  font-size: 26rpx;
  /*float:right;*/
  margin-top: 30rpx;
}
.vipCardCenter{
  width: 670rpx;
  height: 125rpx;
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding:20rpx;
  margin:20rpx auto;
  
}
.vipCardCenter .cli{
  text-align: center;
  width: 236rpx;
}
.vipCardCenter .cli view:nth-of-type(1){
  font-size: 34rpx;
  color:#525252;
  height: 60rpx;
  line-height: 60rpx;
}
.vipCardCenter .cli view:nth-of-type(2){
  color:#FF7E00;
  font-size: 32rpx;
  height: 60rpx;
  line-height: 60rpx;
}