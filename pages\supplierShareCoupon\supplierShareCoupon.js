var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    discountImg: app.imageUrl + 'supplier_discountImg.png',
    state: -1
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    const cardId = decodeURIComponent(options.scene);
    that.setData({
      cardId: cardId
    })
    app.init_getExtMessage().then(res => {
      that.setData({
        cardId: cardId
      })
      that.queryCardDetail(cardId, res.companyId, res.storeId);
    });
  },
  queryCardDetail: function (cardId, companyId, storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryCardDetailByCardId',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "cardId": cardId,
        "companyId": companyId,
        "storeId": storeId
      },
      success: function (res) {
        var cardBean = res.data.cardBean;
        var state = res.data.state;
        if (cardBean != null) {
          that.setData({
            cardBean: cardBean,
            state: state
          });
        } else {
          wx.switchTab({
            url: "/pages/index/index"
          });
        }
      }
    })
  },
  nowReceiveCardBindTap: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin();
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/addUserCard',
      data: {
        "cardId": that.data.cardId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userName": app.getLoginName()
      },
      success: function (res) {
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          wx.showToast({
            title: "恭喜您领取成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                that.setData({
                  state: 2
                })
              }, 1000);
            }
          })
        } else {
          wx.showToast({
            title: message == "" ? "很抱歉，领取失败，请重新领取" : message,
            icon: 'none',
            duration: 1000,
            mask: true
          })
          if (message == ""){
            wx.removeStorageSync("odbtoken");
            wx.removeStorageSync("loginToken");
            wx.removeStorageSync("userSession");
            wx.removeStorageSync("isLoginSession");
            app.init_userOpenId();
          }
          

       
          var isLoginSession = false;
          app.setStorage({
            key: 'isLoginSession',
            data: isLoginSession
          });
        }
      }
    })
  },
  goCardTap: function () {
    app.navigateToPage("/pages/person_coupon/person_coupon");
  }
})