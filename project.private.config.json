{"setting": {"compileHotReLoad": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false, "useIsolateContext": true}, "condition": {}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "%E9%9B%B6%E5%94%AE%E6%99%AE%E9%80%9A%E7%89%88%E4%BF%AE%E6%94%B9%E7%99%BB%E5%BD%95%E7%89%88%E6%9C%AC", "libVersion": "3.8.11"}