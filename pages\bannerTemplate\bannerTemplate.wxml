<wxs src="../../wxs/subutil.wxs" module="tools" />
<!--banner图模块-->
<template name="bannerItem">
	<swiper autoplay="{{autoplay}}" interval="3000" circular="{{circular}}" indicator-dots="{{tj_indicatorDots}}" indicator-color="{{indicatorColor}}" indicator-active-color="{{indicatorActiveColor}}" duration="{{duration}}" style='height:{{imgheights[current]}}rpx;'>
		<block wx:key="unique" wx:for-item="banner" wx:for="{{indexConfig.wechatIndexImageInfoEntities}}">
			<swiper-item>
				<image lazy-load='true' src="{{banner.imageUrl}}" data-id='{{banner.relationId}}' data-type="{{banner.relationType}}" data-pid="{{banner.id}}" data-click="{{banner.isClick}}" bindtap='bannerBindTap' bindload="imageLoad" class="slide-image" style="width:{{imgwidth}}rpx; height:{{imgheights[current]}}rpx;" />
			</swiper-item>
		</block>
	</swiper>
</template>
<!--多条公告模块-->
<template name="announce1">
	<view class="clearfix annoucnePart" hidden='{{announctList.length>0?false:true}}'>
		<image src="{{announce}}" lazy-load='true' class="showAnnounce"></image>
		<view class="announceDetail">
			<swiper display-multiple-items="{{announctList.length>1?2:1}}" vertical="true" autoplay="{{autoplay}}" interval="{{interval}}" circular="{{circular}}" style="height:50px;">
				<block wx:key="unique" wx:for="{{announctList}}" wx:for-item="ann" wx:for-index="index">
					<swiper-item>
						<view class="announceItem {{announctList.length==1?'oneItem':''}}" bindtap='showAnnounceBindTap' data-index='{{index}}'>
							<label></label>
							<label style="color:#FF7E00;">{{ann.contentTitle}}</label>
						</view>
					</swiper-item>
				</block>
			</swiper>
		</view>
	</view>
</template>
<!--单条可自动滑动模块-->
<template name="announce2">
	<view class="example">
		<view class="marquee_box">
			<image lazy-load='true' src="{{announce}}"></image>
			<!--<view class="marquee_text" style="font-size:26rpx;color:red;{{orientation}}:{{marqueeDistance2}}px;font-size: {{size}}px;">
        <block wx:key="unique" wx:for="{{announctList}}" wx:for-item="ann" wx:for-index="index">
          <text>{{ann.contentTitle}}:{{ann.contentText}}</text>
          <text wx:if="{{marquee2copy_status}}" style="margin-left:{{marquee2_margin}}px;">{{ann.contentTitle}}:{{ann.contentText}}</text>
        </block>
      </view>-->
			<view class="marquee_container" style="--marqueeWidth--:{{marqueeW}};--allTs--:{{allT}};">
				<block wx:key="unique" wx:for="{{announctList}}" wx:for-item="ann" wx:for-index="index">
					<view class="a_text" style="font-size:{{size}}px">{{ann.contentTitle}}:{{ann.contentText}}</view>
				</block>
			</view>
		</view>
	</view>
</template>
<!--中部导航模块-->
<template name="navItem">
	<view class='company_introduction'>
		<view style="width:710rpx;margin:0 20rpx;">
			<block wx:key="unique" wx:for="{{indexConfig.wechatIndexNavigationEntities}}" wx:for-item="navigateItem" wx:for-index="index">
				<label style="width:{{navigateW}}" data-id="{{navigateItem.id}}" data-type="{{navigateItem.type}}" data-name="{{navigateItem.name}}" bindtap="indexNavigateBindTap">
					<image lazy-load='true' src='{{navigateItem.imageUrl}}'></image>
					<text>{{navigateItem.name}}</text>
				</label>
			</block>
		</view>
	</view>
</template>
<!--分类模块-->
<template name="classifyItem">
	<view>
		<view class="categoryContent" hidden='{{goodsTypeList.length>0?false:true}}'>
			<view class="cataTitle">
				分类
				<label bindtap='moreTypeBindTap'>
					更多
					<text>></text>
				</label>
			</view>
			<view class="catagory_introduction clearfix">
				<block wx:key="unique" wx:for="{{goodsTypeList}}" wx:for-item="type" wx:for-index="index">
					<block wx:if="{{index<=9}}">
						<label bindtap='goCatogroyTap' data-id='{{type.categoryId}}'>
							<image lazy-load='true' src="{{type.categoryPic}}"></image>
							<text style="font-size:24rpx;">{{type.categoryName}}</text>
						</label>
					</block>
				</block>
			</view>
		</view>
	</view>
</template>
<!--卡券展示直接领取形式-->
<template name="couponType2">
	<view>
		<view class="coupon2">
			<scroll-view scroll-x="true" style="overflow:hidden;">
				<block wx:key="unique" wx:for="{{cardList}}" wx:for-item="card" wx:for-index="index">
					<block wx:if="{{card.have}}">
						<view class="card_one card_append">
							<view class="card_inner inner_append">
								<view class="card_amount">{{card.cardAmount}}
									<label>{{card.cardUnit}}</label>
								</view>
								<view class="card_con">{{card.cardDesc}}</view>
							</view>
							<view class="card_right right_append">
								<view style="color:#999;">已</view>
								<view style="color:#999;">领</view>
								<view style="color:#999;">取</view>
							</view>
						</view>
					</block>
					<block wx:else>
						<view class="card_one" bindtap="userGetCardBindTap" data-id="{{card.cardId}}">
							<view class="card_inner">
								<view class="card_amount">{{card.cardAmount}}
									<label>{{card.cardUnit}}</label>
								</view>
								<view class="card_con">{{card.cardDesc}}</view>
							</view>
							<view class="card_right">
								<view class="right_one">领</view>
								<view class="right_two">取</view>
							</view>
						</view>
					</block>
				</block>
			</scroll-view>
		</view>
	</view>
</template>
<!--卡券跳转领取形式-->
<template name="couponType1">
	<view style="padding-top:20rpx;background:#fff;">
		<image lazy-load='true' mode="widthFix" bindtap="goCouponCenterBind" style="width:750rpx;" src="{{indexConfig.showContent}}" />
	</view>
</template>
<!--文本模块-->
<template name="textItem">
	<view class="text_mode" style="color:#666;">{{indexConfig.showContent}}</view>
</template>
<!--活动模块单个-->
<template name="promotion1">
	<view class="line_one clearfix">
		<image lazy-load='true' mode="widthFix" src="{{indexConfig.wechatIndexMoveBlockEntities[0].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[0].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[0].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[0].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[0].content}}"></image>
	</view>
</template>
<!--活动模块两个并排-->
<template name="promotion2">
	<view class="line_two">
		<view class="clearfix" style="margin:0 auto;width:710rpx;">
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[0].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[0].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[0].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[0].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[0].content}}"></image>
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[1].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[1].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[1].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[1].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[1].content}}"></image>
		</view>
	</view>
</template>

<!--活动模块3个并排-->
<template name="promotion3">
	<view class="line_three">
		<view class="clearfix">
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[0].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[0].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[0].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[0].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[0].content}}"></image>
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[1].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[1].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[1].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[1].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[1].content}}"></image>
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[2].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[2].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[2].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[2].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[2].content}}"></image>
		</view>
	</view>
</template>
<!--活动模块上1下2-->
<template name="promotion4">
	<view class="line_left_one">
		<image lazy-load='true' class="image_left" src="{{indexConfig.wechatIndexMoveBlockEntities[0].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[0].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[0].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[0].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[0].content}}"></image>
		<view class="image_right">
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[1].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[1].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[1].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[1].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[1].content}}"></image>
			<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[2].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[2].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[2].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[2].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[2].content}}"></image>
		</view>
	</view>
</template>
<!--活动模块左一右三-->
<template name="promotion5">
	<view class="line_left_two">
		<image lazy-load='true' class="image_left" src="{{indexConfig.wechatIndexMoveBlockEntities[0].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[0].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[0].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[0].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[0].content}}"></image>
		<view class="image_right">
			<image lazy-load='true' class="right_top" src="{{indexConfig.wechatIndexMoveBlockEntities[1].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[1].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[1].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[1].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[1].content}}"></image>
			<view class="right_bottom">
				<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[2].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[2].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[2].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[2].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[2].content}}"></image>
				<image lazy-load='true' src="{{indexConfig.wechatIndexMoveBlockEntities[3].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[3].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[3].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[3].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[3].content}}"></image>
			</view>
		</view>
	</view>
</template>
<!--活动模块左一右四-->
<template name="promotion6">
	<view class="line_left_four">
		<image lazy-load='true' class="four_image_left" src="{{indexConfig.wechatIndexMoveBlockEntities[0].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[0].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[0].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[0].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[0].content}}"></image>
		<view class="four_image_right">
			<view>
				<image lazy-load='true' class="image_one" src="{{indexConfig.wechatIndexMoveBlockEntities[1].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[1].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[1].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[1].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[1].content}}"></image>
				<image lazy-load='true' class="image_two" src="{{indexConfig.wechatIndexMoveBlockEntities[2].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[2].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[2].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[2].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[2].content}}"></image>
			</view>
			<view>
				<image lazy-load='true' class="image_three" src="{{indexConfig.wechatIndexMoveBlockEntities[3].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[3].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[3].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[3].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[3].content}}"></image>
				<image lazy-load='true' class="image_four" src="{{indexConfig.wechatIndexMoveBlockEntities[4].blockImageUrl}}" bindtap="indexMoveBlockBindTap" data-id="{{indexConfig.wechatIndexMoveBlockEntities[4].id}}" data-relation="{{indexConfig.wechatIndexMoveBlockEntities[4].isRelation}}" data-type="{{indexConfig.wechatIndexMoveBlockEntities[4].showType}}" data-content="{{indexConfig.wechatIndexMoveBlockEntities[4].content}}"></image>
			</view>
		</view>
	</view>
</template>
<!--商品模块单个-->
<template name="goodsType1">
	<view style="position:relative;background:#f9f9f9">
		<view bindtap="goodsMoreBindTap" data-id="{{indexConfig.id}}" hidden="{{indexConfig.showMoreState==1?false:true}}" style="color:#666;text-align:left;padding:20rpx 30rpx;">
			<label style="font-weight：bold;font-size:30rpx;color:#333;">{{indexConfig.moreDesc}}</label>
			<text style="float:right;font-size:26rpx;color:#333;">更多 <text style="display:inline-block;transform:rotate(90deg);">︿</text> </text>
		</view>
		<block wx:if="{{indexConfig.showGoodsNum !=null&&indexConfig.showGoodsNum!=0}}">
			<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
				<block wx:if="{{goodsIndex<indexConfig.showGoodsNum}}">
					<view class="clearfix goods_mode1" style="overflow:hidden;">
						<image lazy-load='true' style="width:100%;" mode="widthFix" bindtap="imageClick" data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
						<block wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">	
							<block wx:for="{{countDownList}}" wx:key="countDownListIndex" wx:for-item="countDownItem">
								<block wx:if="{{countDownItem.actEndTime==goods.promotionEndDate}}">
									<view class='activity_time' style="display:flex">
										<view style="padding:6rpx 0 0 10rpx;text-align:left;font-size:30rpx">秒杀倒计时</view>
										<view style="display:flex;padding:14rpx 40rpx;color:#fff;">
											<label class='clock'>{{countDownItem.day}}</label>:
											<label class='clock'>{{countDownItem.hou}}</label>:
											<label class='clock'>{{countDownItem.min}}</label>:
											<label class='clock'>{{countDownItem.sec}}</label>
										</view>
									</view>
								</block>
							</block>

						</block>
						<view class="goods_desc">
							<view class="desc_title">{{goods.commodityName}}
							</view>
							<view class="desc_spec">{{goods.commodityAdContent}}</view>
							<view class="desc_price">
								<label class="price_tag">￥</label>
								<label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                <label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
								<block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
									<label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
								</block>
                <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
									<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
								</block>
								<block wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">
									<label>
										<text class="promotionDesc">{{goods.promotionName}}</text>
									</label>
								</block>
							</view>
						</view>
					</view>
				</block>
			</block>
		</block>
		<block wx:else>
			<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
				<view class="clearfix goods_mode1">
					<image lazy-load='true' style="width:100%;" mode="widthFix" bindtap="imageClick" data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
					<view class="goods_desc">
						<view class="desc_title">{{goods.commodityName}}</view>
						<view class="desc_spec">{{goods.commodityAdContent}}</view>
						<view class="desc_price">
							<label class="price_tag">￥</label>
              <label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
							<label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
              <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
									<label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
							<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
								<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
							</block>
							<block wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">
									<label>
										<text class="promotionDesc">{{goods.promotionName}}</text>
									</label>
							</block>
						</view>
					</view>
				</view>
			</block>
		</block>
	</view>
</template>
<!--商品模块一排两个-->
<template name="goodsType2">
	<view class="clearfix goods_mode2" style="position:relative;">
		<view bindtap="goodsMoreBindTap" data-id="{{indexConfig.id}}" hidden="{{indexConfig.showMoreState==1?false:true}}" style="color:#666;text-align:left;padding:20rpx 30rpx;">
			<label style="font-weight：bold;font-size:30rpx;color:#333;">{{indexConfig.moreDesc}}</label>
			<text style="float:right;font-size:26rpx;color:#333;">更多 <text style="display:inline-block;transform:rotate(90deg);">︿</text> </text>
		</view>
		<block wx:if="{{indexConfig.showGoodsNum !=null&&indexConfig.showGoodsNum!=0}}">
			<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
				<block wx:if="{{goodsIndex<indexConfig.showGoodsNum}}">
					<view class="mode2_wrap">
						<image lazy-load='true' bindtap="imageClick" data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
						<view class="goods_desc">
							<view class="desc_title">
								{{goods.commodityName}}
							</view>
							<view class="desc_spec">{{goods.commodityAdContent}}</view>
							<view class="desc_price">
								<label class="price_tag" style="font-size:28rpx;">￥</label>
                <label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" style="font-size:36rpx;font-weight: bold;">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
								<label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}" style="font-size:36rpx;font-weight: bold;">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
									<label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
								<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
									<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
								</block>
								<block wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">
									<label>
										<text class="promotionDesc">{{goods.promotionName}}</text>
									</label>
								</block>
							</view>
						</view>
					</view>
				</block>
			</block>
		</block>
		<block wx:else>
			<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
				<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
					<view class="mode2_wrap">
						<image lazy-load='true' bindtap="imageClick" data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
						<view class="goods_desc">
							<view class="desc_title">
								{{goods.commodityName}}
							</view>
							<view class="desc_spec">{{goods.commodityAdContent}}</view>
							<view class="desc_price">
								<label class="price_tag">￥</label>
                <label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" style="font-size:36rpx;font-weight: bold;">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
								<label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}" style="font-size:36rpx;font-weight: bold;">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
									<label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
								<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
									<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
								</block>
								<block wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">
									<label>
										<text class="promotionDesc">{{goods.promotionName}}</text>
									</label>
								</block>
							</view>
						</view>
					</view>
				</block>
			</block>
		</block>
	</view>
</template>
<!--商品模块一排三个-->
<template name="goodsType3">
	<view class="clearfix goods_mode3" style="position:relative;">
		<view bindtap="goodsMoreBindTap" data-id="{{indexConfig.id}}" hidden="{{indexConfig.showMoreState==1?false:true}}" style="color:#666;text-align:left;padding:20rpx 30rpx;">
			<label style="font-weight：bold;font-size:30rpx;color:#333;">{{indexConfig.moreDesc}}</label>
			<text style="float:right;font-size:26rpx;color:#333;">更多 <text style="display:inline-block;transform:rotate(90deg);">︿</text> </text>
		</view>
		<block wx:if="{{indexConfig.showGoodsNum !=null&&indexConfig.showGoodsNum!=0}}">
			<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
				<block wx:if="{{goodsIndex<indexConfig.showGoodsNum}}">
					<view class="mode3_wrap" style="margin-bottom:20rpx;">
						<image lazy-load='true' src="{{goods.commodityMainPic}}" bindtap="imageClick" data-commodityId="{{goods.commodityId}}"></image>
						<view class="goods_desc" style="margin:20rpx 15rpx;">
							<view class="desc_title1">{{goods.commodityName}}</view>
							<view class="desc_price desc_app">
								<label class="price_tag">￥</label>
                <label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
								<label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
									<label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
								<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
									<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
								</block>
							</view>
						</view>
					</view>
				</block>
			</block>
		</block>
		<block wx:else>
			<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
				<view class="mode3_wrap" style="margin-bottom:20rpx;">
					<image lazy-load='true' src="{{goods.commodityMainPic}}" bindtap="imageClick" data-commodityId="{{goods.commodityId}}"></image>
					<view class="goods_desc" style="margin:20rpx 15rpx;">
						<view class="desc_title1">{{goods.commodityName}}</view>
						<view class="desc_price desc_app">
							<label class="price_tag">￥</label>
							<label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
								<label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
              <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
									<label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
							<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
								<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
							</block>
						</view>
					</view>
				</view>
			</block>
		</block>
	</view>
</template>
<!--商品模块一排多个-->
<template name="goodsType4">
	<view bindtap="goodsMoreBindTap" data-id="{{indexConfig.id}}" hidden="{{indexConfig.showMoreState==1?false:true}}" style="color:#666;text-align:left;padding:20rpx 30rpx;">
		<label style="font-weight：bold;font-size:30rpx;color:#333;">{{indexConfig.moreDesc}}</label>
		<text style="float:right;font-size:26rpx;color:#333;">更多 <text style="display:inline-block;transform:rotate(90deg);">︿</text> </text>
	</view>
	<view class="mode_slide">
		<swiper autoplay="true" display-multiple-items="3" interval="2000" circular="false" duration="2000" style='height:408rpx;'>
			<view class="clearfix">
				<block wx:if="{{indexConfig.showGoodsNum !=null&&indexConfig.showGoodsNum!=0}}">
					<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
						<block wx:if="{{goodsIndex<indexConfig.showGoodsNum}}">
							<swiper-item>
								<view class="slide_wrap">
									<image lazy-load='true' src="{{goods.commodityMainPic}}" bindtap="imageClick" data-commodityId="{{goods.commodityId}}"></image>
									<view class="goods_desc" style="margin:20rpx 15rpx;">
										<view class="desc_title1">{{goods.commodityName}}</view>
										<view class="desc_price desc_app">
											<label class="price_tag">￥</label>
											<label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
								      <label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                      <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                          <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                      </block>
											<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
												<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
											</block>
										</view>
									</view>
								</view>
							</swiper-item>
						</block>
					</block>
				</block>
				<block wx:else>
					<block wx:key="unique" wx:for="{{indexConfig.goodsTemplateList}}" wx:for-item="goods" wx:for-index="goodsIndex">
						<swiper-item>
							<view class="slide_wrap">
								<image lazy-load='true' src="{{goods.commodityMainPic}}" bindtap="imageClick" data-commodityId="{{goods.commodityId}}"></image>
								<view class="goods_desc" style="margin:20rpx 15rpx;">
									<view class="desc_title1">{{goods.commodityName}}</view>
									<view class="desc_price desc_app">
										<label class="price_tag">￥</label>
										<label class="price_inner" hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
								      <label class="price_inner" hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                    <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                        <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}" class="goods_price price_append" style="font-size:22rpx;">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                    </block>
										<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
											<label hidden="{{goods.commodityOtUnitDefault==1?false:true}}" class="goods_price price_append" style="font-size:22rpx;">{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
										</block>
									</view>
								</view>
							</view>
						</swiper-item>
					</block>
				</block>
			</view>
		</swiper>
	</view>
</template>
<!--推荐展示商品一排一个-->
<template name="recommand1">
	<view class="section">
		<block wx:key="unique" wx:for="{{categoryIdList}}" wx:for-item="category">
			<view class="section_title">{{category.indexCategoryName}}
			</view>
			<block wx:key="unique" wx:for="{{commodityList}}" wx:for-item="goods">
				<block wx:if="{{goods.indexCategoryId==category.indexCategoryId}}">
					<view class="recommadOne" bindtap="imageClick1" data-commodityId="{{goods.commodityId}}">
						<view class="clearfix one_inner">
							<image lazy-load='true' src="{{goods.commodityMainPic}}" mode="widthFix"></image>
							<view class="right_inner">
								<view class="right_title">{{goods.commodityName}}</view>
								<view class="right_desc">{{goods.commodityAdContent}}</view>
								<view class="right_price" style="right:0;">
									<text style="font-size:24rpx;">￥</text>{{tools.sub.formatAmount(goods.goodsPrice,1)}}
									<label class="goods_price price_append" style="font-size:22rpx;" hidden='{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice?false:true}}'>
										￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}
									</label>
									<text hidden='{{commoditySpec!=null?false:true}}' style="margin-right:20rpx;float:right;color:#666;font-size:26rpx;">规格：{{commoditySpec}}</text>
								</view>
							</view>
						</view>
					</view>
				</block>
			</block>
		</block>
	</view>
</template>
<!--推荐展示商品一排两个-->
<template name="recommand2">
	<view class="section">
		<block wx:key="unique" wx:for="{{categoryIdList}}" wx:for-item="category">
			<view class="section_title">{{category.indexCategoryName}}
			</view>
			<!--上下结构-->
			<view class="flex-wrp clearfix">
				<block wx:key="unique" wx:for="{{commodityList}}" wx:for-item="goods">
					<block wx:if="{{goods.indexCategoryId==category.indexCategoryId}}">
						<view class="flex-item" style="position:relative;">
							<view class="goods_pic">
								<block wx:if="{{goods.commoditySaleWay==1}}">
									<view class='soonlist'>即将上市</view>
								</block>
								<block wx:elif="{{goods.commoditySaleWay==4}}">
									<view class='soonlist'>已售馨</view>
								</block>
								<block wx:key="unique" wx:for="{{goods.promotionList}}" wx:for-item="promotion">
									<view class="eventWrap">
										<image lazy-load='true' src="{{event}}"></image>
										<block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
											<text>秒杀</text>
										</block>
										<block wx:if="{{promotion.promotionType=='TUANGOU'}}">
											<text>团购</text>
										</block>
										<block wx:if="{{promotion.promotionType=='TEJIA'}}">
											<text>特价</text>
										</block>
										<block wx:if="{{promotion.promotionType=='QUDUAN'}}">
											<text>区段</text>
										</block>
									</view>
								</block>
								<!--3人成团-->

								<image lazy-load='true' bindtap='imageClick' data-storeId='{{goods.commoditySupplierId}}' data-commodityId='{{goods.commodityId}}' src="{{goods.commodityMainPic}}"> </image>

							</view>
							<!--<block wx:if="{{goods.commodityAdContent != null && goods.commodityAdContent !=''}}">
              <label class='goods_adv'>{{goods.commodityAdContent}}</label>
            </block>-->
							<label class="goods_title">{{goods.commodityName}}</label>
							<label class="goods_price" style="font-size:28rpx">￥<text style="font-size:36rpx">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</text></label>
							<label class="goods_price price_append" style="font-size:22rpx;" hidden='{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice?false:true}}'>￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
							<text hidden='{{commoditySpec!=null?false:true}}' style="margin-right:20rpx;float:right;color:#666;font-size:26rpx;">规格：{{commoditySpec}}</text>
						</view>
					</block>
				</block>
			</view>
		</block>
	</view>
</template>

<!-- 视频模块 -->
<template name="video">
	<view style="width:750rpx;padding:20rpx 0;background: #fff;">
		<video style="width:750rpx;" show-play-btn object-fit="fill" show-center-play-btn="true" controls show-fullscreen-btn='true' src="{{indexConfig.showContent}}"></video>
	</view>
</template>
<!-- 视频模块 -->