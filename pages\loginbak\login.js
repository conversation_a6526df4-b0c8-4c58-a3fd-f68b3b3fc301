const app = getApp()
var backUrl; //登录携带的地址参数
var visitType;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    img1: app.imageUrl + 'tongyong.png',
    img2: app.imageUrl + 'xinren.png',
    img3: app.imageUrl + 'integral.png',
    img4: app.imageUrl + 'xianjin.png',
    img5: app.imageUrl + 'youhui.png',
    img6: app.imageUrl + 'shengri.png',
    img7: app.imageUrl + 'zhifu.png',
    img8: app.imageUrl + 'hongbao.png',
    img9: app.imageUrl + 'hongbao2.png',
    loginAccount: "", //登录账号
    password: "", //登录密码
    currentTab: 1,
    secondDesc: "获取短信验证码",
    smsCode: "", //短信验证码 
    second: 60, //倒计时秒数
    wechat: app.imageUrl + 'we_chat.png',
    isFromBack: false,
    putCardHidden: true,
    scoreHidden: true,
    giftBox: app.imageUrl + 'giftBox.png',
    loginAuthorType: 1
  },
  goToHome: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log("我跳转到了这个login页面")
    console.log(options.type);
    console.log(options.backUrl);
    var that = this;
    app.init_getExtMessage().then(res => {
      console.log(res);
      console.log("这是res页面的返回结果")
      var visitType = options.type;
      var backUrl = decodeURIComponent(decodeURIComponent(options.backUrl));
      that.getStoreInfoSetting(res.companyId);
      that.queryStoreInfoByStoreId(res.storeId);
      that.setData({
        visitType: visitType,
        backUrl: backUrl
      })
    });
  },
  getStoreInfoSetting: function (companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wholesaleStore/getStoreInfoSetting',
      data: {
        "companyId": companyId
      },
      success: function (res) {
        var returnResult = res.data.returnResult;
        var loginAuthorType = 1;
        if (returnResult != null && returnResult != undefined) {
          var authorizationBean = returnResult.authorizationBean;
          if (authorizationBean != undefined) {
            loginAuthorType = authorizationBean.authorizationType;
          }
        }
        that.setData({
          loginAuthorType: loginAuthorType
        })
      }
    })
  },
  queryStoreInfoByStoreId: function (storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryStoreInfoByStoreId',
      data: {
        "storeId": storeId
      },
      success: function (res) {
        var storeName = res.data.storeName;
        var storeImage = res.data.storeImage;
        that.setData({
          defaultLogo: storeImage,
          storeName: storeName
        })
      }
    })
  },
  /**
   * 新版登录
   */
  getUserProfileBindTap: function () {
    var that = this;
    wx.getUserProfile({
      "desc": "获取您的头像，昵称",
      success: function (res) {
        var userInfo = res.userInfo
        that.bindGetUserInfo(userInfo);
      },
      fail: function (error) {
      }
    })
  },
  bindGetUserInfo: function (userInfo) {
    if (userInfo != undefined) {
      var avatarUrl = userInfo.avatarUrl;
      var nickName = userInfo.nickName;
      var gender = userInfo.gender;
      var that = this;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "openId": app.getOpenId(),
          "avatarUrl": avatarUrl,
          "nickName": nickName,
          "gender": gender,
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        url: app.projectName + '/newAppletLogin/newRetailLogin',
        success: function (res) {

          var returnCode = res.data.returnCode;
          if (returnCode == 0) {
            var userSession = {
              "userId": res.data.loginInfoBean.companyId,
              "userName": res.data.loginInfoBean.companyName,
              "userRole": "2",
              "loginAccount": res.data.loginInfoBean.loginAccount, //登录账号
              "identity": res.data.loginInfoBean.identity,
              "telephone": res.data.loginInfoBean.telephone,
              "headImage": res.data.loginInfoBean.headImage
            };
            var isLoginSession = true;
            wx.removeStorageSync("userSession");
            wx.removeStorageSync("isLoginSession");
            app.setStorage({
              key: 'userSession',
              data: userSession
            });
            app.setStorage({
              key: 'isLoginSession',
              data: isLoginSession
            });
            var loginFlag = res.data.loginFlag; //是否赠送注册卡券
            var isSendScoreFlag = res.data.isSendScoreFlag; //是否赠送注册积分
            var sendScore = res.data.sendScore;
            var storeCardList = res.data.storeCardList;
            if (loginFlag || isSendScoreFlag) {
              that.setData({
                loginFlag: loginFlag,
                isSendScoreFlag: isSendScoreFlag,
                sendScore: sendScore,
                storeCardList: storeCardList,
                putCardHidden: false
              });
              setTimeout(function () {
                that.queryLoginConfig();
              }, 3000);
            } else {
              that.queryLoginConfig();
            }
          } else {
            app.showModal({
              title: '提示',
              content: "登录失败"
            });
          }
        }
      })
    }
  },
  wechatAuthionTelephone:function(e){
    var that = this;
    /*采用新接口获取用户的手机号*/
    var code = e.detail.code;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    var odbtoken = wx.getStorageSync('odbtoken');
    if(odbtoken != null && odbtoken.length>0){
      console.log("testlujing1")
      if(code != null && code.length>0){
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/newAppletLogin/loginByTel',
          data: {
            "telephone": encryptedData,
            "codeReqTel":code,
            "platIv":iv,
            "companyId": app.getExtCompanyId(),
            "odbtoken":odbtoken
          },
          success: function (res) {
            var returnCode = res.data.code;
            var loginToken = res.data.loginToken;
            var odbtoken = res.data.odbtoken;
            var clientOpenId = res.data.clientOpenId;
            if (returnCode == 1) {
              var isLoginSession = true;
              wx.removeStorageSync("isLoginSession");
              wx.setStorageSync(
              'loginToken',loginToken);
              app.setStorage({
                key: 'isLoginSession',
                data: isLoginSession
              });
              app.setStorage({
                key: 'clientOpenId',
                data: clientOpenId
              })
              setTimeout(function () {
                that.queryLoginConfig();
              }, 500);
            } else {
              app.showModal({
                title: '提示',
                content: "获取失败，请您关闭小程序重试"
              });
              wx.removeStorageSync("odbtoken");
              wx.removeStorageSync("loginToken");
              wx.removeStorageSync("userSession");
              wx.removeStorageSync("isLoginSession");
              wx.removeStorageSync("");
              app.init_userOpenId();
            }
          },
          fail: function () {
            app.showModal({
              title: '提示',
              content: "获取失败，请您关闭小程序重试"
            });
            wx.removeStorageSync("odbtoken");
            wx.removeStorageSync("loginToken");
            wx.removeStorageSync("userSession");
            wx.removeStorageSync("isLoginSession");
            wx.removeStorageSync("");
            app.init_userOpenId();
          }
        })
      }
    }
    else{
      app.init_userOpenId().then(res => {
        console.log(res);
        odbtoken = res;
        console.log("testlujing2")
        if(code != null && code.length>0){
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/newAppletLogin/loginByTel',
            data: {
              "telephone": encryptedData,
              "codeReqTel":code,
              "platIv":iv,
              "companyId": app.getExtCompanyId(),
              "odbtoken":odbtoken
    
    
            },
            success: function (res) {
              var returnCode = res.data.code;
              var loginToken = res.data.loginToken;
              var odbtoken = res.data.odbtoken;
              if (returnCode == 1) {
                var isLoginSession = true;
                wx.removeStorageSync("isLoginSession");
                wx.setStorageSync(
                'loginToken',loginToken);
                app.setStorage({
                  key: 'isLoginSession',
                  data: isLoginSession
                });
                setTimeout(function () {
                  that.queryLoginConfig();
                }, 500);
              } else {
                app.showModal({
                  title: '提示',
                  content: "获取失败，请您关闭小程序重试"
                });
                wx.removeStorageSync("odbtoken");
                wx.removeStorageSync("loginToken");
                wx.removeStorageSync("userSession");
                wx.removeStorageSync("isLoginSession");
                wx.removeStorageSync("");
                app.init_userOpenId();
              }
            },
            fail: function () {
              app.showModal({
                title: '提示',
                content: "获取失败，请您关闭小程序重试"
              });
              wx.removeStorageSync("odbtoken");
              wx.removeStorageSync("loginToken");
              wx.removeStorageSync("userSession");
              wx.removeStorageSync("isLoginSession");
              wx.removeStorageSync("");
              app.init_userOpenId();
            }
          })
        }
      })
      
    }

  },
  wechatAuthionTelephone1: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.checkSession({
        success() {
          //session_key 未过期，并且在本生命周期一直有效
          var session_Key = wx.getStorageSync("session_Key");
          var openId = wx.getStorageSync("openId");
          that.getUserTelephone(iv, encryptedData, session_Key,openId);
        },
        fail() {
          // session_key 已经失效，需要重新执行登录流程
          wx.login({
            success: function (res) {
              if (res.code) {
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded'
                  },
                  method: "POST",
                  url: app.projectName + '/newAppletLogin/getUserOpenId',
                  //url: app.projectName + '/newAppletLogin/clientValid',
                  data: {
                    "companyId": app.getExtCompanyId(),
                    //"jscode": res.code
                    "code":res.code
                  },
                  success: function (res) {
                    var openid = res.data.openid;
                    var session_Key = res.data.session_Key;
                    wx.removeStorageSync("openId");
                    app.setStorage({
                      key: 'openId',
                      data: openid
                    });
                    /*var odbtoken = res.data.odbtoken;
                    wx.removeStorageSync("odbtoken");
                    app.setStorage({
                      key: 'odbtoken',
                      data: odbtoken
                    })*/
                    wx.removeStorageSync("session_Key");
                    app.setStorage({
                      key: 'session_Key',
                      data: session_Key
                    });
                    that.getUserTelephone(iv, encryptedData,session_Key, openid);
                  }
                })
              }
            }
          })
        }
      })
    } else {
      app.showModal({
        title: '提示',
        content: "获取失败"
      });
    }
  },
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey, openid) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newAppletLogin/newRetailLoginForTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "openId": openid,
        "userId": app.getUserId()
      },
      success: function (res) {
        var returnCode = res.data.returnCode;
        if (returnCode == 0) {
          var userSession = {
            "userId": res.data.loginInfoBean.companyId,
            "userName": res.data.loginInfoBean.companyName,
            "userRole": "2",
            "loginAccount": res.data.loginInfoBean.loginAccount, //登录账号
            "identity": res.data.loginInfoBean.identity,
            "telephone": res.data.loginInfoBean.telephone,
            "headImage": res.data.loginInfoBean.headImage
          };
          var isLoginSession = true;
          wx.removeStorageSync("userSession");
          wx.removeStorageSync("isLoginSession");
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
          app.setStorage({
            key: 'isLoginSession',
            data: isLoginSession
          });
          var loginFlag = res.data.loginFlag; //是否赠送注册卡券
          var isSendScoreFlag = res.data.isSendScoreFlag; //是否赠送注册积分
          var sendScore = res.data.sendScore;
          var storeCardList = res.data.storeCardList;
          if (loginFlag || isSendScoreFlag) {
            that.setData({
              loginFlag: loginFlag,
              isSendScoreFlag: isSendScoreFlag,
              sendScore: sendScore,
              storeCardList: storeCardList,
              putCardHidden: false
            });
            setTimeout(function () {
              that.queryLoginConfig();
            }, 3000);
          } else {
            that.queryLoginConfig();
          }
        } else {
          app.showModal({
            title: '提示',
            content: "登录失败"
          });
        }
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "获取失败，请您关闭小程序重试"
        });
      }
    })
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail 
    this.setData({
      avatarUrl,
    })
  },
  /**
   * 查询登录跳转配置信息
   */
  queryLoginConfig: function () {
    var that = this;
  
    if (that.data.backUrl != null && that.data.backUrl != "") {
      console.log("222")
      if (that.data.visitType == 1) {
        wx.switchTab({
          url: that.data.backUrl
        });
      } else if (that.data.visitType == 2) {
        app.redirectToPage(that.data.backUrl);
      } else {
        wx.switchTab({
          url: "/pages/index/index"
        });
      }
    } else {
      wx.switchTab({
        url: "/pages/index/index"
      });
    }
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})