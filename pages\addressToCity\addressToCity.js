var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    myAddress: app.imageUrl + 'locate.png',
    localUserCity: "",
    localUserAddress: "",
    latitude: "",
    longitude: "",
    searchData: [],
    queryType: 1, //1:附近地址 2：搜索地址
    region: ['江苏省', '南京市', '雨花台区'],
    regionSwitch: false,   //是否显示定位或者选择地址
    detailAddress: '',   //详细地址
    houseNumber: ''   //门牌号
  },
  goShowCity: function () {
    app.navigateToPage('/pages/showCity/showCity?localCity=' + this.data.localUserCity);
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    if (JSON.stringify(options) === '{}') {

    } else {
      this.setData({
        region: [options.province, options.city, options.area],
        detailAddress: options.address,
        houseNumber: options.houseNumber
      })
    }


    that.getUserLocation();
  },
  getUserLocation: function () {
    let vm = this;
    var that = this;
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取真实地理位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             that.setData({
    //               regionSwitch: false
    //             })
    //             // app.turnBack();
    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })
    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {
    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                   app.turnBack();
    //                 }
    //               }
    //             })
    //           }
    //         },
    //         fail: function () {
    //           app.turnBack();
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       vm.getLocation();
    //       that.setData({
    //         regionSwitch: false
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == true) {
    //       vm.getLocation();
    //     } else {
    //       vm.getLocation();
    //       that.setData({
    //         regionSwitch: false
    //       })
    //     }
    //   }
    // })
  },
  /**
   * 获取定位
   */
  getLocation: function () {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // wx.getLocation({
    //   type: 'gcj02',
    //   altitude: true,
    //   complete: function (res) {
    //     if (res.errMsg == "getLocation:ok") {
    //       var latitude = res.latitude;
    //       var longitude = res.longitude;
    //       qqmapsdk.reverseGeocoder({
    //         location: {
    //           latitude: latitude,
    //           longitude: longitude
    //         },
    //         success: function (res) { //成功后的回调
    //           var res = res.result;
    //           var localUserCity = res.address_component.city;
    //           var localUserAddress = res.address;
    //           that.setData({
    //             localUserCity: localUserCity
    //           });
    //           qqmapsdk.geocoder({
    //             //获取表单传入地址
    //             address: res.address, //地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
    //             success: function (res) { //成功后的回调
    //               var res = res.result;
    //               var latitude = res.location.lat;
    //               var longitude = res.location.lng;
    //               that.setData({
    //                 latitude: latitude,
    //                 longitude: longitude
    //               })
    //               that.nearbySearch(localUserCity, localUserAddress, latitude, longitude);
    //             },
    //             fail: function (error) {
    //               console.error(error);
    //             },
    //             complete: function (res) {
    //             }
    //           })
    //         },
    //         fail: function (error) {
    //           console.error(error);
    //         },
    //         complete: function (res) {
    //         }
    //       })
    //     } else {
    //       that.setData({
    //         regionSwitch: false
    //       })
    //     }
    //   }
    // })
  },
  /**
   * 联想词输入功能
   */
  localAddressBindInput: function (e) {
    // var that = this;
    // // 实例化API核心类
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // var address = e.detail.value.replace(/\s+/g, '');
    // if (address.length > 0) {
    //   qqmapsdk.getSuggestion({
    //     location: {
    //       latitude: that.data.latitude,
    //       longitude: that.data.longitude
    //     },
    //     //获取输入框值并设置keyword参数
    //     keyword: address, //用户输入的关键词，可设置固定值,如keyword:'KFC'
    //     region: that.data.localUserCity, //设置城市名，限制关键词所示的地域范围，非必填参数
    //     success: function (res) { //搜索成功后的回调
    //       that.setData({
    //         searchData: res.data,
    //         queryType: 2
    //       })
    //     },
    //     fail: function (error) {
    //       console.error(error);
    //     }
    //   });
    // }
  },
  /**
   * 选择当前位置
   * @param {*} e 
   */
  selectLocatAddressBindTap: function (e) {
    var that = this;
    var address = e.currentTarget.dataset.address;
    var latitude = e.currentTarget.dataset.latitude;
    var longitude = e.currentTarget.dataset.longitude;
    var province = e.currentTarget.dataset.province;
    var city = e.currentTarget.dataset.city;
    var area = e.currentTarget.dataset.area;
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      province: province,
      city: city,
      area: area,
      latitude: latitude,
      longitude: longitude,
      address: address
    });
    app.turnBack();
  },
  nearbySearch: function (localUserCity, localUserAddress, latitude, longitude) {
    // var that = this;
    // // 实例化API核心类
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // qqmapsdk.search({
    //   region: localUserCity,
    //   keyword: localUserAddress, //搜索关键词
    //   location: {
    //     latitude: latitude,
    //     longitude: longitude
    //   }, //设置周边搜索中心点
    //   success: function (res) { //搜索成功后的回调
    //     that.setData({
    //       searchData: res.data,
    //       queryType: 1
    //     })
    //   },
    //   fail: function (res) {
    //   }
    // });
  },
  bindRegionChange: function (e) {
    this.setData({
      region: e.detail.value
    })
  },
  saveAddressBindTap: function () {
    var that = this
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      address: that.data.region[0] + that.data.region[1] + that.data.region[2] + that.data.detailAddress,
      houseNumber: that.data.houseNumber,
      province: that.data.region[0],
      city: that.data.region[1],
      area: that.data.region[2]
    });
    app.turnBack();
  },
  addressBindInput: function (e) {
    this.setData({
      detailAddress: e.detail.value
    })
  },
  houseNumberBindInput: function (e) {
    this.setData({
      houseNumber: e.detail.value
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})