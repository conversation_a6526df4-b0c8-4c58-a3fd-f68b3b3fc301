// pages/customerScore/customerScore.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    score: 0,
    pageSize: 20,
    currentPage: 1,
    searchLoading: false,
    loginState: 0,
    exchangeList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    app.init_getExtMessage().then(res => {
      that.queryExchangeCommodity(res.companyId, res.storeId);
    });
  },
  onShow() {
    var that = this;
    var childObj = that.selectComponent('#test');
    childObj.setData({
      list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,
      selected: 10,
      color: app.globalData.bottomBean.color,
      selectedColor: app.globalData.bottomBean.selectedColor
    });
    let flag = true;
    app.globalData.bottomBean.wechatAppletIndexBottomContentEntity.forEach(item => {
      if (item.pagePath == 10) {
        flag = false
      }
    })
    childObj.setData({
      showFlag: flag
    });
  },
  goToLoginBindTap: function () {
    app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
  },
  /*
  跳转会员卡转入积分
  */
  goCardIndexBind: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          if (cardList.length == 1) {
            var no = cardList[0].cardId;
            app.navigateToPage('/pages/myvipCard/myvipCard?cardNo=' + no);
          } else {
            app.navigateToPage('/pages/vipCard/vipCard');
          }
        } else {

        }
      }
    })
  },
  /**
   * 查询指定兑换商品详细信息
   */
  exchangeCommodityBindTap: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    } else {
      var exchangeCommodityId = e.currentTarget.dataset.id;
      app.navigateToPage('/pages/scoreGoods/scoreGoods?exchangeCommodityId=' + exchangeCommodityId);
    }
  },
  /**
   * 查询指定店铺的兑换商品信息
   */
  queryExchangeCommodity: function (companyId, storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/queryExchangeCommodity',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "localStoreId": storeId,
        "companyId": companyId,
        "pageSize": that.data.pageSize,
        "currentPage": that.data.currentPage
      },
      success: function (res) {
        var newCommodityBaseList = res.data.exchangeList;
        var oldCommodityBaseList = that.data.exchangeList;
        if (newCommodityBaseList != null && newCommodityBaseList.length > 0) {
          newCommodityBaseList = oldCommodityBaseList.concat(newCommodityBaseList);
        } else {
          newCommodityBaseList = oldCommodityBaseList;
        }
        that.setData({
          exchangeList: newCommodityBaseList
        })
      }
    })
  },
  onReachBottom: function () {
    var that = this;
    that.setData({
      currentPage: that.data.currentPage + 1
    });
    that.queryExchangeCommodity(app.getExtCompanyId(), app.getExtStoreId());
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})