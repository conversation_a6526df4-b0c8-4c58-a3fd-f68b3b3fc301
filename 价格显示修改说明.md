# 商品详情页价格显示修改说明

## 修改需求
根据用户要求，修改商品详情页的价格显示逻辑：
1. 价格水平排列显示
2. 当有划线价格（cutPrice或cutPriceOM）显示时，主价格显示"会员价格"中文
3. 当没有划线价格时，主价格显示"零售价格"中文  
4. 划线价格显示时，cutPrice部分显示"零售价格"中文

## 修改内容

### 1. WXML文件修改 (pages/goodsDetail/goodsDetail.wxml)

#### 1.1 主价格显示区域修改
```xml
<label class="goods_price" style="display:flex;align-items:center;flex-wrap:wrap;">
    <!-- 零售价显示 -->
    <view wx:if="{{commodityUnitOtDefault==1}}" style="display:flex;align-items:center;margin-right:20rpx;">
        <text style="font-size:24rpx;color:#FF7E00;margin-right:8rpx;">
            {{(cutPrice>0&&cutPrice-showGoodsRangePrice>0) ? '会员价格' : '零售价格'}}
        </text>
        <text style="font-size:36rpx;font-weight:bold;color:#FF7E00;">￥{{showGoodsRangePrice}}</text>
        <text style="font-size:24rpx;color:#FF7E00;" wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOtUnit}}</text>
    </view>
    
    <!-- 会员价显示 -->
    <view wx:if="{{commodityUnitOmDefault==1}}" style="display:flex;align-items:center;margin-right:20rpx;">
        <text style="font-size:24rpx;color:#FF7E00;margin-right:8rpx;">
            {{(cutPriceOM>0&&cutPriceOM-goodsPriceOM>0) ? '会员价格' : '零售价格'}}
        </text>
        <text style="font-size:36rpx;font-weight:bold;color:#FF7E00;">￥{{tools.sub.formatAmount(goodsPriceOM,2)}}</text>
        <text style="font-size:24rpx;color:#FF7E00;">/{{goodsOMUnit}}</text>
    </view>
</label>
```

#### 1.2 划线价格显示修改
```xml
<view class="clearfix" style="display:flex;align-items:center;flex-wrap:wrap;margin-top:10rpx;">
    <!-- 会员价划线价格 -->
    <view hidden="{{cutPriceOM>0&&cutPriceOM-goodsPriceOM>0?false:true}}" 
        style="display:flex;align-items:center;margin-right:20rpx;">
        <text style="font-size:22rpx;color:#666;margin-right:5rpx;">零售价格</text>
        <text style="color:#666;text-decoration:line-through;font-size:24rpx;">
            ￥{{tools.sub.formatAmount(cutPriceOM,2)}}/{{goodsOMUnit}}
        </text>
    </view>
    
    <!-- 零售价划线价格 -->
    <view hidden="{{cutPrice>0&&cutPrice-showGoodsRangePrice>0?false:true}}" 
        style="display:flex;align-items:center;margin-right:20rpx;">
        <text style="font-size:22rpx;color:#666;margin-right:5rpx;">零售价格</text>
        <text style="color:#666;text-decoration:line-through;font-size:24rpx;">
            ￥{{tools.sub.formatAmount(cutPrice,2)}}/{{goodsOtUnit}}
        </text>
    </view>
</view>
```

#### 1.3 购物车弹窗价格显示修改
同样应用了新的价格显示逻辑，保持与主显示区域一致。

## 技术实现细节

### 价格显示逻辑
1. **主价格标签判断**：
   - 有划线价格时：显示"会员价格"
   - 无划线价格时：显示"零售价格"

2. **划线价格标签**：
   - 统一显示"零售价格"

3. **布局方式**：
   - 使用flex布局实现水平排列
   - 使用flex-wrap支持换行
   - 适当的间距和对齐方式

### 样式设计
- 主价格使用橙色(#FF7E00)突出显示
- 划线价格使用灰色(#666)并添加删除线
- 价格标签使用较小字体区分
- 保持响应式布局

## 兼容性说明
- 保持了原有的商品单位换算显示
- 保持了原有的SKU属性显示逻辑
- 保持了原有的销量显示功能
- 兼容不同价格类型的商品

## 测试建议
1. 测试有划线价格的商品显示效果
2. 测试无划线价格的商品显示效果
3. 测试只有一种价格类型的商品
4. 测试有两种价格类型的商品
5. 测试购物车弹窗的价格显示
6. 测试不同屏幕尺寸下的显示效果
