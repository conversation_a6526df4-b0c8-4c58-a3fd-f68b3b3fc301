page {
  background: #f5f5f5;
}

.update_address {
  margin-top: 10px;
  background: #fff;
  padding: 0 15px;
  margin-bottom: 10px;
}

.update_address input {
  line-height: 44px;
  height: 44px;
  border-bottom: 1px solid #ececec;
  font-size: 15px;
}

.picker {
  font-size: 15px;
  line-height: 44px;
  height: 44px;
  border-bottom: 1px solid #ececec;
}

.detail_address {
  padding: 10px 0;
}

.detail_address textarea {
  width: 100%;
  height: 60px;
}

.set_default {
  height: 30px;
  background: #fff;
  padding: 5px 15px;
  font-size: 14px;
  line-height: 30px;
}

/* .set_default checkbox {
  float: right;
} */

.add_address {
  height: 36px;
  width: 100%;
  padding: 10px 0;
}

.add_address button {
  margin: 0 10px;
  height: 100%;
  background: #4ac15d;
  color: #fff;
  font-size: 14px;
  line-height: 36px;
}

checkbox-group {
  display:block;
  float:right;
}
.update_address label {
  
  border-bottom: 1px solid #ececec;
  font-size: 14px;
  color: #333;
  background: #fff;
  display: block;

}

.update_address label text {
  float: left;
  width: 120rpx;
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
}

.update_address label input {
  height: 90rpx;
  line-height: 90rpx;
  border-bottom: 0;

}
.update_address label textarea{
  width:520rpx;
  height: 88rpx;
  padding: 26rpx 0;
  line-height: 44rpx;
}
.historyWrap view{
  border-radius:70rpx;
  text-align:center;
  left:30rpx;
  right:30rpx;
  margin:0 30rpx;
  height:80rpx;
  line-height:80rpx;
  position:fixed;
  bottom:160rpx;
  background:#FF7E00;
  color:#fff;
}