<import src="../CouponTem/CouponTem.wxml" />
<block wx:if="{{loginFlag||isSendScoreFlag}}">
	<template is="coupon2" data="{{img2,img3,img4,storeCardList,isSendScoreFlag,sendScore}}" />
</block>
<!--券弹出层-->
<view class="pop_bg" hidden='{{putCardHidden}}'>
</view>
<view class="topWrap">
	<image src="{{defaultLogo}}">
	</image>
	<view>{{storeName}}</view>
</view>
<view style="text-align:center;font-size:30rpx;">您还未登录</view>
<view style="text-align:center;color:#666;font-size:26rpx;margin-top:20rpx;">请您先登录在去下单哦!</view>
<view style="width:600rpx;margin:0 auto;">
	<button class='confirm_btn' style="background:#fff;border:1px solid #FF7E00;color:#FF7E00"
		bindtap='goToHome'>暂不登录</button>
		<button class='confirm_btn' open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">立即登录</button>
	<!--<block wx:if="{{loginAuthorType==2}}">
		<button class='confirm_btn' open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">立即登录</button>
	</block>
	<block wx:else>
		<button class='confirm_btn' bindtap="getUserProfileBindTap">立即登录</button>
		<button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
			<image class="avatar" src="{{avatarUrl}}"></image>
		</button> 
		<input type="nickname" class="weui-input" placeholder="请输入昵称"/>
	</block>-->
</view>