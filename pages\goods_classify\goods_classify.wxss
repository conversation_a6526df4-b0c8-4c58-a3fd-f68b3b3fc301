page{
  /*margin-bottom:400rpx;*/
}
.location_box {
  height: 52rpx;
  position: fixed;
  top: 0px;
  left: 0;
  font-size: 28rpx;
  line-height: 26px;
  width: 100%;
  padding: 10px 0 0 0;
  z-index: 100;
  background: #ececed;
}

.location_box text {
  line-height: 52rpx;
  height: 52rpx;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #fff;
  color: #fff;
  border-radius: 30rpx !important;
  padding: 0 2%;
  overflow: hidden;
}

/*.location_box icon {
  float: left;
  position: absolute;
  top: 32rpx;
  left: 9%;
  z-index: 10;
  line-height: 34px;
}*/
.location_box icon {
  float: left;
  position: absolute;
  top: 32rpx;
  left: 21%;
  z-index: 10;
  line-height: 34px;
}

/*.location_box input {
  line-height: 62rpx;
  height: 62rpx;
  width: 76%;
  display: block;
  float: left;
  margin: 0 6%;
  background: #fff;
  color: #272727;
  border-radius: 14rpx;
  padding-left: 8%;
  padding-right: 4%;
}*/

.location_box input {
  line-height: 62rpx;
  height: 62rpx;
  width: 65%;
  display: block;
  float: left;
  margin: 0 6%;
  margin-left:18%;
  background: #fff;
  color: #272727;
  border-radius: 14rpx;
  padding-left: 8%;
  padding-right: 4%;
}

.classify_box {
  margin-top: 114rpx !important;
}

.classify_box ::after {
  clear: both;
  content: "";
  display: block;
  float: none;
}

.classify_left {
  width: 28%;
  text-align: center;
  /**height:100%;**/
  background: #fff;
  /* border-right: 1rpx solid #c6c7c8; */
  position: fixed;
  top: 190rpx;
  padding-bottom:180rpx;
  bottom:0;
  z-index: 20;
  background-color: #F2F2F2;
}

/**.classify_left second-menu::last-child{
  padding-bottom:50px;
}**/

.classify_left text {
  display: block;
  line-height: 90rpx;
  color: #000;
  border-left: 4px solid #f2f2f2;
  font-size: 28rpx;
  text-align: left;
  padding-left: 5px;
}

.active {
  color: #FF7E00 !important;
  border-left: 6rpx solid #FF7E00 !important;
  font-size: 30rpx !important;
  margin-bottom: 10rpx !important;
  background-color: #fff;
}

.classify_right {
  padding-left: 28%;
  width: 72%;
  /*padding-bottom:180rpx;*/
  position:fixed;
  top:190rpx;
  padding-bottom:18rpx;
}

.second-menu view {
  border-left: 4px solid #fff;
  position: relative;
  font-size: 12px;
  text-align: left;
  padding-left: 15px;
  height: 30px;
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}

.second-menu view text {
  border-left: none;
  padding-left: 0px;
  width: 12rpx;
  height: 12rpx;
  border-radius: 100%;
  background: #F2F2F2;
  position: absolute;
  left: 4px;
  top: 24rpx;
  line-height: 30px;
}

.second-active {
  background: #FF7E00 !important;
}

/**单个商品**/

.single_goods {
  /**background: url('https://www.cn2b2c.com/gsf/img/wa/classify_bg.png');
  background-size: 100% 100%;height: 120px;**/
  width: 100%;
  position: relative;
  margin-bottom: 10px;
}

.single_goods image {
  width: 100%;
}

.single_goods label {
  position: absolute;
  bottom: 14%;
  /**bottom:20px;**/
  left: 5%;
}

.tips_box {
  color: #beb58e;
  border: 1px solid #beb58e;
  border-radius: 3px;
  font-size: 10px;
  padding: 1px 3px;
  /**position: absolute;
  bottom: 76%;
  bottom: 100px;**/
  left: 5%;
}

.price_box {
  color: #0081cc;
  font-weight: bold;
}

.big_title {
  margin-top: 5px;
  color: #3a3a3c;
  font-size: 15px;
  font-weight: bold;
  display: block;
}

.little_title {
  color: #4c4c4e;
  font-size: 12px;
  display: block;
  line-height: 17px;
  height: 34px;
}

.titleBox {
  font-size: 13px;
  display: block;
  text-align: center;
  line-height: 40px;
  color: #666;
}

.classify_goods {
  width: 33%;
  height: 110px;
  margin-top: 10px;
  float: left;
}

.classify_goods image {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  background: green;
  display: block;
  border-radius: 50%;
  overflow: hidden;
}

.classify_goods text {
  text-align: center;
  font-size: 13px;
  color: #000;
  display: block;
  line-height: 32px;
}

.rightWrap {
  width: 94%;
  padding: 0px 3%;
  margin:10px 0;
  /*border-bottom:1px dashed #ccc;*/
}

.rightMainPic {
  width: 100px;
  height: 100px;
  float: left;
  border-radius: 8rpx;
}

.productAttr {
  position: relative;
  padding-left: 110px;
  height: 100%;
}

.specialMark {
  border-radius: 4px;
  font-size: 12px;
  padding: 1px 6px;
  background: #f60;
  color: #fff;
  display: inline-block;
}

.productName {
  display: block;
  font-size: 14px;
  color: #333;
  width: 100%;
  /*white-space: nowrap;*/
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}

.productLine {
  width: 100%;
  float: left;
}

.productLine label {
  height: 20px;
  display: block;
  font-size: 12px;
  color: #bfbfbf;
}

.c_fl {
  float: left;
}

.c_fr {
  float: right;
}

.skuAttr {
  display: block;
  font-size: 13px;
  color: #FF7E00;
  margin-top: 10rpx;
}

.normalPrice {
  display: block;
  font-size: 13px;
  color: #FF7E00;
}

.numWrap {
  height: 24px;
  line-height: 24px;
  float: right;
}

/*抛物线动画*/

.good_box {
  width: 18px;
  height: 18px;
  position: fixed;
  border-radius: 50%;
  overflow: hidden;
  left: 50%;
  top: 50%;
  z-index: +99;
  background: rgba(0, 133, 207, 1);
}

.minusIcon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  /*border:1px solid #228ed8;*/
  text-align: center;
  line-height: 32rpx;
  /*color:#228ed8;*/
  border: 1px solid #c6c7c8;
  background-color: #fff;
  color: #c6c7c8;
  float: left;
  font-size: 40rpx;
}

.addIcon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1px solid #FF7E00;
  /*color:#fff;*/
  /*background:#228ed8;*/
  background-color: #FF7E00;
  color: #fff;
  text-align: center;
  line-height: 32rpx;
  display: inline-block;
  float: left;
  font-size: 40rpx;
}

.buyNum {
  margin-left: 3px;
  margin-right: 3px;
  width: 30px;
  height: 20px;
  line-height: 24rpx;
  display: inline-block;
  text-align: center;
  font-size: 16px;
  float: left;
  margin-top: 10rpx;
}

.validTime {
  float: left;
  width: 56%;
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
  margin-top: 4px;
}

.soldOut {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: #000;
  z-index: 999;
  opacity: 0.3;
}
.price_append {
  text-decoration: line-through;
  font-size: 24rpx !important;
  color: #666 !important;
}
/*.child-active{
  display:inline-block !important;
  border:1px solid #FF7E00;
  border-radius:10rpx;
  padding:2rpx 0;
  border-left:1px solid #FF7E00 !important;
  text-align:center !important;
  padding-left:10rpx !important;
  padding-right:10rpx !important;
  height:26rpx;
  line-height:26rpx;
}
.child-unactive{
  display:inline-block !important;
  border-radius:10rpx;
  padding:2rpx 0;
  padding-left:10rpx !important;
  padding-right:10rpx !important;
  height:26rpx;
  line-height:26rpx;
}*/
.child-active {
  display: inline-block !important;
  /*border:1px solid #FF7E00;*/
  border-radius: 10rpx;
  border-left: 1px solid transparent !important;
  text-align: center !important;
  padding-left: 10rpx !important;
  padding-right: 10rpx !important;
  height: 22rpx;
  line-height: 22rpx;
  color: #F20100 ;
  background: #FEE5E5 ;
}

/*.child-active label{
  background:#ddd;
  color:#666;
  padding:4rpx 8rpx;
  border-radius:4rpx;
  border-left:none !important;
}*/
.child-unactive {
  display: inline-block !important;
  border-radius: 10rpx;
  padding-left: 10rpx !important;
  padding-right: 10rpx !important;
  height: 26rpx;
  line-height: 26rpx;
  color: #191D21;
  background:#F2F2F2;
}

.soldOutIcon {
  z-index: 9999;
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 80px;
  height: 80px;
}

/*加入购物车*/
.scroll_blo {
  width: 100%;
  position: fixed;
  bottom: 96rpx;
  left: 0;
  z-index: 99999;
  padding-bottom:60rpx;
}

.addgoods_box {
  width: 100%;
  height: 60px;
  /* border-bottom: 1px solid #e5e5e5; */
  background: #fff;
}

.addgoods_pic {
  width: 86px;
  height: 86px;
  background: #fff;
  position: absolute;
  top: 10px;
  left: 15px;
  border: 1px solid #ccc;
  /* overflow: hidden; */
}

.addgoods_pic image {
  width: 100%;
  height: 100%;
}

.addgoods_title,
.addgoods_price {
  padding-left: 115px;
  height: 20px;
  overflow: hidden;
  white-space: nowrap;
  padding-right: 30px;
  text-overflow: ellipsis;
  padding-top: 3px;
}

.addgoods_title {
  font-size: 14px;
  background: #fff;
  color: #000;
}

.addgoods_price {
  color: #FF7E00;
  font-size: 13px;
}

.goods_classify {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 10px 5px 10px;
}

.goods_classify label {
  margin-bottom: 10px;
  line-height: 28px;
  font-size: 14px;
}

.goods_classify view text {
  float: left;
  padding: 0 16px;
  color: #333;
  background: #ececec;
  font-size: 12px;
  margin-bottom: 8px;
  margin-right: 10px;
  line-height: 28px;
  border-radius: 5px;
  border: 1px solid transparent;
}

.addgoods_number {
  width: 100%;
  background: #fff;
  padding-top: 15px;
  padding-bottom: 15px;
  /**border-bottom: 1px solid #e5e5e5;**/
}

.limited_quantity {
  font-size: 13px;
  float: left;
  padding-left: 15px;
  padding-top: 5px;
}

.stock {
  font-size: 13px;
  float: right;
  margin-right: 5px;
  margin-top: 4px;
}

.plus_minus {
  float: right;
  padding-right: 15px;
  margin-top: 3px;
}

.minus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 22px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 24px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_minus input {
  width: 50px;
  height: 26px;
  line-height: 10px;
  background: #e4e4e4;
  float: left;
  border: none;
  font-size: 13px;
  margin: 0 5px;
  text-align: center;
  color: #333;
}

.pay-add-to-shoppingcart {
  display: block;
  padding: 0px;
  font-size: 14px;
  line-height: 44px;
  height: 44px;
  flex: 1;
  color: #fff;
  /*position: fixed;
  left: 45rpx;
  right: 0;
  bottom: 30rpx;*/
  z-index: 1300;
  background-color: #FF7E00 !important;
  width: 660rpx;
  border-radius: 22px;
  margin:30rpx auto;
}

.page-dialog-close {
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 10;
  display: block;
  width: 20px;
  height: 20px;
  /* border: 1px solid #67666f;
  color: #67666f; 
  text-align: center;
  line-height: 16px;
  border-radius: 50%;
  font-size: 26px;
  margin: 5*/
}

.black_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 21;
  background: #000;
  opacity: 0.6;
}
.active_classify {
  border: 1px solid #FF7E00 !important;
  color: #FF7E00 !important;
  background: #FFF8F9 !important;
}


.mini_buy{
  margin:20rpx 0 0 0;
  font-size:28rpx;
  color:#353535;
  margin-left:40rpx;
}
.goods_stock{
  font-size:28rpx;
  color:#353535;
  padding-left: 115px;
}
.shopcart_num{
  font-size:28rpx;
  color:#353535;
  margin-bottom:40rpx;
  margin-top:30rpx;
}
/* .shop_om{
  margin-left:140rpx;
  margin-bottom:20rpx;
} */
.shop_om text{
  /* width:100rpx; */
  float:left;
}
.shop_om input{
  float:left;
  border:1rpx solid #ccc;
}
.shop_om{
  /* margin-left:140rpx; */
  margin-bottom:20rpx;
  padding-right:15px;
}
.minus_num{
  height:60rpx;
  border:1rpx solid #ccc;
  border-right:none;
  line-height:60rpx;
  width:60rpx;
  text-align:center;
  font-size:40rpx;
  float:left;
}
.plus_num{
  height:60rpx;
  border:1rpx solid #ccc;
  border-left:none;
  line-height:60rpx;
  width:60rpx;
  text-align:center;
  font-size:40rpx;
  float:left;
}
.numBox{
  height:60rpx;
  border:1rpx solid #ddd;
  text-align: center;
  width:80rpx;
}
.unit_num{
  vertical-align:top;
  margin-left:10rpx;
  font-size:28rpx;
  line-height:60rpx;
}
.cataItems{
  padding-bottom:40rpx;
}
.s_box{
  display:flex;
  padding:20rpx 25rpx;
  background:#f2f2f2;
  align-items: center;
  position:fixed;
  top:0;
  left:0;
  right:0;
  z-index:999;
}
.s_inner{
  display:flex;
  flex:1;
  height:70rpx;
  border-radius:40rpx;
  background:#fff;
  border:1px solid #ddd;
  align-items: center;
}
.s_inner icon{
  margin:0 30rpx;
}
.s_inner text{
  font-size:28rpx;
  color:#666;
}
/*嵌套的template*/
.mark1 {
  align-items: flex-end;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
}

.mark1_l {
  border: 3rpx solid #db2f04;
  border-left: none;
  border-bottom: none;
  font-size: 30rpx;
  z-index: 9999;
  border-radius: 100rpx 100rpx 100rpx 0;
  width: 100rpx;
  height: 100rpx;
  background: #f5f5f5;
  color: #db2f04;
  border-radius: 50rpx 50rpx 50rpx 0;
  width: 50rpx;
  height: 50rpx;
  font-size: 16rpx;
  text-align:center;
}

.mark1_l view {
  padding: 4rpx;
  padding-left: 8rpx;
}

.mark1_r {
  margin-left: -50rpx;
  padding-left: 50rpx;
  display: flex;
  flex: 1;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 14rpx;
  background: #db2f04;
  color: #fff;
  height: 30rpx;
  line-height: 30rpx;
  font-size: 14rpx;
}

/**/
.mark2_1 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 0rpx;
  left: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;

}

.mark2_2 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 0rpx;
  left: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
}

.mark2_3 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.mark2_4 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 0rpx;
  right: 0rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  background: red;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.mark_1 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 30rpx;
  left: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.mark_2 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 30rpx;
  left: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.mark_3 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  top: 30rpx;
  right: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.mark_4 {
  padding-left: 20rpx;
  padding-right: 10rpx;
  position: absolute;
  bottom: 30rpx;
  right: 0rpx;
  height: 40rpx;
  font-size: 24rpx;
  background: #f5f5f5;
  line-height: 40rpx;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  border: 3rpx solid #ff6600;
  color: #ff6600;
}

.tag_content {
  z-index: 99999;
  position: absolute;
  top: 10%;
  left: 5%;
  width: 90%;
  background: #fff;
  border-radius: 20rpx;
  padding: 20rpx 0;
}

.tag_content::after {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-bottom-color: #fff;
  top: -14px;
  left: var(--topLeft);
}

.tagInner{
  display:flex;
  justify-content: space-around;
  align-items: center;
  padding:0 10rpx;
}
.tagInner>view{
  align-items: center;
  flex:1;
  padding:12rpx 0rpx;
  background:#f6f6f6;
  margin:10rpx;
  text-align:center;
  font-size:24rpx;
  border-radius:10rpx;
}
.active_tag{
  background:#FFF8F9;
  border:1px solid #ff6600;
  border-radius:10rpx;
}
.g_t{
  font-size:18rpx;
  padding:2rpx 4rpx;
  border-radius:6rpx;
  color:#ff6600;
  border:1px solid #ff6600;
  display:inline-block;
}

.arrow_down {
  width: 0;
  height: 0;
  border: 5px solid;
  border-color: #bfbfbf transparent transparent transparent;
  position: absolute;
  bottom: -4rpx;
  left: 100rpx;
}

.arrow_down::after {
  content: '';
  position: absolute;
  top: -55px;
  left: -50px;
  border: 5px solid;
  border-color: white transparent transparent transparent;
}

.downTrue {
  border-color: #ff6600 transparent transparent transparent;
}

.arrow_up {
  width: 0;
  height: 0;
  border: 5px solid;
  border-color: transparent transparent #bfbfbf transparent;
  position: absolute;
  top: -4rpx;
  left: 100rpx;
}

.upTrue {
  border-color: transparent transparent #ff6600 transparent;
}

.arrow_up::after {
  content: '';
  position: absolute;
  top: -55px;
  left: -50px;
  border: 5px solid;
  border-color: white transparent transparent transparent;
}
.c_active {
  background: #fff;
  color: #ff6600;
}