<wxs src="../../wxs/subutil.wxs" module="tools" />
<template name="classifyMode1">
  <view class='s_box'>
    <image hidden="{{scanShow==0?true:false}}" src="{{scan}}" bindtap='sweepCodeBindTap' mode="widthFix"
      style="width:24px;margin-right:20rpx;"></image>
    <div class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </div>
  </view>
  <view style="z-index:999;position:fixed;top:100rpx;left:0;right:0;padding:28rpx 0;background:#fff;font-size:29rpx;display:flex;justify-content:space-around;">
		<view bindtap="fastSelectGoodsTypeBindTap" class=" {{fastType==2?'c_active':''}}" data-type="2">综合
		</view>
		<view bindtap="fastSelectGoodsTypeBindTap" class="{{fastType==3?'c_active':''}}" data-type="3">销量
		</view>    
		<view style="position:relative;" bindtap="fastSelectGoodsTypeBindTap" data-type="4">
			<label class="{{fastType==4||fastType==5?'c_active':''}}">价格</label>
			<label class="arrow_up {{fastType==5?'upTrue':''}}"></label>
			<label class="arrow_down {{fastType==4?'downTrue':''}}"></label>
		</view>
    <view bindtap="fastSelectGoodsTypeBindTap" style="position:relative;" data-type="7">
      <label class="{{fastType==7||fastType==8?'c_active':''}}">库存</label>
			<label class="arrow_up {{fastType==7?'upTrue':''}}"></label>
			<label class="arrow_down {{fastType==8?'downTrue':''}}"></label>
		</view>
	</view>
  <view class='classify_box' style='height:1000rpx;margin-top:44px;position:relative;'>
    <!-- 左侧菜单 -->
    <scroll-view scroll-y class='classify_left'>
      <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
        <text bindtap='selectGoodsTypeBindTap' data-id='{{cate.categoryId}}'
          class='{{cate.select?"active":""}}'>{{cate.categoryName}}
        </text>
        <!-- 二级菜单 -->
        <view class='second-menu' hidden='{{!cate.select}}'>
          <block wx:key="unique" wx:for="{{cate.children}}" wx:for-item="child">
            <view style="font-size:13px;" bindtap='selectTwoGoodsTypeBindTap'
              style='{{child.select?"color:#FF7E00":""}}' data-id='{{child.categoryId}}'>
              <text class='{{child.select?"second-active":""}}'></text> {{child.categoryName}}
            </view>
          </block>
        </view>
        <!-- 二级菜单 -->
      </block>
    </scroll-view>
    <!-- 左侧菜单 -->

    <!-- 右侧菜单 -->
    <scroll-view scroll-y="true" class='classify_right' bindscrolltolower="searchScrollLower"
      style='height:{{second_height}}rpx' 	lower-threshold="18">
      <block wx:if="{{adPic!=null&&adPic.length>0&&adOpen==1}}">
        <image src="{{adPic}}" style="width:100%" mode="widthFix"></image>
      </block>
      <block wx:if="{{commodityList.length>0}}">
        <block wx:key="unique" wx:for="{{commodityList}}" wx:for-item="goods">
          <block wx:if="{{goods.commoditySaleWay==3}}">
            <view class="rightWrap" style="position:relative;">
              <block wx:if="{{goods.commoditySaleWay==3}}">
                <block wx:if="{{overallStock==1&&0>=goods.commodityVirtualStore}}">
                  <image lazy-load='true' class="soldOutIcon" src="{{soldOut}}"></image>
                </block>
              </block>
              <block wx:elif="{{goods.commoditySaleWay==4}}">
                <image lazy-load='true' class="soldOutIcon" src="{{soldOut}}"></image>
              </block>
							<view style="float:left;position:relative;">
								<block
									wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
									<template is="mark1"
										data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
								</block>
								<block
									wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
									<template is="mark2"
										data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
								</block>
								<block
									wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
									<template is="mark3"
										data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
								</block>
								<image lazy-load='true' class="rightMainPic"
									src="{{goods.commodityMainPic==''?defaultGoodsMainImage:goods.commodityMainPic}}">
								</image>

							</view>
							<view bindtap='imageClick' bindlongpress="getTagPress"
								data-tag="{{goods.clientCommodityTag}}" data-id="{{goods.commodityId}}"
								data-name="{{goods.commodityName}}" data-pic="{{goods.commodityMainPic}}"
								data-commodityId='{{goods.commodityId}}' class="productAttr">

								<view class="productName">{{goods.commodityName}}</view>
								<!--<view class="g_des">
									<view class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">
										{{goods.clientCommodityTagDesc}}</view>
								</view>-->
                <view class="productLine">
                  <label>
                    <text class="c_fl">{{goods.commoditySpec}}</text>
                  </label>
                  <block wx:if="{{stockBean!=null}}">
                    <block wx:if="{{stockBean.openStock}}">
                      <block wx:if="{{stockBean.stockShowType==1}}">
                        <view style="color:#999;font-size:24rpx;">库存：{{goods.commodityVirtualStore}}</view>
                      </block>
                      <block wx:elif="{{stockBean.stockShowType==2}}">
                        <view style="color:#999;font-size:24rpx;">库存：{{stockBean.showContent}}</view>
                      </block>
                    </block>
                  </block>
                  <block wx:else>
                    <view style="color:#999;font-size:24rpx;" hidden="{{goods.commodityVirtualStore>0?false:true}}">
                      库存：{{goods.commodityVirtualStore}}</view>
                  </block>
                  <label>
                    <block wx:key="unique" wx:for="{{goods.promotionList}}" wx:for-item="promotion">
                      <block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
                        <text class="specialMark">秒杀</text>
                      </block>
                      <block wx:if="{{promotion.promotionType=='TUANGOU'}}">
                        <text class="specialMark">团购</text>
                      </block>
                      <block wx:if="{{promotion.promotionType=='TEJIA'}}">
                        <text class="specialMark">特价</text>
                      </block>
                      <block wx:if="{{promotion.promotionType=='QUDUAN'}}">
                        <text class="specialMark">特价</text>
                      </block>
                    </block>
                    <!-- 活动标记 -->
                  </label>
                  <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                    <label class="goods_price price_append">￥{{tools.sub.formatAmount(goods.cutOffThePrice,2)}}<block
                        wx:if="{{goods.skuList==0}}">/{{goods.commodityWeightUnit}}</block></label>
                  </block>
                  <view class="skuAttr" hidden="{{goods.commodityUnitOmDefault==1?false:true}}">
                    ￥{{tools.sub.formatAmount(goods.omPrice,2)}}<block wx:if="{{goods.skuList==0}}">
                      /{{goods.omName}}</block>
                  </view>
                  <view class="skuAttr" hidden="{{goods.commodityUnitOtDefault==1?false:true}}">
                    ￥{{tools.sub.formatAmount(goods.goodsPrice,2)}}<block wx:if="{{goods.skuList==0}}">
                      /{{goods.otName}}</block>
                  </view>
                </view>
              </view>
              <view class="clearfix" style="position: absolute;bottom: 10rpx;  right: 20rpx;">
                <view class="numWrap">
                  <block wx:if="{{goods.commoditySaleWay==3}}">
                    <block wx:if="{{overallStock==1&&0>=goods.commodityVirtualStore}}">
                      <!--已售罄-->
                    </block>
                    <block wx:else>
                      <view bindtap='addGoodsToShopCartBindTap' data-sku="{{goods.mySkuList.length}}"
                        data-moq="{{goods.commodityMoq}}" data-commodityId='{{goods.commodityId}}'>
                        <image src="{{shopStyle==1?shop_cart1:shop_cart2}}" style="width:50rpx;height:auto;" mode="widthFix">
                        </image>
                        <!--<image src="{{cart}}" style="width:50rpx;" mode="widthFix"></image>-->
                        <!--<view style="width:50rpx;height:50rpx;border-radius:50%;color:#fff;background-color:#FF7E00;font-size:50rpx;line-height:46rpx;text-align:center;">+</view>-->
                      </view>
                    </block>
                  </block>
                  <block wx:else>
                    <view data-sku="{{goods.mySkuList.length}}" data-moq="{{goods.commodityMoq}}"
                      data-commodityId='{{goods.commodityId}}'>
                      <!-- <image src="{{cart}}" style="width:50rpx;" mode="widthFix"></image> -->
                      <!--<view style="width:50rpx;height:50rpx;border-radius:50%;color:#fff;background-color:#FF7E00;font-size:50rpx;line-height:46rpx;text-align:center;">+</view>-->
                      <image src="{{shopStyle==1?shop_cart1:shop_cart2}}" style="width:50rpx;" mode="widthFix"></image>
                    </view>
                  </block>
                </view>
              </view>
            </view>
          </block>
        </block>
      </block>
      <block wx:else>
        <!-- 无商品显示的图 -->
        <view>
          <image src="https://www.cn2b2c.com/gsf/img/zyht/emptyGoods.png" mode="widthFix"
            style="width:300rpx;margin:100rpx"></image>
        </view>
      </block>
      <view animation="{{animationY}}" style="position:fixed;top:{{ballY}}px;" hidden="{{!showBall}}">
        <view class="good_box" animation="{{animationX}}" style="position:fixed;left:{{ballX}}px;"></view>
      </view>
    </scroll-view>
  </view>
</template>
<template name="classifyMode2" style="position:relative;">
  <view class='s_box'>
    <image hidden="{{scanShow==0?true:false}}" src="{{scan}}" bindtap='sweepCodeBindTap' mode="widthFix"
      style="width:24px;margin-right:20rpx;"></image>
    <div class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </div>
  </view>
  <view style="z-index:999;position:fixed;top:100rpx;left:0;right:0;padding:28rpx 0;background:#fff;font-size:29rpx;display:flex;justify-content:space-around;">
		<view class=" {{fastType==2?'c_active':''}}" bindtap="fastSelectGoodsTypeBindTap"
			data-type="2">综合
		</view>
		<view bindtap="fastSelectGoodsTypeBindTap" class="{{fastType==3?'c_active':''}}" data-type=" 3">销量
		</view>
		<view style="position:relative;" bindtap="fastSelectGoodsTypeBindTap" data-type="4">
			<label class="{{fastType==4||fastType==5?'c_active':''}}">价格</label>
			<label class="arrow_up {{fastType==5?'upTrue':''}}"></label>
			<label class="arrow_down {{fastType==4?'downTrue':''}}"></label>
		</view>
	</view>
  <view class='classify_box' style='height:1000rpx;margin-top:44px;position:relative;'>
    <!-- 左侧菜单 -->
    <scroll-view scroll-y class='classify_left'>
      <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
        <text bindtap='selectGoodsTypeBindTap' data-id='{{cate.categoryId}}'
          class='{{cate.select?"active":""}}'>{{cate.categoryName}}
        </text>
        <!-- 二级菜单 -->
      </block>
    </scroll-view>
    <!-- 左侧菜单 -->
    <scroll-view scroll-y class='classify_right'  bindscrolltolower="searchScrollLower"
      style='height:{{second_height}}rpx' lower-threshold="18">
      <!-- 二级 -->
      <block wx:if="{{adPic!=null&&adPic.length>0&&adOpen==1}}">
        <image src="{{adPic}}" style="width:100%" mode="widthFix"></image>
      </block>
      <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
        <!-- 二级菜单 -->
        <view hidden='{{!cate.select}}'>
        <scroll-view scroll-x="true" class='second-menu' style='display:flex;white-space:nowrap' >
          <block wx:key="unique" wx:for="{{cate.children}}" wx:for-item="child">
            <view class="{{child.select?'child-active':'child-unactive'}}"
              style="margin:0 6rpx;height:50rpx;line-height:50rpx;font-size:13px;display:inline-block !important;"
              bindtap='selectTwoGoodsTypeBindTap' data-id='{{child.categoryId}}'>
              <!-- <text class='{{child.select?"second-active":""}}'></text> -->
              <label>{{child.categoryName}}</label>
            </view>
          </block>
        </scroll-view>
        </view>
        <!-- 二级菜单 -->
      </block>
      <!-- 二级 -->
      <view>
        <block wx:if="{{commodityList.length>0}}">
          <block wx:key="unique" wx:for="{{commodityList}}" wx:for-item="goods">
            <block wx:if="{{goods.commoditySaleWay==3}}">
              <view class="rightWrap" style="position:relative;">

                <block wx:if="{{goods.commoditySaleWay==3}}">
                  <block wx:if="{{overallStock==1&&0>=goods.commodityVirtualStore}}">
                    <image lazy-load='true' class="soldOutIcon" src="{{soldOut}}"></image>
                  </block>
                </block>
                <block wx:elif="{{goods.commoditySaleWay==4}}">
                  <image lazy-load='true' class="soldOutIcon" src="{{soldOut}}"></image>
                </block>

								<view style="float:left;position:relative;">
									<block
										wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
										<template is="mark1"
											data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
									</block>
									<block
										wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
										<template is="mark2"
											data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
									</block>
									<block
										wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
										<template is="mark3"
											data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
									</block>
									<image lazy-load='true' class="rightMainPic"
										src="{{goods.commodityMainPic==''?defaultGoodsMainImage:goods.commodityMainPic}}">
									</image>
								</view>
								<view bindtap='imageClick' bindlongpress="getTagPress"
									data-tag="{{goods.clientCommodityTag}}" data-id="{{goods.commodityId}}"
									data-name="{{goods.commodityName}}" data-pic="{{goods.commodityMainPic}}"
									data-commodityId='{{goods.commodityId}}' class="productAttr">
									<view class="productName"><label
											class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>{{goods.commodityName}}
									</view>

                  <view class="productLine">
                    <label>
                      <text class="c_fl">{{goods.commoditySpec}}</text>
                    </label>
                    <block wx:if="{{stockBean!=null}}">
                      <block wx:if="{{stockBean.openStock}}">
                        <block wx:if="{{stockBean.stockShowType==1}}">
                          <view style="color:#999;font-size:24rpx;">库存：{{goods.commodityVirtualStore}}</view>
                        </block>
                        <block wx:elif="{{stockBean.stockShowType==2}}">
                          <view style="color:#999;font-size:24rpx;">库存：{{stockBean.showContent}}</view>
                        </block>
                      </block>
                    </block>
                    <block wx:else>
                      <view style="color:#999;font-size:24rpx;" hidden="{{goods.commodityVirtualStore>0?false:true}}">
                        库存：{{goods.commodityVirtualStore}}</view>
                    </block>
                    <block wx:key="unique" wx:for="{{goods.promotionList}}" wx:for-item="promotion">
                      <block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
                        <text class="specialMark">秒杀</text>
                      </block>
                      <block wx:if="{{promotion.promotionType=='TUANGOU'}}">
                        <text class="specialMark">团购</text>
                      </block>
                      <block wx:if="{{promotion.promotionType=='TEJIA'}}">
                        <text class="specialMark">特价</text>
                      </block>
                      <block wx:if="{{promotion.promotionType=='QUDUAN'}}">
                        <text class="specialMark">区段优惠</text>
                      </block>
                    </block>
                    <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                      <label class="goods_price price_append">￥{{tools.sub.formatAmount(goods.cutOffThePrice,2)}}<block
                          wx:if="{{goods.skuList==0}}">/{{goods.commodityWeightUnit}}</block></label>
                    </block>
                    <view class="skuAttr" hidden="{{goods.commodityUnitOmDefault==1?false:true}}">
                      ￥{{tools.sub.formatAmount(goods.omPrice,2)}}<block wx:if="{{goods.skuList==0}}">
                        /{{goods.omName}}</block>
                    </view>
                    <view class="skuAttr" hidden="{{goods.commodityUnitOtDefault==1?false:true}}">
                      ￥{{tools.sub.formatAmount(goods.goodsPrice,2)}}<block wx:if="{{goods.skuList==0}}">
                        /{{goods.otName}}</block>
                    </view>
                  </view>
                </view>
                <view class="clearfix" style="position: absolute;bottom: 10rpx;  right: 20rpx;">
                  <view class="numWrap">
                    <block wx:if="{{goods.commoditySaleWay==3}}">
                      <block wx:if="{{overallStock==1&&0>=goods.commodityVirtualStore}}">
                        <!--已售罄-->
                      </block>
                      <block wx:else>
                        <view bindtap='addGoodsToShopCartBindTap' data-sku="{{goods.mySkuList.length}}"
                          data-moq="{{goods.commodityMoq}}" data-commodityId='{{goods.commodityId}}'>
                          <image src="{{shopStyle==1?shop_cart1:shop_cart2}}" style="width:50rpx;height:auto;" mode="widthFix">
                          </image>
                        </view>
                      </block>
                    </block>
                    <block wx:else>
                      <view data-sku="{{goods.mySkuList.length}}" data-moq="{{goods.commodityMoq}}"
                        data-commodityId='{{goods.commodityId}}'>
                        <image src="{{shopStyle==1?shop_cart1:shop_cart2}}" style="width:50rpx;" mode="widthFix">
                        </image>
                      </view>
                    </block>
                  </view>
                </view>
              </view>
            </block>
          </block>
        </block>
        <block wx:else>
          <!-- 无商品显示的图 -->
          <view>
            <image src="https://www.cn2b2c.com/gsf/img/zyht/emptyGoods.png" mode="widthFix"
              style="width:300rpx;margin:100rpx"></image>
          </view>
        </block>
      </view>
      <view animation="{{animationY}}" style="position:fixed;top:{{ballY}}px;" hidden="{{!showBall}}">
        <view class="good_box" animation="{{animationX}}" style="position:fixed;left:{{ballX}}px;"></view>
      </view>
    </scroll-view>
  </view>
</template>
<template name="classifyMode3">
  <view class='s_box'>
    <image hidden="{{scanShow==0?true:false}}" src="{{scan}}" bindtap='sweepCodeBindTap' mode="widthFix"
      style="width:24px;margin-right:20rpx;"></image>
    <div class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </div>
  </view>
  <scroll-view class='classify_box' style='margin-top:44px;position:relative;'>
    <!-- 左侧菜单 -->
    <scroll-view scroll-y class='classify_left' style="top:50px;">
      <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
        <text bindtap='selectGoodsTypeBindTap' data-id='{{cate.categoryId}}' class='{{cate.select?"active":""}}'>{{cate.categoryName}}</text>
      </block>
    </scroll-view>
    <!-- 左侧菜单 -->
    <scroll-view scroll-y class='classify_right' style='margin-top:0;height:92%;width:72%;padding-left:28%;margin-bottom:30rpx'
      bindscrolltolower="searchScrollLower">
      <block wx:if="{{adPic!=null&&adPic.length>0&&adOpen==1}}">
        <image src="{{adPic}}" style="width:100%" mode="widthFix"></image>
      </block>
      <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
        <!-- 二级菜单 -->
        <view class='clearfix' style='margin:40rpx 0;background:#fff;' hidden='{{!cate.select}}'>
          <block wx:if="{{cate.children.length>0}}">
            <block wx:key="unique" wx:for="{{cate.children}}" wx:for-item="child">
              <view bindtap="secondCategoryBind" style="float:left;width:33%;text-align:center;"
                data-id='{{child.categoryId}}'>
                <image lazy-load='true' src="{{child.categoryPic}}"
                  style="width:120rpx;height:120rpx;border-radius:50%;"></image>
                <text style="display:block;font-size:24rpx;height:78rpx;color:#666;"> {{child.categoryName}}
                </text>
              </view>
            </block>
          </block>
          <block wx:else>
            <!-- 无分类显示的图 -->
            <view>
              <image src="https://www.cn2b2c.com/gsf/img/zyht/emptyClassify.png" mode="widthFix"
                style="width:300rpx;margin:100rpx"></image>
            </view>
          </block>
        </view>
        <!-- 二级菜单 -->
      </block>
    </scroll-view>
  </scroll-view>
</template>
<!--第四种展示模式-->
<template name="classifyMode4">
  <view class='s_box'>
    <image hidden="{{scanShow==0?true:false}}" src="{{scan}}" bindtap='sweepCodeBindTap' mode="widthFix"
      style="width:24px;margin-right:20rpx;"></image>
    <div class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </div>
  </view>
  <scroll-view class='classify_box' style='margin-top:100rpx;background:#f5f5f5;'>
    <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
      <view style="background:#fff;margin-bottom:20rpx;">
        <view bindtap="secondCategoryBind" data-id='{{cate.categoryId}}' class="clearfix" style="padding:20rpx;">
          <label style="font-size:32rpx;float:left;">{{cate.categoryName}}</label>
          <label style="float:right;">
            <image style="width:36rpx;" mode="widthFix" src="{{personal_more}}"></image>
          </label>
        </view>
        <view style="display:flex;flex-wrap:wrap;" class="{{cate.children.length>0?'cataItems':''}}">
          <block wx:key="unique" wx:for="{{cate.children}}" wx:for-item="child">
            <view bindtap="secondCategoryBind" style="margin-left:20rpx;" data-id='{{child.categoryId}}'>
              <image lazy-load='true' src="{{child.categoryPic}}" style="width:120rpx;height:120rpx;border-radius:50%;">
              </image>
              <view style="font-size:26rpx;color:#666;text-align:center;">{{child.categoryName}}</view>
            </view>
          </block>
        </view>
      </view>
    </block>
  </scroll-view>
</template>
<!--第五种展示模式-->
<template name="classifyMode5">
  <view class='s_box'>
    <image hidden="{{scanShow==0?true:false}}" src="{{scan}}" bindtap='sweepCodeBindTap' mode="widthFix"
      style="width:24px;margin-right:20rpx;"></image>
    <div class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </div>
  </view>
  <scroll-view class='classify_box' style='margin-top:100rpx;'>
    <block wx:key="unique" wx:for="{{categoryBean}}" wx:for-item="cate">
      <view style="background:#fff;margin-bottom:20rpx;">
        <view bindtap="secondCategoryBind" data-id='{{cate.categoryId}}' class="clearfix" style="padding:20rpx;">
          <label style="font-size:32rpx;float:left;">{{cate.categoryName}}</label>
        </view>
        <view style="display:flex;flex-wrap:wrap;" class="{{cate.children.length>0?'cataItems':''}}">
          <block wx:key="unique" wx:for="{{cate.children}}" wx:for-item="child">
            <view bindtap="secondCategoryBind" style="margin-left:20rpx;" data-id='{{child.categoryId}}'>
              <image lazy-load='true' mode="widthFix" src="{{child.categoryPic}}" style="margin:0 auto;width:700rpx;">
              </image>
            </view>
          </block>
        </view>
      </view>
    </block>
  </scroll-view>
</template>

<!--嵌套的模块-->
<template name="mark3">
	<view class="mark1">
		<view class="mark1_l">
			<view>{{nameCircle}}</view>
		</view>
		<view class="mark1_r">
			{{name}}
		</view>
	</view>
</template>
<template name="mark2">
	<view class="mark2_{{pos}}">
		{{name}}
	</view>
</template>
<template name="mark1">
	<view class="mark_{{pos}}">
		{{name}}
	</view>
</template>