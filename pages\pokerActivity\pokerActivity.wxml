<wxs src="../../wxs/subutil.wxs" module="tools" />
<view class="PrizeContainer">
	<view class="rule" bindtap="seeRuleBindTap">规则</view>
	<view class="rulePrize" bindtap="getPrizeBindTap" style="right:0;">我的奖品</view>
	<view class="prizeList1" style="position:absolute;top:100rpx;height:100rpx;">
		<view style="text-align:center;">{{activityName}}</view>
		<view style="font-size:28rpx;">
			<label>活动时间:</label>
			<block wx:if="{{activityTimeContinuity == 1}}">
				{{tools.sub.formatMill(activityStartDate)}} {{activityStartTime}}-
				{{tools.sub.formatMill(activityEndDate)}} {{activityEndTime}}
			</block>
			<block wx:elif="{{activityTimeContinuity != 1}}">
				{{tools.sub.formatMill(activityStartDate)}}至{{tools.sub.formatMill(activityEndDate)}}
				每日{{activityStartTime}}-{{activityEndTime}}
			</block>
		</view>
	</view>
    <!--扑克牌-->
    <!--扑克牌-->
	<image class="bg" src="{{turntable_bg}}" style="width:750rpx;" mode="widthFix"></image>
	<!-- <view>剩余抽奖次数：{{lotteryNum}}次</view> -->
	<view style="width:700rpx;height:700rpx;z-index:0;" >
		<view hidden="{{pokerShow}}">
			<view style="display:flex;flex-wrap:wrap">
				<block wx:key="unique" wx:for="{{pokerArray}}" wx:for-item="oneItem">
					<view  style="border-radius:6rpx;text-align:center;line-height:200rpx;background:#fff;margin:0 50rpx;margin-bottom:20rpx;width:130rpx;height:200rpx;">
						<image data-id="{{index+1}}" hidden="{{oneItem.isClick}}" bindtap="getPokerResultClick" style="width:130rpx;" mode="widthFix" src="{{pkbj}}"></image>
						<view style="font-size:28rpx;" hidden="{{!oneItem.isClick}}">{{isselectData.activityPrizeShowName}}</view>
					</view>
				</block>
			</view>
		</view>
		<view hidden="{{!pokerShow}}">
			<view style="font-size:28rpx;display:flex;flex-wrap:wrap">
				<block wx:key="unique" wx:for="{{award}}" wx:for-item="oneItem">
					<view   style="border-radius:6rpx;text-align:center;line-height:200rpx;background:#fff;margin:0 50rpx;margin-bottom:20rpx;width:130rpx;height:200rpx;">
						<view>{{oneItem.activityPrizeShowName}}</view>
					</view>
				</block>
				<block wx:for="{{9-award.length}}">
					<view   style="border-radius:6rpx;text-align:center;line-height:200rpx;background:#fff;margin:0 50rpx;margin-bottom:20rpx;width:130rpx;height:200rpx;">
						<view>谢谢参与</view>
					</view>
				</block>
			</view>
		</view>
	</view>
	
	<view style="font-size:30rpx;color:#fff;z-index:100;margin-top:20rpx;">剩余{{residueNum}}次</view>
	<view class="startBtn" style="font-size:30rpx;color:#fff;z-index:100;margin-top:20rpx;" bindtap="startPokerClick">开始</view>
	<view class="prizeList" >
		<block wx:if="{{recordData.length>3}}">
			<swiper circular="true" autoplay="true" interval="2000" vertical='true' display-multiple-items="3">
				<block wx:for="{{recordData}}" wx:key="item">
					<swiper-item>
						<view class="prizeLi">
							<view>
								<image style="width:68rpx;height:68rpx;" src="{{userLogo}}"></image>
							</view>
							<view style="width:400rpx;margin:0 20rpx">
								<view>用户{{item.userName}}</view>
								<view style="font-size:28rpx;">获得了
									<block wx:if="{{item.activityPrizeType == 1}}">
										<label>未中奖</label>
									</block>
									<block wx:if="{{item.activityPrizeType == 2}}">
										<label>{{item.activityPrizeNum}}</label>
										<label>{{item.activityPrizeValue.commodityName}}</label>
									</block>
									<block wx:if="{{item.activityPrizeType == 3}}">
										<label>{{item.activityPrizeNum}}张</label>
										<label>{{item.activityPrizeValue.coupousName}}</label>
									</block>
									<block wx:if="{{item.activityPrizeType == 4}}">
										<label>{{item.activityPrizeValue.integralVal}}</label>
										<label>积分</label>
									</block>
								</view>
							</view>
							<view style="width:200rpx;">{{tools.sub.formatMillToS(item.journaTime)}}</view>
						</view>
					</swiper-item>
				</block>
			</swiper>
		</block>
    <block wx:elif="{{recordData.length>0&&recordData.length<4}}">
      <block wx:for="{{recordData}}" wx:key="item">
          <view class="prizeLi">
            <view>
              <image style="width:68rpx;height:68rpx;" src="{{userLogo}}"></image>
            </view>
            <view style="width:400rpx;margin:0 20rpx">
              <view>用户{{item.userName}}</view>
              <view style="font-size:28rpx;">获得了
				<block wx:if="{{item.activityPrizeType == 1}}">
					<label>未中奖</label>
				</block>
				<block wx:if="{{item.activityPrizeType == 2}}">
					<label>{{item.activityPrizeNum}}</label>
					<label>{{item.activityPrizeValue.commodityName}}</label>
				</block>
				<block wx:if="{{item.activityPrizeType == 3}}">
					<label>{{item.activityPrizeNum}}张</label>
					<label>{{item.activityPrizeValue.coupousName}}</label>
				</block>
				<block wx:if="{{item.activityPrizeType == 4}}">
					<label>{{item.activityPrizeValue.integralVal}}</label>
					<label>积分</label>
				</block>
			  </view>
            </view>
            <view style="width:200rpx;">{{tools.sub.formatMillToS(item.journaTime)}}</view>
          </view>
      </block>
		</block>
		<block wx:else>
			<view style="text-align:center">暂无中奖信息</view>
		</block>
	</view>
</view>

<!-- 规则 -->
<view class="ruleBox" hidden='{{!ruleHidden}}'>
	<!-- <icon class="closeTemp" type="clear" size='30' color='gray' bindtap="hideRuleBindTap" /> -->
	<!-- <view style="line-height:100rpx;text-align:center;">活动规则</view>
	<view style="font-size:30rpx;">{{gameExplain}}</view> -->
	<!--<image src="{{ruleLayer}}" style="width: 500rpx;position:absolute;left:0;top:0;z-index:-1" mode="widthFix"></image>
	<view class="sumbitBtn" style="position:absolute;bottom:30rpx;left:120rpx;" bindtap="hideRuleBindTap">我知道了</view>-->
	<template is="wxParse" data="{{wxParseData:commodityIntroduce.nodes}}" />
	<view class="sumbitBtn" style="position:absolute;bottom:30rpx;left:120rpx;" bindtap="hideRuleBindTap">我知道了</view>
	
</view>
<view class="pop_bg" hidden='{{!ruleHidden}}'></view>

<!-- 中奖弹窗 -->
<view class="prizeBox" hidden="{{!prizePopupHidden}}">
	<image src="{{turntable_bj}}" style="width: 473rpx;position:absolute;left:0;top:0;z-index:-1" mode="widthFix"></image>
	<view style="font-size:40rpx;line-height:60rpx;margin-top:20rpx;">恭喜中奖</view>
	<view style="margin-top:60rpx;height:220rpx;">
		<!--<block wx:if="{{isselectData.activityPrizeType==3}}">
			<image src="{{turntable_yhj}}" style="width:220rpx;margin:0 auto;" mode="widthFix"></image>
		</block>
		<block wx:elif="{{isselectData.activityPrizeType==4}}">
			<image src="{{turntable_jifen}}" style="width:240rpx;margin:0 auto;" mode="widthFix"></image>
		</block>
		<block wx:elif="{{isselectData.activityPrizeType==2}}">
			<image src="{{turntable_hb}}" style="width:160rpx;margin:0 auto;" mode="widthFix"></image>
		</block>-->
		<view style="margin-top:70px;color:#ff6600;font-size:36rpx">恭喜您获得{{prizeShowName}}</view>
	</view>
	<view class="sumbitBtn" bindtap="prizeShowBindTap">我知道了</view>
</view>
<view class="pop_bg" hidden='{{!prizePopupHidden}}'></view>

<!-- 谢谢参与弹窗 -->
<view class="noPrizeBox" hidden="{{!noPopupHidden}}">
	<image src="{{turntable_bj}}" style="width: 473rpx;position:absolute;left:0;top:0;z-index:-1" mode="widthFix"></image>
	<view style="font-size:40rpx;line-height:120rpx;margin-top:20rpx;">谢谢参与</view>
	<view style="margin-top:60rpx;color:#000;">很遗憾没有中奖</view>
	<view style="height:200rpx;">
		<image src="{{noPrizeImg}}" style="width:140rpx;margin:20rpx auto;" mode="widthFix"></image>
	</view>
	<view class="sumbitBtn" bindtap="noPrizeHideBindTap">再来一次</view>
</view>
<!-- 查看中奖奖品的弹窗 -->
<view class="PrizeBox" hidden="{{prizeShow}}">
	<!--<template is="wxParse" data="{{wxParseData:commodityIntroduce.nodes}}" />
	-->
	<view style="text-align:center;padding:20rpx 0;">
		中奖纪录
		<icon bindtap="closePrizeBindTap" color="#ddd" style="position:absolute;right:20rpx;top:20rpx;" class="icon-small" type="cancel" size="24"></icon>
	</view>
	
	<block wx:for="{{prizeData}}" wx:key="item" wx:for-index="index">
		<view style="font-size:28rpx;display:flex;justify-content:space-between;">
			<view>
				<label>{{index+1}}.</label>
				<block wx:if="{{item.activityPrizeType == 1}}">
					<label>未中奖</label>
					
				</block>
				<block wx:if="{{item.activityPrizeType == 2}}">
					<label>{{item.activityPrizeNum}}</label>
					<label>{{item.activityPrizeValue.commodityName}}</label>
				</block>
				<block wx:if="{{item.activityPrizeType == 3}}">
					<label>{{item.activityPrizeNum}}张</label>
					<label>{{item.activityPrizeValue.coupousName}}</label>
				</block>
				<block wx:if="{{item.activityPrizeType == 4}}">
					<label>{{item.activityPrizeValue.integralVal}}</label>
					<label>积分</label>
				</block>
			</view>
			<view>
				<label>{{tools.sub.formatMillToS(item.journaTime)}}</label>
			</view>
		</view>
	</block>
</view>
<view class="pop_bg" hidden='{{prizeShow}}'></view>
<view class="pop_bg" hidden='{{!noPopupHidden}}'></view>
<view class="pop_bg" hidden='{{tipShow}}'></view>
<view hidden="{{tipShow}}" style="line-height:150rpx;text-align:center;z-index:9999;font-size:30rpx;color:#000;border-radius:20rpx;padding:20rpx;background:#fff;left:25%;position:fixed;top:40%;width:400rpx;height:150rpx;">
	<view style="color:#FF7E00;opacity:0.5;transform:rotate(-45deg)">{{resCode}}</view>
</view>