/* pages/vipCard/vipCard.wxss */

page {
  background: #fff;
}

.bind_onLine {
  width: 40%;
  position: absolute;
  left: 5%;
  top: 60px;
  background: #fff;
  text-align: center;
  border-radius: 10rpx;
}

.add {
  width: 60rpx;
  height: 60rpx;
  background: #cac18f;
  border-radius: 50%;
  margin: 0 auto;
  font-size: 50rpx;
  line-height: 60rpx;
  margin-top: 20rpx;
  margin-bottom: 14rpx;
  color: #fff;
  border: 1px solid #cac18f;
}

.bind_offLine {
  width: 40%;
  position: absolute;
  right: 5%;
  top: 60px;
  background: #fff;
  text-align: center;
  border-radius: 10rpx;
}

.vip_text {
  margin-bottom: 20rpx;
  color: #cac18f;
  font-size: 30rpx;
}

.no_image {
  width: 40%;
  margin-left: 30%;
  margin-top: 100rpx;
}

.cardWrap {
  margin-left: 25rpx;
  margin-right: 25rpx;
  margin-top: 30rpx;
}

.card_available {
  margin-bottom: 25rpx;
  border-left: 6rpx solid #FF7E00;
}

.card_pic {
  width: 700rpx;
  height: 300rpx;
  border-radius: 10rpx;
  margin-bottom: 4rpx;
}

/*add*/

.card_Wrap {
  padding: 40rpx 0;
  border-radius: 10rpx;
  width: 90%;
  margin: 0 auto;
  margin-bottom: 20rpx;
  position: relative;
  height: 240rpx;
}

.card_Wrap image {
  border-radius: 10rpx;
  z-index: -1;
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.card_title {
  font-size: 26rpx;
  margin-left: 20rpx;
  color: #fff;
  text-align: center;
}

.card_title text {
  color: #fff;
  float: right;
  margin-right: 20rpx;
}

.card_no {
  margin-top: 20rpx;
  font-size: 26rpx;
  text-align: left;
  margin-left: 20rpx;
}

.amount_wrap {
  margin: 30rpx 0;
  text-align: center;
  color: #fff;
}

.remain_amount {
  font-size: 26rpx;
}

.remain_amount text {
  font-size: 32rpx;
  font-weight: bold;
}

.remain_score {
  font-size: 26rpx;
  margin-left: 40rpx;
}

.remain_score text {
  font-size: 32rpx;
  font-weight: bold;
}

.validDate {
  font-size: 26rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
  text-align: right;
  color: #fff;
}

.card_id {
  text-align: left;
  padding-top: 20rpx;
  padding-left: 20rpx;
  color: #fff;
  font-size: 24rpx;
}

.cardTip {
  font-weight: bold;
  text-align: center;
  font-size: 32rpx;
  padding-bottom: 40rpx;
}

.bind_append {
  left: 30%;
}

.bl_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.confirm_btn {
  display: inline-block;
  width: 35%;
  margin: 20px auto;
  height: 70rpx;
  line-height: 70rpx;
  border: none;
  margin-top: 60rpx;
  margin-right: 6%;
  margin-left: 6%;
  font-size: 26rpx;
}

.billWrap {
  position: absolute;
  z-index: 999;
  top: 20%;
  width: 90%;
  background: #fff;
  margin-left: 5%;
  border-radius: 10rpx;
}

.billTitle {
  text-align: center;
  line-height: 40rpx;
  margin-top: 30rpx;
}

.billOrder {
  width: 85%;
  height: 70rpx;
  background: #ededed;
  margin-top: 30rpx;
  text-align: left;
  border-radius: 10rpx;
  margin-left: 4%;
  padding-left: 40rpx;
}

.topWrap {
  text-align: center;
  margin: 15px 0;
  margin-top: 40rpx;
}

.topWrap image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}

.topWrap view {
  font-size: 12px;
  color: #999;
}
.fillCode{
  position: relative;
  height:68rpx;
  line-height:68rpx;
  border-bottom:2rpx solid #717071;
}
.fillCode input{
  padding:0 20rpx;
  height: 68rpx;
  line-height: 68rpx;

}
.fillCode .smsButton {
  border: 1rpx solid #717071;
  padding: 4rpx 8rpx;
  position: absolute;
  right: 0;
  bottom: 8rpx;
  border-radius: 8rpx;
  z-index: 10;
  line-height:52rpx;
  height:52rpx;
  font-size:26rpx;

}

.bl_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.topWrap {
  text-align: center;
  margin: 15px 0;
  margin-top: 40rpx;
}

.topWrap image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}

.topWrap view {
  font-size: 12px;
  color: #999;
}
.fillCode{
  position: relative;
  height:68rpx;
  line-height:68rpx;
  border-bottom:2rpx solid #717071;
}
.fillCode input{
  padding:0 20rpx;
  height: 68rpx;
  line-height: 68rpx;

}
.fillCode .smsButton {
  border: 1rpx solid #717071;
  padding: 4rpx 8rpx;
  position: absolute;
  right: 0;
  bottom: 8rpx;
  border-radius: 8rpx;
  z-index: 10;
  line-height:52rpx;
  height:52rpx;
  font-size:26rpx;

}
.confirm_btn {
  display: inline-block;
  width: 35%;
  margin: 20px auto;
  height: 70rpx;
  line-height: 70rpx;
  border: none;
  margin-top: 60rpx;
  margin-right: 6%;
  margin-left: 6%;
  font-size: 26rpx;
}