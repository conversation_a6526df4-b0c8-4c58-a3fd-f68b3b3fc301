<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<wxs src="../../wxs/subutil.wxs" module="tools" />
<view class="currency">
	<view class="currency_one">
		<view class="currency_two">
			<view class="currency_three">
				<image src="{{calendar}}" style="width:100%;height:auto;" mode="widthFix"></image>
			</view>
			<view>
				<view class="currency_four">
					已{{resData.activityPrizeType==2?'连续':'累计'}}签到
					<span style="color:#FF9002;">{{resData.activityContinuityDays?resData.activityContinuityDays:0}}</span>
					天
				</view>
				<view class="currency_five" bindtap="toSignRules">
					<text>查看签到规则</text>
				</view>
			</view>
		</view>
		<view>
			<button class="currency_button" wx:if="{{joinSet==0}}" style="opacity: 1;" bindtap="toSignBind">立即签到</button>
            <!--<button class="currency_button" wx:elif="{{result.todaySingin==4}}" style="opacity: 0.3;">已满签</button>-->
			<button class="currency_button" wx:else style="opacity: 0.3;" >已签到</button>
			<template wx:if="{{state1}}" is="fullSignature" data="{{...item}}" />
			<template wx:if="{{state4}}" is="signReward" data="{{...item}}" />
		</view>
	</view>
</view>



<view class="notice" style="margin-top:80px;">
	<view class="notice_one">
		<view wx:if="{{result.signinNotice}}">
			<view class="notice_two">
				<image src="{{notice}}" style="width:100%;height:auto;" mode="widthFix"></image>
			</view>
			<view class="notice_three">
				<view class="example">
					<view class="marquee_box">
						<view class="marquee_text" style="{{orientation}}:{{marqueeDistance}}rpx;font-size: {{size}}rpx;">
							{{result.signinNotice}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<view class="notice_four" >
		<!--<view class="notice_five">
			已签到
			<span style="color:#FF9002;">{{signNum}}</span>
			天
		</view>-->
	</view>
	<!--<view class="notice_seven">
				<view class="notice_eight">
					<image class="myRward_img" src="{{signed}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #FF7E00;">已签</view>
			</view>
			<view class="notice_seven" wx:if="{{item.isSingin==0}}">
				<view class="notice_eight">
					<image class="myRward_img" src="{{notsigned}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #999999;">未签</view>
			</view>
			<view class="notice_seven" wx:if="{{item.isSingin==2}}">
				<view class="notice_eight" bindtap="missSignature" data-usersignid="{{item.userSigninId}}">
					<image class="myRward_img" src="{{notsigned}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #999999;">漏签</view>
			</view>-->
	<view class="notice_six" style="height:{{noticeHeight}}rpx;">
		<!--<block wx:for="{{scheduleDetailList}}" wx:key="index">
			<view class="notice_seven" wx:if="{{item.isSingin==1}}">
				<view class="notice_eight">
					<image class="myRward_img" src="{{signed}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #FF7E00;">已签</view>
			</view>
			<view class="notice_seven" wx:if="{{item.isSingin==0}}">
				<view class="notice_eight">
					<image class="myRward_img" src="{{notsigned}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #999999;">未签</view>
			</view>
			<view class="notice_seven" wx:if="{{item.isSingin==2}}">
				<view class="notice_eight" bindtap="missSignature" data-usersignid="{{item.userSigninId}}">
					<image class="myRward_img" src="{{notsigned}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #999999;">漏签</view>
			</view>
		</block>-->
		<view style="display:flex;justify-content:space-around;position:relative;" >
			<view style="z-index:0;position:absolute;left:10%;right:10%;top:25%;border-top:1rpx solid #ff6600;"></view>
			<block wx:for="{{showList}}" wx:for-item="oneResult" wx:key="index" wx:for-index="signIndex">
				<block wx:if="{{oneResult.joinSeat==1}}" >
					<view style="z-index:1;">
						<image style="width:50rpx;" src="{{signed}}" mode="widthFix"></image>
						<view style="font-size:26rpx;margin-top:4rpx;">{{oneResult.monthNum}}.{{oneResult.dayNum}}</view>
						<!--<view style="font-size:28rpx;">{{oneResult.joinSeat==1?'已签':'未签'}}</view>-->
					</view>
					
				</block>
				<block wx:else>
					<view style="z-index:1;">
						<image style="width:50rpx;" src="{{notsigned}}" mode="widthFix"></image>
						<view style="font-size:26rpx;margin-top:4rpx;">{{oneResult.monthNum}}.{{oneResult.dayNum}}</view>
					</view>
				</block>
				<!--<view class="notice_eight">
					<image style="width:40rpx;" src="{{signed}}" mode="widthFix"></image>
				</view>
				<view class="notice_eight">
					<image class="width:40rpx;" src="{{notsigned}}" mode="widthFix"></image>
				</view>
				<view class="notice_nine" style="color: #FF7E00;">已签</view>-->
			</block>
		</view>
		<block wx:if="{{resData.activityTimeContinuity == 4}}">
			<view style="font-size:28rpx;color:#666;padding:20rpx;">断签将从第一天重新开始</view>
		</block>
	</view>
	
</view>
<view class="myRward" style="margin-top:20px;">
	<view class="myRward_one" style="float:left" bindtap="toMyreward">
		<view class="myRward_two">
			<view class="myRward_three">
				<image class="myRward_img" src="{{reward}}" mode="widthFix"></image>
			</view>
			<view class="myRward_four">
				<text>我的奖励</text>
			</view>
		</view>
	</view>
	<view class="myRward_one" style="float:right" bindtap="signature">
		<view class="myRward_two">
			<view class="myRward_three">
				<image class="myRward_img" src="{{signature}}" mode="widthFix"></image>
			</view>
			<view>
				<view class="myRward_four">
					<text>补签卡</text>
				</view>
				<view class="myRward_five">
					<text>{{result.usableNum}}</text>
				</view>
			</view>
		</view>
		<template wx:if="{{state3}}" is="signature" data="{{...item,supplyData}}" />
	</view>
</view>
<template wx:if="{{state5}}" is="supplysignature" data="{{...item}}" />
<template wx:if="{{state2}}" is="missSignature" data="{{...item}}" />
<view class="signAD">
	<image class="myRward_img" src="{{signAD}}" mode="widthFix"></image>
</view>
<view style="background:#f5f5f5;">
	<view style="height:80rpx;line-height:80rpx;padding-left:25rpx;">推荐商品</view>
	<view class="pic_two">
		<block wx:key="unique" wx:for="{{indexGoodsList}}" wx:for-item="goods" wx:for-index="goodsIndex">
			<view class="commodity_box3" style="border-radius:20rpx">
				<image lazy-load='true' style="border-top-left-radius:20rpx;border-top-right-radius:20rpx;" bindtap="imageClick"
					data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
				<view class="goods_desc">
					<view class="desc_title">
						{{goods.commodityName}}
					</view>
					<view class="desc_price" style="display:flex;justify-content:space-between">
						<view class="price_l">
							<label class="price_tag">￥</label>
							<label class="price_inner"
								hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
							<label class="price_inner">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
							<block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
								<label
									hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0&&indexConfig.goodsBean.shopCartStyle != 3)?false:true}}"
									class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
							<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
								<label class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
							</block>
							<block
								wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0&& indexConfig.goodsBean.shopCartHidden==2}}">
								<label>
									<text class="promotionDesc">{{goods.promotionList[0].promotionName}}</text>
								</label>
							</block>
						</view>
					</view>
				</view>
			</view>
		</block>
	</view>
	<!--弹出层提示-->
	<view class="prizeBox" hidden='{{!prizePopupHidden}}'>
		<image src="{{turntable_bj}}" style="width: 473rpx;position:absolute;left:0;top:0;z-index:-1" mode="widthFix"></image>
		<view style="font-size:40rpx;line-height:60rpx;margin-top:50rpx;">签到成功</view>
		
		<view style="margin-top:60rpx;height:220rpx;">
			<view style="margin-top:70px;padding:0 20rpx;color:#ff6600;font-size:28rpx">{{showContent}}"</view>
		</view>
		<view class="sumbitBtn" bindtap="prizeShowBindTap">我知道了</view>
	</view>
	<view class="pop_bg" hidden='{{!prizePopupHidden}}'></view>
</view>