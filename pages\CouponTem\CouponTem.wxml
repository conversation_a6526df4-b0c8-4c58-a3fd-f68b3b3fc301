<wxs src="../../wxs/subutil.wxs" module="tools" />
<!-- 通用劵 start -->
<template name="coupon1">
	<view class="couponWrapTe" style="width:750rpx;height:540rpx">
		<icon class="closeTemp1" bindtap='' type="clear" size='34' color='#fff' />
		<image lazy-load='true' src="{{img1}}" style="width:100%" mode="aspectFit"></image>
		<view style="position:absolute;top: 26%;left: 104rpx;">
			<view class="oneName">活动名称</view>
			<view class="onetitle"><text>恭喜获得\n通用优惠券</text></view>
			<view class="oneTime">2020.03.03-2020.04.04</view>
		</view>
		<view class="onePrice"><text style="font-size:54rpx;">100</text>元</view>
	</view>

</template>
<!-- 通用劵 end -->

<!-- 新人礼包 start -->
<template name="coupon2">
	<view class="couponWrapTe2" bindtap="goToHome">
		<icon class="closeTemp2" bindtap='' type="clear" size='34' color='#fff' />
		<!-- <image lazy-load='true' src="{{img2}}" style="width:640rpx;margin-left:40rpx;" mode="widthFix"></image> -->
		<view style="">
			<view style="font-size:56rpx;color:#fff;line-height:100rpx;text-align:center;margin-top:20rpx;">新人礼包</view>
			<view style="max-height:454rpx;overflow:auto;">
				<block wx:key="unique" wx:for="{{storeCardList}}" wx:for-item="sCard" wx:for-index="cardIndex">
					<view style="margin:10rpx 0;position:relative;text-align:center">
						<image src="{{img4}}" style="width:540rpx;" mode="widthFix"></image>
						<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
							<view class="twoPrice">折<text style="font-size:38rpx">{{sCard.discountAmount}}</text></view>
						</block>
						<block wx:else>
							<view class="twoPrice">￥<text style="font-size:38rpx">{{sCard.discountAmount}}</text></view>
						</block>
						<view class="twoTitle">
							<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
								<text
									style="display:block;padding-top: 10rpx;">{{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"享受"+sCard.discountAmount+"折扣"}}</text>
							</block>
							<block wx:else>
								<text
									style="display:block;padding-top: 10rpx;">{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"优惠"+sCard.discountAmount+"元"}}</text>
							</block>
							<text
								style="display:block;color:#AAAAAA;font-size:24rpx">{{tools.sub.formatDate(sCard.cardStartTime,10)}}-{{tools.sub.formatDate(sCard.cardEndTime,10)}}</text>
						</view>
					</view>
				</block>
				<!--注册积分-->
				<view style="margin:10rpx 0;position:relative;text-align:center" hidden="{{!isSendScoreFlag}}">
					<image src="{{img3}}" style="width:540rpx;" mode="widthFix"></image>
					<view class="twoPrice">￥<text style="font-size:38rpx">100</text></view>
					<view class="twoTitle">
						<text style="display:block;line-height:100rpx;">注册积分{{sendScore}}枚</text>
					</view>
				</view>
				<!--注册积分-->

			</view>

		</view>
	</view>
</template>
<!-- 新人礼包 end -->

<!-- 开屏弹券 start -->
<!-- <template name="coupon3">
	<view class="couponWrapTe3" style="width:750rpx;" bindtap="closeCardBgBindTap">
		<icon class="closeTemp3" type="clear" size='34' color='#fff' />
		<image lazy-load='true' src="{{img5}}" style="width:660rpx;margin-left:60rpx;" mode="widthFix"></image>
		<view
			style="position:absolute;top: 296rpx;left: 160rpx;width:420rpx;max-height:300rpx;overflow-y:auto;border-radius:12rpx">
			<block wx:key="unique" wx:for="{{sendStoreCardList}}" wx:for-item="sCard" wx:for-index="cardIndex">
				<view class="threeCircal clearfix">
					<view class="threeCoupon">
						<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
							<text style="font-size: 40rpx;">{{sCard.discountAmount}}</text>
							<text style="font-size: 28rpx;">折</text>
						</block>
						<block wx:else>
							<text style="font-size: 28rpx;">￥</text>
							<text style="font-size: 40rpx;">{{sCard.discountAmount}}</text>
						</block>
					</view>
					<view class="threeCouponProduct">
						<view class="threeMoney">
							<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
								<text class="couponGift1">
									{{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"享受"+sCard.discountAmount+"折扣"}}
								</text>
							</block>
							<block wx:else>
								<text class="couponGift1">
									{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"优惠"+sCard.discountAmount+"元"}}
								</text>
							</block>
							<text
								class="couponGift2">{{tools.sub.formatDate(sCard.cardStartTime,10)}}-{{tools.sub.formatDate(sCard.cardEndTime,10)}}</text>
						</view>
					</view>
					<label class="circal_top3"></label>
					<label class="circal_bottom3"></label>
				</view>
			</block>
		</view>
	</view>
</template> -->
<template name="coupon3">
	<view class="couponWrapTe3" style="width:750rpx;" bindtap="closeCardBgBindTap">
		<icon class="closeTemp3" type="clear" size='34' color='#fff' style="top: -80rpx;"/>
		<image lazy-load='true' src="{{img5}}" style="width:660rpx;margin-left:60rpx;" mode="widthFix"></image>
		<view
			style="position:absolute;top: 140rpx;left: 94rpx;width:600rpx;max-height:600rpx;overflow-y:auto;border-radius:12rpx">
			<block wx:key="unique" wx:for="{{sendStoreCardList}}" wx:for-item="sCard" wx:for-index="cardIndex">
				<view class="threeCircal1 clearfix">
					<view class="threeCoupon1">
						<image lazy-load='true' src="{{img11}}" style="width:570rpx;" mode="widthFix"></image>
						<!-- <text class="amount">50</text> -->
						<view class="amount-group">
							<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
							<text class="amount">{{sCard.discountAmount}}</text>
							<text class="unit">折</text>
						</block>
						<block wx:else>
							<text class="unit">￥</text>
							<text class="amount">{{sCard.discountAmount}}</text>
						</block>
						</view>
						<text class="txt-1">优惠券</text>
						<view class="text-group">
							<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
								<text class="txt-2">{{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"享受"+sCard.discountAmount+"折扣"}}</text>
							</block>
							<block wx:else>
								<text class="txt-2">{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"优惠"+sCard.discountAmount+"元"}}</text>
							</block>
							<text class="txt-3">{{sCard.cardName}}</text>
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>
<!-- 开屏弹券 end -->

<!-- 生日券 start -->
<template name="coupon4">
	<view class="couponWrapTe4" style="width:750rpx;">
		<icon class="closeTemp4" bindtap='' type="clear" size='34' color='#fff' />
		<image lazy-load='true' src="{{img6}}" style="width:600rpx;margin-left:60rpx;" mode="widthFix"></image>
		<view style="position:absolute;top: 48%;left: 176rpx;padding:20rpx 0;">
			<view class="fourTitle">
				<text style="color:#FF3A00;font-weight:bold;font-size:40rpx">100</text>元代金券
			</view>
			<view class="fourDate">2019.3.25-2019.5.20</view>
		</view>
	</view>
</template>
<!-- 生日券 end -->

<!-- 支付 start -->
<template name="coupon5">
	<swiper indicator-dots="true" class="couponWrapTe5" style="height:880rpx;width:750rpx;">

		<block wx:key="unique" wx:for="{{storeCardList}}" wx:for-item="sCard" wx:for-index="cardIndex">
			<swiper-item>
				<view class="" style="width:750rpx;" bindtap="closeCardBgBindTap">
					<icon class="closeTemp5" bindtap='' type="clear" size='34' color='#fff' />
					<image lazy-load='true' src="{{img7}}" style="width:600rpx;margin-left:60rpx;" mode="widthFix"></image>
					<view style="position:absolute;top: 40%;left: 176rpx;" class="fiveBox">
						<view style="font-size:36rpx">支付有礼</view>
						<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
							<view>折<text style="font-size:46rpx">{{sCard.discountAmount}}</text></view>
						</block>
						<block wx:else>
							<view>￥<text style="font-size:46rpx">{{sCard.discountAmount}}</text></view>
						</block>
						<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
							<view>
								{{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"享受"+sCard.discountAmount+"折扣"}}
							</view>
						</block>
						<block wx:else>
							<view>
								{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"优惠"+sCard.discountAmount+"元"}}
							</view>
						</block>
						<view style="font-size:24rpx;">
							有效期：{{tools.sub.formatDate(sCard.cardStartTime,10)}}-{{tools.sub.formatDate(sCard.cardEndTime,10)}}</view>
						<view class="fiveBtn">立即领取</view>
					</view>
				</view>
			</swiper-item>
		</block>


	</swiper>
</template>
<!-- 支付 end -->

<!-- 红包 start -->
<template name="coupon6">
	<view class="couponWrapTe6" style="width:750rpx;">
		<icon class="closeTemp6" bindtap='' type="clear" size='34' color='#fff' />
		<image lazy-load='true' src="{{img9}}" bindtap='' style="width:600rpx;margin-left:60rpx;" mode="widthFix"></image>
		<view style="position:absolute;top: 50%;left: 210rpx;">
			<view style="color:#FFE023;width:280rpx;text-align:center;">￥<text style="font-size:64rpx;">2.5</text></view>
			<view style="font-size:28rpx;color:#fff;width:280rpx;text-align:center;margin-top:20rpx">奖励金满10元可提现</view>
		</view>

	</view>
</template>

<!-- 开通会员卡 -->
<template name="coupon7">
	<view class="couponWrapTe7" style="width:750rpx;">
		<icon class="closeTemp7" bindtap="closeVipCardBindTap" type="clear" size='34' color='#fff' />
		<image lazy-load='true' src="{{img10}}" bindtap='goVipCard' style="width:600rpx;margin-left:60rpx;" mode="widthFix">
		</image>

	</view>
</template>