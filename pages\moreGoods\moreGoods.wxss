/**热销单品**/
page{
  background:#fff;
}
.section {
  line-height: 26px;
  margin-top: 0px;
}
/* 定位 搜索 */
.location_box {
  height: 52rpx;
  position: fixed;
  top: 0px;
  left: 0;
  font-size: 28rpx;
  line-height: 26px;
  width: 100%;
  background: #fff;
  z-index:1999;
}

.location_box text {
  line-height: 52rpx;
  height: 52rpx;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #fff;
  color: #fff;
  border-radius: 30rpx !important;
  padding: 0 2%;
  overflow: hidden;
}

.location_box icon {
  float: left;
  position: absolute;
  top: 45rpx;
  left: 9%;
  z-index: 10;
  line-height: 34px;
}

.location_box input {
  line-height: 62rpx;
  height: 62rpx;
  width: 76%;
  display: block;
  float: left;
  margin: 0 6%;
  background: #ededed;
  color: #272727;
  border-radius: 14rpx;
  padding-left: 8%;
  padding-right: 4%;
}


.section_title {
  text-align: center;
  font-size: 16px;
  background: #f4f4f4;
  line-height: 50px;
  height: 50px;
  margin-top: 0px;
  color: #fa6a85;
  letter-spacing: 1px;
  position: relative;
}

.section_title image {
  width: 18px;
  height: 18px;
  position: absolute;
  margin-top: 15px;
}

.flex-wrp {
  flex-direction: row;
  background: #fff;
  padding-top: 30rpx;
  padding-left: 10rpx;
  padding-right: 10rpx;

}

.flex-item {
  width: 356rpx;
  float: left;
  background: #fff;
  margin-bottom: 10rpx;
  border-radius: 6rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  padding-bottom:10rpx;

}

.flex-item:nth-child(2n+1) {
  margin-right: 10rpx;
}

.goods_tips {
  color: #958353;
  border: 1px solid #bcb396;
  top: 10px;
  left: 10px;
  width: 12px;
  height: 62px;
  line-height: 16px;
  display: block;
  position: absolute;
  font-size: 12px;
  word-wrap: break-word;
  padding: 4px;
  border-radius: 3px;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.soonlist {
  font-size: 14px;
  writing-mode: vertical-lr;
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 2;
  color: #988250;
  border: 1px solid #988250;
  border-radius: 5px;
  padding: 5px 0;
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.goods_adv {
  padding: 0 5px;
  height: 30rpx;
  line-height: 30rpx;
  font-size: 12px;
  overflow: hidden;
}

.goods_title {
    display: block;
  /**height: 28px;
  line-height: 28px;**/
  overflow: hidden;
  font-size: 30rpx;
  margin: 0 5px;
  height:70rpx;
  line-height:34rpx;
  margin-top:12rpx;
}

.goods_price {
  color: #fa6a85;
  font-size: 15px;
  margin-left: 5px;
}

/*明星单品样式结束*/

/*限时限购样式开始*/

.time_limit {
  width: 100%;
  height: 38px;
  padding: 5px 0;
  background: #cfd0d4;
}

.time_limit label {
  width: 20%;
  height: 100%;
  display: block;
  float: left;
  box-sizing: border-box;
  font-size: 13px;
}

.time_limit label:first-child {
  border-right: 1px solid #b3b7bb;
}

.time_limit label:last-child {
  border-left: 1px solid #b3b7bb;
}

.time-active {
  background: #94999f;
  height: 48px !important;
  margin-top: -5px;
  border-left: 1px solid #94999f;
  position: relative;
}

.time_limit label text {
  display: block;
  text-align: center;
  line-height: 20px;
}

.time_limit label text:first-child {
  color: #2d2e2e;
  font-size: 12px;
}

.time_limit label text:last-child {
  color: #96938c;
  font-size: 13px;
}

.time-active text {
  color: #fff !important;
}

.time-active text:first-child {
  margin-top: 4px;
}

.row {
  position: absolute;
  bottom: -9px;
  left: 37%;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #94999f;
}

.countdown {
  width: 100%;
  background: #fff;
  height: 50px;
  text-align: center;
  line-height: 50px;
  font-size: 12px;
}

.countdown text {
  color: #e8e8e8;
}

.word_tips {
  margin: 0 5px 0 10px;
  color: #313131 !important;
}

.time_tips {
  margin: 0 10px 0 0px;
  color: #343434 !important;
  font-weight: bold;
}

.recommendGoods_2 {
  width: 100%;
  padding: 10px 0 10px 0;
  height: 140px;
  background: #fff;
}

.recommend_goods {
  width: 94%;
  margin: 0 3%;
  height: 100%;
  border: 1px solid #d4d4d4;
}

.recommend_goods image {
  width: 120px;
  height: 120px;
}

.recommend_box {
  width: 58%;
  float: right;
  height: 100%;
}

.recommend_box text {
  overflow: hidden;
}

.big_title {
  color: #2d2d2d;
  font-size: 15px;
  font-weight: bold;
  display: block;
  width: 100%;
  height: 24px;
}

.little_title {
  margin-top: 15px;
  color: #7e7f81;
  font-size: 15px;
  display: block;
  width: 100%;
  margin-bottom: 5px;
  height: 20px;
}

.recommend_box label {
  font-size: 12px;
  width: 100%;
  display: block;
}

.recommend_box label:last-child {
  height: 30px;
}

.limit_price {
  color: #121212;
  font-size: 16px;
  font-weight: bold;
}

.cost_price {
  color: #808080;
  margin-left: 10px;
  text-decoration: line-through;
}

.limit_number {
  border: 1px solid #acacac;
  border-radius: 12px;
  background: #fff;
  padding: 3px 8px;
  color: #2e2e2e;
  line-height: 30px;
}

.buttonBox {
  padding: 2px 16px;
  background: #fa6a85;
  color: #fff;
  float: right;
  font-size: 14px;
  margin-right: 10px;
}

/**热销单品**/

.section {
  line-height: 26px;
  /**margin-top: 20px;**/
}

/*限时限购样式结束*/

/*新品上市样式开始*/

.recommendGoods {
  width: 100%;
  padding: 10px 0 10px 0;
  height: 120px;
  background: #fff;
}

.right_recommend {
  width: 58%;
  float: right;
  height: 100%;
}

.right_recommend text {
  display: block;
  width: 100%;
  overflow: hidden;
  height: 24px;
}

.right_recommend text:nth-child(1) {
  color: #76797e;
  font-size: 14px;
  margin-top: 22px;
}

.right_recommend text:nth-child(2) {
  color: #2e2e2e;
  font-size: 15px;
  font-weight: bold;
}

.right_recommend text:nth-child(3) {
  color: #070707;
  font-size: 17px;
  margin-top: 6px;
  font-weight: bold;
}

.goods_title2 {
  display: block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  font-size: 14px;
  background: #d8d9de;
  margin-top: 4px;
  color: #76797e;
  text-align: center;
}

.goods_tag {
  width: 96%;
  margin: 5px 2%;
}

.goods_tag text {
  color: #fff;
  margin-left: 5px;
  padding: 2px 3px;
  font-size: 12px;
}

.goods_tag text:nth-child(1) {
  background: #94999f;
}

.goods_tag text:nth-child(2) {
  background: #af423d;
}

/*新品上市样式结束*/

/**推荐 新增商品样式**/

.recommendGoods_ad {
  width: 100%;
  background: #fff;
  /** padding-top: 10px;
  border-top: 1px solid #f4f4f4;**/
}

.recommendGoods_ad:after {
  clear: both;
  content: "";
  height: 0;
  display: block;
}

.recommendAd {
  width: 50%;
  float: left;
  border-right: 1px solid #f4f4f4;
  border-top: 1px solid #f4f4f4;
  height: 120px;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.recommendAd:nth-child(2n) {
  border-right: none;
}

.recommendAd label {
  width: 57%;
  height: 100%;
  font-size: 15px;
  color: #030303;
  position: absolute;
  top: 6px;
  left: 10%;
  z-index: 20;
  max-width: 91px;
}

.recommendAd label text {
  display: block;
  width: 100%;
}

.recommendAd label text:first-child {
  height: 25px;
  margin-top: 35%;
}

/**.recommendAd label text:last-child {
  color: #fff;
  font-size: 12px;
  background: #c7b797;
  width: 50px;
  text-align: center;
  border-radius: 4px;
  height: 24px;
  line-height: 24px;
}**/

.recommendAd image {
  width: 100px;
  position: absolute;
  bottom: 0;
  right: -2%;
}

/**通栏**/

.banner_box {
  width: 100%;
  position: relative;
}

.banner_box image {
  width: 100%;
}

.banner_word {
  position: absolute;
  top: 18%;
  right: 10%;
}

.banner_title {
  display: block;
  font-size: 17px;
  text-align: center;
  letter-spacing: 3px;
}

.banner_title text {
  display: block;
}

.banner_price {
  display: block;
  text-align: center;
  color: #000;
  font-size: 13px;
  line-height: 22px;
}

.discount_price {
  display: block;
  text-align: center;
  color: #fa6a85;
  font-size: 13px;
  line-height: 22px;
}

.banner_btn {
  background: #fa6a85;
  color: #fff;
  height: 26px;
  line-height: 26px;
  font-size: 14px;
  width: 98px;
}

.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}

.slideTwo {
  margin-top: 20px;
  height: 100px;
  box-shadow: 0px 0px 8px 3px #e8e8e8;
  border-radius: 10px;
  margin-left: 5%;
  margin-right: 5%;
}

.slideTwo_l {
  float: left;
  width: 30%;
  height: 100%;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.slideTwo_r {
  width: 65%;
  float: right;
  padding-right: 2%;
  height: 100px;
  overflow: hidden;
}

.r_title {
  height: 30px;
  line-height: 30px;
}

.r_content {
  font-size: 12px;
  color: #666;
}

.slideThree_l {
  float: left;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1px solid #ddd;
  margin-top: 5px;
  margin-left: 5px;
}

.promotionMark {
  height: 22px;
  line-height: 22px;
  padding: 0px 6px;
  border-radius: 4px;
  z-index: 999;
  position: absolute;
  background: #e57b7b;
  top: 5%;
  left: 5%;
  color: #fff;
  font-size: 12px;
}

.price_append {
  text-decoration: line-through;
  font-size: 12px;
  color:#666;
}

.promotionPrice {
  font-size: 15px;
  color: #e57b7b;
}

.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}

.announceItem {
  position: relative;
}

.announceItem label:first-child {
  position: absolute;
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #ddd;
  top: 30%;
  left: 0;
}

.announceItem label:last-child {
  color: #444;
  font-size: 13px;
  margin-left: 14px;
  display: inline-block;
  height: 20px;
  width: 250px;
  overflow: hidden;
}

.showAnnounce {
  vertical-align: middle;
  margin-top: 15px;
  float: left;
  width: 20px;
  height: 20px;
  margin-left: 16px;
  margin-right: 5px;
}

.announceDetail {
  float: left;
  margin-left: 10px;
  vertical-align: middle;
  margin-top: 5px;
  width: 80%;
}

.annoucnePart {
  height: 56px;
  padding: 4px 0px 0px 0px;
  background: #fff;
  margin: 20rpx 0;
}

.black_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.scroll_block {
  width: 80%;
  position: absolute;
  top: 40%;
  left: 10%;
  z-index: 130;
  padding-bottom: 46px;
  border-radius: 10px;
  background: #fff;
}

.announce_Title {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 10px;
  font-size: 15px;
  color: #444;
}

.announce_Content {
  font-size: 14px;
  color: #444;
  padding: 0px 10px;
}

.oneItem {
  margin-top: 10px;
  vertical-align: middle;
}

.eventWrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  width: 50px;
  height: 50px;
}

.eventWrap image {
  width: 50px;
  height: 50px;
  top: -2px;
  left: -2px;
}

.eventWrap text {
  z-index: 9999;
  color: #fff;
  position: absolute;
  display: inline-block;
  font-size: 12px;
  top: 3px;
  left: 2px;
  transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
}
.goods_title {
  font-size: 15px;
  color: #333;
  /*height: 20rpx;
  line-height: 20rpx;8*/
  overflow: hidden;
  /*display: block;*/
  width: 100%;
}

.goods_adv {
  font-size: 24rpx;
  color: #666;
  overflow: hidden;
  display: block;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
}
