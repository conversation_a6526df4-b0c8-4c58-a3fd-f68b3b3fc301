const app = getApp()

Page({

    /**
     * 页面的初始数据
     */
    data: {
        categoryId: "",
        storeId: "",
        currPage: 1,
        searchLoading: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function(options) {
        this.data.categoryId = options.categoryId;
        this.data.storeId = options.storeId;
        this.initGoodsDetail();
    },
    initGoodsDetail: function() {
        var that = this;
        wx.request({
            url: app.projectName + '/applet/store/queryGoodsByCategoryId',
            data: {
                "storeId": that.data.storeId,
                "categoryId": that.data.categoryId,
                "currPage": that.data.currPage,
                       "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
                "loginId": app.getLoginId(),
                "userRole": app.getUserRole(),
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId()
            },
            success: function(res) {
                if (res.data.list != null && res.data.list.length > 0) {
                    let commodityList = [];
                    if (that.data.commodityList != null && that.data.commodityList.length > 0) {
                        commodityList = that.data.commodityList.concat(res.data.list);
                    } else {
                        commodityList = res.data.list;
                    }
                    that.setData({
                        commodityList: commodityList, //商品信息
                        searchLoading: true
                    });
                } else {
                    that.setData({
                        searchLoading: false
                    });
                }
            }
        })
    },
    goodsDetailBindTap: function(e) {
        var goodsId = e.currentTarget.dataset.commodityid;
        var storeId = e.currentTarget.dataset.storeid;
        app.turnToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId + "&storeId=" + storeId);
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function() {
        var that = this;
        if (that.data.searchLoading) {
            that.setData({
                currPage: that.data.currPage + 1
            });
            that.initGoodsDetail();
        }
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function(res) {
        var that = this;
        if (res.from === 'button') {
            // 来自页面内转发按钮
        }
        return {
            title: app.storeName,
            path: '/pages/index/index?recommendId=' + app.getUserId() + '&recommendAccount=' + app.getLoginAccount() + '&recommendName=' + app.getLoginName() + '&identity=' + app.getIdentity(),
            imageUrl: app.shareImageUrl,
            success: function(res) {
                // 转发成功
            },
            fail: function(res) {
                // 转发失败
            }
        }
    }
})