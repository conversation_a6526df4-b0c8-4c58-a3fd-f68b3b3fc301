<!--pages/storechain/storechain.wxml -->
<view bindtap="toLocalAddress" style="font-weight:bold;font-size:28rpx;color:#333;padding:20rpx 20rpx 0rpx 20rpx;">当前位置：{{localUserAddress}} ></view>
<view class="chain_bottom">
	<scroll-view scroll-y="true" class="chainWrap" bindscrolltolower="searchScrollLower">
		<!--单店铺-->
		<block wx:key="unique" wx:for="{{showReturnStoreList}}" wx:for-item="store">
			<view style="margin-bottom:30rpx;background:#fff;padding:20rpx 0 10rpx;">
				<view class="onechain" data-name="{{store.storeName}}" data-id="{{store.storeId}}" bindtap="selectStoreInfoBindTap" style="border-bottom:1rpx dashed #ddd;">
					<label class="clearfix" style="position:relative;">
						<image src="{{storeImgerSrc}}" class="chainPic" mode="widthFix"></image>
						<text class="chainName">{{store.storeName}}</text>
						<view class="d_wrap">
							<text class="chainDistance">距离{{store.storeDistance}}km</text>
						</view>
					</label>
					<view class="chainAddress" data-id="{{store.storeId}}" bindtap="goToIndexBindTap">
						{{store.storeAddress}}
					</view>
				</view>
				<view style="padding:10rpx;">
					<view bindtap='makePhoneBindTap' data-phone='{{store.phone}}' style="display:inline-block;width:50%;text-align:center;font-size:26rpx;">
						<image style="margin:0 6rpx;vertical-align:middle;width:36rpx" mode="widthFix" src="{{phone}}"></image>电话
					</view>
					<view data-name="{{store.storeName}}" data-address="{{store.storeAddress}}" data-latitude="{{store.latitude}}" data-longitude="{{store.longitude}}" bindtap="startNavigationBindTap" style="display:inline-block;width:50%;text-align:center;font-size:26rpx;">
						<image style="margin:0 6rpx; vertical-align:middle;width:36rpx" mode="widthFix" src="{{navi}}"></image>导航
					</view>
				</view>
			</view>
		</block>
		<!--单店铺-->
	</scroll-view>
</view>