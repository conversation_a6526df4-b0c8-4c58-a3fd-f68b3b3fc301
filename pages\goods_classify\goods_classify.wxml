<import src="../classifyTemplate/classifyTemplate.wxml" />
<template is="tagItem"
  data="{{tagHidden,topPage,topLeft,casualGoods,cheapGoods,popularGoods,disLikeGoods,tagImg5,tagImg6,tagImg7,tagImg8,c_casualGoods,c_cheapGoods,c_popularGoods,c_disLikeGoods,c_tagImg5,c_tagImg6,c_tagImg7,c_tagImg8,currentTag}}"></template>
<template is="classifyMode{{classifyShowType}}"
	data="{{fastType,commodityList,categoryBean,second_height,soldOut,cart,tmpCompany,stockBean,scan,overallStock,emptyGoods,emptyClassify,adPic,personal_more,scanShow,shopStyle,shop_cart1,shop_cart2,adOpen}}" />
<!-- 在底部的购物车 -->
<!-- 加入购物车   立即购买 -->
<view class='black_bg' hidden="{{skuShowHidden}}"></view>
<view class='scroll_blo page-dialog-wrap' hidden="{{skuShowHidden}}"
	style="background: #fff;border-radius: 8px 8px 0 0;">
	<view style='position:fixed; width:100%; height:100px;background:#fff;border-radius: 8px 8px 0 0;padding:10px 0;'>
		<view class='addgoods_pic'>
			<image lazy-load='true' mode="{{mode}}" src="{{skuGoodsImage}}"></image>
		</view>
		<label class='addgoods_title'>{{s_commodityName}}</label>
		<view class='addgoods_price' hidden="{{s_commodityUnitOmDefault==1?false:true}}">￥{{s_omPrice}}<block
				wx:if="{{showskuAllAttrList.length==0}}">/{{s_omUnit}}</block>
		</view>
		<view class='addgoods_price' hidden="{{s_commodityUnitOtDefault==1?false:true}}">￥{{s_otPrice}}<block
				wx:if="{{showskuAllAttrList.length==0}}">/{{s_otUnit}}</block>
		</view>
		<block wx:if="{{stockBean!=null}}">
			<block wx:if="{{stockBean.openStock}}">
				<block wx:if="{{stockBean.stockShowType==1}}">
					<view class="goods_stock">库存：{{s_goodsStock}}</view>
				</block>
				<block wx:elif="{{stockBean.stockShowType==2}}">
					<view class="goods_stock">库存：{{stockBean.showContent}}</view>
				</block>
			</block>
		</block>
		<block wx:else>
			<view class="goods_stock" hidden="{{s_goodsStock>0?false:true}}">库存：{{s_goodsStock}}</view>
		</block>
		<icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='hiddeSkuAddGoodsShopCartBindTap' />
	</view>
	<scroll-view scroll-y style='padding-top:100px;max-height:200px;'>
		<block wx:key="unique" wx:for="{{showskuAllAttrList}}" wx:for-item="sku">
			<view class='goods_classify'>
				<label>{{sku.skuAttrName}}</label>
				<view class='clearfix'>
					<block wx:key="unique" wx:for="{{sku.skuAttrValueList}}" wx:for-item="skuChild">
						<text bindtap='changeSKUBindTap' data-name='{{sku.skuAttrName}}' data-childname='{{skuChild.skuAttrName}}'
							class='{{skuChild.isSelect?"active_classify":""}}'>{{skuChild.skuAttrName}}
						</text>
					</block>
				</view>
			</view>
		</block>
		<view class="shopcart_num">
			<text style="float:left;margin-left:40rpx;">购买数量：</text>
			<view style="float:right">
				<view class="shop_om clearfix" hidden="{{s_commodityUnitOmDefault==1?false:true}}">
					<label class="minus_num" bindtap="omMinusNumBindTap">-</label>
					<input class="numBox" value="{{buyOmNum}}" type="number" bindinput="omNumBindInput"></input>
					<label class="plus_num" bindtap="omPlusNumBindTap">+</label>
					<text class="unit_num">{{s_omUnit}}</text>
				</view>
				<view class="shop_om clearfix" hidden="{{s_commodityUnitOtDefault==1?false:true}}">
					<label class="minus_num" bindtap="otMinusNumBindTap">-</label>
					<input class="numBox" value="{{buyOtNum}}" type="number" bindinput="otNumBindInput"></input>
					<label class="plus_num" bindtap="otPlusNumBindTap">+</label>
					<text class="unit_num">
						<block wx:if="{{showskuAllAttrList.length==0}}">{{s_otUnit}}</block>
					</text>


				</view>
			</view>
		</view>
	</scroll-view>




	<label class="btn btn-yellow pay-add-to-shoppingcart" bindtap='addSkuGoodsToShopCart'>
		加入购物车
	</label>
</view>
<!-- 加入购物车   立即购买 -->
<!-- 在底部的购物车 -->