@import '../popupTemplate/popupTemplate.wxss';
page{
 background:#F2F2F2; 
}
.score_top{
  width: 710rpx;
  margin:20rpx auto;
  background-color: #fff;
  border-radius: 8rpx;
  height: 290rpx;
  text-align: center;

}
.scoreContent{
  padding: 0 10rpx 20rpx 10rpx;
  width: 690rpx;
  margin:20rpx auto 120rpx;
  background-color: #fff;
  border-radius: 8rpx;
}
.top_title{
  padding:30rpx 0 20rpx 0;
  color:#666;
  font-size:28rpx;
}
.top_score{
  padding:0 0 30rpx 0;
  color:#FF7E00;
  font-size:70rpx;
}
.top_inner{
  margin:20rpx;
  padding-top:20rpx;
  margin-top:30rpx;
}
.inner_score{
  float:right;
  font-size:26rpx;
}
.inner_get{
  color:#FF7E00;
  margin-right:10rpx;
}
.inner_cont{
  margin:20rpx;
}
.cont_wrap{
  border:1rpx solid #ececec;
  border-radius:10rpx;
}
.wrap_one{
  padding:20rpx;
  border-bottom:1rpx solid #ececec;
}
.one_top{
  font-size:28rpx;
}
.one_amount{
  font-weight:normal;
  float:right;
}
.amount_append{
  color:#FF7E00;
}
.one_time{
  color:#666;
  font-size:26rpx;
  margin-top:10rpx;
}
.chargeAmount{
  float:left;
  width:30%;
  border-radius:20rpx;
  height:120rpx;
  /*line-height:100rpx;*/
  text-align:center;
  margin:1.2%;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  position:relative;
}
.charge_append{
  border:1px solid #FF7E00;
  background:#FF7E00;
}
.goCharge{
  text-align:center;
  position:fixed;
  height:88rpx;
  line-height:88rpx;
  color:#fff;
  background:#FF7E00;
  bottom:20rpx;
  left:44rpx;
  width:665rpx;
  border-radius: 44rpx;
}
.goCharge_append{
  color:#fff;
  background:#FF7E00;
}
.chargeMode2{
  position:relative;
}
.chargeMode2{
  position:relative;
  float:left;
  width:30%;
  border-radius:20rpx;
  height:120rpx;
  /*line-height:100rpx;*/
  text-align:center;
  margin:1.2%;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
}
.modeAmount{
  line-height:140rpx;
  text-align:left;
  padding-left:60rpx;
}
.modeDis{
  font-size:22rpx;
  position:absolute;
  right:20rpx;
  top:8rpx;
  color:#FF7E00;
}
.modeDis text{
  font-size:28rpx;
  font-weight:bold;
}
.mask_bg{
  position:absolute;
  background:#FF7E00;
  opacity:0.1;
  top:0;
  left:0;
  right:0;
  bottom:0;
  z-index:-1;
}
.money_add{
  margin-top:20rpx;
  font-weight:bold;
}
.gift_add{
  font-size:24rpx;
  margin-top:10rpx;
}
.amount_mask{
    color:#FF7E00;
}
.dis_mask{
  color:#FF7E00;
}
.border_mask{
  border-color:#FF7E00
}
.couponTitle{
  font-size: 24rpx;
  line-height: 40rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}