var app = getApp();
const popup = require('../popupTemplate/popupTemplate.js')
const http = require('../../utils/http');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    name: "",
    phone: "",
    sex: "",
    cardLevel: "",
    cardType: 0,
    cardTypeArray: ["", "积分卡", "储值卡", "积分储值卡"]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var scene = options.scene;
    if (scene != null && scene != undefined && scene != "undefined" && scene.length > 0) {
      //二维码携带参数优先判断
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/applet/queryStoreInfoByStoreId',
        data: {
          "storeId": scene
        },
        success: function (res) {
          var storeName = res.data.storeName;
          that.setData({
            name: app.getLoginName(),
            phone: app.getTelephone(),
            scene: options.scene,
            storeName: storeName
          });
        }
      })
    } else {
      //如果不是二维码分享，则判断用户是否选择开卡门店
      var selectStoreInfoKey = wx.getStorageSync("selectStoreInfoKey");
      var storeName = "";
      if (selectStoreInfoKey != "" && selectStoreInfoKey != undefined) {
        scene = selectStoreInfoKey.storeId;
        storeName = selectStoreInfoKey.storeName;
      }
      that.setData({
        scene: options.scene,
        name: app.getLoginName(),
        phone: app.getTelephone(),
        storeName: storeName
      })
    }
    that.querySupplierSettingVipFunction();
    that.getPersonTelephone();
  },
  getPersonTelephone:function(){
     var that = this;
     var odbtoken = wx.getStorageSync('odbtoken');
     var logintoken = wx.getStorageSync('loginToken');
     wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),

      },
      url: app.projectName + '/wechatAppletLogin/getPersonalTel',
      success: function (res) {
        if(res.data.code == 1){
          var personalTelephone = res.data.personalTelephone;
          that.setData({
            phone:personalTelephone
          })
        }
        else{
          wx.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000,
            mask: true
          })
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  querySupplierSettingVipFunction: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/querySupplierSettingVipFunction',
      success: function (res) {
        var configEntity = res.data.configEntity;
        if (configEntity != null) {
          var openCardFlag = configEntity.openCard;
          if (openCardFlag == 0) {
            that.setData({
              cardType: configEntity.openType
            })
          } else {
            wx.showToast({
              title: "商家未开通此权限",
              icon: 'none',
              duration: 1000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  app.turnBack();
                }, 1000);
              }
            })
          }
        }
      }
    })
  },
  nameBindInput: function (e) {
    this.setData({
      name: e.detail.value
    });
  },
  sexBindChange: function (e) {
    this.setData({
      sex: e.detail.value
    });
  },
  cardLevelBindChange: function (e) {
    this.setData({
      cardLevel: e.detail.value
    });
  },
  /**
   * 会员申请开新卡
   */
  createNewVipCardInfoBindTap: function () {
    var that = this;
    var odbtoken = wx.getStorageSync('odbtoken');
    var logintoken = wx.getStorageSync('loginToken');
    var name = that.data.name.replace(/\s+/g, '');
    if (name.length == 0) {
      wx.showToast({
        title: '用户姓名不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    /*var phone = that.data.phone.replace(/\s+/g, '');
    if (phone.length == 0) {
      wx.showToast({
        title: '用户手机号码不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }*/
    var sex = that.data.sex.replace(/\s+/g, '');
    if (sex.length == 0) {
      wx.showToast({
        title: '性别不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.showLoading({
      title: '正在处理，请稍后',
      mask: true
    })
    var cardLevel = that.data.cardType;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "name": name,
        /*"phone": phone,*/
        "sex": sex,
        "cardLevel": cardLevel,
        "scene": that.data.scene,
        "odbtoken":odbtoken,
        "loginToken":logintoken

      },
      url: app.projectName + '/vipCard/createNewVipCardInfo',
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var returnMessage = res.data.returnMessage;
        if (flag) {
          //that.checkReward(3)
          wx.showToast({
            title: "开卡成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 2000);
            }
          })
        } else {
          wx.showToast({
            title: returnMessage,
            icon: 'none',
            duration: 2000,
            mask: true
          })
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  //查询是否有奖励
  checkReward: function (eventType) {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'config/loadConfig',
      showLoading: false,
      data: {
        eventType: eventType,
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName: app.getLoginName()
      },
      success: (res) => {
        if (res.length > 0) {
          that.setData({
            rewardList: res
          })
          that.reward();
        } else {
          wx.showToast({
            title: "开卡成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 2000);
            }
          })
        }

      }
    })
  },
  //调用奖励弹窗
  reward: function () {
    var that = this;
    that.setData({
      which: "reward"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
})