page {
  background: #f5f5f5;
  color:#525252;
  font-size:28rpx;
}
/*swtich整体大小*/
.wx-switch-input {
  width: 92rpx !important;
  height: 50rpx !important;
}

/*白色样式（false的样式）*/
.wx-switch-input::before {
  width: 90rpx !important;
  height: 46rpx !important;
}

/*绿色样式（true的样式）*/
.wx-switch-input::after {
  width: 50rpx !important;
  height: 46rpx !important;
}

/*swtich样式end*/
.inputWrap,.smsWrap{
  margin-top:8rpx;
}
.inputWrap input{
  border:1px solid #ddd;height:70rpx;
  padding:0 16rpx;
}
.smsWrap input{
  float:left;
  height:70rpx;
  border:1px solid #ddd;
  width:60%;
  display:inline-block;
  padding:0 16rpx;
}
.smsSend{
  float:right;
  width:30%;
  display:inline-block;
  height:70rpx;
  background:#FF7E00;
  text-align:center;
  line-height:70rpx;
  color:#fff;
  border-radius:8rpx;
}
.packWrap{
  font-size:28rpx;
  line-height:80rpx;
  background:#fff;
  margin-top:20rpx;
  padding:0 30rpx;
}
.historyWrap .exchangeConfirm{
  border-radius:70rpx;
  text-align:center;
  left:30rpx;
  right:30rpx;
  margin:0 30rpx;
  height:80rpx;
  line-height:80rpx;
  background:#FF7E00;
  color:#fff;
}
.giftDetail{
  margin-top:20rpx;
}