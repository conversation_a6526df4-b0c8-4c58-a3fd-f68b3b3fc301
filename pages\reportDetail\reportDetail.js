// pages/reportDetail/reportDetail.js
var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentPage: 1,
    pageSize: 10,
    showAccountList: [],
    searchLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.queryGeneralUserBill();
  },
  queryGeneralUserBill: function() {
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/consumption/queryGeneralUserBill',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "currentPage": that.data.currentPage,
        "pageSize": that.data.pageSize
      },
      success: function(res) {
        wx.hideLoading();
        var accountList = res.data.accountList;
        var oldaccountList = that.data.showAccountList;
        if (accountList != null && accountList.length > 0) {
          accountList = oldaccountList.concat(accountList);
          that.setData({
            searchLoading: true
          });
        } else {
          accountList = oldaccountList;
          that.setData({
            searchLoading: false
          });
        }
        that.setData({
          showAccountList: accountList
        });
      }
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        currentPage: that.data.currentPage + 1
      });
      that.queryGeneralUserBill();
    }
  }
})