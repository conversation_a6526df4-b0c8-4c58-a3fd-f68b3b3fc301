<import src="../CouponTem/CouponTem.wxml" />
<block wx:if="{{loginFlag||isSendScoreFlag}}">
	<template is="coupon2" data="{{img2,img3,img4,storeCardList,isSendScoreFlag,sendScore}}" />
</block>
<!--券弹出层-->
<view class="pop_bg" hidden='{{putCardHidden}}'>
</view>
<view class="topWrap">
	<image src="{{defaultLogo}}">
	</image>
	<view>{{storeName}}</view>
</view>
<view style="text-align:center;font-size:30rpx;">您还未登录</view>
<view style="text-align:center;color:#666;font-size:26rpx;margin-top:20rpx;">请您先登录在去下单哦!</view>
<view style="width:600rpx;margin:0 auto;">	
	<block wx:if="{{loginAuthorType==2 || loginAuthorType==1}}">
		<button class='confirm_btn' style="background:#fff;border:1px solid #FF7E00;color:#FF7E00"
		bindtap='goToHome'>暂不登录</button>
		<button class='confirm_btn' open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">立即登录</button>
  </block>
  <block wx:else>
		<button class='confirm_btn' style="background:#fff;border:1px solid #FF7E00;color:#FF7E00"
		bindtap='goToHome'>暂不登录</button>
		<button class='confirm_btn' bind:tap="showLoginPanel">立即登录</button>
  </block>	
  
</view>
<!-- 手工登录 -->
<block wx:if="{{ showLogin === true }}">
  <view class="login_bg" bind:tap="hiddenLoginPanel"></view>
  <view class="loginCancel" bind:tap="hiddenLoginPanel">×</view>
  <view class="login_panel">
    <view class="loginLogo">
      <image src="{{defaultLogo}}"></image>
      <view>{{storeName}}</view>
    </view>
    <view class="inputLogin">
      <input type="number" value="{{ loginTel }}" bindinput="loginTelInput" maxlength="11" placeholder="输入手机号码"></input>
    </view>
    <view class="inputLogin">
      <input type="number" value="{{ loginCode }}" bindinput="loginTelCode" maxlength="4" placeholder="{{ '请输入验证码 '+captchaCode }}"></input>
    </view>
    <button class="butLogin" open-type="getUserInfo" bindgetuserinfo="submitLogin">登录</button>
  </view>
</block>
<!-- <block wx:else>
		<button class='confirm_btn' bindtap="getUserProfileBindTap">立即登录</button>
		<button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
			<image class="avatar" src="{{avatarUrl}}"></image>
		</button> 
	</block> -->
