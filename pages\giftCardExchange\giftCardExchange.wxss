page {
  background: #f5f5f5;
  color:#525252;
}
.giftWrap{
  font-size:28rpx;
  background:#fff;
  margin-top:20rpx;
  padding:30rpx;
}
.s_mark{
  color:#111;
  font-size:32rpx;
}
/*swtich整体大小*/
.wx-switch-input {
  width: 92rpx !important;
  height: 50rpx !important;
}

/*白色样式（false的样式）*/
.wx-switch-input::before {
  width: 90rpx !important;
  height: 46rpx !important;
}

/*绿色样式（true的样式）*/
.wx-switch-input::after {
  width: 50rpx !important;
  height: 46rpx !important;
}

/*swtich样式end*/
.inputWrap,.smsWrap{
  margin-top:8rpx;
}
.inputWrap input{
  border:1px solid #ddd;height:70rpx;
  padding:0 16rpx;
}
.smsWrap input{
  float:left;
  height:70rpx;
  border:1px solid #ddd;
  width:60%;
  display:inline-block;
  padding:0 16rpx;
}
.smsSend{
  float:right;
  width:30%;
  display:inline-block;
  height:70rpx;
  background:#FF7E00;
  text-align:center;
  line-height:70rpx;
  color:#fff;
  border-radius:8rpx;
}
.packWrap{
  font-size:28rpx;
  line-height:80rpx;
  background:#fff;
  margin-top:20rpx;
  padding:0 30rpx;
}
.exchangeWrap{
  text-align:center;
  margin-top:30rpx;
  font-size:28rpx;
}
.exchangeWrap label{
  display:inline-block;
  border-radius:34rpx;
  border:1px solid #ddd;
  padding:4rpx 20rpx;
}
.nextWrap{
  font-size:28rpx;
  text-align:center;
}
.nextWrap view{
  color:#fff;
  border-radius:70rpx;
  text-align:center;
  left:30rpx;
  right:30rpx;
  margin:0 30rpx;
  height:80rpx;
  line-height:80rpx;
  position:fixed;
  bottom:160rpx;
  background:#FF7E00;
}
.historyWrap view{
  border:1px solid #FF7E00;
  color:#FF7E00;
  border-radius:70rpx;
  text-align:center;
  left:30rpx;
  right:30rpx;
  margin:0 30rpx;
  height:80rpx;
  line-height:80rpx;
  position:fixed;
  bottom:40rpx;
  background:#fff;
}