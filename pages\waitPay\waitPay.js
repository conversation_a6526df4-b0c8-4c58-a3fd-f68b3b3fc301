var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isFlag:false,
    array: [{
      mode: 'aspectFit',
    }],
    single: app.imageUrl + 'banner1.png',
    items: [{
      name: 'wxzf',
      value: '微信支付',
      checked: 'true'
    },
    {
      name: 'hdfk',
      value: '货到付款',
      checked: ''
    },
    ],
    receiveAddress: "",
    receiveRecordId:"",
    username: "",
    telephone: "",
    cart_id_arr: "",
    payType: "",
    goodsId: "",
    storeId: "",
    buyNum: "",
    payMethod: "wxzf",
    goodsList: [],
    exchangeCommodityList: [],
    skuId: "",
    groupBuyUserId: "",
    joinPromotion: 0,
    changePurchaseHidden: true,
    changeGoodsPriceTotal: 0,
    orderTotalMoney: 0,
    orderMoney: 0,
    orderComment: "", //订单备注
    casArray: ["第三方配送", "自配送", "自取"],
    index: 1,
    pickOrderStoreId: "", //自取店铺Id
    chooseStoreValue: "选择门店",
    DateArray: ['选择自取日期', '2018-12-10 星期一', '2018-12-11 星期二', '2018-12-12 星期三', '2018-12-13 星期四', '2018-12-14 星期五', '2018-12-15 星期六', '2018-12-16 星期天'],
    date_index: 1,
    TimeArray: ['选择自取时间', '08:00'],
    time_index: 0,
    timeValue: '',
    cardDesc: "未使用任何优惠券",
    cardNo: "",
    storeCard: [],
    cardHidden: true,
    orderDistributionPayShow: 0,
    isShowCardHidden: true,
    isTuanGou: "",
    vipCardBalance: 0, //会员卡余额
    isUseVipBalance: true, //是否使用会员卡余额支付
    shop_cart: app.imageUrl + 'font_checked.png',
    deliveryStoreId: "", //商家配送店铺Id
    deliveryStoreName: "", //商家配送店铺名称
    paymentType: 1, //付款方式 1：微信支付 2：余额支付w
    isSelectStore: false, //客户是否可以选择店铺配送
    moreGoodsHidden: true, //更多商品查看
    storeAddress: "", //店铺详细地址
    pickOrderUserName: "", //自取联系人姓名
    pickOrderContactNum: "", //自取联系人手机号码
    pickTimeArray: ["08:00 - 18:00"],
    wechat: app.imageUrl + "wechatPay.png",
    vipPay: app.imageUrl + "vipPay.png",
    wechatSwitch: true,
    noneAddressDesc: "请选择收货地址",
    addressLat: 0, //收货地址经度
    addressLon: 0, //收货地址纬度
    wechatisSwitch: true, //微信支付开关
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
    store_personal_more: app.imageUrl + 'accountManager/store_personal_more.png',
    vipCardPay: 2,
    downArrow: app.imageUrl + 'downArrow.png',
    upArrow: app.imageUrl + 'upArrow.png',
    paySwitch: true,   //确保只能点一次付款事件
    storePayArray: [],
    hdfk: app.imageUrl + "payFirst.png",
    rStoreList:[],
    payList: [{
      "payWay": "微信支付",
      "ishidden": true,
      "isCheck": false
    }, {
      "payWay": "银联支付",
      "ishidden": true,
      "isCheck": false
    }, {
      "payWay": "货到付款",
      "ishidden": true,
      "isCheck": false
    }, {
      "payWay": "账期结算",
      "ishidden": true,
      "isCheck": false
    }]
  },
  queryMoreGoodsBindTap: function () {
    this.setData({
      moreGoodsHidden: false
    })
  },
  hideQueryMoreGoodsBindTap: function () {
    this.setData({
      moreGoodsHidden: true
    })
  },
  queryS:function(){
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryLoginBreakStoreInfo',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var returnStoreList = res.data.storeList;
        that.setData({
          rStoreList: returnStoreList,
        })
      }
    })
  },
  useVipBalanceBindchange: function (e) {
    this.setData({
      isUseVipBalance: e.detail.value
    });
  },
  //修改是否微信支付
  wechatPayChange: function () {
    this.setData({
      wechatSwitch: !this.data.wechatSwitch
    });
  },
  pickOrderUserNameBindInput: function (e) {
    this.setData({
      pickOrderUserName: e.detail.value
    })
  },
  pickOrderContactNumBindInput: function (e) {
    this.setData({
      pickOrderContactNum: e.detail.value
    })
  },
  /*选择配送方式*/
  switchNav: function (e) {
    var currentTab = e.currentTarget.dataset.tab;
    var that = this;
    if (currentTab == 2) { //门店自提
      var totalMoney = parseFloat(that.data.orderCommodityTotalMoney) - parseFloat(that.data.orderDistributionPayShow);
      that.setData({
        orderDistributionPay: 0,
        orderTotalMoney: totalMoney.toFixed(2)
      });
    } else if (currentTab == 1) { //商家配送
      var totalMoney = parseFloat(that.data.orderCommodityTotalMoney) + parseFloat(that.data.orderDistributionPayShow)
      var chooseStoreValue = that.data.chooseStoreValue;
      if (chooseStoreValue == "") {
        chooseStoreValue = that.data.deliveryStoreName
      }
     
      that.setData({
        pickOrderStoreId: that.data.deliveryStoreId, //自取店铺Id
        chooseStoreValue: chooseStoreValue,
        orderDistributionPay: that.data.orderDistributionPayShow,
        orderTotalMoney: totalMoney.toFixed(2)
      });
    }else{ //物流配送
      var totalMoney = parseFloat(that.data.orderCommodityTotalMoney)+ parseFloat(that.data.orderDistributionPayShow);
      that.setData({
        orderDistributionPay:that.data.orderDistributionPayShow,
        orderTotalMoney: totalMoney.toFixed(2)
      });
    }
      that.setData({
        index: currentTab,
      });
    
  },
  /*picker change*/
  bindCasPickerChange: function (e) {
    this.setData({
      index: e.detail.value
    })
  },
  chooseStore: function (e) {
    var that = this;
    var select = e.currentTarget.dataset.select;
    if (select) {
      app.navigateToPage('/pages/supplierShop/supplierShop?index=' + that.data.index + "&addressLat=" + that.data.addressLat + "&addressLon=" + that.data.addressLon + "&settingDistance=" + that.data.distributionDistance);
    }
  },
  bindDateChange: function (e) {
    this.setData({
      date_index: e.detail.value
    })
  },
  bindChooseTimeChange: function (e) {
    this.setData({
      time_index: e.detail.value
    })
  },
  bindTimeChange: function (e) {
    this.setData({
      timeValue: e.detail.value
    })
  },
  useCouponBind: function () {
    var that = this;
    app.navigateToPage('/pages/useCoupon/useCoupon?orderTotalMoney=' + that.data.orderTotalMoney + "&goodsList=" + JSON.stringify(that.data.goodsList) + "&orderMoney=" + that.data.orderMoney);
  },
  onShow:function(){
    var that = this;
    if(!that.data.isFlag){
      that.dataInitial();
    }
    that.setData({
      isFlag:false
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.showLoading({
      title: '正在加载，请稍后',
      mask: true
    })
    this.setData({
      isFlag:true,
      payType: options.payType,
      cart_id_arr: decodeURIComponent(decodeURIComponent(options.cart_arr)),
      goodsId: options.goodsId,
      storeId: options.storeId,
      buyNum: options.buyNum,
      buyOmNum: options.buyOmNum,
      skuId: options.skuId,
      groupBuyUserId: options.groupBuyUserId,
      joinPromotion: options.joinPromotion,
      vipCardNo: options.vipCardNo,
      vipCardCode: options.vipCardCode,
      //recommendUserId: options.recommendUserId
      recommendUserId: app.recommendData.recommendUserId
    });
    this.dataInitial();
    this.formatMinute();
    this.queryS();
    //如果不是拼团，则可以使用优惠券
    var isTuanGou = options.isTuanGou;
    if (isTuanGou != 1) {
      this.setData({
        isTuanGou: isTuanGou
      })
    }
    var pickOrderStorage = wx.getStorageSync('pickOrderStorage');
    if (pickOrderStorage) {
      this.setData({
        pickOrderUserName: pickOrderStorage.pickOrderUserName,
        pickOrderContactNum: pickOrderStorage.pickOrderContactNum
      })
    }
  },
  getUserLocation: function () {
    let vm = this;
    vm.queryStoreInfo(0, 0);
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取门店地理位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             vm.queryStoreInfo(0, 0);

    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })
    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {

    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                   vm.queryStoreInfo(0, 0);
    //                 }
    //               }
    //             })
    //           }
    //         },
    //         fail: function () {

    //           vm.queryStoreInfo(0, 0);
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     } else {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     }
    //   }
    // })
  },
  getLocation: function () {
    var that = this;
  //   var qqmapsdk = new QQMapWX({
  //     key: '2HHBZ-I7HWW-WWARR-RFG7V-4DURZ-T2FUL' // 必填
  //   });
  //   wx.getLocation({
  //     type: 'gcj02',
  //     altitude: true,
  //     complete: function (res) {
  //       if (res.errMsg == "getLocation:ok") {
  //         var latitude = res.latitude;
  //         var longitude = res.longitude;
  //         qqmapsdk.reverseGeocoder({
  //           location: {
  //             latitude: latitude,
  //             longitude: longitude
  //           },
  //           success: function (res) { //成功后的回调
  //             var res = res.result;
  //             that.setData({
  //               localUserAddress: res.address,
  //               localUserCity: res.address_component.city
  //             })
  //             qqmapsdk.geocoder({
  //               //获取表单传入地址
  //               address: res.address, //地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
  //               success: function (res) { //成功后的回调
  //                 var res = res.result;
  //                 var latitude = res.location.lat;
  //                 var longitude = res.location.lng;
  //                 that.setData({
  //                   latitude: latitude,
  //                   longitude: longitude
  //                 });
  //                 that.queryStoreInfo(latitude, longitude);
  //               },
  //               fail: function (error) {
  //                 console.error(error);
  //               },
  //               complete: function (res) {
  //               }
  //             })
  //           },
  //           fail: function (error) {
  //             console.error(error);
  //           },
  //           complete: function (res) {
  //           }
  //         })
  //       } else {
  //         that.setData({
  //           latitude: 0,
  //           longitude: 0
  //         });
  //         that.queryStoreInfo(latitude, longitude);
  //       }
  //     }
  //   })
  },
  /**
   * 查询我的客户信息
   */
  queryStoreInfo: function (latitude, longitude) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryLoginBreakStoreInfo',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var returnStoreList = res.data.storeList;
        if (returnStoreList != null && returnStoreList.length > 0) {
          for (var i = 0; i < returnStoreList.length; i++) {
            returnStoreList[i].storeDistance = that.distance(latitude, longitude, returnStoreList[i].latitude, returnStoreList[i].longitude);
          }
          //按照距离排序
          returnStoreList = returnStoreList.sort((el1, el2) =>
            el1.storeDistance - el2.storeDistance
          );
        }
        that.setData({
          pickOrderStoreId: returnStoreList[0].id,
          chooseStoreValue: returnStoreList[0].storeName,
          storeAddress: returnStoreList[0].province + returnStoreList[0].city + returnStoreList[0].area + returnStoreList[0].storeAddress
        })
        console.log('---------------'+storeAddress)
      }
    })
  },
  /**
* 由经纬度计算两点之间的距离，la为latitude缩写，lo为longitude
* @param {*} 第一个坐标点的纬度 
* @param {*} 第一个坐标点的经度 
* @param {*} 第二个坐标点的纬度 
* @param {*} 第二个坐标点的经度 
* @return (int)s   返回距离(单位千米或公里)
*/
  distance: function (la1, lo1, la2, lo2) {
    var La1 = la1 * Math.PI / 180.0;
    var La2 = la2 * Math.PI / 180.0;
    var La3 = La1 - La2;
    var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    s = s.toFixed(2);
    return s;
  },
  storeNameBindInput: function (e) {
    this.setData({
      storeName: e.detail.value
    })
  },
  /*卡券获取所有优惠券*/
  queryUseCardByStoreId: function (orderTotalMoney, goodsList) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryUseCardByStoreId',
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getUserId(),
        "orderTotalMoney": orderTotalMoney,
        "goodsList": JSON.stringify(goodsList),
        "companyId": app.getExtCompanyId(),
      },
      success: function (res) {
        var userCard = res.data.userCard;
        if (userCard != null && userCard.length > 0) {
          for (var i = 0; i < userCard.length; i++) {
            userCard[i]['ruleSwitch'] = false;
          }
          that.setData({
            storeCard: userCard,
            cardHidden: false
          });
        }
      }
    })
  },
  /**
   * 不可使用的券
   */
  noUserCardBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var storeCard = that.data.storeCard;
    app.showModal({
      content: storeCard[index].unavailableCause
    });
  },
  /**
   * 选择可用的券进行使用
   */
  goToUserCardBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var storeCard = that.data.storeCard;
    var cardDesc = "";
    var totalMoney = 0;
    //当卡券类型为2的时候为折扣券，其它的为优惠券
    if (storeCard[index].cardType >= 4 && storeCard[index].cardType <= 6) {
      cardDesc = storeCard[index].discountAmount + "折";
      totalMoney = (that.data.orderMoney * (storeCard[index].discountAmount / 10)).toFixed(2);
    } else {
      cardDesc = "优惠" + storeCard[index].discountAmount + "元";
      totalMoney = (that.data.orderMoney - storeCard[index].discountAmount).toFixed(2);
    }
    that.setData({
      cardDesc: cardDesc,
      cardMoney: storeCard[index].discountAmount,
      cardType: storeCard[index].cardType,
      cardNo: storeCard[index].cardNo,
      cardHidden: true
    });
    that.calculationOrderTotal();
  },
  cardHiddenBindTap: function () {
    this.setData({
      cardHidden: true
    });
  },
  dateInit: function (selfTaking) {
    var dateArry = ['选择自取日期'];
    if (selfTaking == undefined || selfTaking.length == 0) {
      selfTaking = 0;
    }
    for (var i = selfTaking; i < 7 + selfTaking; i++) {
      var dateObj = this.dateLater(this.getCurrentMonthFirst(), i);
      dateArry.push(dateObj.year + "-" + dateObj.month + "-" + dateObj.day + " " + dateObj.week)
    }
    this.setData({
      DateArray: dateArry
    });
  },
  dateLater: function (dates, later) {
    let dateObj = {};
    let show_day = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六');
    let date = new Date(dates);
    date.setDate(date.getDate() + later);
    let day = date.getDay();
    dateObj.year = date.getFullYear();
    dateObj.month = ((date.getMonth() + 1) < 10 ? ("0" + (date.getMonth() + 1)) : date.getMonth() + 1);
    dateObj.day = (date.getDate() < 10 ? ("0" + date.getDate()) : date.getDate());
    dateObj.week = show_day[day];
    return dateObj;
  },
  /**
   * 获取当前时间
   **/
  formatMinute: function () {
    var that = this;
    var date = new Date();
    date.setTime(date.getTime() + 60 * 60 * 1000);
    var timeValue = date.getMinutes();
    var hourValue = date.getHours();
    if (timeValue.toString().length < 2) {
      timeValue = "0" + timeValue;
    }
    that.setData({
      timeValue: hourValue + ":" + timeValue,
    })
  },
  /**
   * 
   */
  getCurrentMonthFirst: function () {
    var date = new Date();
    var todate = date.getFullYear() + "-" + ((date.getMonth() + 1) < 10 ? ("0" + (date.getMonth() + 1)) : date.getMonth() + 1) + "-" + (date.getDate() < 10 ? ("0" + date.getDate()) : date.getDate());
    return todate;
  },
  dataInitial: function () {
    var that = this;
    console.log(that.data.receiveAddress+"///");
    var visitUrl = "";
    //从购物车预生成订单
    if (that.data.payType == 1) {
      visitUrl = '/applet/buyOrder/pregenerateRetailOrderNew';
    } else if (that.data.payType == 2) { //直接由商品生成订单
      visitUrl = '/applet/buyOrder/pregenerateCommodityRetailOrder';
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + visitUrl,
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "cart_id_arr": that.data.cart_id_arr,
        "goodsId": that.data.goodsId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "buyNum": that.data.buyNum,
        "buyOmNum": that.data.buyOmNum,
        "skuId": that.data.skuId,
        "groupBuyUserId": that.data.groupBuyUserId,
        "joinPromotion": that.data.joinPromotion,
        "vipCardNo": that.data.vipCardNo,
        "vipCardCode": that.data.vipCardCode,
        "recommendUserId": that.data.recommendUserId,
        "receiveAddress": that.data.receiveAddress
      },
      success: function (res) {
        wx.hideLoading();
        var orderList = res.data.orderList;
        var flag = res.data.flag;
        var message = res.data.message;
        var code = res.data.code;
        console.log("========"+message);
        console.log("========"+code);
        console.log("========"+code);
        if (flag) {
          if (orderList != null && orderList.length > 0) {
            if (that.data.isTuanGou != 1) {
              that.queryUseCardByStoreId(orderList[0].orderTotalMoney, orderList[0].orderDetail);
              that.setData({
                isShowCardHidden: false
              })
            }
            var pickOrderStoreId = res.data.distributionStoreId;
            var chooseStoreValue = res.data.distributionStoreName;
            var disWayList = res.data.disWayList;
            var disWayIndex = that.data.index;
            //console.log('disWay data:');
            //console.log(disWayList);
            var storeLat = res.data.storeLat;
            var storeLon = res.data.storeLon;
            var addressLat = res.data.addressLat;
            var addressLon = res.data.addressLon;
            var distributionDistance = res.data.distributionDistance;
            var username = that.data.username?that.data.username:orderList[0].receiverName;
            var telephone = that.data.telephone?that.data.telephone:orderList[0].receiveContactNum;
            var receiveAddress = that.data.receiveAddress?that.data.receiveAddress:orderList[0].receiveAddress;
            var receiveRecordId = orderList[0].receiveRecordId;
            var noneAddressDesc = "请选择收货地址";
            if (parseFloat(distributionDistance) > 0) {
              if (parseFloat(storeLat) > 0 && parseFloat(addressLat) > 0) {
                var returnDistance = that.distance(storeLat, storeLon, addressLat, addressLon);
                if (parseFloat(returnDistance) > parseFloat(distributionDistance)) {
                  username = "";
                  telephone = "";
                  receiveAddress = "";
                  noneAddressDesc = "默认地址不在配送范围";
                }
              }
            }
            var selectStoreInfoKey = wx.getStorageSync("selectStoreInfoKey");
            var storeAddress = "";
            if (selectStoreInfoKey != "" && selectStoreInfoKey != undefined) {
              pickOrderStoreId = selectStoreInfoKey.storeId;
              chooseStoreValue = selectStoreInfoKey.storeName;
              storeAddress = selectStoreInfoKey.storeAddress;
            } else {
              if (chooseStoreValue == null || chooseStoreValue.length == 0) {
                if (parseFloat(distributionDistance) == 0) {
                  that.getUserLocation();
                }
              }
            }
            //获取会员卡余额，判断是否显示使用会员卡余额
            var vipCardBalance = res.data.vipCardBalance;
            var isUseVipBalance = false;
            if (vipCardBalance > 0) {
              isUseVipBalance = true
            }
            that.setData({
              storeAddress: storeAddress,
              noneAddressDesc: noneAddressDesc,
              addressLat: res.data.addressLat,
              addressLon: res.data.addressLon,
              distributionDistance: res.data.distributionDistance,
              pickTimeArray: res.data.pickTimeArray == undefined ? that.data.pickTimeArray : res.data.pickTimeArray, //自取时间
              isUseVipBalance: res.data.vipCardPay == 1 ? isUseVipBalance : false,
              isSelectStore: res.data.isSelectStore, //客户是否可以自动选择配送方式
              deliveryStoreId: res.data.distributionStoreId,
              deliveryStoreName: res.data.distributionStoreName,
              index: disWayIndex,
              pickOrderStoreId: pickOrderStoreId, //自取店铺Id
              chooseStoreValue: chooseStoreValue,
              deliveryOne: disWayList.indexOf(1) != -1,
              deliveryTwo: disWayList.indexOf(2) != -1,
              deliveryThree: disWayList.indexOf(3) != -1,
              disWayList: disWayList, //配送方式
              vipCardBalance: vipCardBalance,
              goodsList: orderList[0].orderDetail,
              orderDistributionPay: orderList[0].orderDistributionPay, //运费
              orderDistributionPayShow: orderList[0].orderDistributionPay,
              orderTotalMoney: that.data.orderTotalMoney?that.data.orderTotalMoney:orderList[0].orderTotalMoney, //订单总金额
              orderMoney: orderList[0].orderTotalMoney,
              orderCommodityTotalMoney: orderList[0].orderCommodityTotalMoney, //不加运费的
              receiveAddress: receiveAddress, //默认收货地址
              username: username,
              telephone: telephone,
              exchangeCommodityList: orderList[0].exchangeCommodityList,
              userProfitTotalMoney: orderList[0].userProfitTotalMoney, //让利总额
              orderProfitDetailList: orderList[0].orderProfitDetailList, //让利明细
              vipCardPay: res.data.vipCardPay,   //是否开启会员卡支付 1开启，2不开启
              receiveRecordId:receiveRecordId
            });
            that.dateInit(res.data.selfTaking);
          }
        } else {
          if (code=='-99') {
            app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
            return;
          }else{
            wx.showToast({
              title: message,
              icon: 'none',
              duration: 1000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  app.turnBack();
                }, 1000);
              }
            })
          }          
          wx.removeStorageSync("odbtoken");
          wx.removeStorageSync("loginToken");
          wx.removeStorageSync("userSession");
          wx.removeStorageSync("isLoginSession");
          wx.removeStorageSync("");
          app.init_userOpenId();
        }
        var storePayArray = res.data.storePayArray;
        var payList = that.data.payList;
        var k = 0;
        if (storePayArray != null && storePayArray.length > 0) {
          for (var i = 0; i < storePayArray.length; i++) {
            for (var j = 0; j < payList.length; j++) {
              var pay = payList[j];
              if (storePayArray[i] == pay.payWay) {
                k++;
                if (k == 1) {
                  pay.isCheck = true;
                }
                pay.ishidden = false;
              }
            }
          }
        }
        that.setData({
          payList: payList
        });
      },
      fail: function () {
        wx.showToast({
          title: "生成订单失败",
          icon: 'none',
          duration: 1000,
          mask: true,
          success: function () {
            setTimeout(function () {
              app.turnBack();
            }, 1000);
          }
        })
      }
    })
  },
  /**
 * 选择支付方式
 */
  selectPayWayBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var payList = that.data.payList;
    for (var i = 0; i < payList.length; i++) {
      if (i == index) {
        payList[i].isCheck = true;
      } else {
        payList[i].isCheck = false;
      }
    }
    that.setData({
      payList: payList
    });
  },
  radioChange: function (e) {
    this.setData({
      payMethod: e.detail.value
    });
  },
  orderCommentBindInput: function (e) {
    this.setData({
      orderComment: e.detail.value
    });
  },
  selectAddressBindTap: function () {
    app.navigateToPage('/pages/addAddress/addAddress?pickOrderStoreId=' + this.data.pickOrderStoreId);
  },
  nowPay: function () {
    var that = this;
    if (that.data.paySwitch) {

      if (that.data.pickOrderStoreId == "" || that.data.pickOrderStoreId == 0) {
        app.showModal({
          content: '请选择门店'
        });
        return;
      }
      if (that.data.index == 2) {
        if (that.data.pickOrderUserName == "") {
          app.showModal({
            content: '请输入自取联系人'
          });
          return;
        }
        if (that.data.pickOrderContactNum == "") {
          app.showModal({
            content: '请输入自取联系电话'
          });
          return;
        }
        var pickOrderStorage = {
          pickOrderUserName: that.data.pickOrderUserName,
          pickOrderContactNum: that.data.pickOrderContactNum
        };
        app.setStorage({
          key: 'pickOrderStorage',
          data: pickOrderStorage
        });
        if (that.data.date_index == 0) {
          app.showModal({
            content: '选择自取日期'
          });
          return;
        }
        // if (that.data.time_index == 0) {
        //   app.showModal({
        //     content: '选择自取时间'
        //   });
        //   return;
        // }
      }
      if (that.data.index != 2 && (that.data.receiveAddress == "" || that.data.telephone == "" || that.data.username == "")) {
        app.showModal({
          content: '请选择收货地址'
        });
        return;
      }
      var payWay = "";
      var payList = that.data.payList;
      for (var i = 0; i < payList.length; i++) {
        if (payList[i].isCheck) {
          payWay = i.toString();
          break;
        }
      }
      if (payWay == "" && !that.data.isUseVipBalance) {
        app.showModal({
          content: '选择支付方式'
        });
        return;
      }
      that.setData({
        paySwitch: false
      })
      var pickTime = that.data.pickTimeArray[that.data.time_index];
      var pickExpectArriveTime = pickTime.substring(0, pickTime.indexOf("-")).replace(/\s+/g, '');
      var pickExpectArriveTimeEnd = pickTime.substring(pickTime.indexOf("-") + 1).replace(/\s+/g, '');

      var typeId = that.data.isUseVipBalance ? "2,3,4" : "2,3";

      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/newSubscribe/querySystemTemplateId',
        data: {
          "companyId": app.getExtCompanyId(),
          "typeId": typeId
        },
        success: function (res) {
          var templateArray = res.data.templateArray;
          if (templateArray != null && templateArray.length > 0) {
            wx.requestSubscribeMessage({
              tmplIds: templateArray,
              success(res) {
                wx.showLoading({
                  title: '正在提交，请稍后',
                  mask: true
                })
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded' // 默认值
                  },
                  method: "POST",
                  url: app.projectName + '/applet/buyOrder/payOrder',
                  data: {
                    "pickExpectArriveDate": that.data.DateArray[that.data.date_index],
                    /*"pickExpectArriveTime": that.data.TimeArray[that.data.time_index],*/
                    "pickExpectArriveTime": pickExpectArriveTime,
                    "pickExpectArriveTimeEnd": pickExpectArriveTimeEnd,
                    "distributionWay": that.data.index, //配送方式
                    "pickOrderStoreId": that.data.pickOrderStoreId,
                    "openId": app.getOpenId(),
                           "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
                    "loginId": app.getLoginId(),
                    "userRole": app.getUserRole(),
                    "cart_id_arr": that.data.cart_id_arr,
                    "goodsId": that.data.goodsId,
                    "storeId": app.getExtStoreId(),
                    "companyId": app.getExtCompanyId(),
                    "buyNum": that.data.buyNum,
                    "buyOmNum": that.data.buyOmNum,
                    "payMethod": that.data.payMethod,
                    "payChannel": that.data.payType,
                    "address": that.data.receiveAddress,
                    "username": that.data.username,
                    "telephone": that.data.telephone,
                    "orderComment": that.data.orderComment,
                    "skuId": that.data.skuId,
                    "groupBuyUserId": that.data.groupBuyUserId,
                    "joinPromotion": that.data.joinPromotion,
                    "exchangeCommodityListJSON": JSON.stringify(that.data.exchangeCommodityList),
                    "cardNo": that.data.cardNo,
                    "vipCardNo": that.data.vipCardNo,
                    "vipCardCode": that.data.vipCardCode,
                    "isUseVipBalance": that.data.vipCardPay == 1 ? that.data.isUseVipBalance : false,
                    "orderTotalMoney": that.data.orderTotalMoney,
                    "pickOrderUserName": that.data.pickOrderUserName,
                    "pickOrderContactNum": that.data.pickOrderContactNum,
                    "recommendUserId": that.data.recommendUserId,
                    "payWay": payWay,
                    "receiveRecordId":that.data.receiveRecordId
                  },
                  success: function (res) {
                    wx.hideLoading();
                    var flag = res.data.flag;
                    var orderPay = res.data.orderPay;
                    var param = res.data;
                    var newOrderBean = res.data.newOrderBean;
                    if (flag) {
                      if (orderPay == 0) { //需要支付
                        wx.requestPayment({
                          'timeStamp': param.timeStamp,
                          'nonceStr': param.nonceStr,
                          'package': param.package,
                          'signType': param.signType,
                          'paySign': param.paySign,
                          success: function (res) {
                            app.redirectToPage("/pages/successfulTrade/successfulTrade?newOrderBean=" + JSON.stringify(newOrderBean) + "&goodsList=" + JSON.stringify(that.data.goodsList) + "&exchangeList=" + JSON.stringify(that.data.exchangeCommodityList) + "&storeName=" + that.data.chooseStoreValue + "&indexWay=" + that.data.index + "&eventType=1" + "&minMoney=" + that.data.orderTotalMoney);
                          },
                          fail: function (res) {
                            if (res.errMsg === 'requestPayment:fail cancel') {
                              app.showModal({
                                content: '取消支付'
                              })
                            } else {
                              app.showModal({
                                content: '支付失败'
                              })
                            }
                            app.turnToPage("/pages/indexThree/indexThree");
                          }
                        })
                      } else { //不需要支付的
                        //生成订单成功
                        app.turnToPage("/pages/successfulTrade/successfulTrade?newOrderBean=" + JSON.stringify(newOrderBean) + "&goodsList=" + JSON.stringify(that.data.goodsList) + "&exchangeList=" + JSON.stringify(that.data.exchangeCommodityList) + "&storeName=" + that.data.chooseStoreValue + "&indexWay=" + that.data.index + "&eventType=1" + "&minMoney=" + that.data.orderTotalMoney);
                      }
                    } else {
                      wx.showToast({
                        title: res.data.returnMessage.length > 0 ? res.data.returnMessage : "生成订单失败",
                        icon: 'none',
                        duration: 1500,
                        mask: true
                      })
                      that.setData({
                        paySwitch: true
                      })
                    }
                  }
                })
              }, fail(e) {
              }
            })
          } else {
            wx.showLoading({
              title: '正在提交，请稍后!',
              mask: true
            })
            wx.request({
              header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
              },
              method: "POST",
              url: app.projectName + '/applet/buyOrder/payOrder',
              data: {
                "pickExpectArriveDate": that.data.DateArray[that.data.date_index],
                /*"pickExpectArriveTime": that.data.TimeArray[that.data.time_index],*/
                "pickExpectArriveTime": pickExpectArriveTime,
                "pickExpectArriveTimeEnd": pickExpectArriveTimeEnd,
                "distributionWay": that.data.index, //配送方式
                "pickOrderStoreId": that.data.pickOrderStoreId,
                "openId": app.getOpenId(),
                "odbtoken":app.getodbtoken(),
                "loginToken":app.getloginToken(),
                "loginId": app.getLoginId(),
                "userRole": app.getUserRole(),
                "cart_id_arr": that.data.cart_id_arr,
                "goodsId": that.data.goodsId,
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId(),
                "buyNum": that.data.buyNum,
                "buyOmNum": that.data.buyOmNum,
                "payMethod": that.data.payMethod,
                "payChannel": that.data.payType,
                "address": that.data.receiveAddress,
                "username": that.data.username,
                "telephone": that.data.telephone,
                "orderComment": that.data.orderComment,
                "skuId": that.data.skuId,
                "groupBuyUserId": that.data.groupBuyUserId,
                "joinPromotion": that.data.joinPromotion,
                "exchangeCommodityListJSON": JSON.stringify(that.data.exchangeCommodityList),
                "cardNo": that.data.cardNo,
                "vipCardNo": that.data.vipCardNo,
                "vipCardCode": that.data.vipCardCode,
                "isUseVipBalance": that.data.vipCardPay == 1 ? that.data.isUseVipBalance : false,
                "orderTotalMoney": that.data.orderTotalMoney,
                "pickOrderUserName": that.data.pickOrderUserName,
                "pickOrderContactNum": that.data.pickOrderContactNum,
                "recommendUserId": that.data.recommendUserId,
                "payWay": payWay,
                "receiveRecordId":that.data.receiveRecordId
              },
              success: function (res) {
                wx.hideLoading();
                var flag = res.data.flag;
                var orderPay = res.data.orderPay;
                var param = res.data;
                var newOrderBean = res.data.newOrderBean;
                if (flag) {
                  if (orderPay == 0) { //需要支付
                    wx.requestPayment({
                      'timeStamp': param.timeStamp,
                      'nonceStr': param.nonceStr,
                      'package': param.package,
                      'signType': param.signType,
                      'paySign': param.paySign,
                      success: function (res) {
                        app.redirectToPage("/pages/successfulTrade/successfulTrade?newOrderBean=" + JSON.stringify(newOrderBean) + "&goodsList=" + JSON.stringify(that.data.goodsList) + "&exchangeList=" + JSON.stringify(that.data.exchangeCommodityList) + "&storeName=" + that.data.chooseStoreValue + "&indexWay=" + that.data.index + "&eventType=1" + "&minMoney=" + that.data.orderTotalMoney);
                      },
                      fail: function (res) {
                        if (res.errMsg === 'requestPayment:fail cancel') {
                          app.showModal({
                            content: '取消支付'
                          })
                        } else {
                          app.showModal({
                            content: '支付失败'
                          })
                        }
                        app.turnToPage("/pages/indexThree/indexThree");
                      }
                    })
                  } else { //不需要支付的
                    //生成订单成功
                    app.turnToPage("/pages/successfulTrade/successfulTrade?newOrderBean=" + JSON.stringify(newOrderBean) + "&goodsList=" + JSON.stringify(that.data.goodsList) + "&exchangeList=" + JSON.stringify(that.data.exchangeCommodityList) + "&storeName=" + that.data.chooseStoreValue + "&indexWay=" + that.data.index + "&eventType=1" + "&minMoney=" + that.data.orderTotalMoney);
                  }
                } else {
                  wx.showToast({
                    title: res.data.returnMessage.length > 0 ? res.data.returnMessage : "生成订单失败",
                    icon: 'none',
                    duration: 1500,
                    mask: true
                  })
                  that.setData({
                    paySwitch: true
                  })
                }
              }
            })
          }
        }
      })
    }
  },
  /**
   * 换购商品
   */
  changePurchaseBindTap: function (e) {
    var that = this;
    var commodityId = e.currentTarget.dataset.id;
    var skuId = e.currentTarget.dataset.skuid;
    var changeTotalPrice = that.data.changeGoodsPriceTotal;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].commodityId == commodityId) {
        if (skuId != 0) {
          if (skuId == exchangeCommodityList[i].skuId) {
            exchangeCommodityList[i].select = true;
            exchangeCommodityList[i].actualExchangeNum = 1;
            changeTotalPrice += exchangeCommodityList[i].commoityExchangePrice;
          }
        } else {
          exchangeCommodityList[i].select = true;
          exchangeCommodityList[i].actualExchangeNum = 1;
          changeTotalPrice += exchangeCommodityList[i].commoityExchangePrice;
        }
      }
    }
    that.calculationChangeGoodsTotalPrice();
  },
  /**
   * 减少换购商品数量
   */
  goodsReduceBindTap: function (e) {
    var that = this;
    var commodityId = e.currentTarget.dataset.id;
    var skuId = e.currentTarget.dataset.skuid;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].commodityId == commodityId) {
        if (skuId != 0) {
          if (skuId == exchangeCommodityList[i].skuId) {
            exchangeCommodityList[i].actualExchangeNum--;
            exchangeCommodityList[i].select = exchangeCommodityList[i].actualExchangeNum == 0 ? false : true;
          }
        } else {
          exchangeCommodityList[i].actualExchangeNum--;
          exchangeCommodityList[i].select = exchangeCommodityList[i].actualExchangeNum == 0 ? false : true;
        }
      }
    }
    that.calculationChangeGoodsTotalPrice();
  },
  /**
   * 增加换购商品数量
   */
  goodsPlusBindTap: function (e) {
    var that = this;
    var commodityId = e.currentTarget.dataset.id;
    var skuId = e.currentTarget.dataset.skuid;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].commodityId == commodityId) {
        if (exchangeCommodityList[i].actualExchangeNum >= exchangeCommodityList[i].exchangeNum) {
          app.showModal({
            title: '提示',
            content: "已经超过换购数量"
          });
          return;
        } else {
          if (skuId != 0) {
            if (skuId == exchangeCommodityList[i].skuId) {
              exchangeCommodityList[i].actualExchangeNum++;
              exchangeCommodityList[i].select = true;
            }
          } else {
            exchangeCommodityList[i].actualExchangeNum++;
            exchangeCommodityList[i].select = true;
          }
        }
      }
    }
    that.calculationChangeGoodsTotalPrice();
  },
  /**
   * 计算换购价格
   */
  calculationChangeGoodsTotalPrice: function () {
    var that = this;
    var totalPrice = 0;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].select) {
        totalPrice += exchangeCommodityList[i].commoityExchangePrice * exchangeCommodityList[i].actualExchangeNum;
      }
    }
    that.setData({
      exchangeCommodityList: exchangeCommodityList,
      changeGoodsPriceTotal: totalPrice.toFixed(2)
    });
    that.calculationOrderTotal();
  },
  /**
   * 计算订单实际支付总价格
   */
  calculationOrderTotal: function () {

    var that = this;
    var orderTotal_money = 0.00;
    //获取订单价格
    var orderMoney = that.data.orderMoney;
    //获取换购商品价格
    var exchangeMoney = that.data.changeGoodsPriceTotal;
    orderTotal_money = parseFloat(orderMoney) + parseFloat(exchangeMoney);
    //获取优惠券价格
    var cardType = that.data.cardType;
    if (cardType != undefined) {
      if (cardType >= 4 && cardType <= 6) {
        orderTotal_money = ((orderTotal_money-parseFloat(that.data.orderDistributionPay)) * (that.data.cardMoney / 10)+parseFloat(that.data.orderDistributionPay)).toFixed(2);
      } else {
        orderTotal_money = (orderTotal_money - parseFloat(that.data.cardMoney)).toFixed(2);
      }
    }
    var currTab = that.data.index;
    if (currTab == 2) { //门店自提
      orderTotal_money = parseFloat(orderTotal_money) - parseFloat(that.data.orderDistributionPayShow);
      that.setData({
        orderTotalMoney: orderTotal_money < 0 ? 0 : parseFloat(orderTotal_money).toFixed(2)
      });
    } else {
      that.setData({
        orderTotalMoney: orderTotal_money < 0 ? 0 : parseFloat(orderTotal_money).toFixed(2)
      });
    }
    console.log('门店自提',orderTotal_money);
    
  },
  exchangeGoods: function () { //去换购
    var pagePath = '/pages/exchangeGoodsList/exchangeGoodsList?exchangeCommodityList=' + JSON.stringify(this.data.exchangeCommodityList);
    app.navigateToPage(pagePath);
  },
  wechatisSwitch: function () {
    this.setData({
      wechatisSwitch: !this.data.wechatisSwitch
    })
  },
  // 点击使用规则
  ruleSwitchFun: function (e) {
    var that = this
    var StoreStoreCardData = this.data.storeCard;
    var ruleSwitchData = StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch;
    if (ruleSwitchData == false) {
      if (typeof (StoreStoreCardData[e.currentTarget.dataset.idx].rule) == "undefined") {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/retailCoupon/queryCardUseRule',
          data: {
            "cardId": e.currentTarget.dataset.cardid,
            "storeId": app.getExtStoreId(),
          },
          success: function (res) {
            StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch = true;

            StoreStoreCardData[e.currentTarget.dataset.idx].rule = res.data.cardRule;
            that.setData({
              storeCard: StoreStoreCardData
            })
          }
        })
      } else {
        StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch = true;
        that.setData({
          storeCard: StoreStoreCardData
        })
      }

    } else {
      StoreStoreCardData[e.currentTarget.dataset.idx].ruleSwitch = false;
      that.setData({
        storeCard: StoreStoreCardData
      })
    }

  },
})