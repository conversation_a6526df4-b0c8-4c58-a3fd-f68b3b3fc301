var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    birthday: "",
    cardBean: [],
    userName: "",
    telephone: app.getTelephone(),
    sex: 1,
    password: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var cardBean = JSON.parse(options.cardBean);
    this.setData({
      cardBean: cardBean
    });
  },
  bindDateChange: function(e) {
    this.setData({
      birthday: e.detail.value
    })
  },
  radioChange: function(e) {
    this.setData({
      sex: e.detail.value
    })
  },
  userNameBindInput: function(e) {
    this.setData({
      userName: e.detail.value
    })
  },
  telephoneBindInput: function(e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  passwordBindInput: function(e) {
    this.setData({
      password: e.detail.value
    })
  },
  receiveVipCardBindTap: function() {
    var that = this;
    var userName = that.data.userName.replace(/\s+/g, '');
    var telephone = that.data.telephone.replace(/\s+/g, '');
    var sex = that.data.sex;
    var birthday = that.data.birthday.replace(/\s+/g, '');
    var password = that.data.password;
    if (userName == null || userName == "" || userName.length == 0) {
      wx.showToast({
        title: '请输入姓名',
        duration: 1500
      })
      return;
    }
    if (telephone == null || telephone == "" || telephone.length == 0) {
      wx.showToast({
        title: '请输入手机号码',
        duration: 1500
      })
      return;
    }
    if (password == null || password == "" || password.length == 0) {
      wx.showToast({
        title: '请输入支付密码',
        duration: 1500
      })
      return;
    }
    var cardBean = that.data.cardBean;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/vipCard/addUserVipCard',
      data: {
        "cardId": cardBean.id,
        "userType": 1,
        "userName": userName,
        "telephone": telephone,
        "sex": sex,
        "birthday": birthday,
        "password": password,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function(res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "领取成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function() {
              setTimeout(function() {
                wx.switchTab({
                  url: "/pages/accountManager/accountManager"
                });
              }, 1000);
            }
          })
        } else {
          app.showModal({
            title: '提示',
            content: "领取失败，稍后在试"
          });
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})