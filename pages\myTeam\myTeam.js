// pages/person_directAgent/person_directAgent.js

var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    direct_crown: app.imageUrl + "/direct_crown.png",
    more: app.imageUrl + "/moreInfo.png",
    chainStoreUserNumber: 0,
    fristLevelUserNumber: 0,
    secondLevelUserNumber: 0,
    inviteShow: true,
  },
  hiddeInvite: function() {
    var that = this;
    that.setData({
      inviteShow: true,
    })
  },
  levelAgentBind: function(e) {
    var that = this;
    var type = e.currentTarget.dataset.type;
    app.navigateToPage('/pages/person_agentDetail/person_agentDetail?type=' + type);

  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    this.queryDirectlyUnderUserCount();
  },
  /**
   * 查询我的上级
   */
  queryMySuperior: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/relevantTeam/queryMySuperior',
      success: function(res) {
        var agent = res.data.agent;
        that.setData({
          agent: agent,
          inviteShow: false,
        });
      }
    })
  },
  /**
   * 查询我的连锁店总数、直属一级店主人数、直属二级店主人数
   */
  queryDirectlyUnderUserCount: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/relevantTeam/queryDirectlyUnderUserCount',
      success: function(res) {
        var chainStoreUserNumber = res.data.chainStoreUserNumber;
        var fristLevelUserNumber = res.data.fristLevelUserNumber;
        var secondLevelUserNumber = res.data.secondLevelUserNumber;
        that.setData({
          chainStoreUserNumber: chainStoreUserNumber,
          fristLevelUserNumber: fristLevelUserNumber,
          secondLevelUserNumber: secondLevelUserNumber
        })
        wx.hideLoading();
      }
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})