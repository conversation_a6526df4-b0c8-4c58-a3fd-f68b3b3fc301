var app = getApp();
var TimeUtil = require('../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    accountValidQuota: 0,
    accountWincreaseQuota: 0,
    goMall: app.imageUrl + 'goMall.png',
    billType: 1,
    isFromBack: false,
    order_none: app.imageUrl + 'none_liushui.png',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#ff6600'
    })
    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          windowHeight: res.windowHeight * 2 - 600 + "rpx",
        })
      }
    })
    this.setData({
      headLogo: app.getHeadImage(),
      userName: app.getLoginName()
    })
    this.queryRetailUserAccount();
    this.queryRetailUserBill(1);
  },
  onShow: function () {
    if (this.data.isFromBack) {
      wx.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: '#ff6600'
      })
      wx.getSystemInfo({
        success: function (res) {
          that.setData({
            windowHeight: res.windowHeight * 2 - 600 + "rpx",
          })
        }
      })
      this.setData({
        headLogo: app.getHeadImage(),
        userName: app.getLoginName()
      })
      this.queryRetailUserAccount();
      this.queryRetailUserBill(1);
    } else {
      this.setData({
        isFromBack: true
      })
    }
  },
  /**
   * 查询零售线上积分
   */
  queryRetailUserAccount: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/queryRetailUserAccount',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "localStoreId": app.getExtStoreId()
      },
      success: function (res) {
        var accountBean = res.data.accountBean;
        if (accountBean != null) {
          that.setData({
            accountValidQuota: accountBean.accountValidQuota.toFixed(2),
            accountWincreaseQuota: accountBean.accountWincreaseQuota.toFixed(2)
          })
        }
      }
    })
  },
  goChange: function () {
    app.navigateToPage("/pages/scoreMall/scoreMall");
  },
  queryRetailUserBillBindTap: function (e) {
    var that = this;
    var type = e.currentTarget.dataset.type;
    that.setData({
      billType: type
    })
    that.queryRetailUserBill(type);
  },
  /**
   * 查询批发客户的积分流水
   */
  queryRetailUserBill: function (type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/queryRetailUserBill',
      data: {
        "journaIO": type == 1 ? true : false,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId()
      },
      success: function (res) {
        var billList = res.data.billList;
        if (billList != null && billList.length > 0) {
          for (var i = 0; i < new_orderList.length; i++) {
            billList[i].journaTime = TimeUtil.getSmpFormatDateByLong(billList[i].journaTime, true);
          }
        }
        that.setData({
          billList: billList
        })
      }
    })
  }
})