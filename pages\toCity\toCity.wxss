/* pages/toCity/toCity.wxss */
/* 定位 搜索 */

.location_box {
  height: 52rpx;
  position: fixed;
  /*top: 70rpx;*/
  left: 0;
  font-size: 28rpx;
  line-height: 26px;
  width: 100%;
  padding: 16rpx 0 0 0;
  z-index: 100;
  background: #fff;
}

.location_box text {
  line-height: 52rpx;
  height: 52rpx;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #fff;
  color: #fff;
  border-radius: 30rpx !important;
  padding: 0 2%;
  overflow: hidden;
}

.location_box icon {
  float: left;
  position: absolute;
  top: 32rpx;
  left: 22%;
  z-index: 10;
  line-height: 34px;
}

.location_box input {
  line-height: 62rpx;
  height: 62rpx;
  width: 50%;
  display: block;
  float: left;
  /*margin: 0 6%;*/
  margin-left: 4%;
  background: #ededed;
  color: #272727;
  border-radius: 14rpx;
  padding-left: 8%;
  padding-right: 4%;
}
.nearAdd{
  font-size:30rpx;
}
.nearAdd view{
  font-size:28rpx;
  padding:20rpx;
  padding-bottom:10rpx;
}
.nearAdd .addDetail{
  font-size:26rpx;
  padding:0rpx;
  color:#666;
  padding-left:20rpx;
}