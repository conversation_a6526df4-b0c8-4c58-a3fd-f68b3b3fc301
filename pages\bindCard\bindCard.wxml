<!--<label class="current_account">
  当前登录账号
</label>-->

<view class='account_detail'>
  <label>

    <image src="{{name}}" style="width:40rpx;" mode="widthFix"></image>
    <input placeholder="请输入持卡人姓名" bindinput='userNameBindInput' value='{{userName}}' type='text'></input>
  </label>
  <label>
    <image src="{{card}}" style="width:40rpx;" mode="widthFix"></image>
    <input placeholder="请输入银行卡卡号" bindinput='bankCardBindInput' value='{{cardNo}}' type='number'></input>
  </label>
  <label>
    <image src="{{message}}" style="width:40rpx;" mode="widthFix"></image>
    <input placeholder="请选择开户银行" disabled='true' value='{{bankName}}' type='text'></input>
  </label>
  <label>
    <image src="{{location}}" style="width:40rpx;" mode="widthFix"></image>
    <picker mode="region" bindchange="bindRegionChange" bindtap='bindRegionBindTap' value="{{pac}}"
      custom-item="{{customItem}}">
      <view class="picker">
        <block wx:for="{{pac}}" wx:for-item="region" wx:for-index="addressIndex" wx:key="">
          {{region}}
        </block>
      </view>
    </picker>
  </label>
  <label>
    <image src="{{location}}" style="width:40rpx;" mode="widthFix"></image>
    <picker bindchange="bindPickerChange" value="{{index}}" range="{{branchNameList}}">
      <view class="picker" style="height:80rpx;overflow:hidden;">
        \t{{branch}}
      </view>
    </picker>
  </label>
  <label>
    <image src="{{phone}}" style="width:40rpx;" mode="widthFix"></image>
    <input placeholder="手机号" placeholder-style="color:#717071;" maxlength='11' bindinput='telephoneBindInput'
      value='{{telephone}}' type='number'></input>
  </label>
  <view class='add_address'>
    <view bindtap='boundBankInfo'>
      {{submitTitle}}
    </view>
  </view>
  <view class='tip_box'>
    <text class='tip'>本账号信息用于每月结算和提成。</text>
    <view class='tip'>您提交的信息准确与否，将直接影响到资金的到账记录，如果您不知道开户行信息，可直接致电银行卡背面的客服电话进行查询。</view>

  </view>

</view>