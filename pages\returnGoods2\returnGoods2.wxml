<!--进度条 下一步-->
<!-- <view class='top_nav'> -->
<!--<image class='tips_pic' src='{{return_apply}}' mode='widthFix'></image>-->
<!-- <view class='next_btn' bindtap='nextToPageBindTap'>下一步</view>
</view> -->

<!--选择商品-->

<!--合计-->
<!-- <view class='all_box'>
	<label class="check-box pull-left checkBox {{allCheck?'check-box2':''}}" bindtap='allCheckBindTap'>
		<image src='{{zc_app_fontschecked}}'></image>
	</label>
	<label>全选</label>
	<label class="allBox">合计:
		<text>¥{{totalMoney}}</text>件
	</label>
</view> -->

<view style="margin-bottom:90rpx;">
	<view style=";margin-bottom:20rpx;">
		<block wx:key="unique" wx:for="{{goodsList}}" wx:for-item="goods">
			<!--单个商品-->
			<view class='single_goods'>
				<label class="check-box pull-left {{goods.check?'check-box2':''}}" bindtap='goodsSelectBindTap' data-index="{{ index }}">
					<image src='{{zc_app_fontschecked}}'></image>
				</label>
				<image class='goods_pic' src='{{goods.commodityMainPic}}'></image>
				<view class='goods_box'>
					<label class='goods_name'>{{goods.commodityName}}</label>
					<view class='goodsBox'>
						<label class='goods_price'>¥{{goods.commoditySendOtPrice}}</label>
						<view class='goods_num'>
							<label class='minus' bindtap='minusGoodsBindTap' data-index="{{ index }}">-</label>
							<input type='text' disabled='true' value='{{goods.commodityRejectedOtNum}}'></input>
							<label class='adds' bindtap='plusGoodsBindTap' data-index="{{ index }}">+</label>
						</view>
					</view>
				</view>
			</view>
		</block>
	</view>
	<view class="reason">
		<view style="height:36px;line-height:36px;width:160rpx;">退货原因:</view>
		<view class="section" style='background:#fff;width:520rpx;'>
			<picker bindchange="bindPickerChange" value="{{index}}" range="{{array}}">
				<view class="picker" style='width:92%;padding:0 2%;margin:0 2%;height:36px; line-height:36px; border:1px solid #ececec; border-radius:5px;position:relative;'>
					{{array[index]}}
					<text style='position:absolute;top:10%; right:10px;transform:rotate(180deg);'>︿</text>
				</view>
			</picker>
		</view>
	</view>
	<view style='background:#fff;width:100%; height:110px;display:flex;padding:10px;'>
		<view style="height:36px;line-height:36px;width:160rpx;">退货说明：</view>
		<view style="width:520rpx;padding-top:18rpx;">
			<textarea style='width:520rpx;height:96px; line-height:30px; border-radius:5px;position:relative;' placeholder='为了尽快处理你的售后请求，请填写详细描述~' bindinput='returnGoodsDescBindInput'></textarea>
		</view>
	</view>
</view>
<view class="submitBtn" bindtap='nextToPageBindTap'>提交</view>