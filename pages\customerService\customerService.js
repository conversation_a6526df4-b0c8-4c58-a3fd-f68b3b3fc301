var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    return_goods: app.imageUrl + 'accountManager/return_goods.png',
    customer_service: app.imageUrl + 'accountManager/customer_service.png'
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },
  returnGoodsBindTap: function () {
    app.navigateToPage('/pages/applayReturnGoods/applayReturnGoods');
    //app.navigateToPage('/pages/returnGoods2/returnGoods2');
  },
  customerServiceBindTap: function () {
    app.navigateToPage('/pages/aftersaleList/aftersaleList');
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.storeName,
      path: '/pages/register/register?telephone=' + app.getLoginAccount(),
      imageUrl: app.shareImageUrl,
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})