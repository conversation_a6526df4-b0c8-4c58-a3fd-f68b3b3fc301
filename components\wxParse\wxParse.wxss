
/**
 * author: <PERSON> (微信小程序开发工程师)
 * organization: WeAppDev(微信小程序开发论坛)(http://weappdev.com)
 *               垂直微信小程序开发交流社区
 * 
 * github地址: https://github.com/icindy/wxParse
 * 
 * for: 微信小程序富文本解析
 * detail : http://weappdev.com/t/wxparse-alpha0-1-html-markdown/184
 */

.wxParse{
    margin: 0 5px;
    font-family: Helvetica,sans-serif;
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
}
view{
    word-break:break-all; overflow:auto;
}
.wxParse-inline{
    display: inline;
    margin: 0;
    padding: 0;
}
/*//标题 */
.wxParse-div{margin: 0;padding: 0;}
.wxParse-h1{ font-size:2em; margin: .67em 0 }
.wxParse-h2{ font-size:1.5em; margin: .75em 0 }
.wxParse-h3{ font-size:1.17em; margin: .83em 0 }
.wxParse-h4{ margin: 1.12em 0}
.wxParse-h5 { font-size:.83em; margin: 1.5em 0 }
.wxParse-h6{ font-size:.75em; margin: 1.67em 0 }

.wxParse-h1 {
  font-size: 18px;
  font-weight: 400;
  margin-bottom: .9em;
}
.wxParse-h2 {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: .34em;
}
.wxParse-h3 {
  font-weight: 400;
  font-size: 15px;
  margin-bottom: .34em;
}
.wxParse-h4 {
  font-weight: 400;
  font-size: 14px;
  margin-bottom: .24em;
}
.wxParse-h5 {
  font-weight: 400;
  font-size: 13px;
  margin-bottom: .14em;
}
.wxParse-h6 {
  font-weight: 400;
  font-size: 12px;
  margin-bottom: .04em;
}

.wxParse-h1, .wxParse-h2, .wxParse-h3, .wxParse-h4, .wxParse-h5, .wxParse-h6, .wxParse-b, .wxParse-strong  { font-weight: bolder }

.wxParse-i,.wxParse-cite,.wxParse-em,.wxParse-var,.wxParse-address{font-style:italic}
.wxParse-pre,.wxParse-tt,.wxParse-code,.wxParse-kbd,.wxParse-samp{font-family:monospace}
.wxParse-pre{white-space:pre}
.wxParse-big{font-size:1.17em}
.wxParse-small,.wxParse-sub,.wxParse-sup{font-size:.83em}
.wxParse-sub{vertical-align:sub}
.wxParse-sup{vertical-align:super}
.wxParse-s,.wxParse-strike,.wxParse-del{text-decoration:line-through}
/*wxparse-自定义个性化的css样式*/
/*增加video的css样式*/
.wxParse-strong,.wxParse-s{display: inline}
.wxParse-a{
    color: deepskyblue;
    word-break:break-all;
    overflow:auto;
}

.wxParse-video{
    text-align: center;
    margin: 10px 0;
}

.wxParse-video-video{
    width:100%;
}

.wxParse-img{
    /*background-color: #efefef;*/
    overflow: hidden;
}

.wxParse-blockquote {
    margin: 0;
    padding:10px 0 10px 5px;
    font-family:Courier, Calibri,"宋体";
    background:#f5f5f5;
    border-left: 3px solid #dbdbdb;
}

.wxParse-code,.wxParse-wxxxcode-style{
    display: inline;
    background:#f5f5f5;
}
.wxParse-ul{
    margin: 20rpx 10rpx;
}

.wxParse-li,.wxParse-li-inner{
    display: flex;
    align-items: baseline;
    margin: 10rpx 0;
}
.wxParse-li-text{
    
    align-items: center;
    line-height: 20px;
}

.wxParse-li-circle{
    display: inline-flex;
    width: 5px;
    height: 5px;
    background-color: #333;
    margin-right: 5px;
}

.wxParse-li-square{
    display: inline-flex;
    width: 10rpx;
    height: 10rpx;
    background-color: #333;
    margin-right: 5px;
}
.wxParse-li-ring{
    display: inline-flex;
    width: 10rpx;
    height: 10rpx;
    border: 2rpx solid #333;
    border-radius: 50%;
    background-color: #fff;
    margin-right: 5px;
}

/*.wxParse-table{
    width: 100%;
    height: 400px;
}
.wxParse-thead,.wxParse-tfoot,.wxParse-tr{
    display: flex;
    flex-direction: row;
}
.wxParse-th,.wxParse-td{
    display: flex;
    width: 580px;
    overflow: auto;
}*/

.wxParse-u {
  text-decoration: underline;
}
.wxParse-hide{
    display: none;
}
.WxEmojiView{
    align-items: center;
}
.wxEmoji{
    width: 16px;
    height:16px;
}
.wxParse-tr{
	display: flex;
	border-right:1px solid #e0e0e0;
	border-bottom:1px solid #e0e0e0;
	border-top:1px solid #e0e0e0;
}
.wxParse-th,
.wxParse-td{
	flex:1;
	padding:5px;
	font-size:28rpx;
	border-left:1px solid #e0e0e0;
	word-break: break-all;
}
.wxParse-td:last{
	border-top:1px solid #e0e0e0;
}
.wxParse-th{
	background:#f0f0f0;
	border-top:1px solid #e0e0e0;
}
.wxParse-del{
    display: inline;
}
.wxParse-figure {
  overflow: hidden;
}
