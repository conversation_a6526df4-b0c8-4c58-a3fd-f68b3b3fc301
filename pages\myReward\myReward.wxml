<wxs src="../../wxs/subutil.wxs" module="tools" />
<view class="reward" >
	<view class="reward_one" >
		奖励详情
	</view>
	<block wx:for="{{resData}}" wx:for-item="oneData" wx:wx:key="index">

		<view class="reward_two" style="background: linear-gradient(90deg, #FEFAFA 0%, #FEEEA8 100%);" wx:if="{{oneData.activityPrizeParam.activityPrizeType==2}}" bindtap="toCoupons">
			<view class="reward_three" >
				<view class="reward_dashed">
					<image class="reward_img" src="{{coupon}}"  mode="widthFix"></image>
				</view>
			</view>
			<view style="width:65%;">
				<view class="reward_five">
					优惠券
				</view>
				<view class="reward_six">
					{{oneData.journaExecuteTime}}
				</view>
			</view>
			<view class="reward_seven" style="width:35%;">
				{{rewardState[oneData.activityPrizeStatus]}}
			</view>
		</view>
		<view class="reward_two" wx:if="{{oneData.activityPrizeParam.activityPrizeType==3}}" style="background: linear-gradient(90deg, #FEFAFA 0%, #FEE6E6 100%);" bindtap="toVipCard">
			<view style="display: flex;justify-content: center;align-items: center;width:76rpx;border:0px solid red">
				<view class="reward_dashed">
					<image class="reward_img" src="{{integral}}"  mode="widthFix"></image>
				</view>
			</view>
			<view style="width:65%;">
				<view class="reward_five">
					{{oneData.activityPrizeParam.maxPrizeNum}}积分
				</view>
				<view class="reward_six">
					{{tools.sub.formatMillToS(oneData.journaExecuteTime)}}
				</view>
			</view>
			<view class="reward_seven" style="width:35%;">
				{{rewardState[oneData.activityPrizeStatus]}}
			</view>
		</view>
		<view class="reward_two" wx:if="{{oneData.activityPrizeParam.activityPrizeType==4}}" style="background: linear-gradient(90deg, #FEFAFA 0%, #EEFDED 100%);">
			<view style="display: flex;justify-content: center;align-items: center;width:76rpx;border:0px solid red">
				<view class="reward_dashed">
					<image class="reward_img" src="{{game}}"  mode="widthFix"></image>
				</view>
			</view>
			<view style="width:65%;">
				<view class="reward_five">
					抽奖「大转盘」
				</view>
				<view class="reward_six">
					{{tools.sub.formatMillToS(oneData.journaExecuteTime)}}
				</view>
			</view>
			<view class="reward_seven" style="width:35%;">
				{{rewardState[oneData.activityPrizeStatus]}}
			</view>
		</view>
	</block>
	
	<load-more id="loadMoreView" bindloadMoreListener='loadMoreListener' bindclickLoadMore='clickLoadMore'></load-more>
</view>