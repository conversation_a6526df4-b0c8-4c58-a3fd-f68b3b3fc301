// 定义表情和图片的对应关系
const emojis = {
  '😂': '😂',
  '😓': '😓',
  '💣': '💣',
  '💪': '💪',
  '💩': '💩',
  '😷': '😷',
  '🙏': '🙏',
  '👍': '👍',
  '👎': '👎',
  '⛄': '⛄',
  '😅': '😅',
  '😘': '😘',
  '😃': '😃',
  '😭': '😭',
  '😱': '😱',
  '🙈': '🙈',
  '🎎': '🎎',
  '🚇': '🚇',
  '💏': '💏',
  '🍰': '🍰',
  '🍪': '🍪',
  '❤': '❤',
  '💔': '💔',
  '🚲': '🚲',
  '➕': '➕',
  '➖': '➖',
  '✖': '✖',
  '➗': '➗',
  '👑': '👑',
  '🙌': '🙌',
  '😹': '😹',
  '😡': '😡',
  '🐼': '🐼',
  '🐴': '🐴',
  '🐭': '🐭',
  '🐲': '🐲',
  '🐒': '🐒',
  '🐍': '🐍',
  '🐍': '🐍',
  '🐔': '🐔'
}

// 将表情文字转为图片
const emojiToPath = (i) => `${emojis[i]}`
// 将聊天内容转为一个文字和图片混合的列表
const textToEmoji = (s) => {
  // 定义正则对象
  const r = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig;

  const a = []; // 定义返回的数组
  let t = null; // 是否匹配到表情符号
  let i = 0; // 下次匹配的序号

  while (true) {
    // 正则实例对象的exec方法，用来返回匹配结果。
    // 如果发现匹配，就返回一个数组，成员是匹配成功的子字符串，否则返回null。
    t = r.exec(s);

    if (!t) {
      // 如果匹配不成功，
      // 且序号i至结尾字符不为空，为空则忽略
      // 添加文字类型到数组a，并退出循环
      s.slice(i) && a.push({
        msgType: 'text',
        msgCont: s.slice(i)
      });
      break; // 退出循环
    }

    // 匹配到了，
    // 且 本次匹配的起始序号 与 表情符号第一个字符序号不等
    // 如果相等，则说明表情前面是一个空字符串，需要忽略
    (i !== t.index) && a.push({
      msgType: 'text',
      msgCont: s.slice(i, t.index)
    });

    // 匹配了类似[*]的表情符号
    // 还需要判断是否定义了此表情的图片
    if (emojis[t[0]]) {
      // 定义了表情图片，添加表情类型到数组a
      a.push({
        msgType: 'emoji',
        msgCont: t[0],
        msgImage: emojiToPath(t[0])
      });
    } else {
      // 此表情没有图片与之对应，当做文字类型添加到数组a
      a.push({
        msgType: 'text',
        msgCont: t[0]
      });
    }

    i = t.index + t[0].length; // 更新下一次匹配开始的序号
  }

  return a;
}

// 将聊天内容转为一个文字和图片混合的列表
// 另一个更容易理解的方法
const textToEmoji2 = (s) => {
  // 定义正则对象
  const r = /\[[^\[\]]+?\]/g;

  const textArr = s.split(r); // 字符串分割，返回一个数组
  const emojiArr = s.match(r); // 返回一个数组，成员是所有匹配的子字符串

  const a = []; // 定义返回的数组

  // 文字与表情 轮流添加到a
  // textArr 的长度 永远比 emojiArr 大 1
  // 当然 emojiArr 可能为 null，此时 textArr 长度为 1，成员即为原始字符串
  textArr.forEach((cont, i) => {
    // 当 文字内容不为空 添加到a
    cont && a.push({
      msgType: 'text',
      msgCont: cont
    });

    // 最后一次循环，肯定没有表情与之对应，所以忽略
    // 如果不是最后一次，添加表情到a
    // 当然此处还需判断是否有此表情的图片定义
    (i !== textArr.length - 1) && a.push(emojis[emojiArr[i]] ? ({
      msgType: 'emoji',
      msgCont: emojiArr[i],
      msgImage: emojiToPath(emojiArr[i])
    }) : ({
      msgType: 'text',
      msgCont: emojiArr[i]
    }));

  });

  return a;
}

//把utf16的emoji表情字符进行转码成八进制的字符
function utf16toEntities(str) {
  var patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则  
  return str.replace(patt, function(char) {
    var H, L, code;
    if (char.length === 2) {
      H = char.charCodeAt(0); // 取出高位   
      L = char.charCodeAt(1); // 取出低位  
      code = (H - 0xD800) * 0x400 + 0x10000 + L - 0xDC00; // 转换算法  
      return "&#" + code + ";";
    } else {
      return char;
    }
  });
}

//将编码后的八进制的emoji表情重新解码成十六进制的表情字符
function entitiesToUtf16(str) {
  str = str == null ? '' : str
  return str.replace(/&#(\d+);/g, function(match, dec) {
    let H = Math.floor((dec - 0x10000) / 0x400) + 0xD800;
    let L = Math.floor(dec - 0x10000) % 0x400 + 0xDC00;
    return String.fromCharCode(H, L);
  });
}

module.exports = {
  emojis,
  emojiToPath,
  textToEmoji,
  utf16toEntities: utf16toEntities,
  entitiesToUtf16: entitiesToUtf16
}