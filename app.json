{"pages": ["pages/index/index", "pages/login/login", "pages/sign/sign", "pages/supplier_order_detail2/supplier_order_detail2", "pages/turntableActivityList/turntableActivityList", "pages/pokerActivity/pokerActivity", "pages/turntableActivity/turntableActivity", "pages/messageNotice/messageNotice", "pages/myRight/myRight", "pages/chargeAccount/chargeAccount", "pages/myvipCard/myvipCard", "pages/changeProfile/changeProfile", "pages/giftCardDetail/giftCardDetail", "pages/giftCardList/giftCardList", "pages/giftCardShowMore/giftCardShowMore", "pages/giftCardExchange/giftCardExchange", "pages/giftAddress/giftAddress", "pages/share/share", "pages/vipCard/vipCard", "pages/acceptance/acceptance", "pages/writeInformation/writeInformation", "pages/returnGoods2/returnGoods2", "pages/qrcode/qrcode", "pages/moreGoods/moreGoods", "pages/scoreChooseCard/scoreChooseCard", "pages/toCity/toCity", "pages/showCity/showCity", "pages/waitPay/waitPay", "pages/couponTypeCenter/couponTypeCenter", "pages/chargeDetail/chargeDetail", "pages/logisticDetail/logisticDetail", "pages/bindCard/bindCard", "pages/reportDetail/reportDetail", "pages/reportForms/reportForms", "pages/localAddress/localAddress", "pages/goods_classify/goods_classify", "pages/switchScore/switchScore", "pages/goodsDetail/goodsDetail", "pages/choosepayWay/choosepayWay", "pages/queryoffLineCard/queryoffLineCard", "pages/chooseCard/chooseCard", "pages/bannerTemplate/bannerTemplate", "pages/scoreGoods/scoreGoods", "pages/scoreMall/scoreMall", "pages/scoreGoodsPay/scoreGoodsPay", "pages/customerScore/customerScore", "pages/person_agentDetail/person_agentDetail", "pages/myVip/myVip", "pages/card/card", "pages/lowerConsumption/lowerConsumption", "pages/goPromotionGoods/goPromotionGoods", "pages/verifyCard/verifyCard", "pages/gopayBill/gopayBill", "pages/bindofflineCard/bindofflineCard", "pages/storeCard/storeCard", "pages/changePersonInfo/changePersonInfo", "pages/changePaycode/changePaycode", "pages/onevipcard/onevipcard", "pages/allvipcard/allvipcard", "pages/newUser/newUser", "pages/verifyOrder/verifyOrder", "pages/supplier_order_detail/supplier_order_detail", "pages/onePromotion/onePromotion", "pages/changevipInfo/changevipInfo", "pages/secondCategory/secondCategory", "pages/person_coupon/person_coupon", "pages/couponCenter/couponCenter", "pages/useCoupon/useCoupon", "pages/indexPlateDetail/indexPlateDetail", "pages/gratitudeMoney/gratitudeMoney", "pages/deliveredInformation/deliveredInformation", "pages/applayReturnGoods/applayReturnGoods", "pages/myTeam/myTeam", "pages/accountManager/accountManager", "pages/shopCart/shopCart", "pages/completeRefund/completeRefund", "pages/aftersaleList/aftersaleList", "pages/customerService/customerService", "pages/register/register", "pages/evaluateDetail/evaluateDetail", "pages/evaluate/evaluate", "pages/indexThree/indexThree", "pages/successfulTrade/successfulTrade", "pages/classify/classify", "pages/searchPage/searchPage", "pages/modifyPassword/modifyPassword", "pages/newbuiltAddress/newbuiltAddress", "pages/addAddress/addAddress", "pages/editAddress/editAddress", "pages/personAddressManager/personAddressManager", "pages/logs/logs", "pages/chainstorelevel/chainstorelevel", "pages/imageDesc/imageDesc", "pages/supplierShop/supplierShop", "pages/CouponTem/CouponTem", "pages/addressToCity/addressToCity", "pages/payWay/payWay", "pages/exchangeGoodsList/exchangeGoodsList", "pages/activateVipCard/activateVipCard", "pages/shareCoupon/shareCoupon", "pages/evaluateSuccess/evaluateSuccess", "pages/supplierShareCoupon/supplierShareCoupon", "pages/expireAccout/expireAccout", "pages/consumptionDetail/consumptionDetail", "pages/chargeItem/chargeItem", "pages/myReward/myReward", "pages/signRules/signRules", "pages/openonecard/openonecard", "pages/upgradeOpenonecard/upgradeOpenonecard", "pages/openonecardOld/openonecardOld", "pages/collectStrategy/collectStrategy", "pages/picMatter/picMatter", "pages/collect/collect", "pages/record/record", "pages/find/find", "pages/findInfo/findInfo", "pages/goodsClassify/goodsClassify", "pages/goodsGroup/goodsGroup", "pages/accountRebate/rebateDrawDetail/rebateDrawDetail", "pages/accountRebate/rebateLog/rebateLog", "pages/accountRebate/rebateGoDraw/rebateGoDraw", "pages/chooseCity/chooseCity"], "requiredBackgroundModes": ["audio", "location"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "首页", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "tabBar": {"custom": true, "color": "#7A7E83", "selectedColor": "#FF7E00", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "image/home.png", "selectedIconPath": "image/home1.png", "text": "首页"}, {"pagePath": "pages/goods_classify/goods_classify", "iconPath": "image/classify.png", "selectedIconPath": "image/classify1.png", "text": "分类"}, {"pagePath": "pages/shopCart/shopCart", "iconPath": "image/shopcart.png", "selectedIconPath": "image/shopcart1.png", "text": "购物车"}, {"pagePath": "pages/accountManager/accountManager", "text": "个人", "selectedIconPath": "image/personal1.png", "iconPath": "image/personal.png"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "sitemapLocation": "sitemap.json"}