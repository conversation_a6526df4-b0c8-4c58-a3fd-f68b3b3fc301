page{
  background-color: #2937AE;
}
.rule{
  width: 108rpx;
  height:70rpx;
  line-height:70rpx;
  border-radius: 0 35rpx 35rpx 0;
  background-color: #7482F5;
  color:#fff;
  position: absolute;
  top: 20rpx;
  left: 0;
  z-index: 10;
  font-size: 30rpx;
  text-align: center;
}
.bg{
  position: absolute;
  top: 0;
  left: 0;
}
.PrizeContainer{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding-top: 280rpx;
}

.ruleBox {
  padding-bottom: 60rpx;
  margin: 0 auto;
  position: fixed;
  top: 220rpx;
  left: 130rpx;
  z-index: 9999;
  width: 460rpx;
  height: 560rpx;
  padding:20rpx;
  border-radius: 10rpx;
  background-color: #fff;
}
.closeTemp{
  position: absolute;
  right: 10rpx;
  top: 10rpx
}
.closeTemp1{
  position: absolute;
  right: -64rpx;
  top: -54rpx
}
.pop_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #000;
  opacity: 0.6;
}
.prizeBox{
  width: 473rpx;
  height: 564rpx;
  border-radius: 10rpx;
  margin: 0 auto;
  position: fixed;
  top: 280rpx;
  left: 130rpx;
  z-index: 99999;
  text-align: center;
  color:#fff;
  font-size: 28rpx;
  background-color: #fff;
}
.sumbitBtn{
  width: 267rpx;
  height: 66rpx;
  line-height: 66rpx;
  border-radius: 66rpx;
  text-align: center;
  color:#fff;
  background-color: #547AEC;
  margin:0 auto;
}
.noPrizeBox{
  width: 473rpx;
  height: 564rpx;
  border-radius: 10rpx;
  margin: 0 auto;
  position: fixed;
  top: 280rpx;
  left: 130rpx;
  z-index: 99999;
  text-align: center;
  color:#fff;
  font-size: 28rpx;
  background-color: #fff;
}
.prizeList{
  width: 655rpx;
  border-radius: 8rpx;
  background-color: #7F9AF7;
  max-height: 370rpx;
  overflow-y: auto;
  margin:20rpx auto;
  z-index: 10;
  padding:20rpx;
  /*min-height: 200rpx;*/
}
.prizeList1{
  width: 655rpx;
  border-radius: 8rpx;
  background-color: #7F9AF7;
  max-height: 370rpx;
  overflow-y: auto;
  margin:20rpx auto;
  z-index: 10;
  padding:20rpx;
  height: 140rpx;
}
.prizeLi{
  height:100rpx;
  display: flex;
  color:#fff;
  font-size:26rpx;
}
.PrizeBox{
  width: 655rpx;
  border-radius: 8rpx;
  background-color: #7F9AF7;
  max-height: 800rpx;
  overflow-y: auto;
  margin: 20rpx auto;
  z-index: 9999;
  padding: 20rpx;
  position:fixed;
  top:80rpx;
  left:40rpx;
}
.rulePrize{
  width: 160rpx;
  height:70rpx;
  line-height:70rpx;
  border-radius:35rpx 0 0 35rpx ;
  background-color: #7482F5;
  color:#fff;
  position: absolute;
  top: 20rpx;
  right: 0;
  z-index: 10;
  font-size: 30rpx;
  text-align: center;
}