var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    discountImg: app.imageUrl + 'discountImg.png',
    state: -1
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var cardId = options.cardId;
    var shareUserId = options.shareUserId;
    app.init_getExtMessage().then(res => {
      that.setData({
        cardId: cardId,
        shareUserId: shareUserId
      })
      that.queryCardDetail(cardId, res.companyId, res.storeId, shareUserId);
    });
  },
  queryCardDetail: function (cardId, companyId, storeId, shareUserId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newCardShare/queryCardDetailByCardId',
      data: {
        "cardId": cardId,
        "companyId": companyId,
        "storeId": storeId
      },
      success: function (res) {
        var cardBean = res.data.cardBean;
        var state = res.data.state;
        if (cardBean != null) {
          var isLogin = app.isLogin();
          if (isLogin && shareUserId == app.getUserId()) {
            state = 3;//本人不可领取
          }
          that.setData({
            cardBean: cardBean,
            state: state
          });
        } else {
          wx.switchTab({
            url: "/pages/index/index"
          });
        }
      }
    })
  },
  nowReceiveCardBindTap: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newCardShare/receiveShareCard',
      data: {
        "cardId": that.data.cardId,
        "companyId": app.getExtCompanyId(),
        "receiveUserId": app.getUserId()
      },
      success: function (res) {
        var state = res.data.state;
        var flag = res.data.flag;
        if (state == 0) {//未领取
          if (flag) {
            wx.showToast({
              title: "恭喜您抢到啦",
              icon: 'success',
              duration: 1000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  that.setData({
                    state: 2
                  })
                }, 1000);
              }
            })
          } else {
            wx.showToast({
              title: "很抱歉，领取失败",
              icon: 'none',
              duration: 1000,
              mask: true
            })
          }
        } else {//已领取 
          wx.showToast({
            title: "您的手速太慢啦，已被抢光啦",
            icon: 'none',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                that.setData({
                  state: 1
                })
              }, 1000);
            }
          })
        }
      }
    })
  },
  goCardTap: function () {
    app.navigateToPage("/pages/person_coupon/person_coupon");
  }
})