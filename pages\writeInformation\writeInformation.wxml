<!--<image class='tips_pic' src='{{return_apply}}' mode='widthFix'></image>-->
<view class="shoppingCart-top-nav">
  <view class="top-nav-right">
    <!--<view class="shoppingCart-edit-complete" bindtap="newApplayBindTap">下一步</view>-->
  </view>
</view>
<view bindtap="newApplayBindTap" class="subData">
  提 交
</view>
<view class="shoppingCart-wrap page" style='padding-top:43px;'>
  <block wx:key="unique" wx:for="{{goodsList}}" wx:for-item="goods">
    <view class="shoppingCart-goods-list">
      <view class="shoppingCart-goods-content">
        <image class="shoppingCart-goods-cover" src="{{goods.commodityMainPic}}"></image>
        <view class="ellipsis shoppingCart-goods-title">{{goods.commodityName}}</view>
        <view class='classify_box'>
        </view>
        <view class="shoppingCart-goods-price">
          <text>¥{{goods.commoditySendOtPrice}}</text>
          <view class="shoppingCart-goods-right">
            x{{goods.commodityRejectedOtNum}}
          </view>
        </view>
      </view>
    </view>
  </block>
</view>

<view class="section" style='background:#fff; margin-top:10px;padding:10px 0;'>
  <picker bindchange="bindPickerChange" value="{{index}}" range="{{array}}">
    <view class="picker" style='width:92%;padding:0 2%;margin:0 2%;height:36px; line-height:36px; border:1px solid #ececec; border-radius:5px;position:relative;'>
      {{array[index]}}
      <text style='position:absolute;top:10%; right:10px;transform:rotate(180deg);'>︿</text>
    </view>
  </picker>
</view>
<view style='background:#fff;width:100%; height:110px;'>
  <textarea style='width:92%;padding:5px 2%;margin:0 2%;height:96px; line-height:30px; border:1px solid #ececec; border-radius:5px;position:relative;' placeholder='为了尽快处理你的售后请求，请填写详细描述~' bindinput='returnGoodsDescBindInput'></textarea>
</view>
<view class='addPic' style='background:#fff;' hidden="true">
  <label style='display:block; width:100%; line-height:36px; height:36px;color:#666;'>上传照片(选填)</label>
  <block wx:key="unique" wx:for="{{returnGoodsPic}}" wx:for-item="ge">
    <view style='position:relative;width:60px;height:60px; float:left;margin-right:14px;margin-bottom:14px;'>
      <image class='add_pic' src='{{ge}}' data-src='{{ge}}' bindtap='previewImage'></image>
      <image src='{{close_btn}}' bindtap='deleteEvaluateImage' data-index='{{index}}' data-src='{{ge}}' style='position:absolute; top:-10px;right:-10px;width:20px; height:20px;'></image>
    </view>

  </block>
  <image class='add_pic' data-id='{{goods.commodityId}}' src='{{add_pic}}' bindtap='chooseImageBindTap'>
  </image>
</view>

<!--<button style='border:1px solid #ccc; font-size:14px; width:60%;position:fixed;bottom:0; text-align:center; margin:10px 20%;display:block;'>查看退货政策</button>-->