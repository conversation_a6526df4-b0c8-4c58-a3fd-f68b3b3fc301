<block wx:if="{{deliverWay != 9}}">
  <view class="contant_box">
    <!--<view class="nav bc_white">
      <view class="{{!selected?'blue':'default'}}" bindtap="selected">订单状态</view>
      <view class="{{!selected1?'blue':'default'}}" bindtap="selected1">订单详情</view>
    </view>-->
    <scroll-view scroll-y>
      <!-- 订单状态 -->
      <view style="font-size:28rpx;padding:10rpx;">
          <label>运单号：{{logisticNo}}</label>
        </view>
        <view style="font-size:28rpx;border-top:1px solid #ececec;padding:10rpx;">
          <label>物流公司：{{logisticName}}</label>
        </view>
      <view class='show' style='padding:0 50px; background:#fff;'>
        <block wx:key="unique" wx:for="{{logisticsList}}" wx:for-item="logistics" wx:for-index="logisticsIndex">
          <block wx:if="{{logisticsIndex==0}}">
            <view class='new_state'>
              <text></text>
              <label>{{logistics.status}}</label>
              <label>{{logistics.time}}</label>
            </view>
          </block>
          <block wx:else>
            <view class="old_state">
              <text></text>
              <label>{{logistics.status}}</label>
              <label>{{logistics.time}}</label>
            </view>
          </block>
        </block>
      </view>
    </scroll-view>
  </view>
</block>
<block wx:if="{{deliverWay == 9}}">
  <view style="font-size:29rpx;color:#333;border-radius:10rpx;padding:20rpx 30rpx;width:640rpx;margin:0 auto;margin-top:30rpx;background:#fff;">
    <view style="margin-top:10rpx;">收货人：{{rData.receiverName}}</view>
    <view style="margin-top:10rpx;">联系电话：{{rData.receivePhone}}</view>
    <view style="margin-top:10rpx;">收货地址：{{rData.receiveAddress}}</view>
  </view>
  <view style="display:flex;justify-content:space-between;font-size:32rpx;color:#333;border-radius:10rpx;padding:30rpx 30rpx;width:640rpx;margin:0 auto;margin-top:30rpx;background:#fff;">
    <view>{{rData.channelName}}</view>
    <view style="font-size:32rpx;color:#ff6600">
      <block wx:if="{{orderStatus ==1}}">订单待派单</block>
      <block wx:if="{{orderStatus ==2}}">待骑手接单</block>
      <block wx:if="{{orderStatus ==3}}">待取货</block>
      <block wx:if="{{orderStatus ==4}}">骑手取货中</block>
      <block wx:if="{{orderStatus ==5}}">配送中</block>
      <block wx:if="{{orderStatus ==6}}">配送完成</block>
      <block wx:if="{{orderStatus ==7}}">配送失败</block>
      <block wx:if="{{orderStatus ==8}}">配送被取消</block>
    </view>
  </view>
    <block wx:if="{{orderStatus ==2 || orderStatus ==4 || orderStatus ==5|| orderStatus ==6}}">
      <view style="font-size:29rpx;color:#999;margin-top:20rpx;">预计{{rideTime}}分钟后送达</view>
      <view style="font-size:30rpx;margin-top:20rpx;color:#333;display:flex;justify-content:space-between;">
        <view>骑手：{{riderName}}</view>
        <view bindtap="makePhoneCall" data-phone="{{riderPhone}}">电话：{{riderPhone}}</view>
      </view>
    </block>
  <view style="border-radius:10rpx;width:700rpx;margin:0 auto;margin-top:30rpx;padding:4rpx;background:#fff;">
    <view class='goods_detail'>
      <image class='goods_pic' src='{{rData.commodityShowImage}}'></image>
      <view class='right_detail'>
        <view class='right_top' style="display:flex;justify-content: space-between;">
          <view class='goods_name'>{{rData.commodityShowName}}</view>
        </view>
        <!-- sku属性位置 -->
      </view>
    </view>
  </view>
  <!--显示地图-->
  <view>
  </view>
</block>