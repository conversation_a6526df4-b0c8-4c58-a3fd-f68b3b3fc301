page {
  background: #f4f4f4;
}
.subData{
  position:fixed;
  bottom:0;
  width:100%;
  height:80rpx;
  line-height:80rpx;
  background:#FF7E00;
  color:#fff;
  text-align:center;
}
.shoppingCart-top-nav {
  position: fixed;
  /**top: 30px;**/
  left: 0;
  right: 0;
  height: 2.625rem;
  line-height: 2.625rem;
  padding: 0 0.625rem;
  margin-bottom: 0.625rem;
  /**box-shadow: 0 0.0625rem 0.125rem #eee;**/
  text-align: center;
  z-index: 9;
  background-color: #fff;
}

.top-nav-right {
  position: absolute;
  top: 0;
  right: 0.625rem;
  height: 100%;
}

.top-nav-right view {
  font-size: 14px;
}

.shoppingCart-list-wrap {
  overflow-x: hidden;
  overflow-y: auto;
}

.shoppingCart-list-wrap.editing-list {
  margin-top: 90px;
  margin-bottom: 0;
}

.shoppingCart-goods-list {
  background-color: #fff;
  margin-bottom: 0.625rem;
}

.shoppingCart-goods-list:last-child {
  margin-bottom: 0;
}

.shoppingCart-goods-list > view {
  position: relative;
  border-bottom: 1px solid #e5e5e5;
}

/* .shoppingCart-goods-list > view:first-child {
  border-top: 1px solid #e5e5e5;
} */

.shoppingCart-goods-content {
  padding: 0.625rem 10px 0.625rem 10px;
  height: 150rpx;
}

.shoppingCart-goods-right {
  /* position: absolute;
  bottom: 0.75rem;
  right: 0.75rem; */
  text-align: right;
  float: right;
  font-size: 15px;
  line-height: 26px;
}

.shoppingCart-goods-list .shoppingCart-check-box {
  margin: 60rpx 0.625rem 0;
  padding: 0.3rem 0;
  float: left;
}

.shoppingCart-goods-list .check-box {
  margin: 0;
}

.shoppingCart-goods-cover {
  float: left;
  width: 150rpx;
  height: 150rpx;
  margin-right: 22rpx;
}

.shoppingCart-goods-title {
  color: #666;
  font-size: 14px;
}

.shoppingCart-goods-model, .shoppingCart-goods-original-price {
  margin-top: 6rpx;
  color: #818181;
  font-size: 24rpx;
  line-height: 1.3em;
}

.shoppingCart-goods-original-price > text {
  text-decoration: line-through;
}

.shoppingCart-pay-dialog {
  text-align: center;
}

.shoppingCart-pay-dialog .shoppingCart-check-box {
  margin-top: 0.375rem;
}

.shoppingCart-dialog-detail > view {
  line-height: 2.125rem;
}

.shoppingCart-payment {
  text-align: left;
  padding: 0 0.9375rem;
}

.shoppingCart-pay-directly {
  margin: 0 0.9375rem;
}

.classify_box {
  color: #888;
  font-size: 12px;
  margin-top: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shoppingCart-goods-price {
  color: ##FF7E00;
  margin-top: 3px;
  line-height: 26px;
}

.shoppingCart-goods-price > text {
  font-size: 15px;
  color: #333;
}

#shoppingCart .minus, #shoppingCart .plus {
  position: relative;
  display: inline-block;
  font-size: 16px;
  outline: 0 !important;
  text-indent: -9999px;
  overflow: hidden;
  vertical-align: middle;
  width: 25px;
  height: 25px;
  line-height: 25px;
  font-weight: normal;
  background-color: #f1f1f1;
  color: #878787;
  border-radius: 2px;
  border: none;
}

#shoppingCart .minus.disabled, #shoppingCart .plus.disabled {
  background-color: #f9f9f9;
  color: #cacaca;
}

#shoppingCart .minus:before, #shoppingCart .plus:before,
#shoppingCart .plus:after {
  position: absolute;
  width: 8px;
  height: 2px;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: #878787;
  bottom: 0;
  content: '';
}

#shoppingCart .plus:after {
  width: 2px;
  height: 8px;
}

#shoppingCart .minus.disabled:before, #shoppingCart .plus.disabled:before,
#shoppingCart .plus.disabled:after {
  background-color: #cacaca;
}

.quantity input {
  border: none;
  height: 25px;
  background-color: #f1f1f1;
  padding: 0;
  margin: 0 1px;
  border-radius: 0;
  width: 40px;
  min-height: 25px;
  line-height: 25px;
  font-size: 14px;
}

.tips_pic {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.shoppingCart-wrap {
  position: relative;
}

.addPic {
  width: 96%;
  padding: 5px 2%;
  /**height: 70px;
  border-bottom: 1px solid #e0e0e0;**/
}

.add_pic {
  width: 60px;
  height: 60px;
  /**float: left;
  margin-right: 8px;**/
}
