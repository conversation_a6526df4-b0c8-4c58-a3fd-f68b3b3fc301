const app = getApp();
const myslider = require('../../utils/yxxrui.slider.js');
Page({
  data: {
    indicatorDots: false,
    autoplay: false,
    interval: 5000,
    duration: 5000,
    curPage: 1,
    x: 0,
    borderColor: "#E3E3E3",
    background: "#FFFFFF",
    length: 4,//套餐数量
    mealHeight: 0,//对应套餐高度
    vipList: [],
    planList: [],
    selectPlanId: 0,
    selectTypeId: 0,
    userName: "",
    telephone: "",
    sex: "1",
    vipCardNo: "",
    cardRule: ""//会员卡使用规则
  },
  onLoad: function (options) {
    var that = this;
    that.setData({
      vipCardNo: options.vipCardNo,
      userName: app.getLoginName(),
      telephone: app.getTelephone()
    })
    that.queryWechatAppletVipCard();
  },
  /**
   * 查询商家会员卡配置信息
   */
  queryWechatAppletVipCard: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/queryWechatAppletVipCard',
      success: function (res) {
        var vipList = res.data.vipList;
        if (vipList != null && vipList.length > 0) {
          that.queryWechatAppletVipCardPlanByCardId(vipList[0].id);
          that.setData({
            selectTypeId: vipList[0].id,
            vipList: vipList,
            cardRule: vipList[0].rule
          })
          var length = that.data.length;
          myslider.initMySlider({
            that: that,
            datas: vipList,
            autoRun: false,
            blankWidth: 12,
            newImgWidth: 18,
            interval: 1500,
            duration: 200,
            direction: 'left',
            startSlide: function (curPage) {

            },
            endSlide: function (curPage) {
              if (that.data.curPage != curPage) {
                var vipList = that.data.vipList;
                if (vipList != null && vipList.length > 0) {
                  var planId = vipList[curPage].id;
                  that.queryWechatAppletVipCardPlanByCardId(planId);
                  that.setData({
                    curPage: curPage,
                    selectPlanId: 0,
                    selectTypeId: planId,
                    cardRule: vipList[curPage].rule
                  })
                }
              }
            }
          });

          //如果获取的套餐数量>3
          if (length > 3) {
            that.setData({
              mealHeight: length / 3 * 210,//获取的套餐数量除3*203即可   
            })
          }

        }
      }
    })
  },
  queryWechatAppletVipCardPlanByCardId: function (planId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "planId": planId
      },
      url: app.projectName + '/vipCard/queryWechatAppletVipCardPlanByCardId',
      success: function (res) {
        var planList = res.data.planList;
        that.setData({
          planList: planList
        })
      }
    })
  },
  checkMeal: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var planList = that.data.planList;
    for (var i = 0; i < planList.length; i++) {
      if (i == index) {
        planList[i].select = true;
      } else {
        planList[i].select = false;
      }
    }
    that.setData({
      selectPlanId: planList[index].id,
      planList: planList
    })
  },
  /**
   * 开通会员卡
   */
  openVipCard: function () {
    var that = this;
    if (that.data.vipCardNo == undefined || that.data.vipCardNo.length <= 0) {
      wx.showToast({
        title: '会员卡有异常',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var planList = that.data.planList;
    if (planList != null && planList.length > 0) {
      var isSelect = false;
      for (var i = 0; i < planList.length; i++) {
        if (planList[i].select) {
          isSelect = true;
          break;
        }
      }
      if (!isSelect) {
        wx.showToast({
          title: '请选择一种套餐方案',
          icon: 'none',
          duration: 1000,
          mask: true
        })
        return false;
      }
    }
    var telephone = that.data.telephone;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/vipCard/upgradeVipCard',
      data: {
        "vipCardNo": that.data.vipCardNo,
        "telephone": telephone,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "openId": app.getOpenId(),
        "companyId": app.getExtCompanyId(),
        "id": that.data.selectTypeId,
        "planId": that.data.selectPlanId,
        "storeId": app.getExtStoreId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var message = res.data.returnMessage;
        var code = res.data.code;
        if (flag) {
          var param = res.data;
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              wx.showToast({
                title: "开通会员卡成功",
                icon: 'success',
                duration: 1000,
                mask: true,
                success: function () {
                  setTimeout(function () {
                    app.turnBack();
                  }, 1000);
                }
              })
            },
            fail: function (res) {
              wx.showToast({
                title: "取消支付",
                icon: 'none',
                duration: 1000,
                mask: true,
                success: function () {
                  setTimeout(function () {
                    app.turnBack();
                  }, 1000);
                }
              })
            }
          })
        } else {
          wx.showToast({
            title: message,
            icon: "none",
            mask: true
          })
        }
      }
    })
  },
  userNameBindInput: function (e) {
    this.setData({
      userName: e.detail.value
    })
  },
  telephoneBindInput: function (e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  sexBindChange: function (e) {
    this.setData({
      sex: e.detail.value
    })
  }
})
