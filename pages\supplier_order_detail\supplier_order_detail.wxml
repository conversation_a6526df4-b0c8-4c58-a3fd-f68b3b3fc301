<wxs src="../../wxs/subutil.wxs" module="tools" />
<view style="padding:20rpx;">
	<view class="clearfix detailWrap">
		<image class="address_icon" mode="widthFix" src="{{detail_address}}"></image>
		<view class="address_info">
			<view>{{orderBean.receiverName}} {{orderBean.receiveContactNum}}</view>
			<view style="margin-top:10rpx;">{{orderBean.receiveAddress}}</view>
		</view>
	</view>
	<view style="padding:20rpx 0">
		<!--单品-->
		<block wx:key="unique" wx:for="{{orderBean.orderDetail}}" wx:for-item="goods">
			<view class='single_box clearfix' style="position:relative">
				<image src='{{goods.commodityMainPic}}'></image>
				<view class="product_detail" style="position:relative">
					<view style='height:42rpx;font-size:28rpx;'>
						<label class='single_title'>{{goods.commodityName}}</label>
					</view>
					<block wx:if="{{orderBean.orderBillType!=3}}">
						<view style='height:42rpx;font-size:28rpx;color:#FF7E00'>
							<text style="margin-right:10rpx;float:left;"
								hidden="{{goods.commoditySendOmNum>0?false:true}}">￥{{tools.sub.formatAmount(goods.commoditySendOmPrice,2)}}/{{goods.commodityOmUnit}}</text>
							<text
								style="margin-right:10rpx;float:left;">{{tools.sub.formatAmount(goods.commoditySendOtPrice,2)}}/{{goods.commodityOtUnit}}</text>
						</view>
					</block>
					<view style="position:absolute;top:6rpx;right:0;">
						<block wx:if="{{goods.commodityType==4}}">
							<view class="clearfix" style="height:40rpx;font-size:26rpx;">
								<text>赠品</text>
							</view>
						</block>
						<block wx:else>
							<view class="clearfix" style="height:40rpx;font-size:26rpx;text-align:right">
								<text>X</text>
								<text style="margin-right:10rpx"
									hidden="{{goods.commoditySendOmNum>0?false:true}}">{{goods.commoditySendOmNum}}{{goods.commodityOmUnit}}</text>
								<text style="margin-right:10rpx">{{goods.commoditySendOtNum}}{{goods.commodityOtUnit}}</text>
							</view>
							<block wx:if="{{goods.commodityLargessNum>0}}">
								<view class="clearfix" style="font-size:26rpx;color:#FF7E00">
									<text style="float:right;">包含赠品{{goods.commodityLargessNum}}{{goods.commodityLargessUnit}}</text>
								</view>
							</block>
						</block>
					</view>
					<view class='classify_box'>
						<block wx:for="{{goods.skuUnitList}}" wx:for-item="sku" wx:key="" wx:for-index="skuIdx">
							<block wx:if="{{goods.skuUnitList.length-1==skuIdx}}">
								{{sku.skuName}}:{{sku.skuValue}}
							</block>
							<block wx:else>
								{{sku.skuName}}:{{sku.skuValue}};
							</block>
						</block>
					</view>
				</view>
			</view>
		</block>
		<!--单品-->
		<view class="goods_detail">
			<view>
				商品总价
				<block wx:if="{{orderBean.orderBillType!=3}}">
					<text style="float:right;">￥{{tools.sub.formatAmount(orderBean.orderCommodityTotalMoney,2)}}</text>
				</block>
				<block wx:else>
					<text style="float:right;">{{tools.sub.formatAmount(orderBean.payList[0].payMoney,2)}}积分</text>
				</block>
			</view>
			<view>
				运费
				<text style="float:right;">￥{{tools.sub.formatAmount(orderBean.orderDistributionPay,2)}}</text>
			</view>
			<view>
				订单总价
				<block wx:if="{{orderBean.orderBillType!=3}}">
					<text style="float:right;">￥{{tools.sub.formatAmount(orderBean.orderTotalMoney,2)}}</text>
				</block>
				<block wx:else>
					<text style="float:right;">{{tools.sub.formatAmount(orderBean.payList[0].payMoney,2)}}积分</text>
				</block>
			</view>
			<block wx:if="{{couponPayment>0}}">
				<view style="color:#FF7E00;">
					优惠券
					<text style="float:right;">￥{{couponPayment}}</text>
				</view>
			</block>
			<block wx:if="{{profitMoney>0}}">
				<view style="color:#FF7E00;">
					店铺优惠
					<text style="float:right;">￥{{tools.sub.formatAmount(profitMoney,2)}}</text>
				</view>
			</block>
			<block wx:if="{{scorePayment>0}}">
				<view style="color:#FF7E00;">
					积分抵扣
					<text style="float:right;">￥{{scorePayment}}</text>
				</view>
			</block>
			<block wx:if="{{orderBean.orderProfitDetailList.length>0}}">
				<view style="color:#FF7E00;">
					活动抵扣
					<text style="float:right;"></text>
				</view>
				<block wx:for="{{orderBean.orderProfitDetailList}}" wx:for-item="profit" wx:key="">
					<view style="padding:0 10rpx;color:#666;font-size:26rpx;">
						{{profit.profitReason}}
						<text style="float:right;">-￥{{tools.sub.formatAmount(profit.profitMoney,2)}}</text>
					</view>
				</block>
			</block>

			<view>
				配送方式
				<block wx:if="{{orderBean.orderDistributionWay=='SELF_DISTRIBUTION'}}">
					<text style="float:right;">快递</text>
				</block>
				<block wx:elif="{{orderBean.orderDistributionWay=='SELF_EXTRACT'}}">
					<text style="float:right;">自取</text>
				</block>
				<block wx:else>
					<text style="float:right;">第三方配送</text>
				</block>
			</view>
			<block wx:if="{{orderBean.orderDistributionWay=='SELF_EXTRACT'}}">
				<view class="clearfix">
					<text style="float:left;">自取门店：{{orderBean.pickOrderStoreName}}</text>
				</view>
				<view class="clearfix">
					<text style="float:left;">自取联系人：{{orderBean.pickOrderUserName}}</text>
				</view>
				<view class="clearfix">
					<text style="float:left;">自取联系方式：{{orderBean.pickOrderUserContactNum}}</text>
				</view>
				<view class="clearfix">
					<text
						style="float:left;">自取时间：{{orderBean.pickActualArriveTimeBegin}}~{{orderBean.pickActualArriveTimeEnd}}</text>
				</view>
			</block>
			<view>
				支付方式
				<block wx:if="{{orderBean.orderBillType!=3}}">
					<block wx:if="{{orderBean.orderPayWay=='PAYAFTER'}}">
						<text style="float:right;">货到付款</text>
					</block>
					<block wx:else>
						<text style="float:right;">现金支付</text>
					</block>
				</block>
				<block wx:else>
					<text style="float:right;">积分支付</text>
				</block>
			</view>
		</view>
		<view class="acutal_pay" hidden="{{banlancePayment>0||actualPayment>0?false:true}}">
			<block wx:if="{{banlancePayment>0}}">
				<view>
					会员卡付款
					<text style="float:right;">￥{{tools.sub.formatAmount(banlancePayment,2)}}</text>
				</view>
			</block>
			<block wx:if="{{actualPayment>0}}">
				实付现金
				<text style="float:right;color:#FF7E00;">￥{{tools.sub.formatAmount(actualPayment,2)}}</text>
			</block>
		</view>
		<view class="order_info">
			<view class="info_title">订单信息</view>
			<view class="info_detail">订单编号：{{orderBean.orderNo}}
				<text bindtap='copyOrderNo' data-no='{{orderBean.orderNo}}'>复制</text>
			</view>
			<view class="info_detail">创建时间：{{orderBean.orderGenerateDate}}</view>
			<view class="info_detail" hidden='{{payTime==""?true:false}}'>付款时间：{{payTime}}</view>
		</view>
		<view class="order_operate clearfix">
			<!--待付款状态 -->
			<block wx:if="{{orderBean.orderStatus==2}}">
				<view bindtap='nowPayBindTap' data-orderNo='{{orderBean.orderNo}}'>
					<text>立即付款</text>
				</view>
			</block>
			<block wx:if="{{orderBean.orderStatus==3}}">
				<block wx:if="{{orderBean.orderRejectedStatus==0}}">
					<view bindtap='appleyReturnMoneyBindTap' data-orderId='{{orderBean.orderId}}'>
						<text>申请退款</text>
					</view>
				</block>
				<block wx:if="{{orderBean.orderRejectedStatus==6}}">
					<text> 正在申请退款</text>
				</block>
				<block wx:if="{{orderBean.orderRejectedStatus==7}}">
					<text> 退款成功</text>
				</block>
			</block>
			<block wx:if="{{orderBean.orderStatus==4}}">
				<view bindtap='receiptGoodsBindTap' data-orderId='{{orderBean.orderId}}'>
					<text>确认收货</text>
				</view>
			</block>
			<block wx:if="{{orderBean.orderStatus==4}}">
				<block wx:if="{{orderBean.orderDeliver[0].deliverWay == 'SELF_DISTRIBUTION'}}">
					<view bindtap='queryDistributionBindTap' data-orderId='{{orderBean.orderId}}'
						data-address='{{orderBean.receiveAddress}}'>
						<text>查看物流</text>
					</view>
				</block>
				<block wx:else>
					<view bindtap='queryLogisticsBindTap' data-orderId='{{orderBean.orderId}}'
						data-address='{{orderBean.receiveAddress}}'>
						<text>查看物流</text>
					</view>
				</block>
			</block>
			<block wx:if="{{orderBean.orderStatus==2||orderBean.orderStatus==12}}">
				<view bindtap='cancelOrderformSubmit' data-orderId='{{orderBean.orderId}}'>
					<text>取消订单</text>
				</view>
			</block>
			<block wx:if="{{orderBean.orderStatus==7}}">
				<view bindtap='evaluateBindTap' data-orderId='{{orderBean.orderId}}'>
					<text style="padding:8rpx 30rpx;">评<text style="margin-left:-4rpx;"></text>价</text>
				</view>
			</block>
			<block wx:if="{{orderBean.orderDistributionWay=='SELF_EXTRACT'&&orderBean.orderStatus==3}}">
				<view bindtap='verifyOrderBind' data-code='{{orderBean.pickUpCode128}}'>
					<text>提货码</text>
				</view>
			</block>
		</view>
	</view>
</view>