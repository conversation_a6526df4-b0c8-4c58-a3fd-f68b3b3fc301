var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    return_apply: app.imageUrl + "accountManager/return_apply.png",
    zc_app_fontschecked: app.imageUrl + "shopDetails/zc_app_fontschecked.png",
    allCheck: false,
    totalMoney: 0.00,
    goodsList: [],

    add_pic: app.imageUrl + 'evaluate/add_pic.png',
    return_apply: app.imageUrl + "accountManager/return_apply.png",
    close_btn: app.imageUrl + 'close.png',
    array: ['拍错了，重新购买', '对商品质量不放心', '买多了', '不想买了'],
    objectArray: [{
        id: 0,
        name: '拍错了，重新购买'
      },
      {
        id: 1,
        name: '对商品质量不放心'
      },
      {
        id: 2,
        name: '买多了'
      },
      {
        id: 3,
        name: '不想买了'
      }
    ],
    returnGoodsPic: [], //退货图片
    returnGoodsDesc: "", //退货描述信息
    returnReason: "", //退货原因
    index: 0
  },
  /**
   * 退货描述
   */
  returnGoodsDescBindInput: function (e) {
    this.setData({
      returnGoodsDesc: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    } else {
      var orderId = options.orderId;
      this.getOrderDetail(orderId);
    }
  },
  getOrderDetail: function (orderId) {
    var that = this;
    wx.request({
      url: app.projectName + '/returnGoods/returnGoodsOrderDetail',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "orderId": orderId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var returnGoodsList = res.data.returnGoodsList;
        that.setData({
          goodsList: returnGoodsList,
        });
      }
    })
  },
  /**
   * 勾选商品操作
   */
  goodsSelectBindTap: function (e) {
    var that = this;
    var goodsList = that.data.goodsList;
    var totalMoney = that.data.totalMoney;
    var index = e.currentTarget.dataset.index;
    var goodsPrice = 0.0;
    goodsList[index].check = !goodsList[index].check;
    that.checkIsAllCheck(goodsList);
    that.calculationReturnGoodsTotalMoney();
    that.setData({
      goodsList: goodsList
    });
  },
  /**
   * 检查是否所有商品全部勾选
   */
  checkIsAllCheck: function (goodsList) {
    var that = this;
    var falg = false;
    for (var i = 0; i < goodsList.length; i++) {
      if (!goodsList[i].check) {
        falg = true;
        break;
      }
    }
    that.setData({
      allCheck: !falg
    });
  },
  /**
   * 全选按钮事件
   */
  allCheckBindTap: function () {
    var that = this;
    var allCheck = that.data.allCheck;
    var goodsList = that.data.goodsList;
    if (allCheck) {
      for (var i = 0; i < goodsList.length; i++) {
        goodsList[i].check = false;
      }
    } else {
      for (var i = 0; i < goodsList.length; i++) {
        goodsList[i].check = true;
      }
    }
    that.calculationReturnGoodsTotalMoney();
    that.setData({
      allCheck: !allCheck,
      goodsList: goodsList
    });
  },
  /**
   * 计算选中的退货商品的价格
   */
  calculationReturnGoodsTotalMoney: function () {
    var that = this;
    var goodsList = that.data.goodsList;
    var goodsPrice = 0.0;
    for (var i = 0; i < goodsList.length; i++) {
      if (goodsList[i].check) {
        goodsPrice += goodsList[i].commodityRejectedOtNum;
      }
    }
    that.setData({
      totalMoney: goodsPrice.toFixed(2)
    });
  },
  /**
   * 减少商品数量
   */
  minusGoodsBindTap: function (e) {
    var that = this;
    var goodsList = that.data.goodsList;
    var totalMoney = that.data.totalMoney;
    var index = e.currentTarget.dataset.index;
    var goodsPrice = 0.0;
    if (goodsList[index].commodityRejectedOtNum <= 1) {
      wx.showToast({
        title: '退货数量不能小于1',
        icon: "none",
        duration: 1500
      })
      return;
    } else {
      goodsList[index].commodityRejectedOtNum--;
    }
    that.setData({
      goodsList: goodsList
    });
    that.calculationReturnGoodsTotalMoney();
  },
  plusGoodsBindTap: function (e) {
    var that = this;
    var goodsList = that.data.goodsList;
    var totalMoney = that.data.totalMoney;
    var index = e.currentTarget.dataset.index;
    var goodsPrice = 0.0;
    if (goodsList[index].commodityRejectedOtNum >= goodsList[index].commodityAlreadySendOtNum) {
      wx.showToast({
        title: '退货数量不能大于发货数量',
        icon: "none",
        duration: 1500
      })
      return;
    } else {
      goodsList[index].commodityRejectedOtNum++;
    }
    that.setData({
      goodsList: goodsList
    });
    that.calculationReturnGoodsTotalMoney();
  },
  /**
   * 退货原因
   */
  bindPickerChange: function (e) {
    var returnReason = this.data.objectArray[e.detail.value].name;
    this.setData({
      returnReason: returnReason
    })
  },
  /**
   * 下一步
   */
  nextToPageBindTap: function () {
    var that = this;
    var goodsList = that.data.goodsList;
    var selectGoodsList = [];
    for (var i = 0; i < goodsList.length; i++) {
      if (goodsList[i].check) {
        selectGoodsList.push(goodsList[i]);
      }
    }
    if (selectGoodsList.length > 0) {
      that.newApplayBindTap(selectGoodsList);
    } else {
      wx.showToast({
        title: '请勾选退货商品',
        icon: "none",
        duration: 1500
      })
    }
  },
  newApplayBindTap: function (selectGoodsList) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/returnGoods/applayReturnGoods',
      data: {
        "goodsList": JSON.stringify(selectGoodsList),
        "returnReason": that.data.returnReason,
        "returnGoodsDesc": that.data.returnGoodsDesc,
        "returnGoodsPic": "",
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "申请退货成功,请等待商家处理",
            icon: 'none',
            duration: 1500,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1500);
            }
          })
        } else {
          wx.showToast({
            title: '申请退货失败',
            icon: 'none',
            duration: 1500
          })
        }
      }
    })
  }
})