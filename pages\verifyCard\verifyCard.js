var app = getApp()
var TimeUtil = require('../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cardBean: [],
    hongbaoImg: app.imageUrl + 'hongbaoImg.png',
    downArrow_grey: app.imageUrl + 'downArrow_grey.png',
    upArrow_grey: app.imageUrl + 'upArrow_grey.png'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var id = options.id;
    this.queryMyHaveCard(id);
  },
  queryMyHaveCard: function (id) {
    var that = this;
    var paramData = {
      "companyId": app.getExtCompanyId(),
      "storeId": app.getExtStoreId(),
      "cardId": id, 
      "odbtoken":app.getodbtoken(),
      "loginToken":app.getloginToken()
    };
    console.log(paramData);
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded', // 默认值
        "odbtoken":app.getodbtoken()
      }, 
      method: "POST",
      url: app.projectName + '/retailCoupon/queryCardDetail',
      data: paramData,
      success: function (res) {
        var cardList = res.data.returnList;
        console.log('card data:');      
        if(cardList && cardList.length > 0){          
          cardList[0].ruleSwitch = false;
          cardList[0].receiveTime = TimeUtil.getSmpFormatDateByLong(cardList[0].receiveTime, true).substring(0, 10).replace(/-/g, ".");;
          that.setData({
            cardBean: cardList[0]
          });
        }        
      }
    })
  },
  // 点击使用规则
  ruleSwitchFun: function (e) {
    var that = this
    var StoreCardListData = this.data.cardBean;
    var ruleSwitchData = StoreCardListData.ruleSwitch;
    if (ruleSwitchData == false) {
      if (typeof (StoreCardListData.rule) == "undefined") {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/retailCoupon/queryCardUseRule',
          data: {
            "cardId": e.currentTarget.dataset.cardid,
            "storeId": app.getExtStoreId(),
          },
          success: function (res) {
            StoreCardListData.ruleSwitch = true;

            StoreCardListData.rule = res.data.cardRule;
            that.setData({
              cardBean: StoreCardListData
            })
          }
        })
      } else {
        StoreCardListData.ruleSwitch = true;
        that.setData({
          cardBean: StoreCardListData
        })
      }

    } else {
      StoreCardListData.ruleSwitch = false;
      that.setData({
        cardBean: StoreCardListData
      })
    }

  },
  goIndexBindTap: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  }
})