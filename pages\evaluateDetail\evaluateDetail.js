//index.js
//获取应用实例
var WxParse = require('../../components/wxParse/wxParse.js');
const app = getApp()
Page({
  data: {
    offSet: 1,
    pageSize: 20,
    commodityId: 0,
    searchLoading: false,
    orderEvaluateList: []
  },
  /**
   * 初始化加载
   */
  onLoad: function(options) {
    var that = this;
    var commodityId = options.goodsId;
    that.setData({
      commodityId: commodityId
    });
    that.dataInitial();
  },
  previewImage: function(e) {
    var that = this;
    var imageUrl = e.currentTarget.dataset.src;
    var imageList = [];
    imageList.push(imageUrl);
    wx.previewImage({
      current: imageUrl,
      urls: imageList
    })
  },
  dataInitial: function() {
    var that = this;
    wx.request({
      url: app.projectName + '/applet/goods/queryAllCommodityRetailEvaluate',
      data: {
        "commodityId": that.data.commodityId,
        "offSet": that.data.offSet,
        "pageSize": that.data.pageSize,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function(res) {
        var evaluateBeansList = res.data.evaluateBeansList;
        var oldevaluateBeansList = that.data.orderEvaluateList;
        if (oldevaluateBeansList != null && oldevaluateBeansList.length > 0) {
          evaluateBeansList = oldevaluateBeansList.concat(evaluateBeansList);
        }
        if (res.data.evaluateBeansList == null || res.data.evaluateBeansList.length == 0) {
          that.setData({
            searchLoading: false
          });
        } else {
          that.setData({
            searchLoading: true
          });
        }
        that.setData({
          orderEvaluateList: evaluateBeansList
        });
      }
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        offSet: that.data.offSet + 1
      });
      that.dataInitial();
    }
  }
})