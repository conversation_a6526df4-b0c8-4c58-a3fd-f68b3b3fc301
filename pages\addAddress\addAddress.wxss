page {
  background: #f5f5f5;
}


.address_box {
  padding: 4rpx 30rpx 4rpx 40rpx;
  background: #fff;
  font-size: 28rpx;
  color: #666;
  display: flex;
  min-height:146rpx;
  max-height:180rpx;
  border-bottom: 1px solid #f6f6f6;
}

.address_box>view:nth-of-type(1){
  width:560rpx;
}

.address_box>view:nth-of-type(2){
  width:170rpx;
  padding-top:20rpx
}
.addressCo{
  font-weight: bold;
  font-size: 30rpx;
  line-height: 40rpx;
  color:#000;
}

.contacts_phone {
  margin-bottom: 10rpx;
  
}

.contacts {
  float: left;
  color: #616161;
  width:120rpx;
}

.phone {
  float: left;
  color: #616161;
}

.address {
  padding-bottom: 10rpx;
  max-height:100rpx;
  min-height:60rpx;
  padding:20rpx 0;
  
}

.address label.title {
  float: left;
  color: #ff5468;
  margin-right: 10rpx;
}

.operate_box {
  font-size: 26rpx;
  line-height: 54rpx;
}


.default_address {
  color: #ff7100;
  font-size: 26rpx;
}

.remove_box {
  float: right;
}

.edit_box {
  text-align: right;
}

.edit_box image, .remove_box image {
  width: 36rpx;
  height: 36rpx;
}

.add_address {
  height: 72rpx;
  width: 100%;
  padding: 20rpx 0;
}

.add_address button {
  margin: 0 20rpx;
  height: 100%;
  background: #4ac15d;
  color: #fff;
  font-size: 28rpx;
  line-height: 72rpx;
}
