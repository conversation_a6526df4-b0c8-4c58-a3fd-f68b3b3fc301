// const app = getApp()
// Page({

//   /**
//    * 页面的初始数据
//    */
//   data: {
//     trade_1: app.imageUrl + 'trade/trade_1.png',
//     trade_2: app.imageUrl + 'trade/trade_2.png',
//     goods_pic: app.imageUrl + 'facial_mask.png',
//     pay_success: app.imageUrl + 'pay_success.png',
//     turntableBanner: app.imageUrl + 'turntableBanner.png',
//     iscjActivity: false
//   },

//   /**
//    * 生命周期函数--监听页面加载
//    */
//   onLoad: function (options) {
//     var that = this;
//     var storeName = options.storeName;
//     var indexWay = options.indexWay;
//     var newOrderBean = JSON.parse(options.newOrderBean);
//     newOrderBean.orderGenerateDate = that.formatTime(newOrderBean.orderGenerateDate, 'Y-M-D h:m:s');
//     var goodsList = JSON.parse(options.goodsList);
//     var exchangeList = JSON.parse(options.exchangeList);
//     that.setData({
//       indexWay: indexWay,
//       newOrderBean: newOrderBean,
//       goodsList: goodsList,
//       exchangeList: exchangeList,
//       storeName: storeName
//     });
//     that.querySupplierSetting(newOrderBean.orderTotalMoney);
//   },
//   querySupplierSetting: function (orderTotalMoney) {   //查询是否有抽奖权限
//     var that = this;
//     wx.request({
//       header: {
//         'content-type': 'application/x-www-form-urlencoded' // 默认值
//       },
//       method: "POST",
//       url: app.projectName + '/newSupplierSetting/querySupplierSetting',
//       data: {
//         "companyId": app.getExtCompanyId(),
//         "type": 1   //支付成功
//       },
//       success: function (res) {
//         if (res.data.flag) {
//           wx.request({
//             header: {
//               'content-type': 'application/x-www-form-urlencoded' // 默认值
//             },
//             method: "POST",
//             url: app.gameProjectName + '/game/gameInfoController/getGameInfo',
//             data: {
//               "merchantId": app.getExtCompanyId(),
//                      "odbtoken":app.getodbtoken(),
 //       "loginToken":app.getloginToken(),
//               "sceneType": 1,
//             },
//             success: function (res) {
//               if (res.data.errorcode == 1000) {
//                 var money = res.data.result.money;
//                 if (parseFloat(orderTotalMoney) >= parseFloat(money)) {
//                   that.setData({
//                     iscjActivity: true
//                   })
//                 }
//               }
//             }
//           })
//         }
//       }
//     })
//   },
//   formatNumber: function (n) {
//     n = n.toString()
//     return n[1] ? n : '0' + n
//   },
//   formatTime: function (number, format) {
//     var that = this;
//     var formateArr = ['Y', 'M', 'D', 'h', 'm', 's'];
//     var returnArr = [];

//     var date = new Date(number);
//     returnArr.push(date.getFullYear());
//     returnArr.push(that.formatNumber(date.getMonth() + 1));
//     returnArr.push(that.formatNumber(date.getDate()));

//     returnArr.push(that.formatNumber(date.getHours()));
//     returnArr.push(that.formatNumber(date.getMinutes()));
//     returnArr.push(that.formatNumber(date.getSeconds()));

//     for (var i in returnArr) {
//       format = format.replace(formateArr[i], returnArr[i]);
//     }
//     return format;
//   },
//   /**
//    * 用户点击右上角分享
//    */
//   onShareAppMessage: function (res) {
//     var that = this;
//     if (res.from === 'button') {
//       // 来自页面内转发按钮
//     }
//     return {
//       title: app.storeName,
//       path: '/pages/index/index?recommendId=' + app.getUserId() + '&recommendAccount=' + app.getLoginAccount() + '&recommendName=' + app.getLoginName() + '&identity=' + app.getIdentity(),
//       imageUrl: app.shareImageUrl,
//       success: function (res) {
//         // 转发成功
//       },
//       fail: function (res) {
//         // 转发失败
//       }
//     }
//   },
//   goIndex: function () {
//     wx.switchTab({
//       url: '/pages/index/index'
//     });
//   },
//   goOrder: function () {
//     app.redirectToPage('/pages/indexThree/indexThree');
//   },
//   gocjBindTap: function () {
//     app.reLaunchToPage('/pages/turntableActivity/turntableActivity?sceneType=1');
//   }
// })
const app = getApp();
const http = require('../../utils/http')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    backpage_pay: app.imageUrl + 'backpage_pay.png'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var eventType=options.eventType;
    var minMoney=0;
    if(options.minMoney){
      minMoney=options.minMoney
    }
    /*http.post({
      urlName: 'activity',
      url: 'config/loadConfigList',
      showLoading: false,
      data: {
        eventType:1,
        minMoney:minMoney,
        merchantId: app.getExtCompanyId(),
      },
      success: (res) => {
          for(var i=0;i<res.length;i++){
            res[i]["checked"]=true;
            if (res[i].rightsType == 4 || res[i].rightsType == 5 || res[i].rightsType == 6){
              that.toUser(res[i].configId);
            }
          }
          that.setData({
            result:res
          })       
      }
    })*/
  },
  /*给用户发放优惠券等*/
  toUser: function (configId){
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.errorcode == '1000') {
          console.log("获取成功！")
        }
      }
    })
  },
  goIndex: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  goOrder: function () {
    app.redirectToPage('/pages/indexThree/indexThree');
  },
  gocjBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData
    })
    app.navigateToPage('/pages/turntableActivity/turntableActivity?gameId='+e.currentTarget.dataset.gameid);
  },
  goSignBindTap:function(e){
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length;i++){
      if (resData[i].configId == configId){
        if (resData[i]["checked"] == false){
          return;
        }
        else{
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData
    })
    app.navigateToPage('/pages/sign/sign?configId='+configId);
  },
  goWordBindTap:function(e){
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData
    })
    app.navigateToPage('/pages/collect/collect?configId=' + configId);
  }
})