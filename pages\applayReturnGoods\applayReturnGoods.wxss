page {
  background: #f4f4f4;
}

.tab-h {
  height: 80rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 80rpx;
  background: #fff;
  font-size: 14px;
  white-space: nowrap;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  border-bottom: 1px solid #dedede;
}

.tab-item {
  width: 18%;
  margin: 0 1%;
  text-align: center;
  display: inline-block;
}

.tab-item.active {
  color: #FF7E00;
  position: relative;
}

.tab-item.active:after {
  content: "";
  display: block;
  height: 2px;
  width: 100%;
  background: #FF7E00;
  position: absolute;
  bottom: 0;
  left: 5rpx;
  border-radius: 16rpx;
}

.item-ans {
  width: 100%;
  display: flex;
  flex-grow: row no-wrap;
  justify-content: space-between;
  padding: 30rpx;
  box-sizing: border-box;
  height: 180rpx;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  position: relative;
  padding-right: 30rpx;
}

.avatar .img {
  width: 100%;
  height: 100%;
}

.avatar .doyen {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  bottom: -2px;
  right: 20rpx;
}

.expertInfo {
  font-size: 12px;
  flex-grow: 2;
  color: #b0b0b0;
  line-height: 1.5em;
}

.expertInfo .name {
  font-size: 16px;
  color: #000;
  margin-bottom: 6px;
}

.askBtn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 14px;
  border-radius: 60rpx;
  border: 1px solid #4675f9;
  color: #4675f9;
}

.tab-content {
  margin-top: 80rpx;
}

.scoll-h {
  height: 100%;
}

/**没有订单**/

.order_none {
  width: 160px;
  margin: 20px auto;
  display: block;
}

/**待评价**/

.evaluate_bg {
  margin-bottom: 20px;
  width: 100%;
}

.single_order {
  width: 94%;
  padding: 0 3%;
  margin-bottom: 10px;
  background: #fff;
}

.single_order:after, .order_top:after {
  clear: both;
  content: "";
  height: 0;
  display: block;
}

.order_top {
  width: 100%;
  height: 40px;
  line-height: 40px;
  color: #515151;
  font-size: 13px;
  display: block;
  border-bottom: 1px solid #ececec;
}

.order_top image {
  margin-top: 10px;
}

.order_remove {
  width: 20px;
  height: 20px;
  float: right;
}

.goods_detail {
  width: 100%;
  height: 90px;
  /**border-top: 1px solid #ececec;**/
  border-bottom: 1px solid #ececec;
}

.goods_pic {
  height: 70px;
  width: 70px;
  margin-top: 10px;
  float: left;
}

.right_detail {
  padding-left: 85px;
  height: 100%;
}

.right_top {
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: block;
  padding-top: 15px;
  font-size: 15px;
}

.goods_name {
  color: #2c2c2c;
  /**width: 100%;**/
  width: 76%;
  display: block;
  float: left;
  overflow: hidden;
  height: 30px;
}

.package_number {
  float: right;
  color: #424242;
  width: 24%;
  text-align: right;
}

.right_bottom {
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: block;
}

.goods_tips {
  color: #78797b;
  font-size: 13px;
}

.goods_number {
  float: right;
  font-size: 13px;
}

.goods_static {
  float: right;
  color: #FF7E00;
  font-size: 13px;
}

.detail_box {
  width: 100%;
  height: 60px;
}

.order_time {
  float: right;
  color: #515151;
  font-size: 13px;
  line-height: 30px;
  width: 100%;
  display: block;
  text-align: right;
}

.order_detail {
  float: right;
  color: #2c2c2c;
  font-size: 14px;
  line-height: 30px;
  display: block;
  width: 100%;
  text-align: right;
}

.order_detail text {
  color: #515151;
  font-size: 12px;
  float: right;
}

.button_box {
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: right;
}

.button_box button {
  width: 84px;
  height: 34px;
  display: inline-block;
  font-size: 13px;
  /**color: #3b3b3b;
  border: 1px solid #8b8b8b;**/
  color: #000;
  border: 1px solid #000;
  margin-top: 8px;
  margin-left: 5px;
  padding-left: 10px;
  padding-right: 10px;
  background: #fff;
}

.button2 {
  color: #FF7E00 !important;
  border: 1px solid #FF7E00 !important;
}
