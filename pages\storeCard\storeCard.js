  var app = getApp();
  Page({

    /**
     * 页面的初始数据
     */
    data: {
      telephone: "",
      smsCode: "", //短信验证码
      second: 60, //倒计时秒数
      secondDesc: "获取短信验证码",
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function(options) {
      this.setData({
        telephone: app.getTelephone()
      });
    },
    accountBindInput: function(e) {
      this.setData({
        telephone: e.detail.value
      })
    },
    smsBindInput: function(e) {
      this.setData({
        smsCode: e.detail.value
      })
    },
    /**
     * 点击获取短信验证码
     */
    smsBindTap: function() {
      var that = this;
      var telephone = that.data.telephone.replace(/\s+/g, '');
      if (telephone.length == 0) {
        wx.showToast({
          title: '手机号不能为空',
          icon: 'success',
          duration: 1000,
          mask: true
        })
        return false;
      }
      if (telephone.length < 11) {
        wx.showToast({
          title: '手机号有误',
          icon: 'success',
          duration: 1000,
          mask: true
        })
        return false;
      }
      var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
      if (!myreg.test(telephone)) {
        wx.showToast({
          title: '手机号有误',
          icon: 'success',
          duration: 1000,
          mask: true
        })
        return false;
      }
      wx.request({
        url: app.projectName + '/applet/querySMSCode',
        data: {
          "type": "4",
          "telephone": telephone,
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        success: function(res) {
          var flag = res.data.flag;
          var message = res.data.message;
          if (flag) {
            that.countdown(that);
          } else {
            app.showModal({
              title: '提示',
              content: message == "" ? "系统异常，稍后在试" : message
            });
            return;
          }
        }
      })
    },
    /**
     * 倒计时开始
     */
    countdown: function(that) {
      var second = that.data.second;
      if (second == 0) {
        that.setData({
          secondDesc: "获取短信验证码",
          second: 60,
          isSend: false
        });
        return;
      }
      var time = setTimeout(function() {
        that.setData({
          second: second - 1,
          secondDesc: second + "秒后重新获取"
        });
        that.countdown(that);
      }, 1000)
    },
    submitQueryResultBindTap: function() {
      var that = this;
      var telephone = that.data.telephone.replace(/\s+/g, '');
      var smsCode = that.data.smsCode.replace(/\s+/g, '');
      if (telephone.length == 0) {
        wx.showToast({
          title: '手机号不能为空',
          icon: 'success',
          duration: 1000,
          mask: true
        })
        return false;
      }
      if (telephone.length < 11) {
        wx.showToast({
          title: '手机号有误',
          icon: 'success',
          duration: 1000,
          mask: true
        })
        return false;
      }
      var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
      if (!myreg.test(telephone)) {
        wx.showToast({
          title: '手机号有误',
          icon: 'success',
          duration: 1000,
          mask: true
        })
        return false;
      }
      if (smsCode.length == 0) {
        wx.showToast({
          title: '请输入验证码',
          duration: 1000,
          icon: 'success',
          mask: true
        })
        return false;
      }
      wx.request({
        url: app.projectName + '/vipCard/checkUnderTheLineIsHaveVipCard',
        data: {
          "type": "4",
          "smsCode": smsCode,
          "vipTelephone": telephone,
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        success: function(res) {
          var resultCode = res.data.resultCode;
          var message = res.data.message;
          if (resultCode == 0) {
            app.redirectToPage('/pages/queryoffLineCard/queryoffLineCard?telephone=' + telephone)
          } else {
            app.showModal({
              title: '提示',
              content: message == "" ? "系统异常，稍后在试" : message
            });
          }
        }
      })
    }
  })