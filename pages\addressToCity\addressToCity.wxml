<block wx:if="{{regionSwitch}}">
	<view class='location_box' style='height:80rpx;'>
		<label bindtap="goShowCity">
			<label style="width:80rpx;height:40rpx;overflow:hidden;float:left;margin-left:20rpx;font-size:26rpx;">{{localUserCity}}</label>
			<label style="float:left;margin-left:4rpx;font-size:26rpx;transform:rotate(90deg)">></label>
		</label>
		<icon type="search" size='16' color='#666' />
		<input placeholder='请输入您的当前地址' bindinput="localAddressBindInput" style="width:60%;"></input>
	</view>
	<view style="padding-top:100rpx;width:90%;margin-left:5%">
		<view style="font-size:26rpx;padding-top:20rpx;color:#666;">
			<image src="{{myAddress}}" style="width:20rpx;float:left;margin-top:4rpx;margin-right:10rpx;" mode="widthFix"></image>
			附近地址
		</view>
		<view class="nearAdd">
			<block wx:for="{{searchData}}" wx:for-item="s" wx:key="">
				<block wx:if="{{queryType==1}}">
					<view bindtap="selectLocatAddressBindTap" data-province="{{s.ad_info.province}}" data-city="{{s.ad_info.city}}" data-area="{{s.ad_info.district}}" data-address="{{s.address+s.title}}" data-latitude="{{s.location.lat}}" data-longitude="{{s.location.lng}}" style="padding:10rpx 0;">
						<view>{{s.title}}</view>
						<view class="addDetail">{{s.address}}</view>
					</view>
				</block>
				<block wx:elif="{{queryType==2}}">
					<view bindtap="selectLocatAddressBindTap" data-province="{{s.province}}" data-city="{{s.city}}" data-area="{{s.district}}" data-address="{{s.address+s.title}}" data-latitude="{{s.location.lat}}" data-longitude="{{s.location.lng}}" style="padding:10rpx 0;">
						<view>{{s.title}}</view>
						<view class="addDetail">{{s.address}}</view>
					</view>
				</block>
			</block>
		</view>
	</view>
</block>
<block wx:else>
	<view class="account_detail">
		<label>
			<text>选择地址:</text>
			<picker mode="region" bindchange="bindRegionChange" value="{{region}}">
				<view class="picker">
					{{region[0]}} {{region[1]}} {{region[2]}}
				</view>
			</picker>
		</label>
		<label style="height:240rpx;">
			<text>详细地址:</text>
			<textarea style="font-size:28rpx;width:500rpx;height:240rpx;line-height:90rpx;" value='{{detailAddress}}' bindinput='addressBindInput'></textarea>
		</label>
		<label>
			<text>门牌号</text>
			<input type='text' bindinput='houseNumberBindInput' value='{{houseNumber}}' placeholder='例:5号楼203室'></input>
		</label>
	</view>
	<view class='add_address'>
		<button bindtap='saveAddressBindTap'>
			确定
		</button>
	</view>
</block>