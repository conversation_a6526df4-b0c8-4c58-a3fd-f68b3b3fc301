<!-- <view class='successful_trade'>
  交易成功
</view> -->
<!-- <view class="successBox">
	<view style="width:160rpx;height:160rpx;margin:0 auto 20rpx;padding-top:40rpx">
		<image src="{{pay_success}}" style="width:160rpx;height:160rpx;"></image>
	</view>
	<view style="font-size:32rpx;">支付成功</view>
	<view style="display:flex;justify-content: center;margin-top:20rpx">
		<view class="sucBtn" style="margin-right:20rpx;" bindtap="goIndex">返回首页</view>
		<view class="sucBtn" bindtap="goOrder">查看订单</view>
	</view>
</view>

<block wx:if="{{iscjActivity}}">
	<view style="width:710rpx;height:140rpx;margin:20rpx auto;" bindtap="gocjBindTap">
		<image src="{{turntableBanner}}" style="width:710rpx;" mode="widthFix"></image>
	</view>
</block>
<xm-ad data-id="xmc7cc06ec297986b8550d1357bddc1a" adData="{{adData}}" class="xmc7cc06ec297986b8550d1357bddc1a"
	size="small" bindadLoad="adLoad" bindadClick="adClick" bindadClose="adClose"></xm-ad>

<view style="background-color:#fff;width:710rpx;margin:20rpx auto;border-radius:10rpx">
	<view class='trade_box'>
		<image class='trade_1' src='{{trade_1}}'></image>
		<text class='company_name'>{{storeName}}</text>
		<text class='order_time'>{{newOrderBean.orderGenerateDate}}</text>
	</view>
	<view class='trade_box2' hidden="{{indexWay==1?false:true}}">
		<image class='trade_2' src='{{trade_2}}'></image>
		<label class='trade_top'>
			<text class='consignee'>收货人:{{newOrderBean.receiverName}}</text>
			<text class='phone'>{{newOrderBean.receiveContactNum}}</text>
		</label>
		<label class='address'>收货地址：{{newOrderBean.receiveAddress}}</label>
	</view>
	<block wx:key="unique" wx:for="{{goodsList}}" wx:for-item="goods">
		<view class='goods_box'>
			<image class='goods_pic' src='{{goods.commodityMainPic==""?goods_pic:goods.commodityMainPic}}'></image>
			<label class='right_box'>
				<view class='goods_name'>
					<view class="textName">{{goods.commodityName}}</view>
					<view style="float:right;margin-right:10rpx">
						<text>X</text>
						<text hidden="{{goods.commodityOmNum>0?false:true}}"
							style="margin-right:10rpx">{{goods.commodityOmNum}}{{goods.commodityOmUnit}}</text>
						<text
							hidden="{{goods.commodityOtNum>0?false:true}}">{{goods.commodityOtNum}}{{goods.commodityOtUnit}}</text>
					</view>
				</view>
				<view class='goods_price' style="margin-top:20px;color:#FF7E00">
					<text
						hidden="{{goods.commodityOmNum>0?false:true}}">¥{{goods.commoditySaleOmPrice}}/{{goods.commodityOmUnit}}</text>
					<text
						hidden="{{goods.commodityOtNum>0?false:true}}">¥{{goods.commoditySaleOtPrice}}/{{goods.commodityOtUnit}}</text>
				</view>
			</label>
		</view>
	</block>
	<block wx:key="unique" wx:for="{{exchangeList}}" wx:for-item="goods">
		<block wx:if="{{goods.actualExchangeNum > 0}}">
			<block wx:if="{{goods.select}}">
				<view class='goods_box'>
					<image class='goods_pic' src='{{goods.commodityPic}}'></image>
					<label class='right_box'>
						<text class='goods_name'>{{goods.commodityName}}</text>
						<view class='goods_price'>
							<text>¥{{goods.commoityExchangePrice}}</text>x{{goods.actualExchangeNum}}</view>
					</label>
				</view>
			</block>
		</block>
	</block>
	<view class='order_all'>合计:
		<text>¥{{newOrderBean.orderTotalMoney}}</text>
	</view>
</view> -->
<view class="eval_one">
	<view
		class="eval_two">
		<view class="eval_three">
			<view class="eval_four">
				<image src="{{backpage_pay}}" class="eval_five" mode="widtnFix"></image>
			</view>
			<view
				class="eval_six">
				支付成功</view>
			<view class="eval_seven">
				<view style="width:45%;">
					<view
						class="eval_eight"
						bindtap="goIndex">
						返回首页</view>
				</view>
				<view style="width:10%"></view>
				<view style="width:45%;">
					<view
						class="eval_nine"
						bindtap="goOrder">
						查看订单</view>
				</view>
			</view>

		</view>
		<view class="eval_ten" wx:if="{{result.length>0}}">
			<view class="eval1_one">
				— 商家赠送以下权益 —</view>
			<view wx:for="{{result}}" wx:key="index" class="reward_two eval1_two">
				<view class="reward_three">
					<view class="reward_dashed">
						<image class="reward_img" src="{{item.imagesUrl}}" mode="widthFix"></image>
					</view>
				</view>
				<view style="width:65%;">
					<view class="reward_five">
						{{item.businessName}}
					</view>
					<view class="reward_six">
						{{item.prizeExplain}}
					</view>
				</view>
				<view class="reward_seven eval1_three" >
          <block wx:if="{{item.rightsType == 1}}">
            <view data-configid="{{item.configId}}" class="eval1_four {{item.checked==true?'':'grey_b'}}" bindtap="goSignBindTap">{{item.checked==true?'去领取':'已领取'}}</view>
          </block>
          <block wx:elif="{{item.rightsType == 2}}">
            <view class="eval1_four {{item.checked==true?'':'grey_b'}}" data-configid="{{item.configId}}" data-gameid="{{item.prizeId}}" bindtap="gocjBindTap">{{item.checked==true?'去抽奖':'已抽奖'}}</view>
          </block>
          <block wx:elif="{{item.rightsType == 3}}">
            <view data-configid="{{item.configId}}" class="eval1_four {{item.checked==true?'':'grey_b'}}" bindtap="goWordBindTap">{{item.checked==true?'去领取':'已领取'}}</view>
          </block>
          <block wx:elif="{{item.rightsType == 4}}">
            <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
          </block>
          <block wx:elif="{{item.rightsType == 5}}">
            <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
          </block>
          <block wx:elif="{{item.rightsType == 6}}">
            <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
          </block>
					<!--<view class="eval1_four" 	wx:if="{{item.rightsType==2}}" data-gameid="{{item.prizeId}}" bindtap="gocjBindTap">去抽奖</view>
					<view class="eval1_five" wx:else >
						已发放
          </view>	-->								
				</view>
			</view>
		</view>
	</view>

</view>