<wxs src="../../wxs/subutil.wxs" module="tools" />
<view style="padding-bottom:140rpx;">
	<view style="width:100%;">
		<!-- 配送方式 -->		
		<view class="pay_way clearfix" style="padding-top:10rpx;margin-bottom:20rpx;font-size:28rpx;">
			  <text style="float:left;line-hieght:70rpx;margin-top:20rpx;">配送方式</text>
			  <block wx:if="{{deliveryOne}}">
					<view style="float:left;padding-left:30rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="1">
						<label class="{{index==1?'de_checked':'de_unchecked'}}">
							<image src="{{shop_cart}}"></image>
						</label>
						<label style="vertical-align:top;margin-top:20rpx;line-height:70rpx;color:#666;font-size:26rpx;">
							商家配送
						</label>
					</view>
				</block>
				<block wx:if="{{deliveryTwo}}">
					<view style="float:left;padding-left:30rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="2">
						<label class="{{index==2?'de_checked':'de_unchecked'}}">
							<image src="{{shop_cart}}"></image>
						</label>
						<label style="vertical-align:top;line-height:70rpx;color:#666;font-size:26rpx;">
							门店自提
						</label>
					</view>
				</block>
        <block wx:if="{{deliveryThree}}">
					<view style="float:left;padding-left:30rpx;line-hieght:70rpx;" bindtap="switchNav" data-tab="3">
						<label class="{{index==3?'de_checked':'de_unchecked'}}">
							<image src="{{shop_cart}}"></image>
						</label>
						<label style="vertical-align:top;line-height:70rpx;color:#666;font-size:26rpx;">
							物流配送
						</label>
					</view>
				</block>
		</view>
		<!-- 选择门店 -->
		<block wx:if="{{index==1}}">
				<view class='add_customer' data-select="{{isSelectStore}}" bindtap="chooseStore">
					<view style="font-size:28rpx;color:#000;">配送门店</view>
					<block wx:if="{{chooseStoreValue!=''}}">
						<text style="color:#666;">{{chooseStoreValue}}</text>
					</block>
					<block wx:else>选择门店</block>
					<image style="width:76rpx;float:right;height:38rpx;" mode="widthFix" lazy-load='true'
						hidden="{{!isSelectStore}}" src='{{store_personal_more}}'>
					</image>
					<view>{{storeAddress}}</view>
				</view>
		</block>
		<block wx:if="{{index==2}}">
			<view class='add_customer' bindtap="chooseStore" data-select="true">
					<view style="font-size:28rpx;color:#000;">自取门店</view>
					<block wx:if="{{chooseStoreValue!=''}}">
						<text style="color:#666;">{{chooseStoreValue}}</text>
					</block>
					<block wx:else>选择门店</block>
					<image lazy-load='true' style="width:76rpx;float:right;height:38rpx;" mode="widthFix"
						src='{{store_personal_more}}'>
					</image>
					<view>{{storeAddress}}</view>
			</view>
		</block>
		<!-- 自取人信息 -->
		<view class="leave_word clearfix" hidden='{{index==2?false:true}}'>
			<view style="font-size:28rpx;color:#000;height:56rpx;line-height:56rpx;">自取信息</view>
			<view class="clearfix" style="font-size:28rpx;padding:8rpx 0;">
				<text style="float:left;font-size:28rpx;color:#333;">姓名</text>
				<input bindinput="pickOrderUserNameBindInput" style="float:left;padding-left:20rpx;line-height: 52px;"
					maxlength="20" placeholder="请输入姓名" value="{{pickOrderUserName}}"></input>
			</view>
			<view class="clearfix" style="font-size:28rpx;padding:8rpx 0;">
				<text style="float:left;color:#333;">手机</text>
				<input maxlength="11" bindinput="pickOrderContactNumBindInput" value="{{pickOrderContactNum}}"
					style="float:left;padding-left:20rpx;" placeholder="请输入手机"></input>
			</view>
		</view>
		<!-- 期望取货日期 -->
		<view class="add_customer" style="padding-bottom:70rpx;" hidden='{{index==2?false:true}}'>
			<!-- add_customer -->
			<view style="font-size:28rpx;color:#000;">自取日期</view>
			<picker bindchange="bindDateChange" range="{{DateArray}}">
				<view class="selectArea" style="color:#333;font-size:26rpx;">
					{{DateArray[date_index]}}
					<!-- <label class="icondirect">︿</label> -->
					<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
				</view>
			</picker>
			<picker bindchange="bindChooseTimeChange" range="{{pickTimeArray}}">
				<view class="selectArea" style="color:#333;font-size:26rpx;padding-left:5%;">
					{{pickTimeArray[time_index]}}
					<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
				</view>
			</picker>
		</view>
		<!-- 期望取货日期 -->
		<!-- 添加地址 -->
		<view class='add_address' bindtap='selectAddressBindTap' hidden='{{index==2?true:false}}'>
			<view style="font-size:28rpx;color:#000;">收货地址</view>
			<block wx:if="{{receiveAddress!=''}}">
				<view style="color:#666;font-size:26rpx;">
					<text>姓名：{{username}}</text>
					<text style="margin-left:10px;">电话 {{telephone}}</text>
				</view>
				<view style="color:#666;font-size:26rpx;">{{receiveAddress}}</view>
			</block>
			<block wx:else>
				{{noneAddressDesc}}
				<label style='transform:rotate(90deg); padding-top: 10px;'>︿</label>
			</block>
		</view>
		<!-- 添加地址 -->
		<!-- 单品 -->
		<view style="background:#fff;font-size:28rpx;color:#000;padding:15rpx 0 10rpx 15rpx;">
			订单详情
		</view>
		<!-- 换购start -->
		<view hidden='{{exchangeCommodityList.length>0?false:true}}'>
			<view class="exchangeBox" bindtap="exchangeGoods">
				<view>您已满足换购条件</view>
				<view style="color:#FF7E00;">去换购></view>
			</view>
			<view style="display:flex;background-color: #FEF8F4;" bindtap="exchangeGoods">
				<view class="exchangeGoods">
					<block wx:for="{{exchangeCommodityList}}" wx:for-item="egoods" wx:key wx:if="{{index<2}}">
						<view class="exchangeGoodsLi">
							<view style="width:72rpx;height:72rpx;margin-top:10rpx">
								<image src="{{egoods.commodityPic}}" style="width:72rpx;height:72rpx;"></image>
							</view>
							<view style="margin-left:10rpx;">
								<view
									style="width:68rpx;height:32rpx;line-height:32rpx;background-color:#FF7E00;color:#fff;font-size:20rpx;text-align:center">
									换购</view>
								<view class="exchangeGoodsName">{{egoods.commodityName}}</view>
								<view class="exchangeGoodsPrice">¥{{egoods.commoityExchangePrice}}<text
										style="color:#919398;font-size:22rpx;text-decoration: line-through;">¥{{egoods.commodityOldPrice}}</text>
								</view>
							</view>
						</view>
					</block>

				</view>
				<view style="color:#FF7E00;font-size:24rpx;line-height:120rpx">查看更多</view>
			</view>
		</view>
		<!-- 换购end -->

		<block wx:for="{{goodsList}}" wx:for-item="goods" wx:for-index="goodsIndex" wx:key="">
			<block wx:if="{{goodsIndex<=3}}">
				<view class='single_box clearfix'>
					<image src='{{goods.commodityMainPic}}'></image>
					<view class="product_detail">
						<view style='width:100%;height:20px;font-size:30rpx;' class="clearfix">
							<label class='single_title'>{{goods.commodityName}}</label>
							<label style='float:right;text-align:right;'
								hidden="{{goods.commoditySendOmNum>0?false:true}}">{{goods.commoditySendOmNum}}{{goods.commodityOmUnit}}</label>
							<label style='float:right;text-align:right;margin-right:10rpx;'
								hidden="{{goods.commoditySendOtNum>0?false:true}}">{{goods.commoditySendOtNum}}{{goods.commodityOtUnit}}</label>
							<label style='float:right;text-align:right;margin-right:10rpx;'>X</label>
						</view>
						<view class='classify_box'>
							<block wx:for="{{goods.skuUnitList}}" wx:for-item="sku" wx:key="" wx:for-index="skuIdx">
								<block wx:if="{{goods.skuUnitList.length-1==skuIdx}}">
									{{sku.skuName}}:{{sku.skuValue}}
								</block>
								<block wx:else>{{sku.skuName}}:{{sku.skuValue}};</block>
							</block>
						</view>
						<view class='clearfix plus_minus'>
							<block wx:if="{{goods.commodityType==4}}">
								<label class='goods_number'>赠品</label>
							</block>
							<block wx:else>
								<label class='goods_number'>
									<label
										hidden="{{goods.commoditySendOmNum>0?false:true}}">￥{{tools.sub.formatAmount(goods.commoditySendOmPrice,2)}}
										<block wx:if="{{goods.skuUnitList.length==0}}">/{{goods.commodityOmUnit}}</block>
									</label>
									<label style="margin-left:4px;"
										hidden="{{goods.commoditySendOtNum>0?false:true}}">￥{{tools.sub.formatAmount(goods.commoditySendOtPrice,2)}}
										<block wx:if="{{goods.skuUnitList.length==0}}">/{{goods.commodityOtUnit}}</block>
									</label></label>
								<block wx:if="{{goods.commodityLargessNum>0}}">
									<label class='goods_number'>
										包含赠品{{goods.commodityLargessNum}}{{goods.commodityLargessUnit}}
									</label>
								</block>
							</block>
						</view>
					</view>
				</view>
			</block>
			<block wx:if="{{goodsIndex>3}}">
				<view class='single_box clearfix' hidden="{{moreGoodsHidden}}">
					<image src='{{goods.commodityMainPic}}'></image>
					<view class="product_detail">
						<view style='width:100%;height:20px;font-size:30rpx;' class="clearfix">
							<label class='single_title'>{{goods.commodityName}}</label>
							<label style='float:right;text-align:right;'
								hidden="{{goods.commoditySendOmNum>0?false:true}}">{{goods.commoditySendOmNum}}{{goods.commodityOmUnit}}</label>
							<label style='float:right;text-align:right;margin-right:10rpx;'
								hidden="{{goods.commoditySendOtNum>0?false:true}}">{{goods.commoditySendOtNum}}{{goods.commodityOtUnit}}</label>
							<label style='float:right;text-align:right;margin-right:10rpx;'>X</label>
						</view>
						<view class='classify_box'>
							<block wx:for="{{goods.skuUnitList}}" wx:for-item="sku" wx:key="" wx:for-index="skuIdx">
								<block wx:if="{{goods.skuUnitList.length-1==skuIdx}}">
									{{sku.skuName}}:{{sku.skuValue}}
								</block>
								<block wx:else>{{sku.skuName}}:{{sku.skuValue}};</block>
							</block>
						</view>
						<view class='clearfix plus_minus'>
							<block wx:if="{{goods.commodityType==4}}">
								<label class='goods_number'>赠品</label>
							</block>
							<block wx:else>
								<label class='goods_number'>
									<label
										hidden="{{goods.commoditySendOmNum>0?false:true}}">￥{{tools.sub.formatAmount(goods.commoditySendOmPrice,2)}}
										<block wx:if="{{goods.skuUnitList.length==0}}">/{{goods.commodityOmUnit}}</block>
									</label>
									<label style="margin-left:4px;"
										hidden="{{goods.commoditySendOtNum>0?false:true}}">￥{{tools.sub.formatAmount(goods.commoditySendOtPrice,2)}}
										<block wx:if="{{goods.skuUnitList.length==0}}">/{{goods.commodityOtUnit}}</block>
									</label>
								</label>
								<block wx:if="{{goods.commodityLargessNum>0}}">
									<label class='goods_number'>
										包含赠品{{goods.commodityLargessNum}}{{goods.commodityLargessUnit}}
									</label>
								</block>
							</block>
						</view>
					</view>
				</view>
			</block>
		</block>
		<view class="moreWrap" style="">
			<block wx:if="{{goodsList.length>4}}">
				<view hidden="{{!moreGoodsHidden}}" bindtap="queryMoreGoodsBindTap">
					<label class="icondirect"
						style="vertical-align:bottom;transform:rotate(180deg);margin-right:20rpx;display:inline-block;margin-top:10rpx;">
						︿
					</label>
					<label style="vertical-align:top;">查看更多商品</label>
				</view>
			</block>
			<view hidden="{{moreGoodsHidden}}" bindtap="hideQueryMoreGoodsBindTap">
				<label style="vertical-align:top;margin-right:20rpx;display:inline-block;">︿</label>
				<label style="vertical-align:top;">收起</label>
			</view>
		</view>
		<!-- 单品 -->
	</view>
	<!-- 超值换购 -->
	<!-- <view class='changeView' hidden='{{exchangeCommodityList.length>0?false:true}}'>
		<label class='changeLable'>超值换购</label> -->
	<!-- 换购商品 -->
	<!-- <block wx:for="{{ exchangeCommodityList}}" wx:for-item="egoods" wx:key="">
			<view class='changeGoodsView'>
				<image class='changeGoodsImage' src='{{egoods.commodityPic}}'></image>
				<view class='changeGoodschildView'>
					<label class='changeGoodsName'>{{egoods.commodityName}}</label>
					<label class='changeGoodsNumber'>
						{{egoods.commoditySaletotal}}人换购
						<block wx:if="{{egoods.skuAttrStr!=''}}">({{egoods.skuAttrStr}})</block>
					</label>
					<view style='width:100%; height:30px;'>
						<view style='float:left; width:50%;'>
							<label class='changeGoodsPrice'>¥{{egoods.commoityExchangePrice}}</label>
							<label class='changeGoodsPrice1'>¥{{egoods.commodityOldPrice}}</label>
						</view> -->
	<!-- 加满商品 -->
	<!-- <view style='float:right;' hidden='{{!egoods.select}}'>
							<view class='goodsreduce' data-skuId='{{egoods.skuId}}' data-id='{{egoods.commodityId}}' bindtap='goodsReduceBindTap'>
								-
							</view>
							<label class='goodsNumber'>{{egoods.actualExchangeNum}}</label>
							<view class='goodsplus' data-skuId='{{egoods.skuId}}' data-id='{{egoods.commodityId}}' bindtap='goodsPlusBindTap'>
								+
							</view>
						</view> -->
	<!-- 加满商品 -->
	<!-- 换购按钮 -->
	<!-- <view style='float:right;' hidden='{{egoods.select}}'>
							<button class='changeGoodsBtn' data-skuId='{{egoods.skuId}}' data-id='{{egoods.commodityId}}' bindtap='changePurchaseBindTap'>
								换购
							</button>
						</view> -->
	<!-- 换购按钮 -->
	<!-- </view>
				</view>
			</view>
		</block> -->
	<!-- 换购商品 -->
	<!-- 小计 -->
	<!-- <view class='planView' hidden='{{changePurchaseHidden}}'>
			小计
			<label class='planLabel'>¥{{changeGoodsPriceTotal}}</label>
		</view> -->
	<!-- 小计 -->
	<!-- </view> -->
	<!-- 超值换购 -->
	<!-- 商品金额 -->
	<view style="padding-bottom:20rpx;">
		<view class='goods_sum'>
			商品金额
			<label>￥{{tools.sub.formatAmount(orderCommodityTotalMoney,2)}}</label>
		</view>
		<view hidden="{{isShowCardHidden}}" class='add_customer' style="margin-top:0px" bindtap="useCouponBind">
			优惠券
			<!-- <label class="icondirect">︿</label> -->
			<image lazy-load='true' class='personal_more' style="margin-top:6px" src='{{personal_more}}'></image>
			<label>{{cardDesc}}</label>
		</view>
		<!-- 余额支付 -->
		<view style="background:#fff;font-size:28rpx;color:#000;padding-left:15rpx;padding-top:15rpx;line-height: 60rpx;">
			支付方式</view>
		<view class="add_customer"
			style="background:#fff;font-size:28rpx;color:#000;margin-top:0;margin-bottom:20rpx;padding-left:15rpx;padding-top:15rpx;">
			<block wx:if="{{vipCardBalance>0}}">
				<block wx:if="{{vipCardPay==1}}">
					<view style="display:flex;padding:0 20rpx;border-bottom:1px solid #F7F8F9">
						<view style="width:560rpx;">
							<view>
								<image src="{{vipPay}}" style="width:38rpx;height:30rpx;vertical-align: middle;"></image>
								<text style="padding-left:10rpx;">会员卡支付</text>
							</view>
							<view style="font-size: 24rpx;color: #919398;padding-left:48rpx;">会员卡余额：¥{{vipCardBalance}}</view>
						</view>
						<view style="width:150rpx;padding-top:30rpx;text-align:right;">
							<switch checked="{{isUseVipBalance}}" bindchange="useVipBalanceBindchange" color="#FF7E00" />
						</view>
					</view>
				</block>
			</block>
			<view hidden='{{payList[0].ishidden}}'>
				<view style="padding:0 20rpx;display:flex;">
					<view style="width:680rpx;">
						<view>
							<image src="{{wechat}}" style="width:40rpx;height:34rpx;vertical-align: middle;"></image>
							<text style="padding-left:10rpx;">微信支付</text>
						</view>
					</view>
					<view style="width:30rpx;padding:0;">
						<label class="{{payList[0].isCheck?'de_checked':'de_unchecked'}}" bindtap='selectPayWayBindTap'
							data-index='0' style="padding:0;">
							<image src="{{shop_cart}}"></image>
						</label>
					</view>
				</view>
			</view>
			<view hidden='{{payList[2].ishidden}}'>
				<view style="padding:0 20rpx;display:flex;">
					<view style="width:680rpx;">
						<view>
							<image src="{{hdfk}}" style="width:40rpx;height:34rpx;vertical-align: middle;"></image>
							<text style="padding-left:10rpx;">货到付款</text>
						</view>
					</view>
					<view style="width:30rpx;padding:0;">
						<label class="{{payList[2].isCheck?'de_checked':'de_unchecked'}}" bindtap='selectPayWayBindTap'
							data-index='2' style="padding:0;">
							<image src="{{shop_cart}}"></image>
						</label>
					</view>
				</view>
			</view>
		</view>

		<!-- 余额支付 -->
		<!-- 留言 -->
		<view class="leave_word clearfix">
			<view class="clearfix" style="font-size:28rpx;">
				<text style="float:left;">买家留言</text>
				<input style="float:left;padding-left:20rpx;" placeholder="留言请在50字以内" bindinput='orderCommentBindInput'></input>
			</view>
		</view>
		<!-- 留言 -->
		<!-- 会员支付 -->
		<view class='freight'>
			运费
			<block wx:if="{{index==1 || index==3}}">
				<label>
					￥{{tools.sub.formatAmount(orderDistributionPay,2)}}
				</label>
			</block>
			<block wx:else>
				<label>
					自提
				</label>
			</block>
		</view>
		<block wx:if="{{userProfitTotalMoney>0}}">
			<view class="goods_sum">
				<block>
					优惠总金额
					<label>￥{{tools.sub.formatAmount(userProfitTotalMoney,2)}}</label>
				</block>
			</view>
			<view class="goods_profit">
				<label style="float:left;font-size:13px;color:#444;">优惠明细</label>
			</view>
			<block wx:for="{{orderProfitDetailList}}" wx:for-item="profiit" wx:key="">
				<view class="goods_profit">
					{{profiit.profitReason}}
					<label>
						满{{profiit.profitType==1?"减":"折"}}优惠{{profiit.profitMoney}}{{profiit.profitType==1?"元":"折"}}
					</label>
				</view>
			</block>
		</block>
	</view>
	<!-- 使用余额另一种方式 -->
	<!-- 使用余额 -->
</view>
<view class='foot_box'>
	<view class='total_box'>
		合计:
		<label>￥{{tools.sub.formatAmount(orderTotalMoney,2)}}</label>
	</view>
	<view class="settle_accounts" bindtap='nowPay'>去付款</view>
</view>
<!-- 优惠券 -->
<!--<view class="black_bg" hidden='{{cardHidden}}' bindtap='cardHiddenBindTap'></view>
<view hidden='{{cardHidden}}'
	style="z-index:9999;background:#fff;width:750rpx;bottom:0;position:fixed;max-height:800rpx;overflow-y:scroll;">
	<icon class="page-dialog-close" bindtap='cardHiddenBindTap' type="clear" size='20' color='#666' />
	<!-- 不使用券 -->
	<!--<view class="noCoupon" bindtap='cardHiddenBindTap'
		style="border-bottom:1rpx solid #c6c7c8;margin:10px 0;text-align:center;">
		<label class="darkRed">选择优惠券</label>
	</view>
	<!-- 不使用券 -->
	<!--<block wx:key="unique" wx:for="{{storeCard}}" wx:for-item="sCard" wx:for-index="cardIndex">
		<block wx:if="{{sCard.expireState==0}}">
			<!-- 可使用券 -->
			<!--<view class="oneCoupon clearfix" bindtap='goToUserCardBindTap' data-index='{{cardIndex}}'>
				<view class="red_bg couponInfo clearF">
					<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
						<view class="couponValue">
							{{sCard.discountAmount}}
							<text>折</text>
						</view>
						<view class="couponDiscount">
							<view>
								{{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"元可用"}}
							</view>
							<view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
							<view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
								style="padding:10rpx 0;">
								使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
							</view>
							<view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
						</view>
					</block>
					<block wx:else>
						<view class="couponValue">
							{{sCard.discountAmount}}
							<text>元</text>
						</view>
						<view class="couponDiscount">
							<view>
								{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"元可用"}}
							</view>
							<view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
							<view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
								style="padding:10rpx 0;">
								使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
							</view>
							<view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
						</view>
					</block>
					<view class="couponStatus">
						<text class="undrawStatus">立即使用</text>
					</view>
				</view>
			</view>
			<!-- 可使用券 -->
		<!--</block>
	</block>
	<block wx:key="unique" wx:for="{{storeCard}}" wx:for-item="sCard" wx:for-index="cardIndex">
		<block wx:if="{{sCard.expireState!=0}}">
			<view class="oneCoupon clearfix" bindtap='noUserCardBindTap' data-index='{{cardIndex}}'>
				<view class="red_bg couponInfo clearF" style="background-color:#9e9e9e;">
					<block wx:if="{{sCard.cardType>=4&&sCard.cardType<=6}}">
						<view class="couponValue">
							{{sCard.discountAmount}}
							<text>折</text>
						</view>
						<view class="couponDiscount">
							<view>
								{{sCard.fullAmount==0?"无门槛"+sCard.discountAmount+"折扣":"满"+sCard.fullAmount+"元可用"}}
							</view>
							<view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
							<text style="display:block;font-size:24rpx;">{{sCard.unavailableCause}}</text>
							<view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
								style="padding:10rpx 0;">
								使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
							</view>
							<view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
						</view>
					</block>
					<block wx:else>
						<view class="couponValue">
							{{sCard.discountAmount}}
							<text>元</text>
						</view>
						<view class="couponDiscount">
							<view>
								{{sCard.fullAmount==0?"无门槛优惠"+sCard.discountAmount+"元":"满"+sCard.fullAmount+"元可用"}}
							</view>
							<view>{{sCard.cardStartTime}}-{{sCard.cardEndTime}}</view>
							<text style="display:block;font-size:24rpx;">{{sCard.unavailableCause}}</text>
							<view catchtap="ruleSwitchFun" data-idx="{{cardIndex}}" data-cardId="{{sCard.id}}"
								style="padding:10rpx 0;">
								使用规则<image class="ruleImg" src="{{sCard.ruleSwitch==true?upArrow:downArrow}}"></image>
							</view>
							<view style="line-height:40rpx;" hidden='{{!sCard.ruleSwitch}}'>{{sCard.rule}}</view>
						</view>
					</block>
				</view>
			</view>
		</block>
	</block>
</view>-->