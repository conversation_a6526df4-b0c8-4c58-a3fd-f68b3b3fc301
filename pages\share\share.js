var app = getApp();
Page({

  data: {
    img: app.imageUrl + 'appletShare.png'
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getLocalExtConfig(options);
  },
  getLocalExtConfig: function (options) {
    var that = this;
    app.init_getExtMessage().then(res => {
      //that.getLocalUserOpenId(res.companyId, options);
      that.relevantSuperiorsMessage(options,res.companyId);
    });
  },
  getLocalUserOpenId: function (companyId, options) {
    var that = this;
    wx.login({
      success: function (res) {
        if (res.code) {
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            method: "POST",
            url: app.projectName + '/newAppletLogin/getUserOpenId',
            data: {
              "companyId": companyId,
              "code": res.code
            },
            success: function (res) {
              var openid = res.data.openid;
              if (openid != null && openid.trim().length > 0 && openid != undefined) {
                wx.removeStorageSync("openId");
                app.setStorage({
                  key: 'openId',
                  data: openid
                });
                that.relevantSuperiorsMessage(options, companyId, openid);
              }
            }
          })
        }
      }
    })
  },
  relevantSuperiorsMessage: function (options, companyId) {
    var that = this;
    const scene = decodeURIComponent(options.scene);
    var recommendUserId = "";
    var commodityId = "";
    var type = "";
    if (scene != "undefined" && scene != null && scene.trim().length > 0 && scene != undefined) {
      type = scene.substring(scene.indexOf("#") + 1);
      if (type == 1) { //商品详情
        commodityId = scene.substring(0, scene.indexOf("@"));
        recommendUserId = scene.substring(scene.indexOf("@") + 1, scene.indexOf("#"));
      } else if (type == 2) { //个人中心
        recommendUserId = scene.substring(0, scene.indexOf("#"));
      }
      app.recommendData.recommendUserId = recommendUserId;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      method: "POST",
      url: app.projectName + '/newDistrution/relevantSuperiorsMessage',
      data: {
        "recommendId": recommendUserId,
        "companyId": companyId,
        /*"customerOpenId": openId*/
      },
      success: function (res) {
        setTimeout(function () {
          if (type == 1) {
            app.redirectToPage('/pages/goodsDetail/goodsDetail?goodsId=' + commodityId + '&recommendUserId=' + recommendUserId);
          } else {
            wx.switchTab({
              url: "/pages/index/index"
            });
          }
        }, 100);
      }
    })
  },
  goToHomeBindTap: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  }
})