<!--<image class='return_apply' src='{{return_apply}}' mode='widthFix' style='position:fixed; top:0; left:0;'></image>-->
<view class='address_box' style='padding-top:0px;'>
  <label>申请已受理，请在7天内寄回商品并提交物流信息，过期将自动取消申请。运费由退货人承担，本公司不接收到付件。</label>
  <label>顾客在退还货品的途中有责任保证货品的完整及安全，如退还途中出现丢包、损坏、变质等影响二次销售的情况，责任由顾客承担。</label>
  <label>申请退货后，订单内剩余商品不满足赠品获取条件时，本公司有权要求顾客退回赠品，若赠品丢失或者破损，本公司将按照赠品价格在退款中扣取相关费用。</label>
</view>
<view class='address_box'>
  <label style="font-size:28rpx;">请将商品寄往以下地址</label>
  <label>收件人：{{orderRejectedBean.receiverName}}</label>
  <label>收件人电话：{{orderRejectedBean.receiveContactNum}}</label>
  <label>收件人地址：{{orderRejectedBean.receiveAddress}}</label>
</view>
<view class='logistics_box'>
  <label class='logistics_title'>物流信息</label>
  <view class="section">
    <picker bindchange="bindPickerChange" value="{{index}}" range="{{array}}">
      <view class="picker">
        {{array[index]}}
        <text>︿</text>
      </view>
    </picker>
  </view>
  <view class="section">
    <input placeholder="快递单号" bindinput='logisticNoBindInput' />
  </view>
  <!--<view class="section">
    <input placeholder="运费金额" />
  </view>-->
</view>

<button class="confirm_btn" bindtap='customerAcceptanceBindTap'>
  提交
</button>