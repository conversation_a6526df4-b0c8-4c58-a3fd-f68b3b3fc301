<!--内容-->
<scrollview scroll-y class='contant_box'>
  <import src="../../components/wxParse/wxParse.wxml" />
  <swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" circular="{{circular}}"
    indicator-dots="{{indicatorDots}}" indicator-color="{{indicatorColor}}"
    indicator-active-color="{{indicatorActiveColor}}" duration="{{duration}}"
    style="height:{{imgheights[current]}}rpx; position:relative;">
    <block wx:key="unique" wx:for="{{exchangeNBean.commodityBaseInfo.picList}}" wx:for-item="image">
      <swiper-item>
        <image lazy-load='true' src="{{image.commodityPicPath}}" bindload="imageLoad" class="slide-image"
          style="width:{{imgwidth}}rpx; height:{{imgheights[current]}}rpx;" />
      </swiper-item>
    </block>
    <!--团购   秒杀-->
  </swiper>
  <view class="goods_content">
    <label class="goods_title">{{exchangeNBean.commodityBaseInfo.commodityName}}</label>
    <label class="goods_title">
      <text style="font-size:26rpx;color:#FF7E00;">
        <block wx:key="unique" wx:for="{{exchangeNBean.commodityPriceList[0].priceList}}" wx:for-item="pl">
          <block wx:if="{{exc.commodityPriceList[0].priceList.length>=2}}">
            <block wx:if="pl.priceType=='ACCMULATION'">
              {{pl.priceVal}}积分
            </block>
            <block wx:elif="pl.priceType=='RMB'">
              +{{pl.priceVal}}元
            </block>
          </block>
          <block wx:else>
            <block wx:if="pl.priceType=='ACCMULATION'">
              {{pl.priceVal}}积分
            </block>
            <block wx:elif="pl.priceType=='RMB'">
              {{pl.priceVal}}元
            </block>
          </block>
        </block>
      </text>
    </label>
    <label class="goods_adv">{{exchangeNBean.commodityBaseInfo.commodityAdContent}}</label>
  </view>

  <!--详情-->
  <view class='goods_evaluate'>
    <label class='line'></label>
    <view class='icon-good-comment'>详情</view>
    <label class='line'></label>
  </view>
  <view class='goods_pic'>
    <template is="wxParse" data="{{wxParseData:commodityIntroduce.nodes}}" />
  </view>
</scrollview>
<!--内容-->


<view class='foot_box' data-id="{{exchangeNBean.exchangeCommodityId}}" bindtap="pregenerateCommodityRetailOrderBindTap">
  <view class='flex-sub-box-3 little_icon' style="width:100%;">
    <view class="add_toCart" style="width:100%;background:#FF7E00;">立即兑换</view>
  </view>
</view>