const app = getApp();
Component({
  data: {
      selected:4,
      list:[],
      shopCartNum: wx.getStorageSync('shopCartNum'),
      showFlag:false
   },
  attached() {
  },
  methods: {
    switchTab(e) {
      var that = this;
      const data = e.currentTarget.dataset
      const pathState = data.path
      var url;
      if (pathState == 4){
        url = "/pages/index/index";
      }
      else if (pathState == 5){
        url = "/pages/goods_classify/goods_classify";
      }
      else if (pathState == 6) {
        url = "/pages/shopCart/shopCart";
      }
      else if (pathState == 7) {
        url = "/pages/accountManager/accountManager";
      }
      else if (pathState == 17) {/*发现*/
        url = "/pages/find/find";
      }
      else if (pathState == 11) {/*直播间*/
        url = "/pages/onlineCast/onlineCast";
      }
      else if (pathState == 10) {/*积分商城*/
        url = "/pages/scoreMall/scoreMall";
      }
      else if (pathState == 8) {/*我的订单*/
        url = "/pages/indexThree/indexThree";
      }
      else{
        url = "/pages/index/index";
      }
      if (pathState == 17){
        wx.redirectTo({
          url: "../find/find"
        });
      }
      else if (pathState == 10){
        wx.redirectTo({
          url: "../scoreMall/scoreMall"
        });
      }
      else if (pathState == 8) {
        wx.redirectTo({
          url: "../indexThree/indexThree"
        });
      }
      else if (pathState == 11) {
        wx.redirectTo({
          url: "../onlineCast/onlineCast"
        });
      }
      else{
        wx.switchTab({ url });
      }
      that.setData({
        selected: pathState,
        shopCartNum: wx.getStorageSync('shopCartNum'),
        color: app.globalData.bottomBean.color,//字选中前的颜色
        selectedColor: app.globalData.bottomBean.selectedColor,//字选中后的颜色  
      });
    },
  }
}
)