page {
  background: #f4f4f4;
}

.noCoupon {
  height: 40px;
  font-size: 14px;
  color: #666;
  line-height: 40px;
  background: #fff;
  padding: 0 10px;
}

.oneCoupon {
  margin: 20rpx 0;
}

.couponInfo {
  width: 90%;
  margin: 0 auto;
  border-radius: 20rpx;
}

.couponInfo .couponValue {
  text-align: center;
  color: #fff;
  float: left;
  width: 22%;
  font-size: 40rpx;
  padding-top: 60rpx;
}

.couponValue text {
  font-size: 32rpx;
}

.couponInfo .couponDiscount {
  /* height: 130rpx; */
  float: left;
  width: 50%;
  font-size: 26rpx;
  padding: 24rpx 0;
  color: #fff;
  padding-left: 36rpx;
  border-left: 1px dashed #fff;
}

.couponDiscount view:last-child {
  margin-top: 8rpx;
}

.couponStatus {
  width: 20%;
  text-align: center;
  float: left;
  line-height: 180rpx;
}

.undrawStatus {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  background: #fff;
  border-radius: 10rpx;
}

.drawStatus {
  font-size: 26rpx;
  color: #fff;
}
.ruleImg{
  width: 24rpx;
  height: 12rpx;
  margin-left:10rpx;
}
.clearF:after {
  display: block;
  content: "";
  clear: both;
}
