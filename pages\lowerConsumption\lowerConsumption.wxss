page {
  width: 100%;
  height: 100%;
  background:#fff;
}

.contant_box {
  width: 100%;
  height: 100%;
}

/* 搜索框 */

.search_box {
  height: 26px;
  padding: 5px 3%;
  background: #fff;
  color: #848489;
  position: fixed;
  width: 94%;
  z-index: 20;
  float: left;
}

.search_box icon {
  position: absolute;
  top: 10px;
  left: 6%;
  z-index: 10;
  line-height: 26px;
}

.search_box input {
  width: 80%;
  height: 100%;
  line-height: 26px;
  background: #e7e8ea;
  font-size: 13px;
  padding-left: 10%;
  border-radius: 26px;
  float: left;
  margin: 0 auto;
}

.search_box text {
  line-height: 26px;
  font-size: 12px;
  margin-left: 5px;
}

.contant-box {
  width: 100%;
  padding: 50px 0% 0 0;
  height: 100%;
}

.contantBox, .s_contantBox {
  font-size: 14px;
  line-height: 28px;
  margin-bottom: 5px;
}

/**.contantBox2{text-align: center;}***/

.contantBox2, .s_contantBox2 {
  font-weight: bold;
  margin-top: 10px;
}

.contantBox label {
  width: 25%;
  display:inline-block;
  word-wrap: break-word;
  text-align:center;
  height:50rpx;
  overflow:hidden;
  text-overflow:ellipse;
}

.contantBox label:first-child {
  font-size:26rpx;
}
.price-box {
  color: red;
}

.black_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}

.second_consumption {
  border: 1px solid #fff;
  padding: 20px 5px 20px;
  width: 80%;
  background: #fff;
  border-radius: 8px;
  position: absolute;
  top: 20%;
  left: 9%;
  z-index: 20;
  text-align: center;
  height: 350px;
  overflow: scroll;
}
.s_contantBox label {
  width: 33%;
  display:inline-block;
  word-wrap: break-word;
  height:50rpx;
  white-space:pre-wrap;
  overflow:hidden;
  text-overflow:ellipse;
}
