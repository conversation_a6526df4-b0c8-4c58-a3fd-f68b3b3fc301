const app = getApp();
const http = require('../../utils/http')
const ui = require('../../utils/ui')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    startPage:1,
    pageSize:20
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that=this;
    that.getMyReward(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that=this;
    if (that.data.resultList.length ==that.data.pageSize){
      that.selectComponent("#loadMoreView").loadMore()
    }else{
      that.selectComponent("#loadMoreView").noMore()
    }
  },

  getMyReward:function(){
    var that=this;
    http.get({
      urlName:'activity',
      url: 'collect/queryShareWordPage',
      showLoading:false,
      data: {
        startPage:that.data.startPage,
        pageSize:that.data.pageSize,
        merchantId: app.getExtCompanyId(),
        userId:app.getUserId()
      },
      success: (res) => {   
       var items = that.data.items;
       if (that.data.startPage == 1) {
         items = res.resultList;
         wx.stopPullDownRefresh();
       } else {
          items = items.concat(res.resultList);   
       }
       that.setData({
         items: items,
         startPage: items.length >= that.data.pageSize ? that.data.startPage + 1 : that.data.startPage,     
         resultList: res.resultList
       })
       this.selectComponent("#loadMoreView").loadMoreComplete(res);
      },
      fail: () => {
        this.selectComponent("#loadMoreView").loadMoreFail();
      }
    })
  },
  loadMoreListener: function (e) {
    this.getMyReward(false);
  },
  clickLoadMore: function (e) {
    this.getMyReward(false);
  },
  retrieve:function(e){
    var id= e.currentTarget.dataset.id;
    http.post({
      urlName:'activity',
      url: 'collect/shareRetrieveWord',
      showLoading:false,
      data: {
        collectListId:id
      },
      success: (res) => {   
      ui.showToast("取回成功")
      }
    })
  }
})