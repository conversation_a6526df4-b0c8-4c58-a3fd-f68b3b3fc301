<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<scrollview scroll-y class='contant_box'>
  <!-- 商品轮播图 -->
  <swiper indicator-dots="{{indicatorDots}}" bindchange="swiperChange" autoplay="{{autoplay}}" interval="{{interval}}"
    circular="{{circular}}" indicator-dots="{{indicatorDots}}" indicator-color="{{indicatorColor}}"
    indicator-active-color="{{indicatorActiveColor}}" duration="{{duration}}" style="height:750rpx; position:relative;">
    <swiper-item wx:key="unique" wx:for="{{imgList}}" wx:for-item="image">
      <video wx:if="{{image.type==0}}" style="width:750rpx; height:750rpx;"
        src='https://www.cn2b2c.com/image/img/video/find/20210115/video_ec73d46573be439380245c0158cfb792.mp4'
        muted='true' object-fit='contain' show-mute-btn='{{true}}' title='{{show.disscenname}}' autoplay='{{true}}'
        enable-play-gesture='{{true}}' enable-progress-gesture='{{false}}' bindplay="bofang"
        bindended="endVideo"></video>
      <image wx:if="{{image.type==1}}" lazy-load='true' src="/image/newgood_banner.png" bindtap="enlarge"
        style="width:750rpx; height:750rpx;" mode="widthFill" />
      <!--自定义指示点-->
      <view class="indexShow" wx:if="{{imgList.length>1}}">
        {{swiperIndex}} / {{imgList.length}}
      </view>
      <view class="indexShow" wx:else>
        {{swiperIndex}} / {{imgList.length}}
      </view>
    </swiper-item>
  </swiper>
  <!-- 秒杀商品倒计时 -->
  <view class='group_box' hidden='{{false}}'>
    <view class="goods-seckill-left" >
      <text class="goodn3_one">￥</text><text
        class="goodn3_two">19.5</text>
        <text class="goodn3_three">/盒</text>
        <text class="goodn3_four">40.9</text>   
        <view class="goodn3_five">月销126件</view>   
    </view>
    <view class='activity_time'>
      <label class="goodn3_ten">秒杀倒计时:</label>
      <view style="display:flex;">
        <label>{{15}}</label><text class="goodn3_six">:</text>
        <label >{{15}}</label><text class="goodn3_six">:</text>
        <label >{{15}}</label><text class="goodn3_six">:</text>
        <label>{{30}}</label>
      </view>
    </view>
  </view>

  <!--拼团商品简介-->
  <view class="goods_content clearfix" style="padding-top:20rpx;">
    <view style="float:left;width:85%;">
      <text class="goodn4_one">￥</text><text
        class="goodn4_two">19.5</text><text
        class="goodn4_four">￥40.9</text>
      <text
        class="goodn4_five">3人拼团价</text>
      <label class="goods_title">会说话的萌鸡麦琪剩水残山上的v奋斗</label>
      <label class="goods_adv">最受欢迎的亲子游戏</label>

      <view class="goodn_one" >
        月销126件</view>
    </view>
    <view class="shareGood" bindtap="shareGoodsBind">
      <image src="{{goShare}}" mode="widthFix"></image>
      <view>分享</view>
    </view>
  </view>
  <!--秒杀商品简介-->
  <view class="goods_content clearfix" style="padding-top:20rpx;">
    <view style="float:left;width:85%;">
      <label class="goods_title">会说话的萌鸡麦琪</label>
      <label class="goods_adv">最受欢迎的亲子游戏</label>
      <label class="goods_price" style="display:flex;">
        <label>
          <view>￥<text style="font-size:48rpx;">19.5</text>
            <block>/个</block>
          </view>
        </label>
      </label>
        <view class="goodn_one">
          月销126件</view>
    </view>
    <view class="shareGood" bindtap="shareGoodsBind">
      <image src="{{goShare}}" mode="widthFix"></image>
      <view>分享</view>
    </view>
  </view>
    <!--普通商品简介-->
    <view class="goods_content clearfix" style="padding-top:20rpx;">
    <view style="float:left;width:85%;">
      <label class="goods_title">会说话的萌鸡麦琪</label>
      <label class="goods_adv" style="padding-bottom:21rpx;">最受欢迎的亲子游戏</label>
    </view>
    <view class="shareGood" bindtap="shareGoodsBind">
      <image src="{{goShare}}" mode="widthFix"></image>
      <view>分享</view>
    </view>
  </view>
  <!--拼团滚动-->
  <!-- <swiper    autoplay="{{true}}" vertical="{{vertical}}" interval="{{interval}}" duration="{{duration}}"
    circular="{{circular}}" style="height:160rpx;margin-top:16rpx">
    <block wx:for="{{2}}" wx:key="index">
      <swiper-item style="background:#fff;">
        <view style="border-bottom:16rpx solid #f4f4f4;">
        <view class="goodn2_seven" style="height:144rpx;">
          <image style="width:100rpx;height:auto;border-radius:50%;" src="{{dbbLogo}}" mode="widthFix"></image>
          <view style="width:35%;">小鲜花乌卡</view>
          <view style="width:40%;">
            <view style="height:50rpx;">差<text style="color:#F20100">2人</text>拼团成功</view>
            <view style="height:50rpx;">剩余<text style="color:#F20100">13:23:12</text></view>
          </view>
          <view style="width:25%;">
            <view class="goodn2_eight">去拼团</view>
          </view>
        </view>
      </view>
      </swiper-item>
    </block>
  </swiper> -->
  <view class="swiper-view">
    <swiper next-margin="160rpx" class="swiper_container" vertical="true" autoplay="true" circular="true"
      interval="2000">
      <block wx:for="{{3}}" wx:key="index">
        <swiper-item style="background:#fff; ">
          <view style="border-bottom:16rpx solid #f4f4f4;">
            <view class="goodn2_seven" style="height:144rpx;">
              <image style="width:100rpx;height:auto;border-radius:50%;" src="{{dbbLogo}}" mode="widthFix"></image>
              <view style="width:35%;">小鲜花乌卡</view>
              <view style="width:40%;">
                <view style="height:50rpx;">差<text style="color:#F20100">2人</text>拼团成功</view>
                <view style="height:50rpx;">剩余<text style="color:#F20100">13:23:12</text></view>
              </view>
              <view style="width:25%;">
                <view class="goodn2_eight">去拼团</view>
              </view>
            </view>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!--商品参数-->
  <view class="attr_wrap" style="padding-bottom:18rpx;">
    <view class="choose_attr">
      <text class="goodn_two">商品参数</text>
    </view>
    <view class="goodn_three">
      <view wx:for="{{goodsList}}" wx:key="unique" wx:for-index="index"
        style="border-bottom:{{index==goodsList.length-1? 0:2}}rpx solid #F7F7F7;">
        <view class="goodn_four">
          <view class="goodn_five">
            商品参数书法课几十年进发放那数据开放那可就是南方科技按时看见你发</view>
          <view class="goodn_six">
            商品参数发送卡积分卡上分卡上分卡上分卡上分卡顺丰科技按时缴费卡哈的哈会计师法卡萨放把水爆发式不放卡不上不放假安守本分静安寺不会静安寺</view>
        </view>
      </view>
    </view>
  </view>
  <!--商品评价-->
  <view class="attr_wrap">
    <view class="choose_attr clearfix">
      <label class="goodn_seven">商品评价</label>
      <label wx:if="{{false}}" class="goodn_eight" bindtap='queryAllEvaluateBindTap'>
        更多评价
        <label class="icondirect" style="margin-left:10rpx;">︿</label>
      </label>
    </view>
    <block wx:key="unique" wx:for="{{1}}" wx:for-item="evaluate" wx:for-index="eval_index">
      <view wx:if="{{false}}" class="goodn_nine">
        <view style="width:100rpx;height:100rpx;">
          <image lazy-load='true' class="goodn_ten" src="{{dbbLogo}}" mode="widthFix" />
        </view>
        <view>
          <view class="blueColor">小星星 <text class="goodn1_one">1月3日</text>
          </view>
          <view class="noblueColor">打分 <text class="userS">★★★★★</text></view>
          <view class="goodn1_two">
            大师傅开始恢复卡萨大师课件百度节哀顺不是不回家俺不放假阿坝师范就把世界杯副科级安守本分静安寺不发布时间数据备份几号上班</view>
        </view>
      </view>
      <view wx:elif="{{true}}" class="goodn1_three">
        暂无评价
      </view>
    </block>
  </view>
  <view style="margin-top:20rpx;">
    <image class="goodn1_four" src="/image/newgood_banner.png" mode="widthFix"></image>
  </view>
  <view style="margin-top:20rpx;">
    <image class="goodn1_four" src="/image/newgood_banner.png" mode="widthFix"></image>
  </view>
  <view style="height: calc(env(safe-area-inset-bottom));"></view>
</scrollview>

<!-- 拼团商品底部导航栏 -->
<view wx:if="{{false}}" class='foot_box'>
  <view class='flex-sub-box-3 little_icon' style="width:40%;">
    <view class='flex-sub-box-2' style="width:33%" bindtap='goToHomeBindTap'>
      <image mode="widthFix" src="{{indexIcon}}" class="goodn1_five"></image>
      <label class="goodn1_eight">首页</label>
    </view>
    <view class='flex-sub-box-2 goodn1_six'>
      <image mode="widthFix" src="{{online}}"></image>
      <button open-type="contact" size="20" session-from="weapp" class='goodn1_seven'></button>
      <label class="goodn1_eight">客服</label>
    </view>
    <view class='flex-sub-box-2' style="width:33%" bindtap='goToShopCartBindTap'>
      <image mode="widthFix" src="{{shopcart}}"></image>
      <text class="cartNum" hidden='{{false}}'>11</text>
      <label class="goodn1_eight">购物车</label>
    </view>
  </view>
  <view wx:if="{{true}}" class='flex-sub-box-3 buyBtn' style="width:30%" bindtap='nowBuyBindTap'>
    <view class="goodn2_nine">单独购买</view>
    <view class="goodn2_ten">¥<text style="font-size:32rpx;">40.9</text></view>
  </view>
  <view wx:if="{{true}}" class='flex-sub-box-3 addBtn' style="width:30%;" bindtap='nowBuyBindTap'>
    <view class="goodn2_nine">发起拼团</view>
    <view class="goodn2_ten">¥<text style="font-size:32rpx;">19.5</text></view>
  </view>
  <view wx:if="{{false}}" class='flex-sub-box-3 soldBtn' style="width:60%;">
    已售罄
  </view>
</view>


<!-- 秒杀商品底部导航栏 -->
<view wx:elif="{{false}}" class='foot_box'>
  <view class='flex-sub-box-3 little_icon' style="width:40%;">
    <view class='flex-sub-box-2' style="width:33%" bindtap='goToHomeBindTap'>
      <image mode="widthFix" src="{{indexIcon}}" class="goodn1_five"></image>
      <label class="goodn1_eight">首页</label>
    </view>
    <view class='flex-sub-box-2 goodn1_six'>
      <image mode="widthFix" src="{{online}}"></image>
      <button open-type="contact" size="20" session-from="weapp" class='goodn1_seven'></button>
      <label class="goodn1_eight">客服</label>
    </view>
    <view class='flex-sub-box-2' style="width:33%" bindtap='goToShopCartBindTap'>
      <image mode="widthFix" src="{{shopcart}}"></image>
      <text class="cartNum" hidden='{{false}}'>11</text>
      <label class="goodn1_eight">购物车</label>
    </view>
  </view>
  <view wx:if="{{true}}" class='flex-sub-box-3 goodn3_eight' style="width:30%" bindtap='nowBuyBindTap'>
    加入购物车
  </view>
  <view wx:if="{{true}}" class='flex-sub-box-3 goodn3_seven' style="width:30%;" bindtap='nowBuyBindTap'>
    立即抢购
  </view>
  <view wx:if="{{false}}" class='flex-sub-box-3 soldBtn' style="width:60%;">
    已售罄
  </view>
</view>

<!-- 普通商品底部导航栏 -->
<view wx:elif="{{true}}" class='foot_box'>
  <view class='flex-sub-box-3 little_icon' style="width:40%;">
    <view class='flex-sub-box-2' style="width:33%" bindtap='goToHomeBindTap'>
      <image mode="widthFix" src="{{indexIcon}}" class="goodn1_five"></image>
      <label class="goodn1_eight">首页</label>
    </view>
    <view class='flex-sub-box-2 goodn1_six'>
      <image mode="widthFix" src="{{online}}"></image>
      <button open-type="contact" size="20" session-from="weapp" class='goodn1_seven'></button>
      <label class="goodn1_eight">客服</label>
    </view>
    <view class='flex-sub-box-2' style="width:33%" bindtap='goToShopCartBindTap'>
      <image mode="widthFix" src="{{shopcart}}"></image>
      <text class="cartNum" hidden='{{false}}'>11</text>
      <label class="goodn1_eight">购物车</label>
    </view>
  </view>
  <view wx:if="{{true}}" class='flex-sub-box-3 goodn3_nine' style="width:30%" bindtap='nowBuyBindTap'>
    加入购物车
  </view>
  <view wx:if="{{true}}" class='flex-sub-box-3 goodn3_seven' style="width:30%;" bindtap='nowBuyBindTap'>
    立即购买
  </view>
  <view wx:if="{{false}}" class='flex-sub-box-3 soldBtn' style="width:60%;">
    已售罄
  </view>
</view>

<!-- 黑色背景 -->
<view class='black_bg' hidden="{{buyHidden}}"></view>
<!-- 加入购物车   立即购买弹层-->
<view class='scroll_block page-dialog-wrap goodn2_one' hidden="{{buyHidden}}">
  <view class='goodn2_two'>
    <view class='addgoods_pic'>
      <image lazy-load='true' mode="{{mode}}" src="{{dbbLogo}}"></image>
    </view>
    <label class='addgoods_title'>会说话的萌鸡麦琪</label>
    <view class='addgoods_price'>
      <label class="goods_price" style="display:flex;">
        <label>
          <view>￥<text style="font-size:28rpx;">19.5</text>
            <block>/个</block>
          </view>
        </label>
      </label>
    </view>
    <view class="addgoods_wrap goodn2_three">
      <view class="addgoods_attr">
        <view class="attr_wrap" style="margin:0;">
          <view class="choose_attr" style="padding:0">
            剩余库存： 500件
          </view>
        </view>
      </view>
    </view>
    <view class="page-dialog-close1" bindtap='hiddeAddToShoppingCart'>
      <image style="width:40rpx;height:auto;" src="{{close}}" mode="widthFix"></image>
    </view>
  </view>
  <scroll-view scroll-y style='padding-top:200rpx; max-height:400rpx;'>
    <view wx:key="unique" wx:for="{{2}}" wx:for-item="sku" class='goods_classify' hidden="{{false}}">
      <label class="goodn2_five">颜色分类</label>
      <view class='clearfix'>
        <block wx:key="unique" wx:for="{{10}}" wx:for-item="skuChild">
          <text bindtap='changeSKUBindTap' data-name='{{sku.skuAttrName}}' data-childname='{{skuChild.skuAttrName}}'
            class='{{skuChild.isSelect?"active_classify":""}}'>红色
          </text>
        </block>
      </view>
    </view>
    <view class='addgoods_number clearfix'>
      <view class='limited_quantity' style="float: left;">
        购买数量
      </view>
      <view style="float: right;">
        <view class='clearfix plus_minus' hidden="{{false}}">
          <label class="minus_box" bindtap='clickMinusOmButton'>-</label>
          <input style="border:none;background: #FAFBFA;" type='number' maxlength="5" value='1'
            bindinput="inputBuyOmCount"></input>
          <label class="plus_box" bindtap='clickPlusOmButton'>+</label>
          <text class="unit_num">{{goodsOMUnit}}</text>
          <view class="goodn2_six">共一份,<text style="color: #FF7E00;"> ￥19.5</text></view>
        </view>
      </view>
    </view>
  </scroll-view>
  <label class="btn pay-add-to-shoppingcart" bindtap='addShoppingCartBindTap'>
    完成
  </label>
</view>

<!-- 分享弹出层 -->
<!-- 生成海报 -->
<view class="goodn1_nine" hidden="{{true}}">
  <view style="height:50rpx;">
    <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='cancelButtonBindTap' />
  </view>
  <canvas class="poster" canvas-id="poster" style="width:600rpx;height:900rpx;"></canvas>
  <view class="goodn1_ten" bindtap="savePosterToPhoneBindTap">
    保存图片
  </view>
</view>
<template is="share" data="{{...item}}" />