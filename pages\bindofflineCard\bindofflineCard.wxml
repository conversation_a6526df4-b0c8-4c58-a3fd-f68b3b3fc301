<view style="font-size:28rpx;">
  <view style="margin:20rpx 25rpx;border-left:3px solid red;">
    <text style="margin-left:10rpx;">领卡人信息</text>
  </view>
  <view style="margin:0 30rpx;">
    <view class='account_detail'>
      <label>
        <text>卡号</text>
        <text>{{cardBean.vipCardNo}}</text>
      </label>
      <label>
        <text>姓名</text>
        <text>{{cardBean.vipUserName}}</text>
      </label>
      <label>
        <text>手机号</text>
        <text>{{cardBean.vipTelephone}}</text>
      </label>
      <label style="border-bottom:1px solid #ececec;">
        <text>性别</text>
        <text>{{cardBean.vipSex==0?"女":"男"}}</text>
      </label>
      <label style="border-bottom:1px solid #ececec;">
        <text>地址</text>
        <text>{{cardBean.vipUserAddress}}</text>
      </label>
    </view>
  </view>
  <view class='add_address' bindtap='boundVipCardBindTap'>
    确认并绑定
  </view>
</view>