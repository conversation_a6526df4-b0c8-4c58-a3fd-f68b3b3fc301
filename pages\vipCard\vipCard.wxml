
<view class="billWrap" hidden="{{billHidden}}">
	<view>
		<view class="billTitle">输入小票号</view>
		<input class="billOrder" bindinput="billBindInput" maxlength="20" />
	</view>
	<view style="color:#FF7E00;font-size:26rpx;margin-left:40rpx;margin-top:30rpx;">{{desc}}</view>
	<view style="width:600rpx;margin:0 auto;margin-top:30rpx;">
		<button class='confirm_btn' bindtap="cancleBillBindTap" style="background:#999;color:#fff;">取消</button>
		<button class='confirm_btn' bindtap="checkBillNoBindTap" style="background:#07c160;color:#fff;">确定</button>
	</view>
</view>
<view class="bl_bg" hidden="{{billHidden}}"></view>
<block wx:if="{{cardList.length==0}}">
	<view style="position:relative;" hidden="{{openHidden&&bindHidden?true:false}}">
		<image src="{{old_card_bg}}" mode="widthFix" style="width:100%;"></image>
		<view bindtap="allVipCardBind" class="bind_onLine {{!openHidden&&bindHidden?'bind_append':''}}" hidden="{{openHidden}}">
			<view class="add">+</view>
			<view class="vip_text">电子会员开卡</view>
		</view>
		<view bindtap="storeCardBind" class="bind_offLine {{!bindHidden&&openHidden?'bind_append':''}}" hidden="{{bindHidden}}">
			<view class="add" style="background:#fff;color:#cac18f;">+</view>
			<view class="vip_text">绑定线下实体卡</view>
		</view>
	</view>
</block>
<image src="{{no_card}}" class="no_image" mode="widthFix" hidden="{{cardList.length>0?true:false}}"></image>
<view style="text-align:center;color:#666;" hidden="{{cardList.length>0?true:false}}">
	您还没有会员卡哦
</view>
<view class="cardWrap">
	<view class="card_available" hidden='{{cardList.length>0?false:true}}'>
		<text style="margin-left:10rpx;">可用会员卡</text>
	</view>
	<view>
		<view style="margin-top:60rpx;">
			<block wx:key="unique" wx:for="{{cardList}}" wx:for-item="card" wx:for-index="index">
				<!-- 单张卡 -->
				<view class="card_Wrap" bindtap="useVipCard" data-no="{{card.cardId}}">
					<image src="{{card_bg}}" mode="widthFix"></image>
					<view class="card_title">{{companyName}}</view>
					<view class="card_id">No.{{card.cardId}}</view>
					<view class="amount_wrap">
						<label class="remain_amount">
							余额：
							<text>{{card.spareCash}}</text>
						</label>
						<label class="remain_score">
							积分：
							<text>{{card.integral}}</text>
						</label>
					</view>
					<view class="validDate">有效期：长期</view>
					<view style="font-size:24rpx;color:#fff;position:absolute;color:#fff;top:120rpx;right:60rpx;">
						<!--详情 >-->
						<image style="width:30rpx" mode="widthFix" src="{{moreIcon}}"></image>
					</view>
				</view>
				<!--单张卡-->
			</block>
		</view>

	</view>
</view>

<!--获取手机号-->
<view class="bl_bg" hidden="{{isHaveTelephone}}" style="z-index:20;"></view>
<view hidden="{{isHaveTelephone}}" style="position:absolute;z-index:999;top:20%;width:90%;background:#fff;margin-left:5%;border-radius:10rpx;">
	<view class="topWrap">
		<view style="width:200rpx;margin:0 auto;">
			<open-data type="userAvatarUrl" style="border-radius:50%;"></open-data>
		</view>
		<view>
			<open-data type="userNickName"></open-data>
		</view>
	</view>
	<block wx:if="{{isHaveTelephoneFail}}">
		<view style="width:80%;margin:0 auto">
		<view class="fillCode">
			<input placeholder='请输入手机号' type='number' bindinput="bindVipCardTelephoneBindInputNew" maxlength="11" style="width:300rpx;"></input>
			<text class="smsButton darkBlack" bindtap='smsBindTapNew'>{{secondDescNew}}</text>
		</view>
		<view class="fillCode" style="margin-top:20rpx;">
			<input placeholder='请输入验证码' maxlength="6" type='number' bindinput="smsCodeBindInputNew"></input>
		</view>
	</view>
	<view style="width:600rpx;margin:0 auto;">
		<button class='confirm_btn' style="background:#999;color:#fff;" bindtap='noBoundTelephoneBindTapNew'>暂不绑定</button>
		<button class='confirm_btn' style="background:#07c160;color:#fff;" bindtap="nowBoundTelephoneBindTapNew">立即绑定</button>
	</view>
	</block>
	<block wx:else>
		<view style="width:600rpx;margin:0 auto;text-align:center;">
			<view>为了您能更好的体验会员卡，建议您先绑定手机号</view>
			<button class='confirm_btn' style="background:#07c160;color:#fff;" open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">立即绑定</button>
		</view>
	</block>
	
</view>
<!--获取手机号-->