/* pages/messageNotice/messageNotice.wxss */
page{
    background:#f5f5f5;
}

.message-bar-dot{
    color: #fff;
    position: absolute;
    top: 24rpx;
    right:10%;
    min-width: 18rpx;
    height: 18rpx;
    padding: 8rpx;
    border-radius: 34rpx;
    background: #ef4c5a;
    font-size: 24rpx;
    line-height: 18rpx;
}
.title_top{
    display:flex;
    justify-content: space-around;
    color:#999;
    font-size:30rpx;
}
.c_choose{
    font-size:32rpx;
    color:#000;
    font-weight:bold;

}
.arcStore{

    background:#fff;
    position:relative;
}
.arcStore::after{
    content: '';
    display: block;
    width: 40rpx;
    height: 12rpx;
    border-radius: 50%/0 0 100% 100%;
    position: absolute;
    border:6rpx solid #f1c99c;
    border-top:none;
    transform: translateX(80%);
}
.oneMessage{
    width:640rpx;
    margin:20rpx auto;
    background:#fff;
    border-radius:30rpx;
    padding:20rpx 30rpx;
}
.oneMessageStore{
    width:640rpx;
    margin:20rpx auto;
    padding:20rpx 30rpx;
}
/*timeline*/
#timeline{
    width:600rpx;
    margin:0 auto;
    border-left:1px solid #f1c99c;
    margin-top:30rpx;
}
.timeWrap{
    position:relative;
    margin-bottom:20rpx;
}
.timeLeft{
    text-align:center;
    position:absolute;
    transform:translateX(-50%);
    top:30%;
}
.timeLeft view{
    font-size:29rpx;color:#f2f2f2;
    width:100rpx;
    height:40rpx;
    line-height:40rpx;
    background:#f1c99c;
    border-radius:10rpx;
}
.timeRight{
    margin-left:80rpx;
    background:#fff;
    padding:20rpx;
    border-radius:30rpx;
}
.time_title{
    font-size:29rpx;
    color:#999;
}
.time_content{
    color:#666;
    margin-top:10rpx;
    font-size:29rpx;
}