var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    exchangeCommodityList:[],
    changeGoodsPriceTotal: 0,
    exchangeImg:app.imageUrl + 'exchange.png'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      exchangeCommodityList:JSON.parse(options.exchangeCommodityList)
    })
  },
  /**
   * 减少换购商品数量
   */
  goodsReduceBindTap: function (e) {
    var that = this;
    var commodityId = e.currentTarget.dataset.id;
    var skuId = e.currentTarget.dataset.skuid;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].commodityId == commodityId) {
        if (skuId != 0) {
          if (skuId == exchangeCommodityList[i].skuId) {
            exchangeCommodityList[i].actualExchangeNum--;
            exchangeCommodityList[i].select = exchangeCommodityList[i].actualExchangeNum == 0 ? false : true;
          }
        } else {
          exchangeCommodityList[i].actualExchangeNum--;
          exchangeCommodityList[i].select = exchangeCommodityList[i].actualExchangeNum == 0 ? false : true;
        }
      }
    }
    that.calculationChangeGoodsTotalPrice();
  },
  /**
   * 增加换购商品数量
   */
  goodsPlusBindTap: function (e) {
    var that = this;
    var commodityId = e.currentTarget.dataset.id;
    var skuId = e.currentTarget.dataset.skuid;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].commodityId == commodityId) {
        if (exchangeCommodityList[i].actualExchangeNum >= exchangeCommodityList[i].exchangeNum) {
          app.showModal({
            title: '提示',
            content: "已经超过换购数量"
          });
          return;
        } else {
          if (skuId != 0) {
            if (skuId == exchangeCommodityList[i].skuId) {
              exchangeCommodityList[i].actualExchangeNum++;
              exchangeCommodityList[i].select = true;
            }
          } else {
            exchangeCommodityList[i].actualExchangeNum++;
            exchangeCommodityList[i].select = true;
          }
        }
      }
    }
    that.calculationChangeGoodsTotalPrice();
  },
  /**
   * 换购商品
   */
  changePurchaseBindTap: function (e) {
    var that = this;
    var commodityId = e.currentTarget.dataset.id;
    var skuId = e.currentTarget.dataset.skuid;
    var changeTotalPrice = that.data.changeGoodsPriceTotal;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].commodityId == commodityId) {
        if (skuId != 0) {
          if (skuId == exchangeCommodityList[i].skuId) {
            exchangeCommodityList[i].select = true;
            exchangeCommodityList[i].actualExchangeNum = 1;
            changeTotalPrice += exchangeCommodityList[i].commoityExchangePrice;
          }
        } else {
          exchangeCommodityList[i].select = true;
          exchangeCommodityList[i].actualExchangeNum = 1;
          changeTotalPrice += exchangeCommodityList[i].commoityExchangePrice;
        }
      }
    }
    that.calculationChangeGoodsTotalPrice();
  },
  /**
   * 计算换购价格
   */
  calculationChangeGoodsTotalPrice: function () {
    var that = this;
    var totalPrice = 0;
    var exchangeCommodityList = that.data.exchangeCommodityList;
    for (var i = 0; i < exchangeCommodityList.length; i++) {
      if (exchangeCommodityList[i].select) {
        totalPrice += exchangeCommodityList[i].commoityExchangePrice * exchangeCommodityList[i].actualExchangeNum;
      }
    }
    that.setData({
      exchangeCommodityList: exchangeCommodityList,
      changeGoodsPriceTotal: totalPrice.toFixed(2)
    });
    
  },
  submit:function(){
    var that=this
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      exchangeCommodityList:that.data.exchangeCommodityList,
      changeGoodsPriceTotal:that.data.changeGoodsPriceTotal
    });
    prevPage.calculationOrderTotal();
    app.turnBack();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})