<import src="../CouponTem/CouponTem.wxml" />
<!-- <template is="coupon1" data="{{img1}}"/> -->
<!-- <template is="coupon2" data="{{img2,img3,img4}}"/> -->

<block wx:if="{{indexFlag}}">
  <template is="coupon3" data="{{img5,storeCardList}}" />
</block>
<block wx:if="{{isVipCardSwitch}}">
  <template is="coupon7" data="{{img10,isVipCardSwitch}}" />
</block>

<view class="pop_bg" hidden='{{putCardHidden}}'></view>

<!-- <template is="coupon4" data="{{img6}}"/> -->
<!-- <template is="coupon5" data="{{img7}}"/> -->
<!-- <template is="coupon6" data="{{img8,img9}}"/> -->

<!--券弹出层--->
<import src="../bannerTemplate/bannerTemplate.wxml" />

<view style="position:fixed;left:0;z-index:100;width:100%;">
  <!--切换店铺-->
  <view class="location_store" style="text-align:left;background-color: {{mainLeftColor==''?'#fff':mainLeftColor}};"
    bindtap="switchSelectStoreBindTap">
    <image src="{{myAddress}}" lazy-load='true' mode="widthFix" style="width:28rpx;height:auto;"></image>
    <label class="storeName"
      style="font-weight:bold;font-size:30rpx;color:{{mainLeftColor==''||mainLeftColor=='#ffffff'?'#000':'#fff'}}">{{storeName}}</label>
    <label
      style="font-size:24rpx;margin:0 14rpx;vertical-align:top;margin-top:-4rpx;color:{{mainLeftColor==''||mainLeftColor=='#ffffff'?'#000':'#fff'}}">|</label>
    <text style="font-size:24rpx;color:{{mainLeftColor==''||mainLeftColor=='#ffffff'?'#000':'#fff'}};"
      class="storeChange">切换</text>
  </view>
  <!--切换店铺-->
  <view class='location_box'
    style="height:80rpx;background:linear-gradient({{mainLeftColor==''?'#fff':mainLeftColor}},{{mainRightColor==''?'#fff':mainRightColor}})">
    <icon style="left:6%;" type="search" size='16' color='#666' />
    <input placeholder='请输入关键字' bindfocus='searchBindFocus' class="code outCode"
      style="background:{{mainLeftColor==''||mainLeftColor=='#ffffff'?'#EEEEEE':'#fff'}}"></input>
    <image src="{{scan}}" bindtap='sweepCodeBindTap' style="position:absolute;left:75%;top:9px;width:60rpx;height:auto;"
      mode="widthFix"></image>
    <view bindtap="goToVipCardBindTap">
      <image src="{{vipCode}}" style="position:absolute;left:87%;top:9px;width:60rpx;height:auto;" mode="widthFix"></image>
    </view>
  </view>
</view>
<view
  style="width:750rpx;height:200rpx;background:{{mainRightColor==''?'#fff':mainRightColor}};position:fixed;top:0;left:0;">
</view>
<view style="margin-top:0;">
  <scroll-view scroll-y="true" style="height:{{winHeight}}rpx;">
    <view class="tab-content" style="margin-top:192rpx;">
      <block wx:key="unique" wx:for="{{indexListConfig}}" wx:for-item="indexConfig">
        <!--banner图片开始-->
        <block wx:if="{{indexConfig.floorType==1}}">
          <block wx:if="{{indexConfig.wechatIndexImageInfoEntities.length>0}}">
            <template is="bannerItem"
              data="{{indexConfig,autoplay,circular,tj_indicatorDots,indicatorColor,indicatorActiveColor,duration,imgheights,current,imgwidth}}" />
          </block>
        </block>
        <!--banner图片结束-->
        <!--测试-->
        <!--公告开始-->
        <block wx:elif="{{indexConfig.floorType==2}}">
          <block wx:if="{{announctList.length>0}}">
            <block wx:if="{{indexConfig.showType==1}}">
              <template is="announce{{indexConfig.showType}}"
                data="{{announctList,announce,orientation,marqueeDistance2,size,marquee2copy_status,marquee2_margin}}" />
            </block>
            <block wx:elif="{{indexConfig.showType==2}}">
              <!-->slideItem item-data="{{announctList}}" id="slideItem" />-->
              <template is="announce{{indexConfig.showType}}" data="{{announce,announctList,marqueeW,allT,size}}" />

            </block>
          </block>
        </block>
        <!--公告结束-->

        <!--中部导航开始-->
        <block wx:elif="{{indexConfig.floorType==3}}">
          <block wx:if="{{indexConfig.wechatIndexNavigationEntities.length>0}}">
            <template is="navItem" data="{{indexConfig,navigateW}}" />
          </block>
        </block>
        <!--中部导航结束-->

        <!--卡券开始-->
        <block wx:elif="{{indexConfig.floorType==4}}">
          <block wx:if="{{indexConfig.showType==1}}">
            <template is="couponType1" data="{{indexConfig}}" />
          </block>
          <block wx:elif="{{indexConfig.showType==2}}">
            <template is="couponType2" data="{{cardList}}" />
          </block>
        </block>
        <!--卡券结束-->

        <!--活动版块开始-->
        <block wx:elif="{{indexConfig.floorType==5}}">
          <block wx:if="{{indexConfig.wechatIndexMoveBlockEntities.length>0}}">
            <template is="promotion{{indexConfig.showType}}" data="{{indexConfig}}" />
          </block>
        </block>
        <!--活动版块结束-->

        <!--商品分类开始-->
        <block wx:elif="{{indexConfig.floorType==6}}">
          <block wx:if="{{indexConfig.isShow==1&&goodsTypeList.length>0}}">
            <template is="classifyItem" data="{{goodsTypeList}}" />
          </block>
        </block>
        <!--商品分类结束-->

        <!--首页推荐商品开始-->
        <block wx:elif="{{indexConfig.floorType==7}}">
          <block wx:if="{{indexConfig.isShow==1}}">
            <template is="recommand{{indexConfig.showType}}" data="{{commodityList,categoryIdList,event}}" />
          </block>
        </block>
        <!--首页推荐商品结束-->

        <!--商品模版开始-->
        <block wx:elif="{{indexConfig.floorType==8}}">
          <block wx:if="{{indexConfig.goodsTemplateList.length>0}}">
            <template is="goodsType{{indexConfig.showType}}" data="{{indexConfig,countDownList}}" />
          </block>
        </block>
        <!--商品模版结束-->

        <!--文本模版开始-->
        <block wx:elif="{{indexConfig.floorType==9}}">
          <template is="textItem" data="{{indexConfig}}" />
        </block>
        <!--文本模版结束-->

        <block wx:elif="{{indexConfig.floorType==11}}">
          <template is="video" data="{{indexConfig}}" />
        </block>
      </block>
      <!-- 团购 start -->
      <!-- <view class="groupbuy">
				<view class="groupTop clearfix">
					<view style="font-size:40rpx;float:left;">超值拼团</view>
					<view style="color:#FF7E00;float:left;margin-left:20rpx">4款商品正在拼团。。。</view>
					<view style="float:right;">
						<text>更多</text>
						<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
					</view>
				</view>
				<view class="groupCenter">
					<view class="groupGoods">
						<view>
							<image src="https://www.cn2b2c.com/image/img/crm/202008/201AEC19CD79094F42F2DF0FFBC8349F.jpg" style="width:225rpx;height:176rpx;"></image>
						</view>
						<view class="goodsName">的粉色闪电放松放松粉色闪电放松放松</view>
						<view style="color:#FF7E00;margin:4rpx 0;">3人拼团￥10</view>
						<view style="font-size:22rpx;color:#919398">单买价 ¥32.7</view>
					</view>
				</view>
			</view> -->
      <!-- 团购 end -->


      <!--上下结构-->
      <!--公司信息-->

      <!--<view class="marquee_container" style="--marqueeWidth--:{{marqueeW}};--allTs--:{{allT}};">
          <view class="a_text" style="font-size:{{size}}px">尊敬的燕达VIP客户：目前燕达玩具线上零售商城，正式启用，隔离疫情，不隔离爱，燕达玩具伴您一起成长。</view>
      </view>-->

      <view style="text-align:center;margin-top:10px;font-size:13px;color:#444;">
        <!--<view>店宝贝提供技术支持</view>-->
        <image src="{{bottom_logo}}" style="width:100%;height:auto;" mode="widthFix"></image>
      </view>
      <!--首页--->
      <!--循环商品列表开始-->
      <!--海淘商品结束 -->
    </view>
  </scroll-view>
</view>
<!--在底部的购物车-->

<!--加入购物车   立即购买-->
<view class='scroll_blo page-dialog-wrap' hidden="{{addToShoppingCartHidden}}">
  <view style='position:fixed; width:100%; height:46px;background:#fff;'>
    <view class='addgoods_pic'>
      <image lazy-load='true' mode="{{mode}}" src="{{goodsDetail.picList[0].commodityPicPath}}"></image>
    </view>
    <label class='addgoods_title'>{{goodsDetail.commodityName}}</label>
    <label class='addgoods_price'>￥{{showGoodsPrice}}</label>


    <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='hiddeAddToShoppingCart' />
  </view>
  <scroll-view scroll-y style='padding-top:46px;'>
    <block wx:key="unique" wx:for="{{showskuAllAttrList}}" wx:for-item="sku">
      <view class='goods_classify'>
        <label>{{sku.skuAttrName}}</label>
        <view class='clearfix'>
          <block wx:key="unique" wx:for="{{sku.skuAttrValueList}}" wx:for-item="skuChild">
            <text bindtap='changeSKUBindTap' data-name='{{sku.skuAttrName}}' data-childname='{{skuChild.skuAttrName}}'
              class='{{skuChild.isSelect?"active_classify":""}}'>{{skuChild.skuAttrName}}</text>
          </block>
        </view>
      </view>
    </block>

    <view class='addgoods_number clearfix' style="padding:40rpx 0;">
      <label class='limited_quantity'>购买数量
        <block wx:if="{{goodsDetail.participatePromotion}}">
          <block wx:key="unique" wx:for="{{goodsDetail.retailPromotionList}}" wx:for-item="promotion">
            <block wx:if="{{promotion.promotionLimitEnabled}}">
              <label style='font-size:12px; color:#888;'>(限购{{promoton.promotionLimitOtNum}}件)</label>
            </block>
          </block>
        </block>
      </label>
      <view class='clearfix plus_minus'>
        <label class="minus_box" bindtap='clickMinusButton'>-</label>
        <input type='number' value='{{buyCount}}' bindinput="inputBuyCount"></input>
        <label class="plus_box" bindtap='clickPlusButton'>+</label>
      </view>
      <!--<label class='stock'>库存100</label>-->
    </view>
  </scroll-view>
  <label class="btn btn-yellow pay-add-to-shoppingcart" bindtap='addShoppingCartBindTap'>加入购物车</label>
</view>
<!--加入购物车   立即购买-->
<!--在底部的购物车-->
<!--公告弹出层-->
<view class='black_bg' hidden="{{contentHidden}}"></view>
<view class='scroll_block' hidden="{{contentHidden}}">
  <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='cancelButtonBindTap' />
  <view class="announce_Title">
    {{announceTitle}}
  </view>
  <view class="announce_Content">
    {{announceContent}}
  </view>
</view>
<!--联系客服-->
<view style="position:fixed;right:20rpx;bottom:20rpx;z-index:9999;" hidden="{{!isOnlineService}}">
  <image src="{{online_service}}" mode="widthFix" style="text-align:right;width:140rpx;height:auto;"></image>
  <button open-type="contact" size="20" session-from="weapp"
    style="z-index:2;height:140rpx;width:140rpx;position:absolute;bottom:0;right:0;opacity:0;"></button>
</view>
<!--联系客服-->