var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    companyName: "",
    cardList: [],
    old_card_bg: app.imageUrl + 'old_card_bg.png',
    no_card: app.imageUrl + 'no_card.png',
    entity_card_image: app.imageUrl + 'entity_card_image.jpg',
    card_bg: app.imageUrl + 'card_bg.png',
    isFromBack: false,
    moreIcon: app.imageUrl + 'moreIcon.png',
    billHidden: true,
    openHidden: true,
    bindHidden: true,
    boundTelephoneHidden: true,
    billNo: "",
    desc: "",
    smsCode: "", //短信验证码
    second: 60, //倒计时秒数
    secondDesc: "获取短信验证码",
    vipCardTelephone: "",
    getSmsCodeState: 1,
    isSend: true,
    isHaveTelephone: true,    //是否有手机号
    isHaveTelephoneFail: false,  //是否获取手机号失败
    telephoneNew: '',
    smsCodeNew: "", //短信验证码
    secondNew: 60, //倒计时秒数
    secondDescNew: "获取短信验证码",
    vipCardTelephoneNew: "",
    getSmsCodeStateNew: 1,
    boundTelephoneHiddenNew: true,
    isSendNew: true,
    secondDescNew: "获取短信验证码",
    secondNew: 60,
    isSendNew: true
  },
  cancleBillBindTap: function () {
    app.turnBack();
  },
  billBindInput: function (e) {
    this.setData({
      billNo: e.detail.value
    })
  },
  /**
   * 校验小票号是否满足商家设置的金额
   */
  checkBillNoBindTap: function () {
    var that = this;
    var billNo = that.data.billNo.replace(/\s+/g, '');
    if (billNo.length == 0) {
      wx.showToast({
        title: '小票号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "storeId": app.getExtStoreId(),
        "billNo": billNo
      },
      url: app.projectName + '/wechatAppletLogin/getOfflineBillConsumer',
      success: function (res) {
        var flag = res.data.flag;
        var branchNo = res.data.branchNo;
        var returnMessage = res.data.returnMessage;
        if (flag) {
          that.setData({
            billHidden: true
          })
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            data: {
              "companyId": app.getExtCompanyId()
            },
            url: app.projectName + '/vipCard/queryWechatAppletVipCard',
            success: function (res) {
              var vipList = res.data.vipList;
              if (vipList != null && vipList.length > 0) {
                app.navigateToPage('/pages/openonecard/openonecard?billNo=' + billNo + "&branchNo=" + branchNo);
              } else {
                app.navigateToPage('/pages/openonecardOld/openonecardOld?billNo=' + billNo + "&branchNo=" + branchNo);
              }
            }
          })
        } else {
          wx.showToast({
            title: returnMessage,
            icon: 'none',
            duration: 1000,
            mask: true
          })
        }
      }
    })
  },
  /* 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    const scene = decodeURIComponent(options.scene);
    if (scene != "undefined" && scene != null && scene != "") {
      that.setData({
        scene: scene
      })
    }
    that.initPages();
  },
  initPages: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    app.init_getExtMessage().then(res => {
      that.getMyVipCardInfo(res.companyId, res.storeId);
      /*if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
        that.setData({
          isHaveTelephone: false
        })
      } else {
        //先查询用户有没有会员卡
        that.getMyVipCardInfo(res.companyId, res.storeId);
      }*/
    });
  },
  /**
   * 获取百威线下卡券
   */
  getUnderTheLineVipCardMessage: function (telephone, openCardFlag, boundCardFlag) {
    var that = this;
    wx.showLoading({
      title: '正在处理，请稍后',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "vipTelephone": telephone
      },
      url: app.projectName + '/vipCard/querySureBoundUnderTheLineVipCard',
      success: function (res) {
        wx.hideLoading();
        var cardList = res.data.showVipCardList;
        if (cardList != null && cardList.length > 0) {
          //线下有卡直接跳转绑卡页面
          if (boundCardFlag == 0) {
            app.redirectToPage('/pages/queryoffLineCard/queryoffLineCard?telephone=' + app.getTelephone())
          } else {
            wx.showToast({
              title: "商家未开通绑卡权限,请联系商家处理",
              icon: 'none',
              duration: 2000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  app.turnBack();
                }, 2000);
              }
            })
          }
        } else {
          //线下没有会员卡，直接新建会员卡
          if (openCardFlag == 0) {
            that.querySupplierSettingOpenCardInfo();
          } else {
            wx.showToast({
              title: "商家未开通开卡权限,请联系商家处理",
              icon: 'none',
              duration: 2000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  app.turnBack();
                }, 2000);
              }
            })
          }
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },

  /**
   * 暂不绑定手机号码
   */
  noBoundTelephoneBindTapNew: function () {
    app.turnBack();
  },
  querySupplierSettingVipFunction: function (companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": companyId
      },
      url: app.projectName + '/vipCard/querySupplierSettingVipFunction',
      success: function (res) {
        var configEntity = res.data.configEntity;
        if (configEntity != null) {
          var openCardFlag = configEntity.openCard;// 0：开通 1：不开通
          var boundCardFlag = configEntity.boundCard;
          that.setData({
            openHidden: openCardFlag == 0 ? false : true,
            bindHidden: boundCardFlag == 0 ? false : true
          })
          that.getUnderTheLineVipCardMessage(app.getTelephone(), openCardFlag, boundCardFlag);
        } else {
          //商家未开通用户可以创建会员卡的权限
          wx.showToast({
            title: "商家未开通开卡权限,请联系商家处理",
            icon: 'none',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 2000);
            }
          })
        }
      }
    })
  },
  /**
   * 查询线下实体卡
   */
  storeCardBind: function () {
    app.navigateToPage('/pages/queryoffLineCard/queryoffLineCard?telephone=' + app.getTelephone())
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function (companyId, storeId) {
    var that = this;
    var isLogin = app.isLogin();
    var odbtoken = wx.getStorageSync('odbtoken');
    var logintoken = wx.getStorageSync('loginToken');
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId,
        "phone": app.getTelephone(),
        "odbtoken":odbtoken,
        "loginToken":logintoken
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          var cardNo = cardList[0].cardId;
          app.redirectToPage('/pages/myvipCard/myvipCard?cardNo=' + cardNo);
        } else {
          //检查商家是否开通用户可以创建会员卡或绑定线下会员卡权限
          that.querySupplierSettingVipFunction(app.getExtCompanyId());
        }
      }
    })
  },
  /**
   * 展示所有可以领取的会员卡
   * 
   **/
  querySupplierSettingOpenCardInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/querySupplierSettingOpenCardInfo',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          that.setData({
            billHidden: false,
            desc: res.data.desc
          });
        } else {
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            data: {
              "companyId": app.getExtCompanyId()
            },
            url: app.projectName + '/vipCard/queryWechatAppletVipCard',
            success: function (res) {
              var vipList = res.data.vipList;
              if (vipList != null && vipList.length > 0) {
                app.redirectToPage('/pages/openonecard/openonecard?scene=' + that.data.scene);
              } else {
                app.redirectToPage('/pages/openonecardOld/openonecardOld?scene=' + that.data.scene);
              }
            }
          })
        }
      }
    });
  },
  /**
   * 使用会员卡
   */
  useVipCard: function (e) {
    var that = this;
    var no = e.currentTarget.dataset.no;
    app.redirectToPage('/pages/myvipCard/myvipCard?cardNo=' + no);
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.initPages();
    } else {
      that.setData({
        isFromBack: true
      });
    }
  },
  wechatAuthionTelephone: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.checkSession({
        success() {
          //session_key 未过期，并且在本生命周期一直有效
          var session_Key = wx.getStorageSync("session_Key");
          that.getUserTelephone(iv, encryptedData, session_Key);
        },
        fail() {
          // session_key 已经失效，需要重新执行登录流程
          wx.login({
            success: function (res) {
              if (res.code) {
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded'
                  },
                  method: "POST",
                  url: app.projectName + '/newAppletLogin/getUserOpenId',
                  data: {
                    "companyId": app.getExtCompanyId(),
                    "code": res.code
                  },
                  success: function (res) {
                    var openid = res.data.openid;
                    var session_Key = res.data.session_Key;
                    if (openid == null && session_Key == null) {
                      that.setData({
                        isHaveTelephoneFail: true
                      })
                    } else {
                      that.getUserTelephone(iv, encryptedData, session_Key);
                      wx.removeStorageSync("openId");
                      app.setStorage({
                        key: 'openId',
                        data: openid
                      });
                      wx.removeStorageSync("session_Key");
                      app.setStorage({
                        key: 'session_Key',
                        data: session_Key
                      });
                    }

                  }
                })
              }
            }
          })
        }
      })
    }
  },
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newAppletLogin/getUserTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var telephone = res.data.telephone;
        if (telephone.length > 0) {
          var userSession = wx.getStorageSync('userSession');
          userSession.telephone = telephone;
          userSession.loginAccount = telephone;
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
          that.initPages();
        } else {
          app.showModal({
            title: '提示',
            content: "自动绑定失败，请手动绑定，或者退出重试"
          });
          that.setData({
            isHaveTelephoneFail: true
          })
        }
        that.setData({
          telephoneNew: telephone
        })
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "自动绑定失败，请手动绑定，或者退出重试"
        });
        that.setData({
          isHaveTelephoneFail: true
        })
      }
    })
  },
  oBoundTelephoneBindTapNew: function () {
    this.setData({
      boundTelephoneHiddenNew: true
    })
  },
  /**
   * 绑定手机号码
   */
  nowBoundTelephoneBindTapNew: function () {
    var that = this;
    var telephone = that.data.vipCardTelephoneNew.replace(/\s+/g, '');
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (telephone.length < 11) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var smsCode = that.data.smsCodeNew.replace(/\s+/g, '');
    if (smsCode.length == 0) {
      wx.showToast({
        title: '请输入验证码',
        duration: 1000,
        icon: 'none',
        mask: true
      })
      return false;
    }
    if (that.data.getSmsCodeStateNew == 1) {
      wx.showToast({
        title: '请先获取短信验证码',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "openId": app.getOpenId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "telephone": telephone,
        "smsCode": smsCode
      },
      url: app.projectName + '/newAppletLogin/nowBoundTelephone',
      success: function (res) {
        var returnFlag = res.data.returnFlag;
        if (returnFlag) {
          wx.showToast({
            title: "绑定成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                if (telephone.length > 0) {
                  var userSession = wx.getStorageSync('userSession');
                  userSession.telephone = telephone;
                  userSession.loginAccount = telephone;
                  app.setStorage({
                    key: 'userSession',
                    data: userSession
                  });
                  that.initPages();
                }
              }, 2000);
            }
          })
        } else {
          wx.showToast({
            title: res.data.message,
            duration: 1000,
            icon: 'none',
            mask: true
          })
        }
      }
    })
  },
  smsCodeBindInputNew: function (e) {
    this.setData({
      smsCodeNew: e.detail.value
    })
  },
  /**
   * 点击获取短信验证码
   */
  smsBindTapNew: function () {
    var that = this;
    var telephone = that.data.vipCardTelephoneNew.replace(/\s+/g, '');
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (telephone.length < 11) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (!that.data.isSendNew) {
      return;
    }
    wx.showToast({
      title: '获取成功',
      icon: 'success',
      duration: 1000,
      mask: true
    })
    wx.request({
      url: app.projectName + '/applet/querySMSCode',
      data: {
        "type": "5",
        "telephone": telephone,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          that.setData({
            getSmsCodeStateNew: 2
          })
          that.countdownNew(that);
        } else {
          app.showModal({
            title: '提示',
            content: message == "" ? "系统异常，稍后在试" : message
          });
          return;
        }
      }
    })
  },
  /**
   * 倒计时开始
   */
  countdownNew: function (that) {
    var second = that.data.secondNew;
    if (second == 0) {
      that.setData({
        secondDescNew: "获取短信验证码",
        secondNew: 60,
        isSendNew: true
      });
      return;
    }
    var time = setTimeout(function () {
      that.setData({
        isSendNew: false,
        secondNew: second - 1,
        secondDescNew: second + "秒后重新获取"
      });
      that.countdownNew(that);
    }, 1000)
  },
  bindVipCardTelephoneBindInputNew: function (e) {
    this.setData({
      vipCardTelephoneNew: e.detail.value
    })
  },
})