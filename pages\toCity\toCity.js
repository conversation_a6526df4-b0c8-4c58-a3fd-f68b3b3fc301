/*
 * @Author: lyk
 * @Date: 2019-11-22 11:07:01
 * @LastEditTime: 2019-11-25 10:02:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \retailTemplate\pages\toCity\toCity.js
 */
var app = getApp();
var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    myAddress: app.imageUrl + 'locate.png',
    localUserCity: "",
    localUserAddress: "",
    latitude: "",
    longitude: "",
    searchData: []
  },
  goShowCity: function () {
    app.navigateToPage('/pages/showCity/showCity?localCity=' + this.data.localUserCity);
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var localUserCity = options.localUserCity;
    var localUserAddress = options.localUserAddress;
    var latitude = options.latitude;
    var longitude = options.longitude;
    that.nearbySearch(localUserCity, localUserAddress, latitude, longitude);
    that.setData({
      localUserCity: localUserCity,
      localUserAddress: localUserAddress,
      latitude: latitude,
      longitude: longitude
    })
  },
  /**
   * 联想词输入功能
   */
  localAddressBindInput: function (e) {
    var that = this;
    // 实例化API核心类
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // var address = e.detail.value.replace(/\s+/g, '');
    // if (address.length > 0) {
    //   qqmapsdk.getSuggestion({
    //     location: {
    //       latitude: that.data.latitude,
    //       longitude: that.data.longitude
    //     },
    //     //获取输入框值并设置keyword参数
    //     keyword: address, //用户输入的关键词，可设置固定值,如keyword:'KFC'
    //     region: that.data.localUserCity, //设置城市名，限制关键词所示的地域范围，非必填参数
    //     success: function (res) { //搜索成功后的回调
    //       that.setData({
    //         searchData: res.data
    //       })
    //     },
    //     fail: function (error) {
    //       console.error(error);
    //     }
    //   });
    // }
  },
  /**
   * 选择当前位置
   * @param {*} e 
   */
  selectLocatAddressBindTap: function (e) {
    var that = this;
    var address = e.currentTarget.dataset.address;
    var latitude = e.currentTarget.dataset.latitude;
    var longitude = e.currentTarget.dataset.longitude;
    var locationObj = {
      "address": address,
      "latitude": latitude,
      "longitude": longitude,
      "localUserCity": that.data.localUserCity
    };
    wx.removeStorageSync("locationKey");
    app.setStorage({
      key: 'locationKey',
      data: locationObj
    });
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      latitude: latitude,
      longitude: longitude,
      localUserAddress: address,
      localUserCity: that.data.localUserCity,
      showReturnStoreList: []
    });
    prevPage.queryStoreInfo(latitude, longitude);
    app.turnBack();
  },
  nearbySearch: function (localUserCity, localUserAddress, latitude, longitude) {
    var that = this;
    // 实例化API核心类
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // qqmapsdk.search({
    //   region: localUserCity,
    //   keyword: localUserAddress, //搜索关键词
    //   location: {
    //     latitude: latitude,
    //     longitude: longitude
    //   }, //设置周边搜索中心点
    //   success: function (res) { //搜索成功后的回调
    //     that.setData({
    //       searchData: res.data
    //     })
    //   },
    //   fail: function (res) {
    //   }
    // });
  }
})