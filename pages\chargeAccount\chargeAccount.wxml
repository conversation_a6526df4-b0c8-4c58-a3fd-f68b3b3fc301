<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<view>
	<view class="score_top">
		<!-- <image src="{{charge_bg}}" style="width:750rpx" mode="widthFix"></image> -->
		<!--<view class="top_title">可用积分</view>-->
		<view style="color:#525252;font-size:34rpx;padding:40rpx 0 20rpx;">账户余额</view>
		<view class="top_score"><text style="font-size:38rpx;">￥</text>{{spareCash}}</view>
		<view class="score_area" bindtap="goChargeDetail" style="font-size:24rpx;color:#333;">查看余额明细 ></view>
	</view>
	<view class="scoreContent clearfix">
		<view style="font-size:28rpx;font-weight:bold;padding:20rpx;">充值金额</view>
		<block wx:key="unique" wx:for="{{rechargeSchemeList}}" wx:for-item="scheme" wx:for-index="sche_index">
			<block wx:if="{{scheme.type==0}}">
				<view class="chargeAmount {{scheme.isSelect?'border_mask':''}}" style="line-height:80rpx;" data-index="{{sche_index}}" bindtap="selectPricePlanBindTap">
					<view class="{{scheme.isSelect?'mask_bg':''}}"></view>
					<view class="money_add {{scheme.isSelect?'amount_mask':''}}">{{scheme.rechargeAmount}}元</view>
				</view>
			</block>
			<block wx:elif="{{scheme.type==1}}">
				<view class="chargeMode2 {{scheme.isSelect?'border_mask':''}}" data-index="{{sche_index}}" bindtap="selectPricePlanBindTap">
					<view class="{{scheme.isSelect?'mask_bg':''}}"></view>
					<view class="modeAmount {{scheme.isSelect?'amount_mask':''}}">{{scheme.rechargeAmount}}元</view>
					<view class="modeDis {{scheme.isSelect?'dis_mask':''}}">特价
						<text>{{scheme.discount}}</text>折</view>
				</view>
			</block>
			<block wx:elif="{{scheme.type==2}}">
				<view class="chargeMode2 {{scheme.isSelect?'border_mask':''}}" data-index="{{sche_index}}" bindtap="selectPricePlanBindTap">
					<view class="{{scheme.isSelect?'mask_bg':''}}"></view>
					<view class="modeAmount {{scheme.isSelect?'amount_mask':''}}">{{scheme.rechargeAmount}}元</view>
					<view class="modeDis {{scheme.isSelect?'dis_mask':''}}">优惠
						<text>{{scheme.discountAmount}}</text>元</view>
				</view>
			</block>
			<block wx:elif="{{scheme.type==3}}">
				<view class="chargeAmount {{scheme.isSelect?'border_mask':''}}" data-index="{{sche_index}}" bindtap="selectPricePlanBindTap">
					<view class="{{scheme.isSelect?'mask_bg':''}}"></view>
					<view class="money_add {{scheme.isSelect?'amount_mask':''}}">{{scheme.rechargeAmount}}元</view>
					<view class="gift_add {{scheme.isSelect?'dis_mask':''}}">送{{scheme.giveAmount}}元</view>
				</view>
			</block>
			<block wx:elif="{{scheme.type==4}}">
				<view class="chargeAmount {{scheme.isSelect?'border_mask':''}}" style="line-height:80rpx;" data-index="{{sche_index}}" bindtap="selectPricePlanBindTap">
					<view class="{{scheme.isSelect?'mask_bg':''}}"></view>
					<view class="money_add {{scheme.isSelect?'amount_mask':''}}" style="line-height: 40rpx;">{{scheme.rechargeAmount}}元</view>
					<view class="couponTitle {{scheme.isSelect?'amount_mask':''}}">活动：送卡券</view>
				</view>
			</block>

		</block>
	</view>
	<view class="goCharge" bindtap="nowPayBindTap">立即充值</view>
</view>
	<!--调用弹窗-->
	<template  is="reward" data="{{...item}}" />