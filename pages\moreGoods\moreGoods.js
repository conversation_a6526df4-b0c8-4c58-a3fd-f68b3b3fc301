var app = getApp();
Page({

  data: {
    commodityBaseList: [],
    pageSize: 10,
    currpage: 1,
    categoryId: "",
    mode: "widthFix",
    event: app.imageUrl + 'event.png',
    showGoodsTemplateList: [],
    totalHidden: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    /*var type = options.type;
    var id = options.id;
    that.setData({
      id: id,
      type: type
    });
    that.queryGoodsInfoByIndexConfigId(id, type);*/
    var idJson = options.idJson;
    that.queryGoodsById(idJson)
  },
  queryGoodsById: function (idJson){
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newIndexTemplate/queryGoodsInfoByGoodsIdList',
      data: {
        "goodsJSON": idJson,
        "identity": 1,
        "storeId": app.getExtStoreId()
      },
      success: function (res) {
        var new_goodsTemplateList = res.data.goodsTemplateList;
        that.setData({
          totalHidden: new_goodsTemplateList.length > 0 ? true : false
        });
        var old_goodsTemplateList = that.data.showGoodsTemplateList;
        if (old_goodsTemplateList != null && old_goodsTemplateList.length > 0) {
          new_goodsTemplateList = old_goodsTemplateList.concat(new_goodsTemplateList);
        }
        that.setData({
          showGoodsTemplateList: new_goodsTemplateList,
        });
      }
    })
  },
  queryGoodsInfoByIndexConfigId: function (id, type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/indexTemplate/queryGoodsInfoByIndexConfigId',
      data: {
        "id": id,
        "type": type,
        "identity": 1,
        "page": that.data.currpage,
        "pageSize": that.data.pageSize,
        "storeId": app.getExtStoreId()
      },
      success: function (res) {
        var new_goodsTemplateList = res.data.goodsTemplateList;
        that.setData({
          totalHidden: new_goodsTemplateList.length > 0 ? true : false
        });
        var old_goodsTemplateList = that.data.showGoodsTemplateList;
        if (old_goodsTemplateList != null && old_goodsTemplateList.length > 0) {
          new_goodsTemplateList = old_goodsTemplateList.concat(new_goodsTemplateList);
        }
        that.setData({
          showGoodsTemplateList: new_goodsTemplateList,
        });
      }
    })
  },
  imageClick: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (that.data.totalHidden) {
      that.setData({
        currpage: that.data.currpage + 1
      });
      that.queryGoodsInfoByIndexConfigId(that.data.id, that.data.type);
    }
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})