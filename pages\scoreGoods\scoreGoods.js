// pages/scoreGoods/scoreGoods.js
var WxParse = require('../../components/wxParse/wxParse.js');
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autoplay: false, //是否自动切换
    interval: 2000, //自动切换时间间隔
    duration: 500, //滑动动画时长
    circular: true, //是否采用衔接滑动
    indicatorDots: true, //是否显示面板指示点
    indicatorColor: "rgba(0, 0, 0, .3)", //指示点颜色
    indicatorActiveColor: "#FF7E00", //当前选中的指示点颜色
    imgheights: [],
    current: 0,
    imgwidth: 750,
    array: [{
      mode: 'aspectFill',
    }],
    exchangeNBean: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var exchangeCommodityId = options.exchangeCommodityId;
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/integralService/getExchangeCommodityDetail',
      data: {
        "exchangeCommodityId": exchangeCommodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "localStoreId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var exchangeNBean = res.data.exchangeNBean;

        WxParse.wxParse("commodityIntroduce", 'html', exchangeNBean.commodityBaseInfo.commodityIntroduce, that, 5);
        that.setData({
          exchangeNBean: exchangeNBean
        })
      }
    })
  },
  imageLoad: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight
    var imgheights = this.data.imgheights
    //把每一张图片的高度记录到数组里  
    imgheights.push(imgheight)
    this.setData({
      imgheights: imgheights,
      current: 0
    })
  },
  pregenerateCommodityRetailOrderBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var exchangeCommodityId = e.currentTarget.dataset.id;
    app.navigateToPage('/pages/scoreGoodsPay/scoreGoodsPay?exchangeCommodityId=' + exchangeCommodityId);
  }
})