// pages/goodsDetailNew/goodsDetailNew.js
const app = getApp()
const popup = require('../popupTemplate/popupTemplate.js');
var commodityId; //商品ID
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autoplay: false, //是否自动切换
    interval: 5000, //自动切换时间间隔
    duration: 500, //滑动动画时长
    circular: true, //是否采用衔接滑动
    indicatorDots: false, //是否显示面板指示点
    swiperIndex:1,//自定义指示点起始位置
    goShare: app.imageUrl + 'share.png',
    dbbLogo: app.imageUrl + 'dbb_logo.jpg',
    indexIcon: app.imageUrl + 'accountManager/home.png',
    online: app.imageUrl + 'shopDetails/online.png',
    shopcart: app.imageUrl + 'shopDetails/shopcart_icon.png',
    close: app.imageUrl + 'goodsnew_close.png',
    goodsList:[{},{},{}],
    imgList:[{type:0},{type:1}],
    buyHidden:true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    commodityId = options.commodityId;
    if (commodityId == "undefined" || commodityId == null || commodityId == "") {
      if (scene != "undefined" && scene != null && scene != "") {
        commodityId = scene.substring(0, scene.indexOf("@"));
      } else {
        commodityId = options.goodsId;
      }
    }
  },
    //加购物车或者购买商品时出现弹窗
    nowBuyBindTap:function(){
      this.setData({
        buyHidden:false
      })
    },
    //隐藏弹窗
    hiddeAddToShoppingCart:function(){
      this.setData({
        buyHidden:true
      })
    },
  //事件处理函数
  swiperChange(e){
    this.setData({
      swiperIndex:e.detail.current +1
    })
  },
  //视频播放
  bofang:function() {
    var that = this
    that.setData({
      autoplay: false
    })
  },
  //结束播放触发的事件
  endVideo:function() {
    var that = this
    that.setData({
      autoplay: true
    })
  },
  shareGoodsBind:function(){
    this.share();
  },
    //调用分享弹窗
    share: function () {
      var that = this;
      popup.animationEvents(that, 0, true);
  
    },
    //隐藏弹窗
    hiddenFloatView: function (e) {
      var that = this;
      popup.animationEvents(that, 200, false);
    },
    nono: function () {
  
    },
      /**
   * 获取商品详情海报
   */
  getPosterBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (app.getTelephone() == null || app.getTelephone().length == 0) {
      app.reLaunchToPage("/pages/changeProfile/changeProfile");
      return;
    }
    wx.showLoading({
      title: '正在加载，请稍后',
      mask: true
    })
    that.setData({
      shareBlackBgHidden: true,
      shareShowHidden: true
    });
    var share = e.currentTarget.dataset.share;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "isDistribution": share,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "commodityId": commodityId,
        "recommendUserId": app.getUserId(),
        "type": 1
      },
      url: app.projectName + '/newAppletQR/getWechatAppletPersonalPosterQRcode',
      success: function (res) {
        var returnUrl = res.data.returnUrl;
        if (returnUrl != "") {
          wx.getImageInfo({
            src: returnUrl, //服务器返回的图片地址
            success: function (res) {
              //res.path是网络图片的本地地址
              let qrcodePath = res.path;
              that.setData({
                localPostQrcodeImage: qrcodePath
              })
              var commodityMainPic = that.data.goodsDetail.commodityMainPic;
              if (commodityMainPic.indexOf("https") == -1) {
                commodityMainPic = commodityMainPic.replace("http", "https");
              }
              wx.getImageInfo({
                src: commodityMainPic, //服务器返回的图片地址
                success: function (res) {
                  //res.path是网络图片的本地地址
                  let imagePath = res.path;
                  that.setData({
                    localImageMain: imagePath
                  })
                  if (app.getExtStoreImage() != null && app.getExtStoreImage() != "") {
                    wx.getImageInfo({
                      src: app.getExtStoreImage(), //服务器返回的图片地址
                      success: function (res) {
                        //res.path是网络图片的本地地址
                        let Path = res.path;
                        that.setData({
                          localStoreImage: Path
                        })
                        that.initCanvas();
                      },
                      fail: function (res) {
                        //失败回调
                        wx.hideLoading();
                      }
                    });

                  } else {
                    that.setData({
                      localStoreImage: qrcodePath
                    })
                    that.initCanvas();
                  }
                },
                fail: function (res) {
                  //失败回调
                  wx.hideLoading();
                }
              });
            },
            fail: function (res) {
              //失败回调
              wx.hideLoading();
            }
          });
        }
      },
      fail: function (res) {
        wx.hideLoading();
      }
    })
  },
   /*分享二维码海报*/
   initCanvas: function (returnUrl) {
    var userName = "";
    if (app.getLoginName() == undefined) {
      userName = app.getExtStoreName();
    } else {
      userName = app.getLoginName();
    }
    var ctx = wx.createCanvasContext('poster');
    ctx.fillStyle = "#fff";
    ctx.fillRect(0, 0, 600, 900);
    ctx.drawImage(this.data.localStoreImage, 25, 20, 50, 50, 0, 0); //画用户头像
    ctx.setFontSize(15);
    ctx.setFillStyle('#000');
    ctx.fillText(userName, 90, 40);
    ctx.setFontSize(14);
    ctx.setFillStyle('#666');
    ctx.fillText('给你推荐了一个好东西', 90, 60);
    ctx.drawImage(this.data.localImageMain, 25, 80, 250, 250, 0, 0); //画海报
    ctx.drawImage(this.data.localPostQrcodeImage, 200, 350, 80, 80, 0, 0); //画二维码
    ctx.setFontSize(20);
    ctx.setFillStyle('#FF7E00');
    ctx.fillText('￥' + this.data.showGoodsPrice + '/' + this.data.goodsOtUnit, 25, 370);
    if (this.data.cutPrice > 0 && this.data.cutPrice - this.data.showGoodsPrice > 0) {
      var tmp_width = 80 + ctx.measureText(this.data.showGoodsPrice).width;
      var end_width = ctx.measureText(this.data.cutPrice).width;
      ctx.setFontSize(15);
      ctx.setFillStyle('#666');
      ctx.fillText('￥' + this.data.cutPrice, tmp_width, 370);
      ctx.beginPath();
      ctx.moveTo(tmp_width, 365);
      ctx.lineTo(tmp_width + end_width + 10, 365);
      ctx.lineWidth = 1;
      ctx.strokeStyle = '#666';
      ctx.stroke();
    }
    ctx.setFontSize(14);
    ctx.setFillStyle('#666');
    ctx.fillText("测试的", 25, 390, 160, 300);
    ctx.stroke();
    ctx.draw();
    this.save(); //生成微信临时模板文件path
  },
  save: function () {
    var that = this;
    setTimeout(function () {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: that.data.imageWidth,
        height: that.data.imageHeight,
        destWidth: that.data.imageWidth,
        destHeight: that.data.imageHeight,
        canvasId: 'poster',
        success: function (res) {
          that.data.saveUrl = res.tempFilePath //保存临时模板文件路径
        },
        fail: function (res) {
          return;
        }
      })
    }, 500);
    that.setData({
      posterHidden: false,
      shareBlackBgHidden: false,
      shareShowHidden: true
    });
    wx.hideLoading();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
})