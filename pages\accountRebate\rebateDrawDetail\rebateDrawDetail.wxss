page{
  background:#f5f5f5;
  font-size:29rpx;
}
.accountDetail{
  padding-bottom:30rpx;
}
.detailTop{
  display:flex;
  justify-content:space-around;
  padding-top:20rpx;
  background:#ffffff;
  position:fixed;
  top:0;
  left:0;
  right:0;
}
.detailTop view{
  padding:16rpx 0;
}
.detailTop .active{
  border-bottom:4rpx solid #FF7E00;
  color:#FF7E00;
}
.selectImage{
  width:20rpx;
  margin-left:8rpx;
  vertical-align:middle;
}
.r_wrap{
  width:700rpx;
  margin:25rpx;
  margin-top:125rpx;
}
.r_wrap .oneItem{
  background:#fff;
  border-radius:20rpx;
  margin:30rpx 0;
  padding:20rpx;
}
.oneItem .itemTop{
  padding-bottom:20rpx;
  border-bottom:1px solid #ddd;
  display:flex;
  align-items:center;
}
.itemTop .checkImg{
  width:44rpx;
  margin-right:20rpx;
}
.itemTop .checkState{
  font-size:30rpx;
  margin-right:20rpx;
}
.itemTop .checkTime{
  font-size:26rpx;
  color:#666;
  flex:1;
  display:flex;
  align-items:center;
}
.itemTop .showImg{
  width:30rpx;
}
.itemContent{
  padding:20rpx 0
}
.content_v{
  display:flex;
  justify-content: space-between;
  align-items: center;
  margin:16rpx 0;
}
.item_no{
  display:flex;
}
.success_c{
  color:#3996F2;
}
.itemDetail{
  border-top:1px solid #ddd;
  padding-top:20rpx;
}
.item_no .order_no{
  flex-direction:row-reverse;
  display:flex;
  flex:1;
  text-align:right;
}
.item_no .order_copy{
  color:#4A79E5;
  margin-left:20rpx;
}
.under_c{
  color:#FF7E00;
}
.fail_c{
  color:#EF2700;
  flex:1;
  flex-direction: column-reverse;
  margin-left:10rpx;
  text-align:right;
}
.black_bg{
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}
.popWrap{
  position:fixed;
  top:0;
  left:0;
  right:0;
  z-index:99999;
  background:#fff;
  font-size:29rpx;
  padding-bottom:30rpx;
}
.popWrap .detailTop{
  display:flex;
  justify-content:space-around;
}
.timeSelect{
  margin-top:40rpx;
  padding-left:30rpx;
}
.timePicker{
  height: 80rpx;
  display: flex;
  padding-left:20rpx;
  margin-top:20rpx;
}
.timeBox{
  width: 300rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 8rpx;
  margin:10rpx;
}
.picker{
  border:1px solid #ddd;
}
.popBtn{
  display:flex;
  justify-content:space-around;
  margin-top:30rpx;
}
.popBtn label{
  background:#FF7E00;
  color:#fff;
  width:40%;
  text-align:center;
  height:70rpx;
  line-height:70rpx;
  border-radius:10rpx;
}
.selectItem{
  color:#FF7E00;
}
.success_p{
  color:#3988B1;
}
.no_journal{
  text-align:center;
}
.no_journal image{
    margin-top:150rpx;
    width:240rpx;
}