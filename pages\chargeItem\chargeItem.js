// pages/chargeItem/chargeItem.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cardNo: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var cardNo = options.cardNo;
    var that = this;
    that.setData({
      cardNo: cardNo
    })
  },
  cumsuptionTotalClick: function () {
    var that = this;
    app.navigateToPage("/pages/chargeDetail/chargeDetail?cardNo=" + that.data.cardNo);
  },
  cumsuptionDetailClick: function () {
    var that = this;
    app.navigateToPage("/pages/consumptionDetail/consumptionDetail?cardNo=" + that.data.cardNo);
  }
})