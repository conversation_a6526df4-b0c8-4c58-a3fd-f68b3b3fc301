<wxs src="../../wxs/subutil.wxs" module="tools" />
<!--搜索框模块-->
<template name="searchItem">
  <view class="s_box" style="background:{{indexConfig.searchBean.bgColor}}">
    <div class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </div>
    <image bindtap='sweepCodeBindTap' src="{{scan}}" hidden="{{indexConfig.searchBean.scanState==0?false:true}}"
      mode='widthFix'></image>
    <image bindtap="goToVipCardBindTap" src="{{vipCode}}" hidden="{{indexConfig.searchBean.vipState==0?false:true}}"
      mode='widthFix'></image>
  </view>
</template>
<!--banner图模块-->
<template name="bannerItem">
  <swiper autoplay="{{indexConfig.bannerBean.switchTime>0?true:false}}"
    interval="{{indexConfig.bannerBean.switchTime*1000}}" circular="{{indexConfig.bannerBean.switchTime>0?true:false}}"
    indicator-dots="{{indexConfig.bannerBean.indicationPoint==0?true:false}}" indicator-color="#fff"
    indicator-active-color="{{indexConfig.bannerBean.indicationPointColor}}" duration="500"
    style='height:{{imgheights[current]}}rpx;'>
    <block wx:key="unique" wx:for-item="banner"
      wx:for="{{indexConfig.bannerBean.wechatAppletIndexBannerImagerContentEntity}}">
      <swiper-item>
        <image lazy-load='true' src="{{banner.imageUrl}}" data-content="{{banner}}" bindtap='jumpPageBindTap'
          bindload="imageLoad" class="slide-image" style="width:{{imgwidth}}rpx; height:{{imgheights[current]}}rpx;" />
      </swiper-item>
    </block>
  </swiper>
</template>
<!--公告模块开始-->
<template name="noticeItem">
  <!--多条-->
  <block wx:if="{{announctList.length>1}}">
    <view class="clearfix annoucnePart" style="padding-top:10rpx;padding-bottom:10rpx;">
      <image mode="widthFix" src="{{indexConfig.noticeBean.hornImage}}" lazy-load='true' class="showAnnounce"></image>
      <view class="announceDetail">
        <swiper display-multiple-items="1" vertical="true" autoplay="true" interval="3000" circular="false}}"
          style="height:50rpx;">
          <block wx:key="unique" wx:for="{{announctList}}" wx:for-item="ann" wx:for-index="index">
            <swiper-item>
              <view data-content="{{ann}}" bindtap='jumpPageBindTap' class="announceItem" style="oveflow:hidden;"
                data-index='{{index}}'>
                <text style="font-size:28rpx;color:{{indexConfig.noticeBean.color}};">{{ann.noticeContent}}</text>
              </view>
            </swiper-item>
          </block>
        </swiper>
      </view>
    </view>
  </block>
  <!--单条-->
  <block wx:if="{{announctList.length == 1}}">
    <view class="example">
      <view class="marquee_box">
        <image style="background:{{bg_color}}" lazy-load='true' src="{{indexConfig.noticeBean.hornImage}}"></image>
        <view class="marquee_container" style="--marqueeWidth--:{{marqueeW}};--allTs--:{{allT}};">
          <block wx:key="unique" wx:for="{{announctList}}" wx:for-item="ann" wx:for-index="index">
            <view data-content="{{ann}}" bindtap='jumpPageBindTap' class="a_text"
              style="color:{{indexConfig.noticeBean.color}};font-size:{{size}}px">{{ann.noticeContent}}</view>
          </block>
        </view>
      </view>
    </view>
  </block>
</template>
<!--公告模块结束-->
<!--店铺导航模块开始-->
<template name="navigateItem">
  <view class="navi_box">
    <block wx:key="unique" wx:for="{{indexConfig.navigationBean.wechatAppletIndexNavigationContentEntities}}"
      wx:for-item="navigateItem" wx:for-index="index">
      <view data-content="{{navigateItem}}" bindtap='jumpPageBindTap'>
        <image src="{{navigateItem.navigationImage}}"></image>
        <view class="navi_text" style="color:{{navigateItem.fontColor}}">{{navigateItem.navigationName}}</view>
      </view>
    </block>
  </view>
</template>
<!--店铺导航模块结束-->
<!--图片导航模块开始-->
<template name="picNavItem">
  <!--第一到四种展示的样式-->
  <block wx:if="{{indexConfig.customBean.showType>0 && indexConfig.customBean.showType<5}}">
    <view class="picNav_box" style="width:{{indexConfig.customBean.pageSpace==0?700:750}}rpx;">
      <block wx:if="{{indexConfig.customBean.showType == 1}}">
        <view class="pic_box1">
          <image data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[0]}}"
            bindtap='jumpPageBindTap'
            src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[0].imageUrl}}" mode="widthFix"></image>
        </view>
      </block>
      <block wx:else>
        <block wx:key="unique" wx:for="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities}}"
          wx:for-item="customItem" wx:for-index="index">
          <view data-content="{{customItem}}" bindtap='jumpPageBindTap'>
            <block wx:if="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities.length>index+1}}">
              <image src="{{customItem.imageUrl}}"
                style="margin-right:{{indexConfig.customBean.lrSpace}}rpx;width:{{tools.sub.formatWidth(indexConfig.customBean.pageSpace,indexConfig.customBean.lrSpace,indexConfig.customBean.showType)}}"
                mode="widthFix"></image>
            </block>
            <block wx:else>
              <image src="{{customItem.imageUrl}}"
                style="margin-right:0;width:{{tools.sub.formatWidth(indexConfig.customBean.pageSpace,indexConfig.customBean.lrSpace,indexConfig.customBean.showType)}}"
                mode="widthFix"></image>
            </block>
          </view>
        </block>
      </block>
    </view>
  </block>
  <!--左一右二-->
  <block wx:elif="{{indexConfig.customBean.showType == 5}}">
    <view class="picNav_box" style="width:{{indexConfig.customBean.pageSpace==0?700:750}}rpx;">
      <view data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[0]}}"
        bindtap='jumpPageBindTap' class="pic_box2">
        <image src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[0].imageUrl}}"></image>
      </view>
      <view class="rightPic">
        <view data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[1]}}"
          bindtap='jumpPageBindTap'>
          <image src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[1].imageUrl}}"></image>
        </view>
        <view data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[2]}}"
          bindtap='jumpPageBindTap'>
          <image src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[2].imageUrl}}"></image>
        </view>
      </view>
    </view>
  </block>
  <!--左二右一-->
  <block wx:elif="{{indexConfig.customBean.showType == 6}}">
    <view class="picNav_box" style="width:{{indexConfig.customBean.pageSpace==0?700:750}}rpx;">
      <view class="leftPic">
        <view data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[0]}}"
          bindtap='jumpPageBindTap'>
          <image src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[0].imageUrl}}"></image>
        </view>
        <view data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[1]}}"
          bindtap='jumpPageBindTap'>
          <image src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[1].imageUrl}}"></image>
        </view>
      </view>
      <view data-content="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[2]}}"
        bindtap='jumpPageBindTap' class="pic_box3">
        <image src="{{indexConfig.customBean.wechatAppletIndexCustomContentEntities[2].imageUrl}}"></image>
      </view>
    </view>
  </block>
</template>
<!--图片导航模块结束-->
<!--商品模块开始-->
<template name="commodityItem">
  <import src="../styleTemplate/styleTemplate.wxml" />
  <!--上图下文-->
  <view style="padding-top:20rpx;">
    <block wx:if="{{indexConfig.goodsBean.showType == 1}}">
      <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
        wx:for-index="goodsIndex">
        <view class="commodity_box1" style="border-radius:{{indexConfig.goodsBean.borderContent == 2?30:0}}rpx;">
          <view style="position:relative;" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
            data-tag="{{goods.clientCommodityTag}}">
            <image lazy-load='true'
              style="width:100%;border-top-left-radius:{{indexConfig.goodsBean.borderContent == 2?30:0}}rpx;border-top-right-radius:{{indexConfig.goodsBean.borderContent == 2?30:0}}rpx;"
              mode="widthFix" bindtap="imageClick" data-name="{{goods.commodityName}}"
              data-pic="{{goods.commodityMainPic}}" data-commodityId="{{goods.commodityId}}"
              src="{{goods.commodityMainPic}}"></image>
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
              <template is="mark1"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
              <template is="mark2"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
              <template is="mark3"
                data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
            </block>
          </view>
          <view class="goods_desc" style="flex:1;padding-right:30rpx;">
            <view class="desc_title">{{goods.commodityName}}</view>
            <view class="desc_spec">{{goods.commodityAdContent}}</view>
            <view style="display:flex;justify-content:space-between;align-items:center;">
              <view class="desc_price">
                <label class="price_tag">￥</label>
                <label class="price_inner"
                  hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                <label class="price_inner"
                  hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                  <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}"
                    class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                </block>
                <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                  <label hidden="{{goods.commodityOtUnitDefault==1?false:true}}"
                    class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                </block>
                <block
                  wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">
                  <label>
                    <text class="promotionDesc">{{goods.promotionName}}</text>
                  </label>
                </block>
              </view>
              <view class="price_r" hidden="{{indexConfig.goodsBean.shopCartHidden==1?false:true}}" bindtap="addCartClick" data-id="{{goods.commodityId}}">
                <block wx:if="{{indexConfig.goodsBean.shopCartStyle == 1}}">
                  <image src="{{shop_cart1}}" style="width:44rpx;" mode="widthFix" ></image>
                </block>
                <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 2}}">
                  <image src="{{shop_cart2}}" style="width:44rpx;" mode="widthFix" ></image>
                </block>
                <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 3}}">
                  <text class="goodsText">{{indexConfig.goodsBean.shopCartBtnText}}</text>
                </block>
              </view>
            </view>
          </view>
          <view style="height:40rpx;margin-left:20rpx;margin-top:-20rpx;">
            <label class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>
          </view>
        </view>
      </block>
    </block>
    <!--左图右文-->
    <block wx:elif="{{indexConfig.goodsBean.showType == 2}}">
      <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
        wx:for-index="goodsIndex">
        <view class="commodity_box2" style="border-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;">
          <view style="position:relative;" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
            data-tag="{{goods.clientCommodityTag}}">
            <image lazy-load='true'
              style="border-top-left-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;border-bottom-left-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;"
              bindtap="imageClick" data-commodityId="{{goods.commodityId}}" data-name="{{goods.commodityName}}"
              data-pic="{{goods.commodityMainPic}}" src="{{goods.commodityMainPic}}"></image>
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
              <template is="mark1"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
              <template is="mark2"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
              <template is="mark3"
                data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
            </block>
          </view>
          <view class="box_one">
            <view class="goods_desc" style="flex:1;padding-right:30rpx;">
              <view class="desc_title">{{goods.commodityName}}</view>
              <view style="display:flex;justify-content:space-between;align-items:center;">
                <view class="desc_price">
                  <label class="price_tag">￥</label>
                  <label class="price_inner"
                    hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                  <label class="price_inner"
                    hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                  <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                    <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}"
                      class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                  </block>
                  <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                    <label hidden="{{goods.commodityOtUnitDefault==1?false:true}}"
                      class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                  </block>
                  <block
                    wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0}}">
                    <label>
                      <text class="promotionDesc">{{goods.promotionName}}</text>
                    </label>
                  </block>
                </view>
                <view class="price_r" hidden="{{indexConfig.goodsBean.shopCartHidden==1?false:true}}" bindtap="addCartClick" data-id="{{goods.commodityId}}">
                  <block wx:if="{{indexConfig.goodsBean.shopCartStyle == 1}}">
                    <image src="{{shop_cart1}}" style="width:44rpx;" mode="widthFix" ></image>
                  </block>
                  <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 2}}">
                    <image src="{{shop_cart2}}" style="width:44rpx;" mode="widthFix" ></image>
                  </block>
                  <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 3}}">
                    <text class="goodsText">{{indexConfig.goodsBean.shopCartBtnText}}</text>
                  </block>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </block>
    <!--一排两个-->
    <block wx:elif="{{indexConfig.goodsBean.showType == 3}}">
      <view class="pic_two">
        <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
          wx:for-index="goodsIndex">
          <view class="commodity_box3"
            style="position:relative;border-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;">
            <view style="position:relative;" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
              data-tag="{{goods.clientCommodityTag}}">
              <image lazy-load='true'
                style="border-top-left-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;border-top-right-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;"
                bindtap="imageClick" data-name="{{goods.commodityName}}" data-pic="{{goods.commodityMainPic}}"
                data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
              <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
                <template is="mark1"
                  data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
              </block>
              <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
                <template is="mark2"
                  data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
              </block>
              <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
                <template is="mark3"
                  data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
              </block>
            </view>
            <view class="goods_desc">
              <view class="desc_title">
                {{goods.commodityName}}
              </view>
              <view class="desc_price" style="display:flex;justify-content:space-between">
                <view class="price_l">
                  <label class="price_tag">￥</label>
                  <label class="price_inner"
                    hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                  <label class="price_inner"
                    hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                  <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                    <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0&&indexConfig.goodsBean.shopCartStyle != 3)?false:true}}"
                      class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                  </block>
                  <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                    <label hidden="{{goods.commodityOtUnitDefault==1&&indexConfig.goodsBean.shopCartStyle != 3?false:true}}"
                      class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                  </block>
                  <block
                    wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0&& indexConfig.goodsBean.shopCartHidden==2}}">
                    <label>
                      <text class="promotionDesc">{{goods.promotionName}}</text>
                    </label>
                  </block>
                </view>
                <view class="price_r" hidden="{{indexConfig.goodsBean.shopCartHidden==1?false:true}}" bindtap="addCartClick" data-id="{{goods.commodityId}}">
                  <block wx:if="{{indexConfig.goodsBean.shopCartStyle == 1}}">
                    <image src="{{shop_cart1}}" style="width:44rpx;" mode="widthFix" ></image>
                  </block>
                  <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 2}}">
                    <image src="{{shop_cart2}}" style="width:44rpx;" mode="widthFix" ></image>
                  </block>
                  <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 3}}">
                    <text class="goodsText">{{indexConfig.goodsBean.shopCartBtnText}}</text>
                  </block>
                </view>
              </view>
            </view>
            <view style="height:40rpx;margin-left:20rpx;margin-top:-20rpx;">
              <label class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>
            </view>
          </view>
        </block>
      </view>
    </block>
    <!--一排三个-->
    <block wx:elif="{{indexConfig.goodsBean.showType == 4}}">
      <view class="pic_three">
        <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
          wx:for-index="goodsIndex">
          <view class="commodity_box4" style="border-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;">
            <view style="position:relative;" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
              data-tag="{{goods.clientCommodityTag}}">
              <image
                style="border-top-left-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;border-top-right-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;"
                lazy-load='true' src="{{goods.commodityMainPic}}" data-name="{{goods.commodityName}}"
                data-pic="{{goods.commodityMainPic}}" bindtap="imageClick" data-commodityId="{{goods.commodityId}}">
              </image>
              <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
                <template is="mark1"
                  data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
              </block>
              <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
                <template is="mark2"
                  data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
              </block>
              <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
                <template is="mark3"
                  data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
              </block>
            </view>
            <view class="goods_desc" style="padding:20rpx 4rpx 8rpx 4rpx !important;">
              <view class="desc_title" style="font-size:26rpx;height:70rpx;overflow:hidden;">{{goods.commodityName}}
              </view>
              <view class="desc_price desc_app" style="display:flex;justify-content:space-between;align-items:center;">
                <view class="price_l">
                  <label class="price_tag">￥</label>
                  <label class="price_inner"
                    hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                  <label class="price_inner"
                    hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                  <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                    <label
                      hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0&& indexConfig.goodsBean.shopCartHidden==2)?false:true}}"
                      class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                  </block>
                  <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                    <label
                      hidden="{{goods.commodityOtUnitDefault==1&& indexConfig.goodsBean.shopCartHidden==2?false:true}}"
                      class="line_price">{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                  </block>
                </view>
                <view class="price_r" hidden="{{indexConfig.goodsBean.shopCartHidden==1?false:true}}"
                  bindtap="addCartClick" data-otdefault="{{goods.commodityOtUnitDefault}}"
                  data-omdefault="{{goods.commodityOMUnitDefault}}" data-id="{{goods.commodityId}}">
                  <block wx:if="{{indexConfig.goodsBean.shopCartStyle == 1}}">
                    <image src="{{shop_cart1}}" style="width:32rpx;" mode="widthFix"></image>
                  </block>
                  <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 2}}">
                    <image src="{{shop_cart2}}" style="width:32rpx;" mode="widthFix"></image>
                  </block>
                  <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 3}}">
                    <text class="goodsText">{{indexConfig.goodsBean.shopCartBtnText}}</text>
                  </block>
                </view>
              </view>
            </view>
            <view style="height:40rpx;margin-left:20rpx;margin-top:-20rpx;">
              <label class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>
            </view>
          </view>
        </block>
      </view>
    </block>
    <!--一排多个可滑动-->
    <block wx:elif="{{indexConfig.goodsBean.showType == 5}}">
      <view class="mode_slide">
        <swiper autoplay="true" display-multiple-items="3" interval="2000" circular="false" duration="2000"
          style='height:400rpx;'>
          <view class="clearfix">
            <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
              wx:for-index="goodsIndex">
              <swiper-item>
                <view class="slide_wrap"
                  style="background:#fff;border-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;">
                  <view style="position:relative;" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
                    data-tag="{{goods.clientCommodityTag}}">
                    <image
                      style="border-top-left-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;border-top-right-radius:{{indexConfig.goodsBean.borderContent == 2?20:0}}rpx;"
                      lazy-load='true' src="{{goods.commodityMainPic}}" data-name="{{goods.commodityName}}"
                      data-pic="{{goods.commodityMainPic}}" bindtap="imageClick"
                      data-commodityId="{{goods.commodityId}}"></image>
                    <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
                      <template is="mark1"
                        data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
                    </block>
                    <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
                      <template is="mark2"
                        data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
                    </block>
                    <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
                      <template is="mark3"
                        data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
                    </block>
                  </view>
                  <view class="goods_desc">
                    <view class="desc_title" style="font-size:26rpx;height:70rpx;overflow:hidden;">
                      {{goods.commodityName}}</view>
                    <view class="desc_price desc_app"
                      style="display:flex;justify-content:space-between;align-items:center;">
                      <view class="price_l">
                        <label class="price_tag">￥</label>
                        <label class="price_inner"
                          hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                        <label class="price_inner"
                          hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                        <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                          <label
                            hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0&& indexConfig.goodsBean.shopCartHidden==2)?false:true}}"
                            class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                        </block>
                        <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                          <label
                            hidden="{{goods.commodityOtUnitDefault==1&& indexConfig.goodsBean.shopCartHidden==2?false:true}}"
                            class="line_price">{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                        </block>
                      </view>
                      <view class="price_r" hidden="{{indexConfig.goodsBean.shopCartHidden==1?false:true}}"
                        bindtap="addCartClick" data-otdefault="{{goods.commodityOtUnitDefault}}"
                        data-omdefault="{{goods.commodityOMUnitDefault}}" data-id="{{goods.commodityId}}">
                        <block wx:if="{{indexConfig.goodsBean.shopCartStyle == 1}}">
                          <image src="{{shop_cart1}}" style="width:32rpx;" mode="widthFix"></image>
                        </block>
                        <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 2}}">
                          <image src="{{shop_cart2}}" style="width:32rpx;" mode="widthFix"></image>
                        </block>
                        <block wx:elif="{{indexConfig.goodsBean.shopCartStyle == 3}}">
                          <text class="goodsText">{{indexConfig.goodsBean.shopCartBtnText}}</text>
                        </block>
                      </view>
                    </view>
                  </view>
                  <view style="height:40rpx;margin-left:20rpx;margin-top:-20rpx;">
                    <label
                      class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>
                  </view>
                </view>
              </swiper-item>
            </block>
          </view>
        </swiper>
      </view>
    </block>
  </view>
</template>
<!--商品模块结束-->
<!--视频模块开始-->
<template name="videoItem">
  <view class="video_box" style="width:{{indexConfig.vedioBean.pageSpace==0?700:750}}rpx;">
    <block
      wx:if="{{indexConfig.vedioBean.vedioCover == 'http://www.cn2b2c.com/gsf/img/crm/miniProgrammeConfig/custom_module.png'}}">
      <video custom-cache="{{false}}" show-play-btn object-fit="fill" show-center-play-btn="true" controls
        show-fullscreen-btn='true' autoplay="{{indexConfig.vedioBean.automaticPlay == 0?true:false}}"
        src="{{indexConfig.vedioBean.vedioSrc}}"></video>
    </block>
    <block wx:else>
      <video custom-cache="{{false}}" show-play-btn object-fit="fill" show-center-play-btn="true" controls
        show-fullscreen-btn='true' autoplay="{{indexConfig.vedioBean.automaticPlay == 0?true:false}}"
        poster="{{indexConfig.vedioBean.vedioCover}}" src="{{indexConfig.vedioBean.vedioSrc}}"></video>
    </block>
  </view>
</template>
<!--视频模块结束-->
<!--文本模块开始-->
<template name="textItem">
  <view bindtap="textClickJump" class="text_box" data-content="{{indexConfig.textBean}}"
    style="background:{{indexConfig.textBean.backColor}};text-align:{{indexConfig.textBean.alignWay==1?'left':'center'}};padding-left:{{indexConfig.textBean.alignWay==1?20:0}}rpx;width:{{indexConfig.textBean.pageSpace==0?700:750}}rpx;">
    <text
      style="font-size:{{indexConfig.textBean.fontSize}}rpx;color:{{indexConfig.textBean.fontColor}};font-weight:{{indexConfig.textBean.fontWay==1?'normal':'bold'}}">{{indexConfig.textBean.textContent}}</text>
    <block wx:if="{{indexConfig.textBean.moreWay == 0}}">
      <view class="moreText">
        <block wx:if="{{indexConfig.textBean.moreType == 1}}">
          更多
        </block>
        <block wx:elif="{{indexConfig.textBean.moreType == 2}}">
          >
        </block>
        <block wx:elif="{{indexConfig.textBean.moreType == 3}}">
          更多>
        </block>
      </view>
    </block>
  </view>

</template>
<!--文本模块结束-->
<!--占位符模块-->
<template name="placeholderItem">
  <view class="place_box" style="height:{{indexConfig.divistonBean.hight>0?indexConfig.divistonBean.hight:10}}rpx;">
  </view>
</template>
<!--占位符模块-->
<!--卡券模块-->
<template name="couponItem">
  <view class="coupon_box" style="width:{{indexConfig.couponBean.pageSpace==0?700:750}}rpx;">
    <block wx:if="{{indexConfig.couponBean.showType == 1}}">
      <view class="oneCoupon" data-content="{{indexConfig.couponBean.wechatAppletIndexCouponRelationImageEntities[0]}}"
        bindtap="couponClickBindTap">
        <image src="{{indexConfig.couponBean.wechatAppletIndexCouponRelationImageEntities[0].imageUrl}}"
          mode="widthFix"></image>
      </view>
    </block>
    <block wx:else>
      <block wx:key="unique" wx:for="{{indexConfig.couponBean.wechatAppletIndexCouponRelationImageEntities}}"
        wx:for-item="cItem" wx:for-index="index">
        <view data-content="{{cItem}}" bindtap="couponClickBindTap">
          <image src="{{cItem.imageUrl}}"
            style="width:{{tools.sub.formatWidth(indexConfig.couponBean.pageSpace,20,indexConfig.couponBean.showType)}}"
            mode="widthFix"></image>
        </view>
      </block>
    </block>
  </view>
</template>
<!--卡券模块-->
<!--大转盘模块-->
<template name="lotteryItem">
  <view class="lottery_box" data-busid="{{indexConfig.luckBean.relationPkid}}" bindtap="lotteryClickBindTap"
    style="width:{{indexConfig.luckBean.pageSpace==0?700:750}}rpx;">
    <image src="{{indexConfig.luckBean.imagerUrl}}" style="width:100%;" mode="widthFix"></image>
  </view>
</template>
<!--大转盘模块-->
<!--直播模块-->
<template name="castItem">
  <view class="cast_box" style="width:{{indexConfig.liveBean.pageSpace==0?700:750}}rpx;">
    <block wx:if="{{indexConfig.liveBean.showType == 1}}">
      <block wx:if="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities[0].jumpType == 1}}">
        <view class="oneCast"
          data-content="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities[0]}}"
          bindtap="castClickBindTap">
          <image src="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities[0].imageUrl}}"
            mode="widthFix"></image>
        </view>
      </block>
      <block wx:if="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities[0].jumpType == 2}}">
        <navigator
          url="plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id={{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities[0].liveBroadcastId}}">
          <view style="width:{{indexConfig.liveBean.pageSpace==0?700:750}}rpx;">
            <image style="width:100%;" src="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities[0].imageUrl}}"
              mode="widthFix"></image>
          </view>
        </navigator>
      </block>
    </block>
    <block wx:else>
      <block wx:key="unique" wx:for="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities}}"
        wx:for-item="cItem" wx:for-index="index">
        <block wx:if="{{cItem.jumpType == 1}}">
          <view data-content="{{cItem}}" bindtap="castClickBindTap">
            <block wx:if="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities.length==index+1}}">
              <image src="{{cItem.imageUrl}}"
                style="margin-right:0;width:{{tools.sub.formatWidth(indexConfig.liveBean.pageSpace,20,indexConfig.liveBean.showType)}}"
                mode="widthFix"></image>
            </block>
            <block wx:else>
              <image src="{{cItem.imageUrl}}"
                style="width:{{tools.sub.formatWidth(indexConfig.liveBean.pageSpace,20,indexConfig.liveBean.showType)}}"
                mode="widthFix"></image>
            </block>
          </view>
        </block>
        <block wx:if="{{cItem.jumpType == 2}}">
          <navigator
            url="plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id={{cItem.liveBroadcastId}}">
            <view data-content="{{cItem}}">
              <block
                wx:if="{{indexConfig.liveBean.wechatAppletIndexLiveBroadcastRelationImageEntities.length==index+1}}">
                <image src="{{cItem.imageUrl}}"
                  style="margin-right:0;width:{{tools.sub.formatWidth(indexConfig.liveBean.pageSpace,20,indexConfig.liveBean.showType)}}"
                  mode="widthFix"></image>
              </block>
              <block wx:else>
                <image src="{{cItem.imageUrl}}"
                  style="width:{{tools.sub.formatWidth(indexConfig.liveBean.pageSpace,20,indexConfig.liveBean.showType)}}"
                  mode="widthFix"></image>
              </block>
            </view>
          </navigator>
        </block>
      </block>
    </block>
  </view>
</template>
<!--直播模块-->
<!--活动模块-->
<template name="promotionItem">
  <view class="promotionWrap">
    <view
      hidden="{{(indexConfig.activityBean.cuntdownOpen==1 && indexConfig.activityBean.activityTitle.length<1)?true:false}}">
      <view class="promotionTop">
        <text
          style="font-size:30rpx;color:{{indexConfig.activityBean.activityTitleColor}}">{{indexConfig.activityBean.activityTitle}}</text>
        <view hidden="{{indexConfig.activityBean.cuntdownOpen==1?true:false}}">
          <view class='activity_time' style="display:flex">
            <view style="font-size:30rpx;padding-right:20rpx;color:{{indexConfig.activityBean.cuntdownColor}};">
              <block wx:if="{{indexConfig.promotionSaleBean.promotionStatus == 'PROMOTION_NOT_START'}}">
                距离开始
              </block>
              <block wx:if="{{indexConfig.promotionSaleBean.promotionStatus == 'PROMOTION_EXECUTION'}}">
                距离结束
              </block>
              <block
                wx:if="{{indexConfig.promotionSaleBean.promotionStatus == 'PROMOTION_FINISH' || indexConfig.promotionSaleBean.promotionStatus == 'PROMOTION_CANCLE'}}">
                活动已结束
              </block>
            </view>
            <block wx:if="{{indexConfig.promotionSaleBean.promotionStatus == 'PROMOTION_NOT_START'}}">
              <block wx:for="{{countDownList}}" wx:key="countDownListIndex" wx:for-item="countDownItem">
                <block
                  wx:if="{{countDownItem.actEndTime==tools.sub.formatCountDown(indexConfig.promotionSaleBean.promotionStartDate,indexConfig.promotionSaleBean.promotionStartTime)}}">
                  <view style="display:flex;color:#fff;">
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.day}}</label><text
                      style="color:{{indexConfig.activityBean.cuntdownColor}}">:</text>
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.hou}}</label><text
                      style="color:{{indexConfig.activityBean.cuntdownColor}}">:</text>
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.min}}</label><text
                      style="color:{{indexConfig.activityBean.cuntdownColor}}">:</text>
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.sec}}</label>
                  </view>
                </block>
              </block>
            </block>
            <block wx:if="{{indexConfig.promotionSaleBean.promotionStatus == 'PROMOTION_EXECUTION'}}">
              <block wx:for="{{countDownList}}" wx:key="countDownListIndex" wx:for-item="countDownItem">
                <block
                  wx:if="{{countDownItem.actEndTime==tools.sub.formatCountDown(indexConfig.promotionSaleBean.promotionEndDate,indexConfig.promotionSaleBean.promotionEndTime)}}">
                  <view style="display:flex;color:#fff;">
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.day}}</label><text
                      style="color:{{indexConfig.activityBean.cuntdownColor}}">:</text>
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.hou}}</label><text
                      style="color:{{indexConfig.activityBean.cuntdownColor}}">:</text>
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.min}}</label><text
                      style="color:{{indexConfig.activityBean.cuntdownColor}}">:</text>
                    <label class='clock'
                      style="background:{{indexConfig.activityBean.cuntdownColor}}">{{countDownItem.sec}}</label>
                  </view>
                </block>
              </block>
            </block>
          </view>
        </view>
      </view>
    </view>
    <!--一排一个-->
    <block wx:if="{{indexConfig.activityBean.showType == 1}}">
      <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
        wx:for-index="goodsIndex">
        <view class="oneGoods"
          style="border:{{indexConfig.activityBean.haveBorder == 0?1:0}}px solid {{indexConfig.activityBean.borderColor}};border-radius:{{indexConfig.activityBean.borderStyle==1?0:20}}rpx;"
          bindtap="imageClick1" data-commodityId="{{goods.commodityId}}" data-name="{{goods.commodityName}}"
          data-pic="{{goods.commodityMainPic}}" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
          data-tag="{{goods.clientCommodityTag}}">
          <view style="position:relative;">
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
              <template is="mark1"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
              <template is="mark2"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
              <template is="mark3"
                data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
            </block>
            <image lazy-load='true' src="{{goods.commodityMainPic}}"
              style="border-top-left-radius:{{indexConfig.activityBean.borderStyle==1?0:20}}rpx;border-bottom-left-radius:{{indexConfig.activityBean.borderStyle==1?0:20}}rpx;">
            </image>
          </view>
          <view class="goodsDetail" style="flex:1;padding-left:30rpx;">
            <view>
              <view class="desc_title">{{goods.commodityName}}</view>
                <!--<block wx:if="{{indexConfig.activityBean.activityType==2 || indexConfig.activityBean.activityType==3}}">
                  <view hidden="{{indexConfig.activityBean.stockOpen == 0?false:true}}" style="font-size:24rpx;">
                    <view style="position:relative;border-radius:8rpx;background:#ccc;width:320rpx;height:16rpx;">
                      <view style="position:absolute;top:0;left:0;bottom:0;right:{{tools.sub.formatDistance(2,20)}};border-radius:8rpx;background:#fc6366"></view>
                    </view>
                    <view style="margin-top:6rpx;">已抢2件</view>
                  </view>
                </block>
                <block wx:elif="{{indexConfig.activityBean.activityType==1}}">
                  <view style="font-size:24rpx;">
                    <view><text style="padding:0rpx 8rpx;border-radius:10rpx;border:1px solid #ff6600;color:#ff6600;">2人团</text><text hidden="{{indexConfig.activityBean.groupNumOpen == 0?false:true}}"style="margin-left:20rpx;">已团20件</text>
                    </view>
                  </view>
                </block>-->
            </view>
            <view class="one_b">
              <label>
                <text class="price_tag">￥</text>
               
                <block wx:if="{{goods.commodityOMUnitDefault == 1&&goods.commodityOtUnitDefault==0}}">
                  <label
                  class="price_inner">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                <label class="line_price"
                  hidden='{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice?false:true}}'>
                  ￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}
                </label>
                </block>
                <block wx:if="{{goods.commodityOtUnitDefault == 1}}">
                <label
                  class="price_inner">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                <label class="line_price"
                  hidden='{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice?false:true}}'>
                  ￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}
                </label>
                </block>
              </label>
              <text class="goodsText">{{indexConfig.activityBean.btnText}}</text>
            </view>
            <view style="height:40rpx;margin-left:20rpx;margin-top:-20rpx;">
              <label class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>
            </view>
          </view>
        </view>
      </block>
    </block>
    <!--一排一个-->
    <!--一排两个--->
    <view class="pic_two">
      <block wx:if="{{indexConfig.activityBean.showType == 2}}">
        <block wx:key="unique" wx:for="{{indexConfig.wechatAppletGoodsList}}" wx:for-item="goods"
          wx:for-index="goodsIndex">
          <view bindtap="imageClick" class="pic_two_goods"
            style="box-sizing:border-box;border:{{indexConfig.activityBean.haveBorder == 0?1:0}}px solid {{indexConfig.activityBean.borderColor}};border-radius:{{indexConfig.activityBean.borderStyle==1?0:20}}rpx;">
            <view style="position:relative;" bindlongpress="getTagPress" data-id="{{goods.commodityId}}"
              data-tag="{{goods.clientCommodityTag}}">

              <image lazy-load='true' bindtap="imageClick" data-name="{{goods.commodityName}}"
                data-pic="{{goods.commodityMainPic}}" data-commodityId="{{goods.commodityId}}"
                src="{{goods.commodityMainPic}}"
                style="border-top-left-radius:{{indexConfig.activityBean.borderStyle==1?0:20}}rpx;border-bottom-left-radius:{{indexConfig.activityBean.borderStyle==1?0:20}}rpx;">
              </image>
              <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdContent.length>0}}">
                <template is="mark1"
                  data="{{name:goods.commoditySideDesc,pos:goods.commoditySideDescStyle.showPosition}}"></template>
              </block>
              <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdContent.length>0}}">
                <template is="mark2"
                  data="{{name:goods.commoditySideDesc,pos:goods.commoditySideDescStyle.showPosition}}"></template>
              </block>
              <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
                <template is="mark3"
                  data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
              </block>
            </view>
            <view class="goods_desc">
              <view class="desc_title">
                {{goods.commodityName}}
              </view>
               <!--<block wx:if="{{indexConfig.activityBean.activityType==2 || indexConfig.activityBean.activityType==3}}">
                <view hidden="{{indexConfig.activityBean.stockOpen == 0?false:true}}" style="font-size:24rpx;">
                    <view style="position:relative;border-radius:7rpx;background:#ccc;width:300rpx;height:14rpx;">
                      <view style="position:absolute;top:0;left:0;bottom:0;right:{{tools.sub.formatDistance(2,20)}};border-radius:8rpx;background:#fc6366"></view>
                    </view>
                    <view style="margin-top:6rpx;">已抢2件</view>
                </view>
              </block>
              
              <block wx:elif="{{indexConfig.activityBean.activityType==1}}">
                  <view style="font-size:24rpx;">
                    <view><text style="padding:0rpx 8rpx;border-radius:10rpx;border:1px solid #ff6600;color:#ff6600;">2人团</text><text hidden="{{indexConfig.activityBean.groupNumOpen == 0?false:true}}" style="margin-left:20rpx;">已团20件</text>
                    </view>
                  </view>
              </block>-->
              <view class="desc_price">
                <label class="price_tag">￥</label>
                <label class="price_inner"
                  hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
                <label class="price_inner"
                  hidden="{{goods.commodityOtUnitDefault==1?false:true}}">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
                <block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
                  <label hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}"
                    class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
                </block>
                <block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
                  <label hidden="{{goods.commodityOtUnitDefault==1?false:true}}"
                    class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
                </block>
                

                <text class="goodsText" style="float:right;margin-right:10rpx;">{{indexConfig.activityBean.btnText}}</text>
              </view>
            </view>
            <view style="height:40rpx;margin-left:20rpx;margin-top:-20rpx;">
              <label class="{{goods.clientCommodityTagDesc!=''?'g_t':''}}">{{goods.clientCommodityTagDesc}}</label>
            </view>
          </view>
        </block>
      </block>
    </view>
    <!--一排两个-->
  </view>
</template>
<!--倒计时模块-->
<template name="countItem">
  <view class="countBox"
    style="display:flex;align-items:center;justify-content:{{indexConfig.countDownBean.contentStyle==1?'flex-start':'center'}};padding:20rpx 25rpx;background:{{indexConfig.countDownBean.bgStyle==1?indexConfig.countDownBean.colorStyle:''}};background-size:{{indexConfig.countDownBean.bgStyle==2?'100%':''}};background-image:url({{indexConfig.countDownBean.bgStyle==2?indexConfig.countDownBean.bgImage:''}})">
    <block wx:for="{{countDownList}}" wx:key="countDownListIndex" wx:for-item="countDownItem">
      <block
        wx:if="{{countDownItem.actEndTime == tools.sub.formatCountDownTime(indexConfig.countDownBean.startTimeYmd,indexConfig.countDownBean.startTimeHms)}}">
        <view style="font-size:30rpx;display:inline-block;color:{{indexConfig.countDownBean.copywritingColor}}">
          距离<text>{{indexConfig.countDownBean.auxiliaryCopy}}</text>开始还有</view>
        <view class='activity_time' style="display:inline-block;color:#fff;">
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.day}}</label><text
            style="color:{{indexConfig.countDownBean.countDownColor}}">:</text>
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.hou}}</label><text
            style="color:{{indexConfig.countDownBean.countDownColor}}">:</text>
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.min}}</label><text
            style="color:{{indexConfig.countDownBean.countDownColor}}">:</text>
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.sec}}</label>
        </view>
      </block>
      <block
        wx:elif="{{countDownItem.actEndTime == tools.sub.formatCountDownTime(indexConfig.countDownBean.endTimeYmd,indexConfig.countDownBean.endTimeHms)}}">
        <view style="display:inline-block;font-size:30rpx;color:{{indexConfig.countDownBean.copywritingColor}}">
          距离<text>{{indexConfig.countDownBean.auxiliaryCopy}}</text>结束还有</view>
        <view class='activity_time' style="display:inline-block;color:#fff;">
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.day}}</label><text
            style="color:{{indexConfig.countDownBean.countDownColor}}">:</text>
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.hou}}</label><text
            style="color:{{indexConfig.countDownBean.countDownColor}}">:</text>
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.min}}</label><text
            style="color:{{indexConfig.countDownBean.countDownColor}}">:</text>
          <label class='clock'
            style="background:{{indexConfig.countDownBean.countDownColor}}">{{countDownItem.sec}}</label>
        </view>
      </block>
    </block>
    <block
      wx:if="{{tools.sub.formatCountDownTime(indexConfig.countDownBean.endTimeYmd,indexConfig.countDownBean.endTimeHms)-tools.sub.formatcurrentTime()<0}}">
      <view style="display:inline-block;font-size:30rpx;color:{{indexConfig.countDownBean.copywritingColor}}">
        <text>{{indexConfig.countDownBean.auxiliaryCopy}}</text>已结束</view>
    </block>
  </view>
</template>
<!--倒计时模块-->
<!--活动模块结束-->
<!--嵌套的模块-->
<template name="mark3">
    <view class="mark1">
        <view class="mark1_l">
            <view>{{nameCircle}}</view>
        </view>
        <view class="mark1_r">
            {{name}}
        </view>
    </view>
</template>
<template name="mark2">
    <view class="mark2_{{pos}}">
    {{name}}
    </view>
</template>
<template name="mark1">
    <view class="mark_{{pos}}">
    {{name}}
    </view>
</template>