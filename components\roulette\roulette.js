const app = getApp();
var dot_inter;
Component({
  properties: {
    options: {
      type: Object,
      value: {},
      observer: function (newVal, oldVal) { 
      }
    },
    angel:{
      type:Number,
      value:0,
      observer: function (newVal, oldVal) {
        console.log(newVal);
        console.log(newVal+"n");
        console.log(oldVal);
        console.log(oldVal+"o");
        if (newVal){
          this.start()
         
        }
      }
    },
    award:{
      type: Object,
      value:[],
      observer: function (newVal, oldVal) {
        if (newVal){
          this.setData({
            award:newVal
          })
        }
       }
    }
  },
  data: {
    show: false,
    turntable_zhen:app.imageUrl + 'turntable_zhen.png',
    turntable_yhj:app.imageUrl + 'turntable_yhj.png',
    turntable_jb:app.imageUrl + 'turntable_jb.png',
    turntable_ps:app.imageUrl + 'turntable_ps.png',
    turntable_hb:app.imageUrl + 'turntable_hb.png',
    turntable_success:app.imageUrl + 'turntable_success.png',
    turntable_bj:app.imageUrl + 'turntable_bj.png',
    turntable_jifen: app.imageUrl + 'turntable_jifen.png',
    triggerNum:true,
    canDraw:true
  },
  attached: function () {
  },
  detached: function () { 
    clearInterval(dot_inter)
  },
  ready: function () { 

    var that=this;
    that.downloadImg();
    that.drawCanvas();
    that.dotStart();
    
  },
  methods: {
    drawCanvas: function (){
      var that=this;
      const ctx = wx.createCanvasContext('roulette', this);
      let options=this.data.options;
      var angelTo = this.data.angelTo || 0;
      var width=295;
      var height=295;
      var x = width / 2;
      var y = width / 2;
      var award=this.data.award
      var num=award.length;
      ctx.translate(x, y)
      ctx.clearRect(-width, -height, width, height);

      var angel = (2 * Math.PI / 360) * (360 / num);
      var startAngel = 2 * Math.PI / 360 * (-90);
      var endAngel = 2 * Math.PI / 360 * (-90) + angel;

      ctx.rotate(angelTo * Math.PI / 180);
      // 画外圆
      ctx.beginPath();
      ctx.lineWidth = 20;
      ctx.strokeStyle = options.bgOut;
      ctx.arc(0, 0, 130, 0, 2 * Math.PI)
      ctx.stroke();
      // 画里圆
      ctx.beginPath();
      ctx.lineWidth = 6;
      ctx.strokeStyle = options.bgMiddle;
      ctx.arc(0, 0, 120, 0, 2 * Math.PI)
      ctx.stroke();

      // 装饰点
      var dotColor = options.dotColor;
      for (var i = 0; i < 26; i++) {
        ctx.beginPath();
        var radius = 131;
        var xr = radius * Math.cos(startAngel)
        var yr = radius * Math.sin(startAngel)
        ctx.fillStyle = dotColor[i % dotColor.length]
        ctx.arc(xr, yr, 5, 0, 2 * Math.PI)
        ctx.fill()
        startAngel += (2 * Math.PI / 360) * (360 / 26);
      }
       // 画里转盘   
      var colors = options.bgInner;
      for (var i = 0; i < num; i++) {
        ctx.beginPath();
        ctx.lineWidth =116;
        ctx.strokeStyle = colors[i % colors.length]
        ctx.arc(0, 0, 60, startAngel, endAngel)
        ctx.stroke();
        startAngel = endAngel
        endAngel += angel
      }

      var awardTitle = this.data.award;
      startAngel = angel / 2
      for (var i = 0; i < num; i++) {
        ctx.save();
        ctx.rotate(startAngel);
        if(awardTitle[i].activityPrizeType==3){
          ctx.drawImage(that.data.turntable_yhj, -14, -90, 30, 20)
        }else if(awardTitle[i].activityPrizeType==4){
          
          ctx.drawImage(that.data.turntable_jifen, -18, -90, 35, 25)
        }else if(awardTitle[i].activityPrizeType==2){
          ctx.drawImage(that.data.turntable_hb, -20, -90, 40, 40)
          
        }
        ctx.font = options.font;
        ctx.fillStyle = options.fontColor,
        ctx.textAlign = "center";
        ctx.fillText(awardTitle[i].activityPrizeShowName, 0, -95);
        
        
        startAngel += angel
        ctx.restore();
      }
      ctx.draw()
    },
    rollStart(e){
    if(this.data.canDraw){  
        this.triggerEvent('getAngel');
        this.setData({
          canDraw:false
        })
    }
    else{
      wx.showToast({
        title: '您稍后点击~',
        icon: 'none'
      })
    }
      
      /*if(this.data.triggerNum){
        console.log("2222");
        this.triggerEvent('getAngel');
      }
      this.setData({
        triggerNum:false
      })*/
      
      
    },
    start(){
      var that=this;
      let options=that.data.options;
      var angel = that.data.angel;
      angel = 360 - angel;
      angel += 360*6;
      var baseStep = 30
      // 起始滚动速度
      var baseSpeed = 0.3
      var count = 1;

      var timer = setInterval(function () {
        that.setData({
          angelTo:count
        })
        clearInterval(dot_inter);
        that.drawCanvas();
        if (count == angel) {
          clearInterval(timer)
          that.triggerEvent('getPrize')
          that.setData({
            triggerNum:true,
            canDraw:true
          })
          that.dotStart();
        }
        count = count + baseStep * (((angel - count) / angel) > baseSpeed ? baseSpeed : ((angel - count) / angel))
        if (angel - count < 0.5) {
          count = angel
        }
      }, options.speed)
    },
    dotStart: function () {
      var that = this;
      let times = 0;
      let options =that.data.options;
      dot_inter = setInterval(function () {
        if (times % 2) {
          options.dotColor = options.dotColor_1
        } else {
          options.dotColor = options.dotColor_2
        }
        times++;
        that.setData({
          options: options
        })
        that.drawCanvas();
      }, options.speedDot)
    },
    //网络图片需先下载才可以加载在canvas里
    downloadImg:function(){
      var that=this;
      wx.getImageInfo({
        src:that.data.turntable_hb,
        success: function (res) {
          //res.path是网络图片的本地地址
          let Path = res.path;
          that.setData({
            turntable_hb: Path
          })
        },
        fail: function (res) {
        }
      });
      wx.getImageInfo({
        src:that.data.turntable_yhj,
        success: function (res) {
          let Path = res.path;
          that.setData({
            turntable_yhj: Path
          })
        },
        fail: function (res) {
        }
      });
      wx.getImageInfo({
        src:that.data.turntable_jifen,
        success: function (res) {
          let Path = res.path;
          that.setData({
            turntable_jifen: Path
          })
        },
        fail: function (res) {
        }
      });
    }
  }
})