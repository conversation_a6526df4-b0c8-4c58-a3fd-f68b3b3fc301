var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var TimeUtil = require('../../utils/util.js');
var wxMarkerData = [];
Page({
  data: {
    img1: app.imageUrl + 'tongyong.png',
    img2: app.imageUrl + 'xinren.png',
    img3: app.imageUrl + 'integral.png',
    img4: app.imageUrl + 'xianjin.png',
    img5: app.imageUrl + 'youhui.png',
    img6: app.imageUrl + 'shengri.png',
    img7: app.imageUrl + 'zhifu.png',
    img8: app.imageUrl + 'hongbao.png',
    img9: app.imageUrl + 'hongbao2.png',
    order_none: app.imageUrl + 'order/order_none.png',
    evaluate_bg: app.imageUrl + 'order/evaluate_bg.png',
    order_remove: app.imageUrl + 'order/order_remove.png',
    goods_pic: app.imageUrl + 'classify_bg.png',
    imgUrls: [
      app.imageUrl + 'production/production_bg.png'
    ],
    array: [{
      mode: 'aspectFit'
    }],
    mode: 'aspectFill',
    isFromBack: false,
    localCity: "", //当前地理位置
    winHeight: "", //窗口高度
    currentTab: 0, //预设当前项的值
    scrollLeft: 0, //tab标题的滚动条位置
    orderStatus: ["", "待审核", "待付款", "待发货", "待收货", "订单取消", "订单取消",
      "订单待评价", "订单完成", "订单关闭", "部分发货", "部分付款", "待商家接单", "", "", "", "待成团", "待买家确认"
    ],
    //订单状态Id 1：待审核 2：待付款 3：待发货 4：待收货 5：零售商取消 6：供货商取消 7：订单待评价 8：订单完成 9：订单关闭 10：部分发货 11：部分付款 12：待商家接单 
    offSet: 1,
    searchLoading: true,
    onshowLoading: false,
    all_order: false,
    wait_pay_order: false,
    wait_send_order: false,
    send_order: false,
    wait_evaluate: false,
    shopperGift: app.imageUrl + 'shopperGift.png',
    cancelMark: app.imageUrl + 'cancelMark.png',
    vipGift: app.imageUrl + 'vipGift.png',
    vip_shopperGift: app.imageUrl + 'vipshopperGift.png',
    cardImageSrc: "",
    putCardHidden: true,
    giftBox: app.imageUrl + 'giftBox.png',
    orderCode: app.imageUrl + 'orderCode.png',
  },
  // 点击标题切换当前页时改变样式
  swichNav: function (e) {
    var cur = e.target.dataset.current;
    this.setData({
      offSet: 1,
      orderList: [],
      isScroll: false,
      currentTab: cur
    })
    this.queryAllOrder(cur, app.getExtCompanyId(), app.getExtStoreId());
  },
  queryDistributionBindTap: function (e) {
    var orderId = e.currentTarget.dataset.orderid;
    app.navigateToPage("/pages/selfDeliverInfo/selfDeliverInfo?orderId=" + orderId);
  },
  appleyReturnMoneyBindTap: function (e) {
    var that = this;
    var orderId = e.target.dataset.orderid;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateIdForOrderRufund',
      data: {
        "typeId": "6",
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              that.appleyReturnMoney(orderId);
            }
          })
        } else {
          that.appleyReturnMoney(orderId);
        }
      }
    })
  },
  appleyReturnMoney: function (orderId) {
    var that = this;
    app.showModal({
      content: '确定申请退款？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/applyRejectedOrderNoneCommodity',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
            } else {
              app.showModal({
                title: '提示',
                content: "申请失败"
              });
            }
          }
        })
      }
    });
  },
  /**
   * 确认收货
   */
  receiptGoodsBindTap: function (e) {
    var that = this;
    app.showModal({
      content: '确定收货？',
      showCancel: true,
      confirm: function () {
        var orderId = e.target.dataset.orderid;
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/okReceipt',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              var orderFlag = res.data.orderFlag;
              if (orderFlag) {
                var storeCardList = res.data.storeCardList;
                that.setData({
                  storeCardList: storeCardList,
                  putCardHidden: false,
                  orderFlag: orderFlag,
                  offSet: 1,
                  orderList: []
                });
                that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
              } else {
                that.setData({
                  offSet: 1,
                  putCardHidden: true,
                  orderFlag: orderFlag,
                  orderList: []
                });
                app.navigateToPage("/pages/evaluate/evaluate?orderId=" + orderId);
              }
            }
          }
        })
      }
    });
  },
  /**
   * 取消订单
   */
  cancelOrderformSubmit: function (e) {
    var that = this;
    var orderId = e.target.dataset.orderid;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateIdForOrderRufund',
      data: {
        "typeId": "6",
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              that.userCancleOrder(orderId);
            }
          })
        } else {
          that.userCancleOrder(orderId);
        }
      }
    })
  },
  userCancleOrder: function (orderId) {
    var that = this;
    app.showModal({
      content: '确定取消订单？',
      showCancel: true,
      confirm: function () {
        wx.showLoading({
          title: '正在处理中...',
          mask: true
        })
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/cancelOrder',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "levelRole": app.getIdentity(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            wx.hideLoading();
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
            } else {
              app.showModal({
                content: '取消订单失败，请联系客服'
              });
              return;
            }
          }
        })
      }
    });
  },
  queryCardBagBindTap: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
    } else {
      app.navigateToPage('/pages/person_coupon/person_coupon');
    }
  },
  closeCardBgBindTap: function () {
    var that = this;
    that.setData({
      putCardHidden: true,
      orderFlag: false
    });
    that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
  },
  /**
   * 再来一单
   */
  buyAgainBindTap: function (e) {
    var orderId = e.target.dataset.orderid;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/addBatchRetailShppingCart',
      data: {
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.switchTab({
            url: "/pages/shopCart/shopCart"
          });
        } else {
          app.showModal({
            content: '操作失败，请稍后在试'
          });
          return;
        }
      }
    })
  },
  /**
   * 立即付款
   */
  nowPayBindTap: function (e) {
    wx.showLoading({
      title: '正在请求支付',
      mask: true
    })
    var that = this;
    var orderNo = e.target.dataset.orderno;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/nowPay',
      data: {
        "openId": app.getOpenId(),
        "orderNo": orderNo,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "levelRole": app.getIdentity(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var param = res.data;
        var newOrderBean = res.data.newOrderBean;
        if (flag) {
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              app.showModal({
                content: '支付成功'
              })
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
              /*app.turnToPage("/pages/successfulTrade/successfulTrade?newOrderBean=" + JSON.stringify(newOrderBean) + "&goodsList=" + JSON.stringify(that.data.goodsList));*/
            },
            fail: function (res) {
              if (res.errMsg === 'requestPayment:fail cancel') {
                app.showModal({
                  content: '取消支付'
                })
              } else {
                app.showModal({
                  content: '支付失败'
                })
              }
              app.turnToPage("/pages/indexThree/indexThree");
            }
          })
        }
      }
    })
  },
  /**
   * 查看物流
   */
  queryLogisticsBindTap: function (e) {
    var orderId = e.currentTarget.dataset.orderid;
    app.navigateToPage('/pages/supplier_order_detail/supplier_order_detail?orderId=' + orderId);
  },
  queryLogisticsBindTap1: function (e) {
    var orderId = e.target.dataset.orderid;
    var totalmoney = e.target.dataset.totalmoney;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/queryLogisticsMessage',
      data: {
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "levelRole": app.getIdentity(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var logisticNo = res.data.logisticNo;
        var logisticName = res.data.logisticName;
        var receivePhoneNum = res.data.receivePhoneNum;
        var deliverWay = res.data.deliverWay;
        app.navigateToPage("/pages/supplier_order_detail2/supplier_order_detail2?totalmoney="+totalmoney+"&deliverWay="+deliverWay+"&logisticNo=" + logisticNo + "&receivePhoneNum=" + receivePhoneNum+"&logisticName=" + logisticName+"&orderId=" + orderId);
      }
    })
  },
  /**
   * 删除订单
   */
  removeOrderBindTap: function (e) {
    var that = this;
    app.showModal({
      content: '确定删除订单？',
      showCancel: true,
      confirm: function () {
        var orderId = e.target.dataset.orderid;
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/deleteOrder',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "levelRole": app.getIdentity(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
            }
          }
        })
      }
    });
  },
  /**
   * 再次购买
   */
  againBuyBindTap: function (e) {
    var commodityId = e.target.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + commodityId);
  },
  /**
   * 退货
   */
  returnGoodsBindTap: function (e) {
    /*app.showModal({
      content: '请在3E创美APP内申请售后服务'
    });*/
    var orderId = e.target.dataset.orderid;
    app.navigateToPage('/pages/returnGoods2/returnGoods2?orderId=' + orderId);
  },
  /**
   * 待自提
   */
  selfMentionBindTap: function (e) {
    var that = this;
    var pickUpCode128 = "";
    var pickUpCode = "";
    var orderId = e.target.dataset.orderid;
    var orderList = that.data.orderList;
    if (orderList != null && orderList.length > 0) {
      for (var i = 0; i < orderList.length; i++) {
        if (orderList[i].orderId == orderId) {
          pickUpCode128 = orderList[i].pickUpCode128;
          pickUpCode = orderList[i].pickUpCode;
          break;
        }
      }
    }
    app.navigateToPage('/pages/verifyOrder/verifyOrder?type=1&pickUpCode128=' + pickUpCode128 + '&pickUpCode=' + pickUpCode);
  },
  /**
  * 助手核销码
  */
  verificationBindTap: function (e) {
    var that = this;
    var orderNoCode128 = "";
    var orderNo = "";
    var orderId = e.target.dataset.orderid;
    var orderList = that.data.orderList;
    if (orderList != null && orderList.length > 0) {
      for (var i = 0; i < orderList.length; i++) {
        if (orderList[i].orderId == orderId) {
          orderNoCode128 = orderList[i].orderNoCode128;
          orderNo = orderList[i].orderNo;
          break;
        }
      }
    }
    app.navigateToPage('/pages/verifyOrder/verifyOrder?type=2&pickUpCode128=' + orderNoCode128 + '&pickUpCode=' + orderNo);
  },
  /**
   * 评价
   */
  evaluateBindTap: function (e) {
    var orderId = e.target.dataset.orderid;
    app.navigateToPage("/pages/evaluate/evaluate?orderId=" + orderId);
  },
  onLoad: function (options) {
    var that = this;
    var currentTab = options.currentTab;
    if (currentTab == null || currentTab == undefined || currentTab == "") {
      currentTab = "0";
    }
    that.setData({
      currentTab: currentTab
    })
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    //  高度自适应
    wx.getSystemInfo({
      success: function (res) {
        var clientHeight = res.windowHeight,
          clientWidth = res.windowWidth,
          rpxR = 750 / clientWidth;
        var calc = clientHeight * rpxR;
        that.setData({
          winHeight: calc
        });
      }
    });
    app.init_getExtMessage().then(res => {
      that.queryAllOrder(currentTab, res.companyId, res.storeId);
    });
  },
  searchBindFocus: function () {
    app.navigateToPage('/pages/searchPage/searchPage');
  },
  queryAllOrder: function (type, companyId, storeId) {
    var that = this;
    wx.showLoading({
      title: '订单加载中。。。',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/allOrder',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "type": type,
        "offSet": that.data.offSet,
        "pageSize": 20,
        "storeId": storeId,
        "companyId": companyId
      },
      success: function (res) {
        if(res.data.code == -99){ /*表示登录已经失效*/
          wx.removeStorageSync("odbtoken");
          wx.removeStorageSync("loginToken");
          app.init_userOpenId();
          wx.switchTab({
            url: "/pages/accountManager/accountManager"
          });
          return;
        }
        wx.hideLoading();
        var new_orderList = res.data.orderList;
        var oldOrderList = that.data.orderList;
        if (new_orderList != null && new_orderList.length > 0) {
          for (var i = 0; i < new_orderList.length; i++) {
            new_orderList[i].orderGenerateDate = TimeUtil.getSmpFormatDateByLong(new_orderList[i].orderGenerateDate, true);
          }
          if (oldOrderList != null && oldOrderList.length > 0) {
            new_orderList = oldOrderList.concat(new_orderList);
          }
          that.setData({
            orderList: new_orderList,
            searchLoading: true
          });
        } else {
          that.setData({
            orderList: oldOrderList,
            searchLoading: false
          });
        }
      },
      fail: function () {
        wx.hideLoading();
        wx.showToast({
          title: '订单加载异常',
          duration: 1500,
          icon: "none"
        })
      }
    })
  },
  DingBu: function (e) {

  },
  DiBu: function (e) {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        offSet: that.data.offSet + 1,
        isScroll: true
      });
      that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
    }
  },
  onShow: function () {
    var that = this;
    var that = this;
    var childObj = that.selectComponent('#test');
    childObj.setData({
      list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,
      selected: 8,
      color: app.globalData.bottomBean.color,
      selectedColor: app.globalData.bottomBean.selectedColor
    });
    let flag = true;
    app.globalData.bottomBean.wechatAppletIndexBottomContentEntity.forEach(item =>{
      if (item.pagePath == 8) {
        flag = false
      }
    })
    childObj.setData({
      showFlag: flag
    });
    if (that.data.onshowLoading) {
      that.setData({
        offSet: 1,
        orderList: [],
        isScroll: true
      });
      that.queryAllOrder(that.data.currentTab, app.getExtCompanyId(), app.getExtStoreId());
    } else {
      that.setData({
        onshowLoading: true
      })
    }
  }
})