<wxs src="../../wxs/subutil.wxs" module="tools" />
<view>
  <view class="vipCardTop">
    <image src="{{cardbg}}" style="width:710rpx;height:300rpx;border-radius:16rpx;position:absolute;z-index:-1"></image>
    <view style="padding:20rpx;height:140rpx">
      <view class='head_box' style="width:100rpx;height:100rpx;float:left;">
        <open-data type="userAvatarUrl" style="border-radius:50%;"></open-data>
      </view>
      <view style="color:#fff;font-size:26rpx;margin-left:16rpx;float:left">
        <view style="padding-top:10rpx">{{cardBean.vipName}}</view>
        <view style="padding-top:10rpx">{{cardBean.telephone}}</view>
      </view>
      <view style="float:right;">
        <block wx:if="{{cardBean.vipType==2||cardBean.vipType==3}}">
          <view bindtap="goChargeBind" hidden="{{storeValueHidden}}" class="rechargeBtn">立即充值</view>
        </block>
        <view class="rechargeBtn" bindtap="upgradeVipCardBindTap" hidden="{{!isUpgradeVipCard}}">
          升级
        </view>
      </view>
    </view>
    <view style="padding:80rpx;font-size: 28rpx;color:#fff;">卡号：{{cardBean.cardId}}</view>
  </view>
  <view class="vipCardCenter">
    <view class="cli">
      <view>余额</view>
      <view>{{tools.sub.formatAmount(cardBean.spareCash,2)}}</view>
    </view>
    <view class="cli">
      <view>积分</view>
      <view>{{tools.sub.formatAmount(cardBean.integral,2)}}</view>
    </view>
    <view class="cli" bindtap="goChargeDetailBind">
      <view>消费记录</view>
      <view>查看</view>
    </view>
  </view>

  <view class="payWrap" style="width:700rpx;margin:0 auto;background-color:#fff;margin-bottom:20rpx;">
    <view style="padding:30rpx 0 0;margin-top:20rpx;">
      <image src="data:image/png;base64,{{cardBarcode}}" style="width:100%;" mode="widthFix"></image>
    </view>
    <view>
      <image src="data:image/png;base64,{{cardQrcode}}" style="width:100%;" mode="widthFix"></image>
    </view>
  </view>
</view>
<!--积分转线上-->
<view class="black_bg" hidden="{{scoreHidden}}"></view>
<view class="scoreWrap" style="padding:10rpx 30rpx 30rpx;" hidden="{{scoreHidden}}">
  <view class="score_title">转入积分</view>
  <view class="score_avail">可用积分：{{cardBean.integral}}</view>
  <view class="score_switch">转入积分：
    <input value="{{cardBean.integral}}" bindinput="integralBindInput" type="number"></input>
  </view>
  <view class="score_oper" style="width:80%; margin-left:10%;">
    <view class="score_confirm" bindtap="transferBindTap">确定</view>
    <view class="score_cancel" bindtap="cancelScoreBindTap" style="border:1rpx solid #c6c7c8;;">取消</view>
  </view>
  <view style="color:#FF7E00;font-size:26rpx;text-align:left;margin-top:40rpx;">
    提示：线下积分转入小程序积分后，线上积分可以用于兑换积分商品
  </view>
</view>
<view class="submitbtn" hidden="{{wechatPackage}}" bindtap="receiveVipCardToWehcatBindTap">领取到微信卡包</view>
<view class="goCouponBtn" style="" bindtap="goCoupon">我的卡券</view>