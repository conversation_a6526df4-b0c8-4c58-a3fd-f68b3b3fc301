<view class="section">
  <!--上下结构-->
  <view class="flex-wrp clearfix">
    <block wx:key="unique" wx:for="{{showGoodsTemplateList}}" wx:for-item="goods">
      <view class="flex-item" style="position:relative;">
        <view class="goods_pic">
          <block wx:if="{{goods.commoditySaleWay==1}}">
            <view class='soonlist'>即将上市</view>
          </block>
          <block wx:elif="{{goods.commoditySaleWay==4}}">
            <view class='soonlist'>已售馨</view>
          </block>
          <block wx:key="unique" wx:for="{{goods.promotionList}}" wx:for-item="promotion">
            <view class="eventWrap">
              <image lazy-load='true' src="{{event}}"></image>
              <block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
                <text>秒杀</text>
              </block>
              <block wx:if="{{promotion.promotionType=='TUANGOU'}}">
                <text>团购</text>
              </block>
              <block wx:if="{{promotion.promotionType=='TEJIA'}}">
                <text>特价</text>
              </block>
              <block wx:if="{{promotion.promotionType=='QUDUAN'}}">
                <text>区段</text>
              </block>
              <block wx:if="{{promotion.promotionType=='NYH'}}">
                <text>优惠</text>
              </block>
            </view>
          </block>
          <!--3人成团-->

          <image lazy-load='true' bindtap='imageClick' data-storeId='{{goods.commoditySupplierId}}' data-commodityId='{{goods.commodityId}}' src="{{goods.commodityMainPic}}"></image>
        </view>
        <label class="goods_title">{{goods.commodityName}}</label>
        <label class="goods_adv">{{goods.commodityAdContent}}</label>
        <view style="height:50rpx;">
          <view style="float:left;">
            <block wx:if="{{goods.commodityOtUnitDefault == 1}}">
              <label class="goods_price">￥{{goods.goodsPrice}}</label>
              <label class="goods_price price_append" hidden='{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice?false:true}}'>￥{{goods.cutOffThePrice}}</label>
            </block>
            <block wx:if="{{goods.commodityOtUnitDefault == 0 && goods.commodityOMUnitDefault == 1}}">
              <label class="goods_price">￥{{goods.omPrice}}</label>
              <label class="goods_price price_append" hidden='{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice?false:true}}'>￥{{goods.cutOffTheOmPrice}}</label>
            </block>
         </view>
        </view>
      </view>
    </block>
  </view>
</view>