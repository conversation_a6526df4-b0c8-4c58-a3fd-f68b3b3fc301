var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    minExtractMoney: 0,
    validRebateMoney: 0,
    withdrawQuota: 0,//提现金额
    isSend: false,
    bankNo: "",
    isFromBack: false,
    bankBtnText: "绑定",
    zfbAccount: "",
    extractType: 1,//1：银行卡 2:支付宝
    unionPay: app.imageUrl + 'unionPay.png',
    aliPay: app.imageUrl + 'aliPay.png',
    zfbBtnText: "绑定"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage();
  },
  initPage: function () {
    this.userQueryRebateAccount();
    this.queryBoundCardInfo();
  },
  updateBankInfo: function () {
    app.navigateToPage('/pages/bindCard/bindCard');
  },
  updateAlipayInfo: function () {
    app.navigateToPage('/pages/accountRebate/bindAlipay/bindAlipay');
  },
  queryBoundCardInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/boundCard/queryBoundCardInfo',
      success: function (res) {
        var boundBean = res.data.BoundBean;
        if (boundBean != null) {
          var bankNo = boundBean.bankNo;
          that.setData({
            bankNo: bankNo.substr(0, 4) + "********" + bankNo.substr(-4),
            bankBtnText: "修改"
          });
        }
        var userList = res.data.userList;
        if (userList != null && userList.length > 0) {
          that.setData({
            zfbAccount: userList[0].zfbAccount.substr(0, 4) + "*****" + userList[0].zfbAccount.substr(-4),
            zfbBtnText: "修改"
          })
        }
      }
    })
  },
  radioChange: function (e) {
    this.setData({
      extractType: e.detail.value
    })
  },
  /**
     * 用户查询自己的返利账目信息
     */
  userQueryRebateAccount: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/rebateAccountServer/userQueryRebateAccount',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var rebateAccountEntity = res.data.rebateAccountEntity;
        if (rebateAccountEntity != null) {
          that.setData({
            minExtractMoney: rebateAccountEntity.minExtractMoney > 0 ? rebateAccountEntity.minExtractMoney : 2,
            validRebateMoney: rebateAccountEntity.validRebateMoney
          });
        }
      }
    })
  },
  /**
   * 用户申请提现
   */
  userApplyExtractBindTap: function () {
    var that = this;
    var withdrawQuota = that.data.withdrawQuota;
    var extractType = that.data.extractType;
    if (!that.data.isSend) {
      return;
    }
    if (withdrawQuota <= 0) {
      return;
    }
    if (extractType == 1) {
      if (that.data.bankNo == "") {
        app.showModal({
          title: '提示',
          content: "请先绑定银行卡"
        });
        return;
      }
    } else if (extractType == 2) {
      if (that.data.zfbAccount == "") {
        app.showModal({
          title: '提示',
          content: "请先绑定支付宝账号"
        });
        return;
      }
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/extractServer/userApplyExtract',
      data: {
        "extractType": extractType,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "withdrawQuota": withdrawQuota
      },
      success: function (res) {
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          wx.showToast({
            title: "申请成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1000);
            }
          })
        } else {
          app.showModal({
            title: '提示',
            content: message
          });
        }
      }
    })
  },
  withdrawQuotaBindInput: function (e) {
    this.setData({
      isSend: parseFloat(e.detail.value) > this.data.minExtractMoney ? true : false,
      withdrawQuota: e.detail.value
    })
  },
  /**
   * 全部提现
   */
  allDrawBindTap: function () {
    this.setData({
      withdrawQuota: this.data.validRebateMoney.toFixed(2),
      isSend: this.data.validRebateMoney > this.data.minExtractMoney ? true : false
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.initPage();
    } else {
      that.setData({
        isFromBack: true
      })
    }
  }
})