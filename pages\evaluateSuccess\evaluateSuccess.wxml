<!-- <view class="evaluateSuccess">
	<view style="text-align:center;padding-top: 40rpx;">
		<image src="{{evaluateSuccess}}" style="width:153rpx;height:160rpx;"></image>
	</view>
	<view style="font-size:32rpx;color:#FF7E00;font-weight:bold;text-align:center;height:100rpx;line-height:100rpx;">评价成功
	</view>
	<view style="text-align:center;display:flex;justify-content: center;">
		<view class="submitBtn" style="margin-right:40rpx" bindtap="goIndex">返回首页</view>
		<view class="submitBtn" bindtap="goOrderList">返回订单</view>
	</view>
</view>
<block wx:if="{{iscjActivity}}">
	<view style="width:710rpx;height:140rpx;margin:20rpx auto;" bindtap="gocjBindTap">
		<image src="{{turntableBanner}}" style="width:710rpx;" mode="widthFix"></image>
	</view>
</block> -->
<view class="eval_one">
	<view
		class="eval_two">
		<view class="eval_three">
			<view class="eval_four">
				<image src="{{backpage_comment}}" class="eval_five" mode="widtnFix"></image>
			</view>
			<view
				class="eval_six">
				评价成功</view>
			<view class="eval_seven">
				<view style="width:45%;">
					<view
						class="eval_eight"
						bindtap="goIndex">
						返回首页</view>
				</view>
				<view style="width:10%"></view>
				<view style="width:45%;">
					<view
						class="eval_nine"
						bindtap="goOrder">
						查看订单</view>
				</view>
			</view>

		</view>
				<view class="eval_ten" wx:if="{{result.length>0}}">
			<view class="eval1_one">
				— 商家赠送以下权益 —</view>
			<view wx:for="{{result}}" wx:key="index" class="reward_two eval1_two">
				<view class="reward_three">
					<view class="reward_dashed">
						<image class="reward_img" src="{{item.imagesUrl}}" mode="widthFix"></image>
					</view>
				</view>
				<view style="width:65%;">
					<view class="reward_five">
						{{item.businessName}}
					</view>
					<view class="reward_six">
						{{item.prizeExplain}}
					</view>
				</view>
				<view class="reward_seven eval1_three" >
          <block wx:if="{{item.rightsType == 1}}">
            <view data-configid="{{item.configId}}" class="eval1_four {{item.checked==true?'':'grey_b'}}" bindtap="goSignBindTap">{{item.checked==true?'去领取':'已领取'}}</view>
          </block>
          <block wx:elif="{{item.rightsType == 2}}">
            <view class="eval1_four {{item.checked==true?'':'grey_b'}}" data-configid="{{item.configId}}" data-gameid="{{item.prizeId}}" bindtap="gocjBindTap">{{item.checked==true?'去抽奖':'已抽奖'}}</view>
          </block>
          <block wx:elif="{{item.rightsType == 3}}">
            <view data-configid="{{item.configId}}" class="eval1_four {{item.checked==true?'':'grey_b'}}" bindtap="goWordBindTap">{{item.checked==true?'去领取':'已领取'}}</view>
          </block>
          <block wx:elif="{{item.rightsType == 4}}">
            <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
          </block>
          <block wx:elif="{{item.rightsType == 5}}">
            <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
          </block>
          <block wx:elif="{{item.rightsType == 6}}">
            <view data-configid="{{item.configId}}" class="eval1_four grey_b">已发放</view>
          </block>
					<!--<view class="eval1_four" 	wx:if="{{item.rightsType==2}}" data-gameid="{{item.prizeId}}" bindtap="gocjBindTap">去抽奖</view>
					<view class="eval1_five" wx:else >
						已发放
          </view>	-->								
				</view>
			</view>
		</view>
	</view>

</view>