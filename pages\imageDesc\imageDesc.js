// pages/subPageList/subPageList.js
var WxParse = require('../../components/wxParse/wxParse.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    itemId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    var name = options.name;
    var id = options.id;
    wx.setNavigationBarTitle({
      title: name,
    })
    that.queryComponentDevelopers(id);
  },
  queryComponentDevelopers: function(id) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "id": id
      },
      url: app.projectName + '/indexTemplate/queryNavigationDetail',
      success: function(res) {
        var navigationEntity = res.data.navigationEntity;
        if (navigationEntity != null) {
          WxParse.wxParse("detailedContent", 'html', navigationEntity.content, that, 5);
        }
      }
    })
  }
})