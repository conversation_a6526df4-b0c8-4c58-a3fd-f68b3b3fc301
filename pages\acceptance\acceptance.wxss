.return_apply {
  width: 100%;
}

.address_box {
  width: 100%;
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}

.address_box label {
  display: block;
  width: 94%;
  margin: 0 auto;
  font-size: 13px;
  color: #333;
  line-height: 24px;
}

/**物流信息**/

.logistics_box {
  width: 94%;
  margin: 0 auto;
}

.logistics_title {
  display: block;
  font-size: 13px;
  margin-top: 10px;
  font-weight: bold;
}

.picker {
  padding: 0 2%;
  height: 36px;
  line-height: 36px;
  border: 1px solid #ececec;
  border-radius: 5px;
  position: relative;
}

.section {
  background: #fff;
  margin-top: 10px;
}

.picker text {
  position: absolute;
  top: 10%;
  right: 10px;
  transform: rotate(180deg);
}

.section input {
  height: 36px;
  line-height: 36px;
  border: 1px solid #ececec;
  border-radius: 5px;
  padding: 0 2%;
}
.confirm_btn{
  width: 92%;
  margin: 20px auto;
  height: 40px;
  line-height: 40px;
  font-size: 15px;
  border: none !important;
  color: #fff;
  background: #FF7E00;
}