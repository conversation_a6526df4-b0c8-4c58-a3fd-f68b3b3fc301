// pages/subPageList/subPageList.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    promotionType:3,
    appletPromotionList:[],
    specialPrice: app.imageUrl +"specialPrice.jpg",
    secondKill: app.imageUrl + "secondKill.jpg",
    grounpBuy: app.imageUrl + "groupBuy.jpg",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var name = options.name;
    var boundType = options.boundType;
    if(boundType == 6){
      boundType =3
    }
    else if (boundType == 7){
      boundType = 1
    }
    else if (boundType == 8){
      boundType = 2;
    }
    wx.setNavigationBarTitle({
      title: name,
    })
    that.queryPromotiondbCommodity(boundType);
  },
  /**
 * 跳转商品详情
 */
  imageClick: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 查询活动商品
   */
  queryPromotiondbCommodity: function (type) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/activity/queryPromotionCommodity',
      data: {
        "offSet": 1,
        "pageSize": 20,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "promotionType": type,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var pageResult = res.data.pageResult;
        that.setData({
          promotionType: type,
        });
        if (pageResult != null && pageResult.length>0){
          that.setData({
            appletPromotionList: pageResult,
          });
        }
        else{
          wx.showToast({
            title: "暂无活动",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1000);
            }
          })
        }
      }
      
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})