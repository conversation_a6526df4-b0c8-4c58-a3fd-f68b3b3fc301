const app = getApp();
const {
  emojis,
  emojiToPath,
} = require('../../utils/emojis');
var value ="";
//动画事件
function animationEvents(that, moveY, show) {
  console.log(that.data);
  if(that.data.which=="collect"){
    var addWordImg=decodeURIComponent(that.data.addWordImg);
  }else if(that.data.which=="sign") {
    var usableNum = that.data.result.usableNum;
    var fullPrize = that.data.fullPrize;
    var prizeType=that.data.prizeType;
    var prizeName=that.data.prizeName;
    var missNum = that.data.missNum;
    var isFill = that.data.isFill;
  }else if(that.data.which=="find"){
    var index=that.data.index;
    var currentPage=that.data.currentPage;
    var findinfoId=that.data.findinfoId;
    var  reportOrdelText=that.data.reportOrdelText;
    var  commentId=that.data.commentId;
  }else if(that.data.which=="reward"){
    var rewardList=that.data.rewardList
  }
  that.animation = wx.createAnimation({
    transformOrigin: "50% 50%",
    duration: 400,
    timingFunction: "step-start",
    delay: 0
  })
  that.setData({
    item: {
      animation: that.animation.export(), //动画
      show: show, //显示隐藏
      usableNum: usableNum, //补签卡数量
      disabled: usableNum == 0 ? true : false,//是否禁用
      fullPrize: fullPrize,//满签参数
      prizeType:prizeType,//奖励类型
      prizeName:prizeName,//奖励内容
      addWordImg:addWordImg,//返回自定义字图片,
      rewardList:rewardList,//奖励列表
      fullSignImg:app.imageUrl + 'sign_fullSignImg.png',
      close:app.imageUrl + 'sign_close.png',
      newuser:app.imageUrl + 'sign_newuser.png',
      share:app.imageUrl + 'sign_share.png',
      money:app.imageUrl + 'sign_money.png',
      couponmode:app.imageUrl + 'sign_couponmode.png',
      intergralmode:app.imageUrl + 'sign_intergralmode.png',
      moneymode:app.imageUrl + 'sign_moneymode.png',
      successmode:app.imageUrl + 'sign_successmode.png',
      turnmode:app.imageUrl + 'sign_turnmode.png',
      exchange:app.imageUrl + 'collect_exchange.png',
      prize:app.imageUrl + 'collect_prize.png',
      backpage_top:app.imageUrl + 'backpage_top.png',
      value: value,
      bottom: 0,
      state: false,     
      currentPage:currentPage,//页面参数
      index:index,//索引
      imgUrl: app.imageUrl+'find_emoji.png',
      find_wechat:app.imageUrl+'find_wechat.png',
      find_image: app.imageUrl+'find_image.png',
      commentId:commentId,//评论ID
      reportOrdelText:reportOrdelText,//删除或者举报
      findinfoId:findinfoId,//内容ID
      missNum:missNum,
      isFill:isFill
    }
  })
  that.animation.translateY(moveY + 'vh').step();
}

function changeHeight(that) {
  if (!that.data.item.state) {
    that.setData({
      item: {
        state: true,
        imgUrl: app.imageUrl+'find_jianpan.png',
        // isFocus: true,
        emojiList: Object.keys(emojis).map(key => ({
          key: key,
          img: emojiToPath(key)
        })),
        bottom:500
      }
    })
  } else {
    that.setData({
      item: {
        state: false,
        imgUrl: app.imageUrl+'find_emoji.png',
        // isFocus: true,
        emojiList: Object.keys(emojis).map(key => ({
          key: key,
          img: emojiToPath(key)
        })),
        bottom: 0
      }
    })
  }
}
function inputComment(that, e) {
  that.setData({
      msg: e.detail.value  
  })
}
// 点击表情
function clickEmoji(that, e) {
  const {
    key
  } = e.currentTarget.dataset;
  const  msg  = that.data.msg;
  that.setData({
    msg: msg + key,
    item: {
      state: true,
      msg: msg + key,
      emojiList: Object.keys(emojis).map(key => ({
        key: key,
        img: emojiToPath(key)
      }))
    }
  });
}

module.exports = {
  animationEvents: animationEvents,
  changeHeight:changeHeight,
  clickEmoji: clickEmoji,
  inputComment: inputComment
}