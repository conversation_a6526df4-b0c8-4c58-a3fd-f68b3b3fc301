<!--pages/onePromotion/onePromotion.wxml-->
<view>
  <view class="infoWrap">
    <block wx:if="{{promotionType == 3}}">
      <image src="{{specialPrice}}" style="width:100%" mode="widthFix"></image>
    </block>
    <block wx:elif="{{promotionType == 2}}">
      <image src="{{grounpBuy}}" style="width:100%" mode="widthFix"></image>
    </block>
    <block wx:elif="{{promotionType == 1}}">
      <image src="{{secondKill}}" style="width:100%" mode="widthFix"></image>
    </block>
    <!--<image src="{{promotionPic}}" style="width:100%;" mode="widthFix"></image>
    <view class="promotionTitle">
      距离结束有：
      <text class="hour_end">{{clock}}</text>
    </view>-->
  </view>
  <view class="onSaleProduct">
    <block wx:key="unique" wx:for="{{appletPromotionList}}" wx:for-item="goods">
      <!--单个商品-->
      <view class="rightWrap" style="position:relative;height:300rpx;" data-commodityId="{{goods.commodityId}}" bindtap="imageClick">
        <!--售罄展示此样式-->
        <view class="soldOut" hidden="{{!goods.commoditySaleEmpty}}">
        </view>
        <image class="soldOutIcon" src="{{soldOut}}" hidden="{{!goods.commoditySaleEmpty}}"></image>
        <!--售罄展示此样式-->
        <image class="rightMainPic" style="width:300rpx;height:300rpx;" src="{{goods.commodityPic}}"></image>
        <view class="clearfix productAttr">
          <text class="productName">{{goods.commodityName}}</text>
          <view class="productLine">
            <label>
              <text class="c_fl">{{goods.commoditySpec}}</text>
              <!--<text class="c_fr" hidden='{{goods.commodityInventoryShow?false:true}}'>库存：{{goods.commodityVirtualStore}}</text>-->
            </label>
            <text class="newPrice">￥{{goods.goodsPrice}}</text>
            <text class="oldPrice">￥{{goods.cutOffThePrice}}</text>
            <text class="buy_Num"></text>
          </view>
          <view style="margin-top:30rpx;color:#fff;background:#ff6600;font-size:28rpx;display:inline-block;padding:8rpx 30rpx;float:right;margin-right:30rpx;">
            <block wx:if="{{promotionType == 3}}">马上抢</block>
            <block wx:elif="{{promotionType == 2}}">去团购</block>
            <block wx:elif="{{promotionType == 1}}">去秒杀</block>
          </view>
        </view>
        <view class="clearfix">
        </view>
      </view>
      <!--单个商品-->
    </block>
  </view>
</view>