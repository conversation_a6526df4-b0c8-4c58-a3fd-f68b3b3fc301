var WxParse = require('../../components/wxParse/wxParse.js');
const app = getApp();
Page({

    /**
     * 页面的初始数据
     */
    data: {

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function(options) {
        var that = this;
        var id = options.id;
        wx.request({
            header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/matter/queryIndexPlateDetail',
            data: {
                "id": id
            },
            success: function(res) {
                var detailedContent = res.data.detailedContent;

                detailedContent = detailedContent.replace('<img', '<img style="width:100px !important;height:auto" ') //防止富文本图片过大
                WxParse.wxParse("detailedContent", 'html', detailedContent, that, 5);
            }
        })
    }

})