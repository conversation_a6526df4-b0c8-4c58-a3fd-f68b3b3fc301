/* components/slideItem/slideItem.wxss */
/*新加的样式*/
.example {
  display: block;
  width: 100%;
  height: 60rpx;
  background:#fff;
  padding-top:20rpx;
}
.marquee_box {
  width: 100%;
  position: relative;
  padding-left:50rpx;
}
.marquee_box image{
  padding-left:30rpx;
  padding-right:30rpx;
  background:#fff;
  z-index:2;
  position:absolute;
  top:0;
  left:0;
  width:40rpx;
  height:40rpx;
}
.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
  color:#de535f !important;
}