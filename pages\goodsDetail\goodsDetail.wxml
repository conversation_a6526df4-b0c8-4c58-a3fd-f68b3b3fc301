<wxs src="../../wxs/subutil.wxs" module="tools" />
<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<!-- 内容 -->
<scrollview scroll-y class='contant_box'>
	<import src="../../components/wxParse/wxParse.wxml" />
	<swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" circular="{{circular}}"
		indicator-dots="{{indicatorDots}}" indicator-color="{{indicatorColor}}"
		indicator-active-color="{{indicatorActiveColor}}" duration="{{duration}}" style="height:750rpx; position:relative;">
		<block wx:key="unique" wx:for="{{goodsDetail.picList}}" wx:for-item="image">
			<swiper-item>
				<view style="position:relative;">
					<image lazy-load='true' src="{{image.commodityPicPath}}" bindtap="enlarge" data-list="{{goodsDetail.picList}}"
					data-src="{{image.commodityPicPath}}" bindload="imageLoad" class="slide-image"
					style="width:{{imgwidth}}rpx; height:750rpx;" />
					<block wx:if="{{goodsDetail.commodityAdTagStyle.showStyle == 1 && goodsDetail.commodityAdTag.length>0}}">
						<template is="mark1" data="{{name:goodsDetail.commodityAdTag,pos:goodsDetail.commodityAdTagStyle.showPosition}}"></template>
					</block>
					<block wx:if="{{goodsDetail.commodityAdTagStyle.showStyle == 2 && goodsDetail.commodityAdTag.length>0}}">
						<template is="mark2" data="{{name:goodsDetail.commodityAdTag,pos:goodsDetail.commodityAdTagStyle.showPosition}}"></template>
					</block>
					<block wx:if="{{goodsDetail.commoditySideDescStyle.showStyle == 3 && goodsDetail.commoditySideDesc.length>0}}">
						<template is="mark3" data="{{name:goodsDetail.commoditySideDesc,nameCircle:goodsDetail.commoditySideDescStyle.specialHead}}"></template>
					</block>
					<!--<block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
						<template is="mark1"
						data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
					</block>
					<block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
						<template is="mark2"
						data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
					</block>
					<block wx:if="{{goodsDetail.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
						<template is="mark3"
						data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
					</block>-->
				</view>
			</swiper-item>
		</block>
	</swiper>
	<!-- 团购   秒杀 -->
	<view class='group_box' hidden='{{!goodsDetail.participatePromotion}}'>
		<view class="goods-seckill-left">
			<view class="goods-seckill-original">
				<block wx:key="unique" wx:for="{{goodsDetail.retailPromotionList}}" wx:for-item="promotion"
					wx:for-index="index">
					<block wx:if="{{index == 0}}">
						<block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
							<label class="goods-seckill-sign">秒杀商品</label>
						</block>
						<block wx:if="{{promotion.promotionType=='TUANGOU'}}">
							<label class="grounpProduct"></label>
							<label class="goods-seckill-sign" style="font-size:15px;">
								<block wx:if="{{promotion.promotionGroupBean.groupType==1}}">
									{{promotion.promotionGroupBean.groupBuyerNum}}人团
								</block>
								<block wx:if="{{promotion.promotionGroupBean.groupType==2}}">
									{{promotion.promotionGroupBean.groupCommodityNum}}件数量团
								</block>
								<text>
									<block wx:if="{{promotion.promotionGroupBean.groupType==1}}">
										<text>· 已团{{pgbBean.totalUserNum}}人</text>
									</block>
									<block wx:if="{{promotion.promotionGroupBean.groupType==2}}">
										<text>· 已团{{pgbBean.totalCommodityNum}}件</text>
									</block>
								</text>
							</label>
						</block>
						<block wx:if="{{promotion.promotionType=='TEJIA'}}">
							<label class="goods-seckill-sign">特价商品</label>
						</block>
						<block wx:if="{{promotion.promotionType=='QUDUAN'}}">
							<label class="goods-seckill-sign">区段商品</label>
						</block>
						<block wx:if="{{promotion.promotionType=='NYH'}}">
							<label class="goods-seckill-sign">
								<block wx:if="{{showskuAllAttrList.length>0}}">
									第{{promotion.commoditySkuList[0].numberDiscount.orderNum}}件优惠
								</block>
								<block wx:else>
									第{{promotion.numberDiscount.orderNum}}件优惠
								</block>
							</label>
						</block>
					</block>
				</block>
			</view>
		</view>
		<!-- <view class='activity_time'>
			<label style="height: 36rpx;">秒杀倒计时：</label>
			<view style="display:flex;padding:0rpx 40rpx;">
				<label class='clock' style="font-size:26rpx;background:none;color:#000;">{{days}}</label>
				<label class='clock'>{{hours}}</label>:
				<label class='clock'>{{minutes}}</label>:
				<label class='clock'>{{seconds}}</label>
			</view> -->
		<view class='activity_time'>
			<label class="goodn3_ten">秒杀倒计时:</label>
			<view style="display:flex;">
				<label>{{days}}</label><text class="goodn3_six">:</text>
				<label>{{hours}}</label><text class="goodn3_six">:</text>
				<label>{{minutes}}</label><text class="goodn3_six">:</text>
				<label>{{seconds}}</label>
			</view>
		</view>
	</view>
	<!-- 团购   秒杀 -->
	<view class="goods_content clearfix" style="padding-top:20rpx;">
		<view style="float:left;width:85%;">
			<label class="goods_title">{{goodsDetail.commodityName}}</label>
			<label class="goods_adv">{{goodsDetail.commodityAdContent}}</label>
			<label class="goods_price" style="display:flex;">
				<label hidden="{{commodityUnitOtDefault==1?false:true}}">
					<view>￥<text style="font-size:36rpx;">{{showGoodsRangePrice}}</text>
						<block wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOtUnit}}</block>
					</view>

					<!--<view style="font-size:24rpx;color:#686868;padding-left: 32rpx;">
						<block wx:if="{{goodsDetail.unitList[0].commodityMoq>0}}">
							{{goodsDetail.unitList[0].commodityMoq}}{{goodsDetail.unitList[0].commodityWeightUnit}}起订
						</block>
					</view>-->
				</label>
				<label hidden="{{commodityUnitOmDefault==1?false:true}}">
					<view>￥<text
							style="font-size:36rpx;font-weight:bold;">{{tools.sub.formatAmount(goodsPriceOM,2)}}</text>/{{goodsOMUnit}}
					</view>
					<!--<view style="font-size:24rpx;color:#686868;padding-left: 32rpx;">
						<block wx:if="{{goodsDetail.unitList[1].commodityMoq>0}}">
							{{goodsDetail.unitList[1].commodityMoq}}{{goodsDetail.unitList[1].commodityWeightUnit}}起订
						</block>
					</view>-->
				</label>
				<label style="font-size:26rpx;font-weight:bold;color:#666;padding-top: 10rpx;"
					hidden="{{commodityUnitOmDefault==1?false:true}}">(1{{goodsOMUnit}}={{commodityMultiple}}{{goodsOtUnit}})</label>
			</label>
			<!-- <label class="goodsMoq">
				<label>
					<block wx:if="{{goodsDetail.unitList[0].commodityMoq>0}}">
						{{goodsDetail.unitList[0].commodityMoq}}{{goodsDetail.unitList[0].commodityWeightUnit}}起订
					</block>
				</label>
				<label>
					<block wx:if="{{goodsDetail.unitList[1].commodityMoq>0}}">
						{{goodsDetail.unitList[1].commodityMoq}}{{goodsDetail.unitList[1].commodityWeightUnit}}起订
					</block>
				</label>
			</label> -->
			
			<view class="clearfix">
				<label hidden="{{cutPriceOM>0&&cutPriceOM-goodsPriceOM>0?false:true}}"
					style="margin-left:12rpx;color:#666;text-decoration:line-through;font-size:26rpx;float:left">
					<text hidden="{{commodityUnitOmDefault==1?false:true}}">￥{{tools.sub.formatAmount(cutPriceOM,2)}}<block
							wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOMUnit}}</block></text>
				</label>
				<!--hidden="{{cutPrice>0&&cutPrice-showGoodsPrice>0?false:true}}"-->
				<label hidden="{{cutPrice>0&&cutPrice-showGoodsPrice>0?false:true}}"
					style="margin-left:12rpx;color:#666;text-decoration:line-through;font-size:26rpx;float:left">
					<text hidden="{{commodityUnitOtDefault==1?false:true}}">￥{{tools.sub.formatAmount(cutPrice,2)}}<block
							wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOtUnit}}</block></text>
				</label>
				<view hidden="{{salesVolume==1?false:true}}"
					style="color:#666;font-size:26rpx;float:right;margin-right: -80rpx;">
					已售{{goodsDetail.unitList[0].commoditySaleTotal}}</view>
			</view>
		</view>
		<view class="shareGood" bindtap="shareGoodsBind">
			<image src="{{goShare}}" mode="widthFix"></image>
			<view>分享</view>
		</view>
	</view>
	<view class="clearfix onPromotion" hidden="{{fullCut>0?false:true}}">
		<view class="pro_left">促销：</view>
		<view style="float:left;">
			<view>
				<text class="pro_title">包邮</text>
				<text class="pro_inner">满{{fullCut}}元包邮</text>
			</view>
		</view>
	</view>
	<!-- 拼单 -->
	<block
		wx:if="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='TUANGOU'&&groupList.length>0}}">
		<view class='collageView'>
			<view class='collageFristView'>
				<label class='collageFristLable'>{{groupList.length}}人在拼单</label>
				<label class='collageTwoLable' bindtap='queryGroupByMoreBindTap'>查看更多 >></label>
			</view>
			<swiper scroll-y="true" style='max-height:{{groupList.length>1?160:80}}px;'
				display-multiple-items="{{groupList.length>1?2:1}}" vertical="true" autoplay="true" circular="true"
				interval="2000">
				<block wx:key="unique" wx:for="{{groupList}}" wx:for-item="group">
					<swiper-item>
						<!-- 用户 -->
						<view class='collageUserView'>
							<image lazy-load='true' class='collageUserImage' src='{{group.userPic==""?userHeadImage:group.userPic}}'>
							</image>
							<view class='collageUserFristView'>
								<label class='collageUserLable'>{{group.userName}}</label>
								<view class='collageUserTwoView'>
									<view class='collageUserThreeView'>
										还差
										<block wx:if="{{group.groupType==1}}">
											<label style='color:red;'>{{group.surplusUserNum}}人</label>
										</block>
										<block wx:elif="{{group.groupType==2}}">
											<label style='color:red;'>{{group.surplusCommodityNum}}件</label>
										</block>
										拼成
									</view>
									<label class='collageUserFristLable'>剩余{{clock}}</label>
								</view>
								<button class='collageUserButton' data-id='{{group.userId}}' bindtap='nowBuyBindTap' data-type='2'>
									去拼单
								</button>
							</view>
						</view>
					</swiper-item>
				</block>
			</swiper>
		</view>
	</block>
	<!-- 拼单 -->
	<!-- 选择规格 -->
	<view class="attr_wrap" bindtap="chooseAttrBind" hidden="{{showskuAllAttrList.length>0?false:true}}">
		<view class="choose_attr">
			<text style="font-size:24rpx;">选择：</text> {{selectSkuStr}}
			<label class="icondirect">︿</label>
		</view>
	</view>
	<block
		wx:if="{{skuRegionPriceList!=null&&skuRegionPriceList.length>0&&goodsDetail.retailPromotionList[0].promotionType=='QUDUAN'}}">
		<view class='service_note'>
			<text class='note_title'>活动:</text>
			<view class='quduan_tips' style="height:90px;overflow:hidden;overflow-y:scroll;padding-left:20px;">
				<block wx:key="unique" wx:for="{{skuRegionPriceList}}" wx:for-item="sku">
					<block wx:key="unique" wx:for="{{sku.regionPriceList}}" wx:for-item="region">
						<label>
							<text></text> 购买
							<block wx:if="{{region.endRegion==0}}">大于{{region.beginRegion}}</block>
							<block wx:elif="{{region.endRegion==region.beginRegion}}">
								{{region.beginRegion}}
							</block>
							<block wx:else>{{region.beginRegion}}~{{region.endRegion}}</block>
							{{sku.skuWeightUnit}} (
							<block wx:key="unique" wx:for="{{sku.skuAttrList}}" wx:for-index="skuIdx" wx:for-item="attr">
								<block wx:if="{{sku.skuAttrList.length-1==skuIdx}}">
									{{attr.skuName}}:{{attr.skuValue}}
								</block>
								<block wx:else>{{attr.skuName}}:{{attr.skuValue}};</block>
							</block>
							)
							<label style="color:#FF7E00;display:inline-block">
								{{region.commoditySalePrice}}元
							</label>
						</label>
					</block>
				</block>
			</view>
		</view>
	</block>
	<!-- 区段价格（不带sku属性的） -->
	<block
		wx:if="{{unitRegionPriceList!=null&&unitRegionPriceList.length>0&&goodsDetail.retailPromotionList[0].promotionType=='QUDUAN'}}">
		<view class='service_note'>
			<text class='note_title'>活动:</text>
			<view class='quduan_tips' style="height:90px;overflow:hidden;overflow-y:scroll;padding-left:20px;">
				<block wx:key="unique" wx:for="{{unitRegionPriceList}}" wx:for-item="unit">
					<block wx:key="unique" wx:for="{{unit.regionPriceList}}" wx:for-item="region">
						<label>
							<text></text> 购买
							<block wx:if="{{region.endRegion==0}}">大于{{region.beginRegion}}</block>
							<block wx:elif="{{region.endRegion==region.beginRegion}}">
								{{region.beginRegion}}
							</block>
							<block wx:else>{{region.beginRegion}}~{{region.endRegion}}</block>
							{{unit.commodityWeightUnit}}
							<label style="margin-left:5px;color:#FF7E00;display:inline-block;">
								{{region.commoditySalePrice}}元
							</label>
						</label>
					</block>
				</block>
			</view>
		</view>
	</block>
	<!-- 区段价格 -->
	<!-- 黑色背景 -->
	<view class='black_bg1' hidden='{{groupMoreHidden}}'></view>
	<!-- 拼单点击更多 -->
	<block
		wx:if="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='TUANGOU'&&groupList.length>0}}">
		<view class="saleWrap" hidden='{{groupMoreHidden}}'>
			<view class="titleWrap">
				<label style="font-size:16px">正在拼单</label>
				<icon class="page-dialog-close" bindtap='closeUserGroupBindTap' type="clear" size='20' color='#666' />
			</view>
			<view style="overflow:hidden;height:280px;overflow-y:scroll">
				<block wx:key="unique" wx:for="{{groupList}}" wx:for-item="group">
					<view class='collageUserView'>
						<image lazy-load='true' class='collageUserImage' src='{{group.userPic==""?userHeadImage:group.userPic}}'>
						</image>
						<view class='collageUserFristView'>
							<view class='collageUserpopView'>
								<view class='collageUserThreeView'>
									<label class="userNeed" style="font-size:12px;">{{group.userName}}</label>
									<block wx:if="{{group.groupType==1}}">
										<label class="personNeed">还差{{group.surplusUserNum}}人</label>
									</block>
									<block wx:elif="{{group.groupType==2}}">
										<label class="personNeed">还差{{group.surplusCommodityNum}}件</label>
									</block>
								</view>
								<label class='collageUserFristLable'>剩余{{clock}}</label>
							</view>
							<button class='collageUserButton' data-id='{{group.userId}}' bindtap='nowBuyBindTap' data-type='2'>
								去拼单
							</button>
						</view>
					</view>
				</block>
			</view>
		</view>
	</block>
	<!-- 拼单点击更多 -->
	<!-- 选择配送方式 -->
	<view class="attr_wrap">
		<view class="choose_attr">
			<text style="font-size:24rpx;">配送：</text> 
			<block wx:if="{{d_way.length == 2}}">
				快递/自提
			</block>
			<block wx:else>
				<block wx:if="{{d_way[0] == 1}}">
					快递
				</block>
				<block wx:if="{{d_way[0] == 2}}">
					自提
				</block>
			</block>
			
		</view>
	</view>
	<!-- 选择配送方式 -->


	<block wx:if="{{stockBean!=null}}">
		<block wx:if="{{stockBean.openStock}}">
			<block wx:if="{{stockBean.stockShowType==1}}">
				<view class="attr_wrap">
					<view class="choose_attr">
						<text style="font-size:24rpx;">库存：</text> {{commodityVirtualStore}}
					</view>
				</view>
			</block>
			<block wx:elif="{{stockBean.stockShowType==2}}">
				<view class="attr_wrap">
					<view class="choose_attr">
						<text style="font-size:24rpx;">库存：</text>{{stockBean.showContent}}
					</view>
				</view>
			</block>
		</block>
	</block>
	<block wx:else>
		<view class="attr_wrap" hidden="{{commodityVirtualStore>0?false:true}}">
			<view class="choose_attr">
				<text style="font-size:24rpx;">库存：</text> {{commodityVirtualStore}}
			</view>
		</view>
	</block>

	<block wx:if="{{storeCard.length>0}}">
		<view class="attr_wrap clearfix" style="padding:20rpx" bindtap="cardshowBindTap">
			<view class="redBox" style="float:left;">领券</view>
			<view class="redBox_empty" style="float:left;margin-left:20rpx;">{{storeCard[0].discountDesc}}</view>
			<image lazy-load='true' src='{{personal_more}}' style="top:0;width:14px;height:14px;float:right"></image>
		</view>
	</block>


	<!-- 官方标记 -->
	<view class="attr_wrap" bindtap='goToHomeBindTap'>
		<view class="choose_attr clearfix">
			<image class="shopImage" src="{{storeImage==''?dbbLogo:storeImage}}"></image>
			<view class="clearfix">
				<view class="">
					<label class="shopName">{{storeName}}</label>
					<label class="shopMark">官方认证</label>
				</view>
			</view>
		</view>
	</view>
	<view class="attr_wrap">
		<view class="choose_attr clearfix">
			<label>商品评价（{{orderEvaluateList.length}}）</label>
			<label hidden="{{orderEvaluateList.length>0?false:true}}" style="float:right;font-size:24rpx;"
				bindtap='queryAllEvaluateBindTap'>
				全部评价
				<label class="icondirect" style="margin-left:10rpx;">︿</label>
			</label>
		</view>
		<block wx:key="unique" wx:for="{{orderEvaluateList}}" wx:for-item="evaluate" wx:for-index="eval_index">
			<block wx:if="{{eval_index<3}}">
				<view class="choose_attr clearfix">
					<image class="userImg" src="{{storeImage==''?dbbLogo:storeImage}}"></image>
					<label class="userN">{{evaluate.evaluateUserName}}</label>
					<view class="userS">{{evaluate.evaluateScore}}</view>
				</view>
				<view class="choose_attr clearfix e_content">{{evaluate.evaluateContent}}</view>
			</block>
		</block>
	</view>
	<!-- 详情 -->
	<view class='goods_evaluate' style="background:#fff;margin-top:20rpx;">
		<!-- <label class='line'></label> -->
		<view class='icon-good-comment' style="font-size:28rpx;color:#666;margin-bottom:40rpx;">商品详情</view>
		<!-- <label class='line'></label> -->
	</view>
	<!-- <label class='goods_detail'>产品详情</label> -->
	<view class='goods_pic' style="background:#fff;">
		<template is="wxParse" data="{{wxParseData:commodityIntroduce.nodes}}" />
	</view>
</scrollview>
<!-- 内容 -->
<view class='foot_box'>
	<view class='flex-sub-box-3 little_icon' style="width:40%;">
		<!-- 没有客服时，class添加noContact属性 -->
		<view class='flex-sub-box-2 {{isOnlineService==false?"noContact":""}}' style="width:33%" bindtap='goToHomeBindTap'>
			<image mode="widthFix" src="{{indexIcon}}" style="width:48rpx;margin-top:4rpx;"></image>
			<label style="font-size:20rpx;color:#666;">首页</label>
		</view>
		<view class='flex-sub-box-2 {{isOnlineService==false?"noContact":""}}'
			style='position:relative;overflow:hidden;width:33%;' hidden="{{!isOnlineService}}">
			<image mode="{{item.mode}}" src="{{online}}"></image>
			<button open-type="contact" size="20" session-from="weapp"
				style='width:100rpx;height:375rpx;position:absolute;top:0; left:0;opacity:0;'></button>
			<label style="font-size:20rpx;color:#666;">客服</label>
		</view>
		<view class='flex-sub-box-2 {{isOnlineService==false?"noContact":""}}' style="width:33%"
			bindtap='goToShopCartBindTap'>
			<image mode="{{item.mode}}" src="{{shopcart}}"></image>
			<text class="cartNum" hidden='{{shopCartNum>0?false:true}}'>{{shopCartNum}}</text>
			<label style="font-size:20rpx;color:#666;">购物车</label>
		</view>
	</view>
	<block wx:if="{{goodsDetail.commoditySaleWay==3}}">
		<block wx:if="{{overallStock==1&&0>=commodityVirtualStore}}">
			<view class='flex-sub-box-3' style="width:60%;">
				<label class="btn buy-goods-directly" style='text-align:center;color:#000;border-left:1px solid #e6e6e6;'>
					已售罄
				</label>
			</view>
		</block>
		<block wx:else>
			<block wx:if="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='TUANGOU'}}">
				<view class='flex-sub-box-3' bindtap='aloneNowBuyBindTap'>
					<label class="btn buy-goods-directly" style='color:#FF7E00;border-left:1px solid #e6e6e6'>
						<text class="bottom_price">
							<block wx:key="unique" wx:for="{{goodsDetail.unitList}}" wx:for-item="unit">
								<block wx:if="{{unit.commodityWeightType=='OT'}}">￥{{tools.sub.formatAmount(unit.commoditySalePrice,2)}}
								</block>
							</block>
						</text>
						<text class="bottom_title">单独购买</text>
					</label>
				</view>
				<view class='flex-sub-box-3' style="padding:0;">
					<label class="btn add-to-shoppingcart cart_append" bindtap='nowGroupBuyBindTap'>
						<text class="bottom_price">￥{{tools.sub.formatAmount(showGoodsPrice,2)}}</text>
						<text class="bottom_title">发起拼团</text>
					</label>
				</view>
			</block>
			<block
				wx:elif="{{goodsDetail.participatePromotion&&goodsDetail.retailPromotionList[0].promotionType=='MIAOSHA'}}">
				<view class='flex-sub-box-3  buyBtn' style="width:29%;margin-left:28%" bindtap='nowBuyBindTap' data-type='1'>
					<label class="btn buy-goods-directly"
						style='color:#FF7E00;border-left:1px solid #e6e6e6;line-height:40px;border-radius:25px'>
						立即购买
					</label>
				</view>

			</block>
			<block wx:else>
				<view class='flex-sub-box-3  buyBtn' style="width:29%" bindtap='showAddToShoppingCart'>
					<label class="btn buy-goods-directly" style='color:#FF7E00;border-left:1px solid #e6e6e6;line-height:40px;'>
						加入购物车
					</label>
				</view>
				<view class='flex-sub-box-3 addBtn' style="width:29%;margin-right:2%" bindtap='nowBuyBindTap' data-type='1'>
					<label class="btn btn-red add-to-shoppingcart" style="height:40px;line-height:40px;">
						立即购买
					</label>
				</view>
			</block>
		</block>
	</block>
	<block wx:elif="{{goodsDetail.commoditySaleWay==4}}">
		<view class='flex-sub-box-3' style="width:60%;">
			<label class="btn buy-goods-directly" style='text-align:center;color:#000;border-left:1px solid #e6e6e6;'>
				已售罄
			</label>
		</view>
	</block>
	<block wx:elif="{{goodsDetail.commoditySaleWay==1}}">
		<view class='flex-sub-box-3' style="width:60%;">
			<label class="btn buy-goods-directly" style='text-align:center;color:#000;border-left:1px solid #e6e6e6;'>
				即将上市
			</label>
		</view>
	</block>
</view>
<!-- 黑色背景 -->
<view class='black_bg' hidden="{{serverHidden}}" bindtap='hideServerBindTap'></view>
<view class='black_bg' hidden="{{addToShoppingCartHidden}}"></view>
<!-- 黑色背景 -->
<!-- 加入购物车   立即购买 -->
<view class='scroll_block page-dialog-wrap' hidden="{{addToShoppingCartHidden}}"
	style="z-index:10;background-color:#fff;border-radius: 16rpx 16rpx 0 0;">
	<view style='position:fixed; width:100%; height:100px;background:#fff;padding:10px 0;border-radius: 8px 8px 0 0;'>
		<view class='addgoods_pic'>
			<image lazy-load='true' mode="{{mode}}" src="{{goodsDetail.picList[0].commodityPicPath}}"></image>
		</view>
		<label class='addgoods_title'>{{goodsDetail.commodityName}}</label>
		<!--<label class='addgoods_price'>￥{{tools.sub.formatAmount(showGoodsPrice,2)}}/{{goodsOtUnit}}
    </label>-->
		<view class='addgoods_price' hidden="{{commodityUnitOmDefault==1?false:true}}">
			￥<text>{{tools.sub.formatAmount(goodsPriceOM,2)}}</text>
			<block wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOMUnit}}</block>
		</view>

		<view class='addgoods_price' hidden="{{commodityUnitOtDefault==1?false:true}}">
			￥<text>{{tools.sub.formatAmount(showGoodsPrice,2)}}</text>
			<block wx:if="{{showskuAllAttrList.length==0}}">/{{goodsOtUnit}}</block>
		</view>

		<view class="addgoods_wrap" style="display:flex;justify-content: space-between;color:#919398">
			<view class="addgoods_attr">
				<block wx:if="{{stockBean!=null}}">
					<block wx:if="{{stockBean.openStock}}">
						<block wx:if="{{stockBean.stockShowType==1}}">
							<view class="attr_wrap" style="margin:0;">
								<view class="choose_attr" style="padding:0">
									<text style="font-size:24rpx;">库存：</text> {{commodityVirtualStore}}
								</view>
							</view>
						</block>
						<block wx:elif="{{stockBean.stockShowType==2}}">
							<view class="attr_wrap" style="margin:0;">
								<view class="choose_attr" style="padding:0">
									<text style="font-size:24rpx;">库存：</text>{{stockBean.showContent}}
								</view>
							</view>
						</block>
					</block>
				</block>
				<block wx:else>
					<view class="attr_wrap" style="margin:0;" hidden="{{commodityVirtualStore>0?false:true}}">
						<view class="choose_attr" style="padding:0">
							<text style="font-size:24rpx;">库存：</text> {{commodityVirtualStore}}
						</view>
					</view>
				</block>
			</view>

			<view class="addgoods_attr">
				<block wx:if="{{goodsDetail.participatePromotion}}">
					<block wx:key="unique" wx:for="{{goodsDetail.retailPromotionList}}" wx:for-item="promotion">
						<block wx:if="{{promotion.promotionLimitEnabled}}">
							<label style='font-size:12px; color:#888;'>
								(限购{{promoton.promotionLimitOtNum}}件)
							</label>
						</block>
					</block>
				</block>
				<block wx:else>
					<block wx:if="{{goodsDetail.unitList[0].commodityMoq>0}}">
						(起订量{{goodsDetail.unitList[0].commodityMoq}}{{goodsDetail.unitList[0].commodityWeightUnit}})
					</block>
				</block>
			</view>
		</view>


		<icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='hiddeAddToShoppingCart' />
	</view>
	<scroll-view scroll-y style='padding-top:100px; max-height:200px;'>
		<block wx:key="unique" wx:for="{{showskuAllAttrList}}" wx:for-item="sku">
			<view class='goods_classify'>
				<label>{{sku.skuAttrName}}</label>
				<view class='clearfix'>
					<block wx:key="unique" wx:for="{{sku.skuAttrValueList}}" wx:for-item="skuChild">
						<text bindtap='changeSKUBindTap' data-name='{{sku.skuAttrName}}' data-childname='{{skuChild.skuAttrName}}'
							class='{{skuChild.isSelect?"active_classify":""}}'>{{skuChild.skuAttrName}}
						</text>
					</block>
				</view>
			</view>
		</block>
		<view class='addgoods_number clearfix'>
			<view class='limited_quantity' style="float: left;">
				购买数量
			</view>
			<view style="float: right">
				<view class='clearfix plus_minus' hidden="{{commodityUnitOmDefault==1?false:true}}">
					<label class="minus_box" bindtap='clickMinusOmButton'>-</label>
					<input type='number' value='{{buyOmCount}}' bindinput="inputBuyOmCount"></input>
					<label class="plus_box" bindtap='clickPlusOmButton'>+</label>
					<text class="unit_num">{{goodsOMUnit}}</text>
				</view>
				<view class='clearfix plus_minus' hidden="{{commodityUnitOtDefault==1?false:true}}">
					<label class="minus_box" bindtap='clickMinusButton'>-</label>
					<input type='number' value='{{buyCount}}' bindinput="inputBuyCount"></input>
					<label class="plus_box" bindtap='clickPlusButton'>+</label>
					<block wx:if="{{showskuAllAttrList.length==0}}">
						<text class="unit_num">{{goodsOtUnit}}</text>
					</block>

				</view>
			</view>
		</view>
	</scroll-view>
	<label class="btn pay-add-to-shoppingcart" bindtap='addShoppingCartBindTap'>
		{{joinOrBuy}}
	</label>
</view>
<!-- 分享弹出层 -->
<view class="black_bg" hidden="{{shareBlackBgHidden}}"></view>
<view style="z-index:999;text-align:center;position:fixed;background:#f0f0f0;bottom:0;left:0;right:0;"
	hidden="{{shareShowHidden}}">
	<view style="background:#fff;margin-bottom:10rpx;" data-share="{{isDistribution}}" bindtap="getPosterBindTap"><button
			style="background:#fff;font-size: 32rpx;">分享海报</button></view>
	<view style="background:#fff;"><button style="background:#fff;font-size: 32rpx;" open-type="share">分享商品</button>
	</view>
	<view style="background:#fff;margin-top:20rpx;padding:24rpx 0;" bindtap="cancelShareBindTap">
		取消
	</view>
</view>
<!-- 分享弹出层 -->
<!-- 生成海报 -->
<view style="padding-top:10rpx;width:600rpx;z-index:999;position:absolute;top:5%;background:#fff;margin-left:75rpx;"
	hidden="{{posterHidden}}">
	<view style="height:50rpx;">
		<icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='cancelButtonBindTap' />
	</view>
	<canvas class="poster" type="2d" id="canvas_box" style="width:600rpx;height:900rpx;"></canvas>
	<view style="margin-top:20rpx;background:#FF7E00;color:#fff;padding:20rpx 0;text-align:center;"
		bindtap="savePosterToPhoneBindTap">
		保存图片
	</view>
</view>
<!-- 生成海报 -->

<!-- 优惠券弹层 start -->
<view class="black_bg" hidden='{{cardHidden}}'></view>
<view hidden='{{cardHidden}}'
	style="z-index:9999;background:#fff;width:750rpx;bottom:0;position:fixed;max-height:800rpx;overflow-y:scroll;">
	<icon class="page-dialog-close" bindtap='cardHiddenBindTap' type="clear" size='20' color='#666' />
	<view class="noCoupon" bindtap='cardHiddenBindTap'
		style="border-bottom:1rpx solid #c6c7c8;margin:10px 0;text-align:center;">
		<label class="darkRed">选择优惠券</label>
	</view>
	<block wx:key="unique" wx:for="{{storeCard}}" wx:for-item="sCard" wx:for-index="cardIndex">
		<view class="oneCoupon clearfix" bindtap='goToUserCardBindTap' data-index='{{cardIndex}}'
			data-cardId="{{sCard.cardId}}" data-have="{{sCard.have}}">
			<view class="red_bg couponInfo clearF">
				<view class="couponValue">
					<text>{{sCard.discountName}}</text>{{sCard.discountMoney}}
				</view>
				<view class="couponDiscount">
					<view>
						{{sCard.discountDesc}}
					</view>
					<view>{{sCard.cardTime}}</view>
				</view>
				<view class="couponStatus">
					<block wx:if="{{sCard.have}}">
						<text class="undrawStatus">已领取</text>
					</block>
					<block wx:else>
						<text class="undrawStatus">立即领取</text>
					</block>

				</view>
			</view>
		</view>
	</block>
</view>
<template is="share" data="{{...item}}" />
<!-- 优惠券弹层 end -->
<template name="mark3">
    <view class="mark1">
        <view class="mark1_l">
            <view>{{nameCircle}}</view>
        </view>
        <view class="mark1_r">
            {{name}}
        </view>
    </view>
</template>
<template name="mark2">
    <view class="mark2_{{pos}}">
    {{name}}
    </view>
</template>
<template name="mark1">
    <view class="mark_{{pos}}">
    {{name}}
    </view>
</template>