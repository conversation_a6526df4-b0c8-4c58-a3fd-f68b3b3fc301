@import "../CouponTem/CouponTem.wxss";

page {
  background: #fff;
}

.list-block {
  width: 100%;
  height: 90px;
  background: #fff;
}

.list-block view {
  margin-left: 30px;
  height: 44px;
  border-bottom: 1px solid #ececec;
}

.list-block view:last-child {
  border-bottom: none;
}

.list-block view input {
  line-height: 44px;
  height: 44px;
  font-size: 15px;
}

.confirm_btn,
.confirm_btn2 {
  display: inline-block;
  width: 35%;
  margin: 20px auto;
  height: 70rpx;
  line-height: 70rpx;
  border: none;
  margin-top: 60rpx;
  margin-right: 6%;
  margin-left: 6%;
  font-size: 26rpx;
}

/*.confirm_btn, .confirm_btn2 {
  width: 92%;
  margin: 20px auto;
  height: 48px;
  line-height: 48px;
  font-size: 15px;
  border: none;
  margin-top:60rpx;
  margin-left:4%;
}*/

.confirm_btn {
  color: #fff;
  background: #FF7E00;
}

.confirm_btn2 {
  color: #333;
  background: #d9d9d9;
}

.prompt_box {
  width: 92%;
  margin: 0 auto;
  font-size: 13px;
}

.prompt_box label:first-child {
  float: left;
  color: blue;
}

.prompt_box label:last-child {
  float: right;
  color: red;
}

.goods_evaluate {
  padding: 8px;
  text-align: center;
  background: #f4f4f4;
  color: #b2b2b2;
  font-size: 18px;
}

.line {
  width: 90px;
  height: 1px;
  background: #ccc;
  display: inline-block;
  vertical-align: middle;
}

.icon-good-comment {
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px;
}

.no_login {
  width: 92%;
  margin: 20px auto;
  height: 48px;
  line-height: 48px;
  font-size: 15px;
  text-align: center;
  color: #666;
  background: #fff;
}

button {
  border: none;
}

.topWrap {
  text-align: center;
  margin: 15px 0;
  margin-top: 100rpx;
}

.topWrap image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}

.topWrap view {
  font-size: 12px;
  color: #999;
}

.companyLog {
  float: left;
  height: 100%;
  width: 49%;
  border-right: 1px solid #ececec;
  text-align: center;
  color: #b2b2b2;
}

.personLog {
  float: left;
  height: 100%;
  width: 49%;
  text-align: center;
  color: #b2b2b2;
}

.active {
  border-bottom: 2px solid #FF7E00;
  color: #444;
}

.loginType {
  background: #fff;
  font-size: 15px;
  margin-bottom: 20px;
  height: 44px;
  line-height: 44px;
}

.sms_btn {
  width: 30%;
  height: 30px;
  margin-top: 6px;
  line-height: 30px;
  color: #FF7E00;
  font-size: 12px;
  text-align: center;
  margin-right: 15px;
  position: absolute;
  right: 0px;
  top: 0px;
  bottom: 0px;
  z-index: 999;
}

.authWechat {
  padding-top: 0;
  height: 50rpx;
  text-align: left;
  background: #f5f5f5;
  float: left;
  width: 40%;
  color: #3c3c3c;
  position: relative;
}

.authWechat text {
  font-size: 26rpx;
  position: absolute;
  left: 80rpx;
  top: -10rpx;
}

.bl_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.innerWrap {
  z-index: 999;
  height: 300px;
  position: absolute;
  top: 200rpx;
  width: 500rpx;
  left: 125rpx;
  background: #fff;
  border-radius: 10rpx;
  padding-top: 100rpx;
}

.wrap_title {
  text-align: center;
  font-size: 32rpx;
}

.wrap_subtitle {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
}

.wrapImg {
  padding-top: 60rpx;
}

.wrapImg image {
  margin-left: 100rpx;
  width: 300rpx;
}

/*卡券**/

.cardImg {
  z-index: 11;
  width: 100%;
  position: fixed;
}

.coupon_inner {
  width: 100%;
  max-height: 500rpx;
  background: #fc6366;
  padding: 5rpx 0 40rpx;
  overflow-y: auto;
  margin-top: -25rpx;
}

.pop_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.couponWrap {
  padding-bottom: 30px;
  margin: 0 auto;
  width: 90%;
  position: fixed;
  top: 14%;
  overflow-y: auto;
  left: 5%;
  z-index: 9999;
}

.circal_top {
  display: inline-block;
  width: 12px;
  height: 6px;
  position: absolute;
  top: -1px;
  left: calc(25% - 6px);
  background: #fc6366;
  border-radius: 0 0 6px 6px;
  border-top: none;
}

.circal_bottom {
  display: inline-block;
  width: 12px;
  height: 6px;
  position: absolute;
  bottom: -1px;
  left: calc(25% - 6px);
  background: #fc6366;
  border-radius: 6px 6px 0 0;
  border-bottom: none;
}

.couponTitle {
  color: #fff;
  text-align: center;
  height: 60px;
  line-height: 80px;
}

.onecircal {
  /*height: 80px;*/
  margin: 15px 20px;
  background: #fff;
  position: relative;
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.oneCoupon {
  height: 80px;
  float: left;
  border-right: 1rpx dashed #c7c7c7;
  width: 25%;
  line-height: 50px;
  margin: 0 auto;
}

.couponImage {
  padding: 15px 0px 0px 10px;
  display: inline-block;
  width: 80%;
  margin: 0 auto;
  text-align: center;
}

.imageShow {
  width: 50px;
  height: 50px;
}

.couponProduct {
  padding-top: 15px;
  padding-left: 10px;
  display: inline-block;
  vertical-align: top;
  overflow: hidden;
  width: 45%;
  float: left;
}

.productDetail {
  font-size: 13px;
  color: #444;
  height: 24px;
  overflow: hidden;
}

.couponMoney {
  margin-top: 17px;
  font-size: 13px;
  color: #fb6165;
  height: 24px;
  overflow: hidden;
}

.couponGift {
  font-size: 14px;
  color: #666;
}

.couponLimit {
  color: #666;
  font-size: 12px;
}

.lineNum {
  color: #444;
  font-size: 12px;
  text-decoration: line-through;
}

.couponRight {
  float: left;
  margin-top: 30px;
  width: 20%;
  padding: 10rpx 6rpx;
  border-radius: 30rpx;
  color: #fff;
  background: #FF7E00;
  text-align: center;
  font-size: 26rpx;
}

.couponText {
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  background: #fb6165;
  color: #fff;
  margin-left: 12px;
}

.coupon_append {
  color: #999;
  background: none;
  /*color:#fff;*/
}

.couponLine {
  font-size: 12px;
  color: #666;
  text-decoration: line-through;
}

.numDiscount {
  font-size: 26px;
  color: red;
}

.titleDiscount {
  font-size: 12px;
  color: red;
}

/**卡券结束*/

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.container {
  display: flex;
}
