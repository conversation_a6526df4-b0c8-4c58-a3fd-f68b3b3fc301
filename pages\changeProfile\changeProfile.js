var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
const popup = require('../popupTemplate/popupTemplate.js')
const http = require('../../utils/http');
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatarUrl1: "",
    avatarUrl: defaultAvatarUrl,
    loginAccount: "",
    isFromBack: false,
    identity: app.getIdentity(),
    items: [{
      name: 'man',
      value: '男'
    },
    {
      name: 'feman',
      value: '女',
      checked: 'true'
    }
    ],
    pac: ['省', '市', '区'],
    newAccount: "",
    oldAccount: "",
    id: "",
    userName:"",
    wechatId: "",
    headImage: "",
    sex: "",
    birthday: "",
    address: "",
    userBean: [],
    telephone: "",
    boundTelephoneHidden: true,
    smsCode: "", //短信验证码
    second: 60, //倒计时秒数
    secondDesc: "获取短信验证码",
    vipCardTelephone: "",
    getSmsCodeState: 1,
    isSend: true,
    isHaveTelephoneFail: true,
    rewardList: '',
    isFill: false
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail 
    this.setData({
      headImage:avatarUrl,
    })
  },
  radioChange: function (e) {
    this.setData({
      sex: e.detail.value
    })
  },
  bindDateChange: function (e) {
    this.setData({
      birthday: e.detail.value
    })
  },
  bindRegionChange: function (e) {
    this.setData({
      pac: e.detail.value
    })
  },
  userNameInput: function (e) {
    var that = this;
    this.setData({
      userName: e.detail.value
    })
  },
  accountBindInput: function (e) {
    this.setData({
      newAccount: e.detail.value
    })
  },
  wechatIdBindInput: function (e) {
    this.setData({
      wechatId: e.detail.value
    })
  },
  addressBindInput: function (e) {
    this.setData({
      address: e.detail.value
    })
  },
  bindVipCardTelephoneBindInput: function (e) {
    this.setData({
      vipCardTelephone: e.detail.value
    })
  },
  smsCodeBindInput: function (e) {
    this.setData({
      smsCode: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    that.setData({
      defaultLogo: app.getExtStoreImage()
    });
    app.init_getExtMessage().then(res => {
      that.queryPersonalInfo(res.companyId, res.storeId);
      //that.checkRewardIndentity(res.companyId);
    });
  },
  noBoundTelephoneBindTap: function () {
    this.setData({
      boundTelephoneHidden: true
    })
  },
  boundUserTelephoneBindTap: function () {
    this.setData({
      boundTelephoneHidden: false
    })
  },
  returnIndexBindTap: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
   * 点击获取短信验证码
   */
  smsBindTap: function () {
    var that = this;
    var telephone = that.data.vipCardTelephone.replace(/\s+/g, '');
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (telephone.length < 11) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (!that.data.isSend) {
      return;
    }
    wx.showToast({
      title: '获取成功',
      icon: 'success',
      duration: 1000,
      mask: true
    })
    wx.request({
      url: app.projectName + '/applet/querySMSCode',
      data: {
        "type": "5",
        "telephone": telephone,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          that.setData({
            getSmsCodeState: 2
          })
          that.countdown(that);
        } else {
          app.showModal({
            title: '提示',
            content: message == "" ? "系统异常，稍后在试" : message
          });
          return;
        }
      }
    })
  },
  /**
   * 倒计时开始
   */
  countdown: function (that) {
    var second = that.data.second;
    if (second == 0) {
      that.setData({
        secondDesc: "获取短信验证码",
        second: 60,
        isSend: true
      });
      return;
    }
    var time = setTimeout(function () {
      that.setData({
        isSend: false,
        second: second - 1,
        secondDesc: second + "秒后重新获取"
      });
      that.countdown(that);
    }, 1000)
  },
  /**
   * 绑定手机号码
   */
  nowBoundTelephoneBindTap: function () {
    var that = this;
    var telephone = that.data.vipCardTelephone.replace(/\s+/g, '');
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (telephone.length < 11) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var smsCode = that.data.smsCode.replace(/\s+/g, '');
    if (smsCode.length == 0) {
      wx.showToast({
        title: '请输入验证码',
        duration: 1000,
        icon: 'none',
        mask: true
      })
      return false;
    }
    if (that.data.getSmsCodeState == 1) {
      wx.showToast({
        title: '请先获取短信验证码',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "openId": app.getOpenId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "telephone": telephone,
        "smsCode": smsCode
      },
      url: app.projectName + '/newAppletLogin/nowBoundTelephone',
      success: function (res) {
        var returnFlag = res.data.returnFlag;
        if (returnFlag) {
          wx.showToast({
            title: "绑定成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                if (telephone.length > 0) {
                  var userSession = wx.getStorageSync('userSession');
                  userSession.telephone = telephone;
                  userSession.loginAccount = telephone;
                  app.setStorage({
                    key: 'userSession',
                    data: userSession
                  });
                }
                that.setData({
                  telephone: telephone,
                  boundTelephoneHidden: true
                })
              }, 2000);
            }
          })
        } else {
          wx.showToast({
            title: res.data.message,
            duration: 1000,
            icon: 'none',
            mask: true
          })
        }
      }
    })
  },
  /**
   * 提交保存
   */
  savePersonalBindTap: function () {
    var that = this;
    var pacArray = that.data.pac;
    var province = pacArray[0];
    var city = pacArray[1];
    var area = pacArray[2];
    var odbtoken = wx.getStorageSync('odbtoken');
    var logintoken = wx.getStorageSync('loginToken');
    /*var userBean = that.data.userBean;
    var userName = that.data.userName.replace(/\s+/g, '');*/
    var userName = that.data.userName;
    if (userName.length == 0) {
      wx.showToast({
        title: '用户姓名不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var telephone = that.data.telephone;
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号码不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var birthday = that.data.birthday;
    if (birthday.length == 0) {
      wx.showToast({
        title: '出生日期不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "id": that.data.id,
        "userName": userName,
        "wechatId": that.data.wechatId,
        "headImage": that.data.headImage,
        "sex": that.data.sex,
        "birthday": birthday,
        "pac": province + " " + city + " " + area,
        "address": that.data.address,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "odbtoken":odbtoken,
        "loginToken":logintoken
      },
      url: app.projectName + '/applet/updatePersonInfo',
      success: function (res) {
        var returnCode = res.data.returnCode;
        var returnMessage = res.data.returnMessage;
        var isGetSignCard = res.data.isGetSignCard;
        if (returnCode == 0) {
          /*var userSession = wx.getStorageSync('userSession');
          userSession.userName = that.data.userName;
          userSession.telephone = that.data.telephone;
          userSession.loginAccount = that.data.telephone;*/
          /*app.setStorage({
            key: 'userSession',
            data: userSession
          });*/
          
          wx.showToast({
            title: "保存成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function(){
                app.switchTab('/pages/accountManager/accountManager');
              },1000)
              
            }
          })
          /*if (that.data.isFill) {
            that.checkReward()
          }
          if (isGetSignCard) {
          } else {
            wx.showToast({
              title: "保存成功",
              icon: 'success',
              duration: 2000,
              mask: true,
              success: function () {
              }
            })
          }*/
        } else {
          app.showModal({
            content: returnMessage
          });
        }
      }
    })
  },
  checkRewardIndentity: function (companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": companyId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
      },
      url: app.projectName + '/applet/queryUserIsWritePersonInfo',
      success: function (res) {
        var flag = res.data.flag;
        if (!flag) { /*代表未完善*/
          that.setData({
            isFill: true
          })
        }
      }
    })
  },
  //查询是否有奖励
  checkReward: function (eventType) {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'config/loadConfigList',
      showLoading: false,
      data: {
        eventType: 5,
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName: app.getLoginName()
      },
      success: (res) => {
        if (res.length > 0) {
          for (var i = 0; i < res.length; i++) {
            res[i]["checked"] = true;
            if (res[i].rightsType == 4 || res[i].rightsType == 5 || res[i].rightsType == 6) {
              that.toUser(res[i].configId);
            }
          }
          that.setData({
            rewardList: res,
            result: res
          })
          that.reward();
        } else {
          wx.showToast({
            title: "保存成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
            }
          })
        }

      }
    })
  },
  //调用奖励弹窗
  reward: function () {
    var that = this;
    that.setData({
      which: "reward"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
  /**
   * 查询个人资料
   */
  queryPersonalInfo: function (companyId, storeId) {
    var that = this;
    var odbtoken = wx.getStorageSync('odbtoken');
    var logintoken = wx.getStorageSync('loginToken');
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "storeId": storeId,
        "companyId": companyId,
        "odbtoken":odbtoken,
        "loginToken":logintoken
      },
      url: app.projectName + '/wechatAppletLogin/queryPersonalInfo',
      success: function (res) {
        var userBean = res.data.userBean;
        if (userBean != null) {
          var pacStr = userBean.pac;
          var pacArray = [];
          if (pacStr != null && pacStr != "") {
            pacArray = pacStr.split(" ");
          } else {
            pacArray = that.data.pac;
          }
          if (pacStr == "省 市 区" || pacStr == "") {
            that.getUserLocation();
          }
          var isUpdateBirthday = false;
          if (userBean.birthday == null || userBean.birthday.length == 0) {
            isUpdateBirthday = true;
          }
          that.setData({
            isUpdateBirthday: isUpdateBirthday,
            userBean: userBean,
            oldAccount: userBean.account,
            id: userBean.id,
            userName: userBean.userName,
            wechatId: userBean.wechatId,
            headImage: userBean.headImage?userBean.headImage:that.data.avatarUrl,
            sex: userBean.sex,
            birthday: userBean.birthday,
            pac: pacArray,
            address: userBean.address,
            telephone: userBean.telephone
          });
        }
      }
    })
  },
  getUserLocation: function () {
    let vm = this;
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取当前地理位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })
    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {

    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                 }
    //               }
    //             })
    //           }
    //         },
    //         fail: function () { }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       vm.getLocation();
    //     } else {
    //       vm.getLocation();
    //     }
    //   }
    // })
  },
  getLocation: function () {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // wx.getLocation({
    //   type: 'gcj02',
    //   altitude: true,
    //   complete: function (res) {
    //     if (res.errMsg == "getLocation:ok") {
    //       var latitude = res.latitude;
    //       var longitude = res.longitude;
    //       qqmapsdk.reverseGeocoder({
    //         location: {
    //           latitude: latitude,
    //           longitude: longitude
    //         },
    //         success: function (res) { //成功后的回调
    //           var res = res.result;
    //           that.setData({
    //             pac: [res.address_component.province, res.address_component.city, res.address_component.district]
    //           })
    //         },
    //         fail: function (error) {
    //           console.error(error);
    //         },
    //         complete: function (res) {
    //         }
    //       })
    //     }
    //   }
    // })
  },
  /*swechatAuthionTelephone: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.checkSession({
        success() {
          //session_key 未过期，并且在本生命周期一直有效
          var session_Key = wx.getStorageSync("session_Key");
          that.getUserTelephone(iv, encryptedData, session_Key);
        },
        fail() {
          // session_key 已经失效，需要重新执行登录流程
          wx.login({
            success: function (res) {
              if (res.code) {
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded'
                  },
                  method: "POST",
                  url: app.projectName + '/newAppletLogin/getUserOpenId',
                  data: {
                    "companyId": app.getExtCompanyId(),
                    "code": res.code
                  },
                  success: function (res) {
                    var openid = res.data.openid;
                    var session_Key = res.data.session_Key;

                    if (openid == null && session_Key == null) {
                      that.setData({
                        boundTelephoneHidden: false
                      })
                    } else {
                      that.getUserTelephone(iv, encryptedData, session_Key);
                      wx.removeStorageSync("openId");
                      app.setStorage({
                        key: 'openId',
                        data: openid
                      });
                      wx.removeStorageSync("session_Key");
                      app.setStorage({
                        key: 'session_Key',
                        data: session_Key
                      });
                    }
                  }
                })
              }
            }
          })
        }
      })
    } else {
      app.showModal({
        title: '提示',
        content: "获取失败"
      });
    }
  },*/
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newAppletLogin/getUserTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "openId": app.getOpenId(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var telephone = res.data.telephone;
        if (telephone.length > 0) {
          var userSession = wx.getStorageSync('userSession');
          userSession.telephone = telephone;
          userSession.loginAccount = telephone;
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
        } else {
          app.showModal({
            title: '提示',
            content: "获取失败，请您手动绑定，或者退出重试"
          });
          that.setData({
            boundTelephoneHidden: false
          })
        }
        that.setData({
          telephone: telephone
        })
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "获取失败，请您手动绑定，或者退出重试"
        });
        that.setData({
          boundTelephoneHidden: false
        })
      }
    })
  },
  /*给用户发放优惠券等*/
  toUser: function (configId) {
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.errorcode == '1000') {
          console.log("获取成功！")
        }
      }
    })
  },
  gocjBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/turntableActivity/turntableActivity?gameId=' + e.currentTarget.dataset.gameid);
  },
  goSignBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/sign/sign?configId=' + configId);
  },
  goWordBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/collect/collect?configId=' + configId);
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})