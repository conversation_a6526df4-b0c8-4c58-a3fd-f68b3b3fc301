const app = getApp();
const http = require('../../utils/http');
const ui = require('../../utils/ui');
const popup = require('../popupTemplate/popupTemplate.js');
const { utf16toEntities, entitiesToUtf16 } = require('../../utils/emojis')
var w = wx.getSystemInfoSync().windowWidth;
var h = wx.getSystemInfoSync().windowHeight;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //是否显示指示点
    indicatorDots: false,
    //自动播放
    autoplay: true,
    interval: 5000,
    duration: 1000,
    //是否可以连续滚动
    circular: true,
    find_agree: app.imageUrl + 'find_agree.png',
    find_comment: app.imageUrl + 'find_comment.png',
    find_emoji: app.imageUrl + 'find_emoji.png',
    find_jianpan: app.imageUrl + 'find_jianpan.png',
    find_notagree: app.imageUrl + 'find_notagree.png',
    find_share: app.imageUrl + 'find_share.png',
    comnotagree: app.imageUrl + 'find_comnotagree.png',
    comagree: app.imageUrl + 'find_comagree.png',
    find_arrow: app.imageUrl + 'find_arrow.png',
    msg: '',
    imgheights: [],
    imgwidth: 750,
    fontSize: h / w > 1.23 ? 12 : 24,
    borderRadius: h / w > 1.23 ? 22 : 50,
    strokeWidth: h / w > 1.23 ? 44 : 100,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var findId = options.findId;
    var time = options.time;
    http.post({
      urlName: 'find',
      url: 'find/findFindInfo',
      showLoading: false,
      data: {
        findId: findId,
        userId: app.getUserId()
      },
      success: (res) => {
        var allVoteNum = 0;
        if (res.voteList) {
          res.voteList.forEach(vote => {
            allVoteNum += vote.voteNum
            if (vote.userList == "1") {
              vote.activeColor = "skyblue"
            } else if (vote.userList == "0") {
              vote.activeColor = "#CCCCCC"
            }
          });
          res.allVoteNum = allVoteNum
        }
        that.setData({
          result: res,
          time: time
        })
        that.getCommentPage(1, -1, 0)
      }
    })


  },
  /**
   * 
   * 查询评论分页
   */
  getCommentPage: function (startPage, index, state) {
    var that = this;
    var result = that.data.result;
    var userId = app.getUserId();
    var data;
    if (userId == undefined) {
      data = {
        infoId: result.findInfo.id,
        startPage: startPage
      }
    } else if (userId != undefined) {
      data = {
        infoId: result.findInfo.id,
        userId: app.getUserId(),
        startPage: startPage
      }
    }

    http.post({
      urlName: 'find',
      url: 'find/queryInfoCommentPage',
      showLoading: false,
      data: data,
      success: (res) => {
        if (startPage == 1) {
          result.commentList = res.resultList;
        } else {
          if (index == -1) {
            result.commentList = result.commentList.concat(res.resultList);
          } else {
            if (state == 1) {
              result.commentList[index].isDelete = 1
            } else {
              result.commentList[index] = res.resultList[index % res.pageSize]
            }

          }
        }
        result.commentList.forEach(comment => {
          comment.commentText = entitiesToUtf16(comment.commentText);
          comment.userName = entitiesToUtf16(comment.userName);
          comment.replyUserName = entitiesToUtf16(comment.replyUserName);
        });
        that.setData({
          result: result,
          startPage: startPage,
          nextPage: res.nextPage,
          lastPage: res.lastPage,
          pageSize: res.pageSize
        })
      },
      fail: () => {
        //调用加载更多组件加载失败方法
        this.selectComponent("#loadMoreView").loadMoreFail();
      }
    })
  },
  loadMoreListener: function (e) {
    this.getCommentPage(this.data.nextPage, -1, 0);
  },
  clickLoadMore: function (e) {
    this.getCommentPage(this.data.nextPage, -1, 0);
  },
  /**
* 跳转到商品详情(点击image)
*/
  imageClick: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
    * 图片预览
    */
  previewImage: function (e) {
    var src = e.currentTarget.dataset.src;
    var list = e.currentTarget.dataset.list;
    var imgList = [];
    for (var i = 0; i < list.length; i++) {
      imgList.push(list[i].imagesUrl)
    }
    //图片预览
    wx.previewImage({
      current: src, // 当前显示图片的http链接
      urls: imgList // 需要预览的图片http链接列表
    })
  },
  imageLoad: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight
    var imgheights = this.data.imgheights
    //把每一张图片的高度记录到数组里  
    imgheights.push(imgheight)
    this.setData({
      imgheights: imgheights,
      current: 0
    })
  },
  /**
  * 用户投票
  */
  setVote: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var that = this;
    var voteId = e.currentTarget.dataset.id;
    http.post({
      urlName: 'find',
      url: 'find/setVote',
      showLoading: false,
      data: {
        voteId: voteId,
        userId: app.getUserId()
      },
      success: (res) => {
        var result = that.data.result;
        result.voteList = res;
        result.isVote = 1;
        var allVoteNum = 0;
        if (result.voteList) {
          result.voteList.forEach(vote => {
            allVoteNum += vote.voteNum
            if (vote.userList == "1") {
              vote.activeColor = "skyblue"
            } else if (vote.userList == "0") {
              vote.activeColor = "#F2F2F2"
            }
          });
        }
        result.allVoteNum = allVoteNum
        that.setData({
          result: result
        })
      }
    })
  },
  setVote1: function () {
    ui.showToast("您已经投过票啦")
  },
  /**
   * 点赞或取消
   */
  likeOrCancel: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var that = this;
    var isRagree = e.currentTarget.dataset.isragree;//是否点赞1：点赞，0未点赞
    var id = e.currentTarget.dataset.id;//infoid
    http.get({
      urlName: 'find',
      url: 'find/setUseragree',
      showLoading: false,
      data: {
        infoId: id,
        userId: app.getUserId(),
        isRagree: isRagree,
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName(),
        userHead: app.getHeadImage()
      },
      success: (res) => {
        var result = that.data.result;
        if (isRagree == "0") {
          result.isAgree = 0;
          result.findInfo.agreeNum -= 1
        } else if (isRagree == "1") {
          result.isAgree = 1
          result.findInfo.agreeNum += 1
        }
        that.setData({
          result: result
        })
      }
    })
  },
  bindTouchStart: function (e) {
    this.startTime = e.timeStamp;
  },
  bindTouchEnd: function (e) {
    this.endTime = e.timeStamp;
  },
  /**
 * 调用评论弹窗
 */
  toComment: function (e) {
    if (this.endTime - this.startTime < 350) {
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
        return;
      }
      var that = this;
      var index = e.currentTarget.dataset.index == undefined ? 0 : e.currentTarget.dataset.index;//当前点赞内容的index
      var comment = e.currentTarget.dataset.comment;
      var findinfoId = e.currentTarget.dataset.findinfoid == undefined ? comment.findinfoId : e.currentTarget.dataset.findinfoid;
      that.setData({
        findinfoId: findinfoId,
        comment: comment,
        index: index,
        msg: '',
        item: {
          msg: ''
        }
      })
      that.comment();
    }

  },
  /**
   * 调用删除或举报评论弹窗
   */
  toReportOrdel: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var that = this;
    var comment = e.currentTarget.dataset.comment;
    var index = e.currentTarget.dataset.index;//当前要删除评论的内容index
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    if (comment.userId == app.getUserId()) {
      that.setData({
        reportOrdelText: "删除",
        commentId: comment.id,
        currentPage: currentPage,
        index: index,
        findinfoId: comment.findinfoId
      })
    } else {
      that.setData({
        reportOrdelText: "举报",
        commentId: comment.id,
      })
    }
    var that = this;
    that.reportOrdel();
  },
  /**
   * 删除或举报评论
   */
  reportOrdelComment: function (e) {
    var that = this;
    var reportordeltext = e.currentTarget.dataset.reportordeltext;
    var commentId = e.currentTarget.dataset.commentid;
    var index = e.currentTarget.dataset.index;//当前要删除评论的内容index
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    if (reportordeltext == "删除") {
      http.post({
        urlName: 'find',
        url: 'find/delMyComment',
        showLoading: false,
        data: {
          commentId: commentId
        },
        success: (res) => {
          var result = that.data.result
          result.findInfo.commentNum -= 1
          that.setData({
            result: result,
            findinfoId: e.currentTarget.dataset.findinfoid
          })
          that.hiddenFloatView();
          that.getCommentPage(currentPage, index, 1);
        }
      })
    } else if (reportordeltext == "举报") {
      http.post({
        urlName: 'find',
        url: 'find/setCommentReport',
        showLoading: false,
        data: {
          commentId: commentId
        },
        success: (res) => {
          that.hiddenFloatView();
          ui.showToast("举报成功");
        }
      })
    }
  },
  //调用评论模板弹窗
  comment: function () {
    var that = this;
    that.setData({
      state1: true,
      state2: false,
      which: "find"
    })
    popup.animationEvents(that, 0, true);
  },
  //调用删除或举报弹窗
  reportOrdel: function () {
    var that = this;
    that.setData({
      state1: false,
      state2: true,
      which: "find"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
  //获取输入的评论
  inputComment: function (e) {
    popup.inputComment(this, e)
  },
  //emoji表情评论
  emojiComment() {
    popup.changeHeight(this)
  },
  clickEmoji(e) {
    popup.clickEmoji(this, e)
  },
  //发表评论
  releaseEvent(e) {
    var that = this
    var index = e.currentTarget.dataset.index;//当前要删除评论的内容index
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    if (that.data.msg == undefined || that.data.msg == '') {
      ui.showToast("请输入评论内容")
      return
    }

    app.msg_sec_check(that.data.msg).then(res => {
      if (res) {
        that.setInfoComment(currentPage, index);
      } else {
        app.showModal({
          content: "评论内容违规，请重新修改"
        });
      }
    })
  },
  setInfoComment: function (currentPage, index) {
    var that = this;
    var jsonData = {};
    var comment = that.data.comment
    jsonData.merchantId = app.getExtCompanyId();
    jsonData.findinfoId = that.data.findinfoId;
    jsonData.userId = app.getUserId();
    jsonData.userHead = app.getHeadImage();
    jsonData.userName = app.getLoginName() == "" ? "匿名" : app.getLoginName();
    jsonData.commentText = utf16toEntities(that.data.msg);
    if (comment != undefined) {
      jsonData.replyId = comment.userId;
      jsonData.replyUserHead = comment.userHead;
      jsonData.replyUserName = comment.userName;
    }
    http.post({
      urlName: 'find',
      url: 'find/setInfoComment',
      header: "application/json",
      data: JSON.stringify(jsonData),
      success: (res) => {
        var result = that.data.result;
        result.findInfo.commentNum += 1;
        that.setData({
          result: result
        })
        that.getCommentPage(currentPage, index, 0)
        that.hiddenFloatView()
        ui.showToast("评论成功")
      }
    })
  },
  commentLike: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var that = this;
    var index = e.currentTarget.dataset.index;
    var isRagree = e.currentTarget.dataset.isragree;
    var result = that.data.result;
    http.post({
      urlName: 'find',
      showLoading: false,
      url: 'find/setCommentRagree',
      data: {
        commentId: e.currentTarget.dataset.commentid,
        userId: app.getUserId(),
        isRagree: isRagree
      },
      success: (res) => {
        if (isRagree == 1) {
          result.commentList[index].userList = "1";
          result.commentList[index].agreeNum += 1;
        } else if (isRagree == 0) {
          result.commentList[index].userList = "";
          result.commentList[index].agreeNum -= 1;
        }
        that.setData({
          result: result
        })
      }
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (!that.data.lastPage) {
      that.getCommentPage(that.data.nextPage, -1, 0)
    } else {
      that.selectComponent("#loadMoreView").noMore()
    }

  },
  /**
  * 用户点击右上角分享
  */
  onShareAppMessage: function (e) {
    var findId = e.target.dataset.findid;
    http.post({
      urlName: 'find',
      url: 'find/setforward',
      showLoading: false,
      data: {
        infoId: findId
      },
      success: (res) => {
      }
    })
    return {
      title: '发现分享',
      path: '/pages/findInfo/findInfo?findId=' + findId + "&time=" + e.target.dataset.time
    };
  },
})