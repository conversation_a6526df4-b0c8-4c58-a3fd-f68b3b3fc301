<view class="billWrap">
	<view class="timePicker">
		<view class="timeBox">
			<picker mode="date" value="{{date1}}" start="" end="" bindchange="bindDateChange1">
				<view class="picker">
					{{date1}}
				</view>
			</picker>
		</view>
		<view style="padding-top: 20rpx;">~</view>
		<view class="timeBox">
			<picker mode="date" value="{{date2}}" start="" end="" bindchange="bindDateChange2">
				<view class="picker">
					{{date2}}
				</view>
			</picker>
		</view>
		<view class="timeSearchBtn" bindtap="timeSearchFun">搜索</view>
	</view>
	<block wx:key="unique" wx:for="{{consumptionShowList}}" wx:for-item="consum">
		<view class="oneBill clearfix">
			<view class="desc_left">
				<view>{{consum.operType}}</view>
				<text class="darkBlack">{{consum.operTime}}</text>
			</view>
			<view class="darkRed bill_right">
				￥{{consum.operMoney}}
			</view>
		</view>
	</block>
</view>