var app = getApp()

Page({
  data: {
    return_apply: app.imageUrl + "accountManager/return_apply.png",
    returnGoodsPic: [], //退货图片
    returnGoodsDesc: "", //退货描述信息
    returnReason: "", //退货原因
    goodsList: [],
    userName: app.getLoginName(),
    telephone: app.getLoginAccount()
  },
  onLoad: function(options) {
    var goodsList = JSON.parse(options.goodsList);
    var returnReason = options.returnReason;
    var returnGoodsDesc = options.returnGoodsDesc;
    var returnGoodsPic = options.returnGoodsPic;
    this.setData({
      goodsList: goodsList,
      returnReason: returnReason,
      returnGoodsDesc: returnGoodsDesc,
      returnGoodsPic: returnGoodsPic
    });
  },
  /**
   * 提交申请退货
   */
  applayReuturnGoodsBindTap: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/returnGoods/applayReturnGoods',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "goodsList": JSON.stringify(that.data.goodsList),
        "returnReason": that.data.returnReason,
        "returnGoodsDesc": that.data.returnGoodsDesc,
        "returnGoodsPic": that.data.returnGoodsPic,
        "storeId": app.storeId,
        "companyId": app.companyId
      },
      success: function(res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "申请退款成功",
            icon: 'success',
            duration: 1500,
            mask: true,
            success: function() {
              setTimeout(function() {
                app.turnToPage("pages/aftersaleList/aftersaleList");
              }, 1500);
            }
          })
        } else {
          wx.showToast({
            title: '申请退款失败',
            duration: 1500
          })
        }
      }
    })
  }
})