// pages/onePromotion/onePromotion.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    hiddenBox: true,
    soldOut: app.imageUrl + 'soldOut.png',
    vipCart: app.imageUrl + "vipCart.png",
    promotionName: '',
    promotionPic: '',
    promotionStartDate: '',
    promotionEndDate: '',
    commodityBaseList: [],
    shopCartNum: '0',
    clock: '',
    count: '0',
    commodityNum: 1,
    id: '',
    changeNum: '0',
    change_mark: '0',
    promotionType: '',
    promotionStatus: '',
    promotionCommodityType: 1
  },
  changeNumBind: function (e) {
    var that = this;
    var count = e.currentTarget.dataset.count;
    var commodityId = e.currentTarget.dataset.commodityid;
    that.setData({
      hiddenBox: false,
      commodityNum: count,
      id: commodityId,
    })
  },
  cancelBoxBindTap: function () {
    var that = this;
    this.setData({
      hiddenBox: true,
    })
  },

  inputBuyCount: function (e) {
    var that = this;
    var count = e.detail.value;
    var change_Num = 0;
    var mark = 0;
    if (count - that.data.commodityNum > 0) {
      change_Num = count - that.data.commodityNum;
    } else if (count - that.data.commodityNum < 0) {
      change_Num = that.data.commodityNum - count;
      mark = 1;
    } else {
      change_Num = 0;
    }
    that.setData({
      changeNum: change_Num,
      change_mark: mark,
    });
  },
  /**
   * 跳转商品详情
   */
  imageClick: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  addShoppingCartBindTap: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wholesaleShopCart/changeRetailCartNum',
      data: {
        "type": that.data.change_mark == '0' ? "plus" : "minus", //1:增加 2：减少
        "id": that.data.id,
        "num": that.data.changeNum,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getCompanyId(),
        "storeId": app.getStoreId(),
        "companyName": app.getCompanyName(),
        "storeName": app.getStoreName(),
        "a_supplierId": app.getExtStoreId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          if (res.data.shopCartNum > 0) {
            wx.setStorageSync("shopCartNum", res.data.shopCartNum.toString())
            that.setData({
              shopCartNum: res.data.shopCartNum.toString()
            })
          } else {
            wx.setStorageSync("shopCartNum", "0")
          }
          var new_commodityBaseList = that.data.commodityBaseList;
          for (var i = 0; i < new_commodityBaseList.length; i++) {
            if (new_commodityBaseList[i].commodityId == that.data.id) {
              if (that.data.change_mark == '0') {
                new_commodityBaseList[i].goodsNumber = parseInt(that.data.commodityNum) + parseInt(that.data.changeNum);
              } else {
                new_commodityBaseList[i].goodsNumber = parseInt(that.data.commodityNum) - parseInt(that.data.changeNum);
              }
              break;
            }
          }
          that.setData({
            commodityBaseList: new_commodityBaseList,
            hiddenBox: true
          });
        } else {
          wx.showToast({
            title: '增加商品失败',
            duration: 1500
          })
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var id = options.id;
    this.dataInitial(id);
  },
  dataInitial: function (promotionId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/activity/queryOnePromotionCommodity',
      data: {
        "promotionId": promotionId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "operateUserName": app.getLoginName(),
      },
      success: function (res) {
        var onePromiotion = res.data.pageResult;
        var num = res.data.shopCartNum;
        var promotionStatus = res.data.pageResult.promotionStatus;
        that.setData({
          promotionName: onePromiotion.promotionName,
          promotionPic: onePromiotion.promotionPic,
          promotionStartDate: onePromiotion.promotionStartDate,
          promotionEndDate: onePromiotion.promotionEndDate,
          commodityBaseList: onePromiotion.commodityList,
          shopCartNum: num.toString(),
          promotionStatus: promotionStatus,
          promotionType: onePromiotion.promotionType,
          promotionCommodityType: onePromiotion.promotionCommodityType
        });
        if (promotionStatus == 1) {
          that.count_down(onePromiotion.promotionStartDate);
        }
        else if (promotionStatus == 2) {
          that.count_down(onePromiotion.promotionEndDate);
        }
      }
    })
  },
  /**
   * 购物车中增加商品
   */
  addGoodsToShopCart: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var commodityId = e.currentTarget.dataset.commodityid;
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "a_id": commodityId,
        "a_num": 1,
        "skuId": "",
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getCompanyId(),
        "storeId": app.getStoreId(),
        "companyName": app.getCompanyName(),
        "storeName": app.getStoreName(),
        "a_supplierId": app.getExtStoreId(),
        "recommendUserId": app.recommendData.recommendUserId
      },
      url: app.projectName + '/wholesaleShopCart/addRetailShppingCart',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          if (res.data.shopCartNum > 0) {
            wx.setStorageSync("shopCartNum", res.data.shopCartNum.toString())
            that.setData({
              shopCartNum: res.data.shopCartNum.toString()
            })
          } else {
            wx.setStorageSync("shopCartNum", "0")
          }
          var commodityBaseList = that.data.commodityBaseList;
          for (var i = 0; i < commodityBaseList.length; i++) {
            if (commodityBaseList[i].commodityId == commodityId) {
              commodityBaseList[i].goodsNumber++;
              break;
            }
          }
          that.setData({
            commodityBaseList: commodityBaseList
          });
        } else {
          wx.showToast({
            title: '增加商品失败',
            duration: 1500
          })
        }
      }
    })
  },
  /**
   * 购物车中减少商品
   */
  reduceGoodsToShopCart: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var that = this;
    var commodityId = e.currentTarget.dataset.commodityid;
    var commodityBaseList = that.data.commodityBaseList;
    for (var i = 0; i < commodityBaseList.length; i++) {
      if (commodityBaseList[i].commodityId == commodityId) {
        var goodsNumber = commodityBaseList[i].goodsNumber;
        if (goodsNumber <= 0) {
          return;
        }
      }
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "id": commodityId,
        "type": "minus", //1:增加 2：减少
        "num": 1,
        "skuId": "0",
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getCompanyId(),
        "storeId": app.getStoreId(),
        "companyName": app.getCompanyName(),
        "operateUserName": app.getLoginName(),
        "storeName": app.getStoreName(),
        "localstoreId": app.getExtStoreId()
      },
      url: app.projectName + '/wholesaleShopCart/changeRetailCartNum',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          if (res.data.shopCartNum > 0) {
            wx.setStorageSync("shopCartNum", res.data.shopCartNum.toString())
            that.setData({
              shopCartNum: res.data.shopCartNum.toString()
            })
          } else {
            wx.setStorageSync("shopCartNum", "0")
            that.setData({
              shopCartNum: "0"
            })
          }
          var commodityBaseList = that.data.commodityBaseList;
          for (var i = 0; i < commodityBaseList.length; i++) {
            if (commodityBaseList[i].commodityId == commodityId) {
              commodityBaseList[i].goodsNumber--;
              break;
            }
          }
          that.setData({
            commodityBaseList: commodityBaseList
          });
        } else {
          wx.showToast({
            title: '减少商品失败',
            duration: 1500
          })
        }
      }
    })
  },
  count_down: function (promotionEndDate) {
    var that = this;
    promotionEndDate = promotionEndDate.replace(/-/g, '/');
    var promotionEndDate = (new Date(promotionEndDate)).getTime();
    this.data.intervarID = setInterval(function () {
      var leftTime = promotionEndDate - Date.parse(new Date()); //计算剩余的毫秒数
      var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
      var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
      var minutes = parseInt(leftTime / 1000 / 60 % 60, 10); //计算剩余的分钟
      var seconds = parseInt(leftTime / 1000 % 60, 10); //计算剩余的秒数
      that.setData({
        clock: days + "天:" + hours + "时:" + minutes + "分:" + seconds + "秒",
      })
      if (days == '00' && hours == '00' && minutes == '00' && seconds == '00') {
        clearInterval(that.data.intervarID);
      }
    }, 1000)
  },
  getShoppingCartData: function () {
    wx.switchTab({
      url: "/pages/shopCart/shopCart"
    });
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    var that = this;
    return {
      title: that.data.goodsDetail.commodityName,
      path: '/pages/onePromotion/onePromotion?id=' + that.data.id,
      success: function (res) {
        that.setData({
          shareBlackBgHidden: true,
          shareShowHidden: true
        })
      },
      fail: function (res) {
        that.setData({
          shareBlackBgHidden: true,
          shareShowHidden: true
        })
      }
    }
  }
})