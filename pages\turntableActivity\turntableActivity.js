const app = getApp();
var WxParse = require('../../components/wxParse/wxParse.js');
Page({
  data: {
    rouletteData: {
      speed: 10,/**转盘速度 */
      fontColor: '#FF682D',/**文字颜色 */
      font: '12px Arial',
      bgOut: '#3246c5',/**外层 */
      bgMiddle: '#1a1b65',/**中间层 */
      bgInner: ['#ddddda', '#ffffff', '#ddddda', '#ffffff', '#ddddda', '#ffffff', '#ddddda', '#ffffff', '#ffffff', '#ffffff'],
      speedDot: 1000,/**点切换速度 */
      dotColor: ['#ffffff', '#feb100'],
      dotColor_1: ['#ffffff', '#feb100'],
      dotColor_2: ['#feb100', '#ffffff'],
      angel: 0 /**选择角度 */
    },
    award: [],   //游戏的奖品
    gameExplain: '无',  /**规则 */
    gameId: '',
    lotteryNum: 1,    //剩余次数
    turntable_bg: app.imageUrl + 'turntable_bg2.png',
    turntable_bj: app.imageUrl + 'turntable_bj.png',
    turntable_yhj: app.imageUrl + 'turntable_yhj.png',
    turntable_jb: app.imageUrl + 'turntable_jb.png',
    turntable_hb: app.imageUrl + 'turntable_hb.png',
    turntable_jifen: app.imageUrl + 'turntable_jifen.png',
    noPrizeImg: app.imageUrl + 'noPrizeImg.png',
    ruleLayer: app.imageUrl + 'ruleLayer.png',
    ruleHidden: false,   //规则弹窗
    index: 2,
    prizePopupHidden: false,  //中奖弹窗开关
    noPopupHidden: false,  //未中奖弹窗开关
    isselectData: {},  //中奖的数据
    isselectIndex: '',
    prizeInfoList: [], //已经中奖的用户信息
    sceneType: '',
    turntableSwitch: false,
    vipCardNo: "",
    isFirst: true,
    returnFlag: false,
    cardFlag: false,
    isShow:false,
    prizeShow:true,
    userLogo:app.getExtStoreImage(),
    tipShow:true,
  },
  getMyVipCardInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          var cardNo = cardList[0].cardId;
          that.setData({
            cardNo:cardNo
          })
          wx.showModal({
            title: '积分兑换',
            content: '是否积分兑换抽奖次数',
            success: function (res) {
              if (res.cancel) {
                wx.hideToast()
                } else if (res.confirm) {
                  that.getlotteryNum();
                }
              },
          })
        } else {
            that.goOpenCard();
        }
      }
    })
  },
  goOpenCard: function () {
    var that = this;
    wx.showModal({
      title: '开卡',
      content: '您暂无会员卡，是否前往开卡？',
      success: function (res) {
        if (res.cancel) {
          wx.hideToast()
          } else if (res.confirm) {
            app.navigateToPage('/pages/vipCard/vipCard');
          }
        },
    })
  },
  drawTimesClick:function(e){
    var that = this;
    var type = e.currentTarget.dataset.type;
 
    if(type == 5){
      wx.switchTab({
        url: "/pages/goods_classify/goods_classify"
      });
    }
    else if(type == 6){/*这里用户确认要积分兑换抽奖次数*/

      /**/
      var cumulativenum = e.currentTarget.dataset.cumulativenum;
      var missionId = e.currentTarget.dataset.missionid;
      that.setData({
        cumulativeNumStr:cumulativenum,
        missionId:missionId
      })
      /*查询是否有会员卡*/
      that.getMyVipCardInfo();
      var data = {};
      data["tm"] = "/luckdraw/retailClient/startMission";
      data["companyId"] = app.getExtCompanyId();
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      data["promotionId"] = that.data.promotionId;
      data["missionId"] = that.data.missionId;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/stickness',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
              var resData = JSON.parse(res.data.data);
              var missionParam=resData.missionQueryParam;
              var validArray  = missionParam.split("&");
							var ictStr = validArray[0].substring(4,validArray[0].length);
              var vtStr = validArray[1].substring(3,validArray[1].length);
              that.setData({
                interactionStr:ictStr,
                validateStr:vtStr

              })
          } else {
            wx.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000,
              mask: true,
            })
          }
        },
        fail: function () {
          wx.hideLoading();
        }
      }) /*end*/
      
    }
  },
  getlotteryNum:function(){
    var that = this;
    var data = {};
    data["tm"] = "/luckdraw/retailClient/executeMission";
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["promotionId"] = that.data.promotionId;
    data["interactionStr"] = that.data.interactionStr  ;
    data["validateStr"] = that.data.validateStr ;
    data["cumulativeNum"] = that.data.cumulativeNumStr ;
    data["cardNo"] = that.data.cardNo;

    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/simpleRouter/stickness',
      data: data,
      success: function (res) {
        wx.hideLoading();
        if (res.data.code == 1) {
            wx.showToast({
              title: "兑换成功",
              icon: 'none',
              duration: 2000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  that.getPromotionDetail();
                }, 1000);
              }
            })
            
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000,
            mask: true,
          })
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    }) /*end*/
  },
  onShow:function(options){
    console.log("second");
    console.log(options);
  },
  onLoad: function (options) {
    console.log(options);
    console.log("first");
    var that = this;
    var data = {};
    if(options.id != "undefined" && options.id != null && options.id != ""){
      that.setData({
        promotionId:options.id
      })
      that.getPromotionDetail();
      return;
    }
    const scene = decodeURIComponent(options.scene);
    if(app.globalData.scene != "undefined" && app.globalData.scene != null && app.globalData.scene != ""){
      if (scene != "undefined" && scene != null && scene != "") {

      }
      else{
        app.init_getExtMessage().then(res => {
          var isLogin = app.isLogin();
         
          if (!isLogin) {
            app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
          } else {
            that.goScanEvent(app.globalData.scene);
          }
        });
      }
    }
    if (scene != "undefined" && scene != null && scene != "") {
      that.setData({
        scene:scene
      });
      app.globalData.scene = scene;
      app.init_getExtMessage().then(res => {
        var isLogin = app.isLogin();
        console.log("isLogin的值为"+isLogin);
        console.log("这是我在转盘页面")
        if (!isLogin) {
          app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
        } else {
          that.goScanEvent(scene);
        }
      });
    }
    else{
      app.init_getExtMessage().then(res => {
        var isLogin = app.isLogin();
        if (!isLogin) {
          app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
        } else {
          that.getPromotionDetail();
        }
      });
    }
    
  },
  closeTipBindTap:function(){
      var that = this;
      that.setData({
        tipShow:true,
      })

  },
  goScanEvent:function(scene){
    var that = this;
    var data = {};
    data["tm"] = "/luckdraw/retailClient/scanInteractive";
    data["scene"] = scene;
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
        /*获取当前任务信息*/
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/simpleRouter/stickness',
          data: data,
          success: function (res) {
            if(res.data.code == 1){
              var promotionId = res.data.data;
              that.setData({
                promotionId:promotionId
              });
              that.getPromotionDetail();
            }
            else{
              if(res.data.msg == '请求目标已失效（S00006） '){
                wx.showToast({
                  title: "二维码已失效，请重新扫码",
                  icon: 'none',
                  duration: 2000,
                  mask: true,
                  success: function () {
                    setTimeout(function () {
                      wx.switchTab({
                        url: "/pages/index/index"
                      });
                    }, 3000);
                  }
                })
              }
              else if(res.data.msg.indexOf('身份认证失败')>-1){
                wx.showToast({
                  title: "请退出小程序重新登录",
                  icon: 'none',
                  duration: 2000,
                  mask: true,
                  success: function () {
                    setTimeout(function () {
                      wx.switchTab({
                        url: "/pages/index/index"
                      });
                    }, 3000);
                  }
                })
                wx.removeStorageSync("odbtoken");
                wx.removeStorageSync("loginToken");
                wx.removeStorageSync("userSession");
                wx.removeStorageSync("isLoginSession");
                wx.removeStorageSync("");
                app.init_userOpenId();
              }
              else{
                wx.showToast({
                  title: res.data.msg,
                  icon: 'none',
                  duration: 2000,
                  mask: true,
                  success: function () {
                    setTimeout(function () {
                      wx.switchTab({
                        url: "/pages/index/index"
                      });
                    }, 3000);
                  }
                })
              }
            }
          },
          fail: function () {
            wx.hideLoading();
          }
        })
  },
  getPromotionDetail:function(){
    var that = this;
    that.getRecordBindTap();
    var data = {};
    data["promotionId"] = that.data.promotionId;
    data["tm"] = "/luckdraw/retailClient/detail";
    data["storeId"] = app.getExtStoreId();
    data["companyId"] = app.getExtCompanyId();
    data["userId"] = app.getUserId(),
    data["loginId"] = app.getLoginId(),
    data["userRole"] = app.getUserRole(),
    data["odbtoken"] = app.getodbtoken(),
    data["loginToken"] = app.getloginToken()
    /*获取当前抽奖活动的奖品*/
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/simpleRouter/stickness',
      data: data,
      success: function (res) {
        wx.hideLoading();
        if (res.data.code == 1) {
          var resData = JSON.parse(res.data.data);
          var prizeList = resData.prizeInfoList;
          if(resData.activityDisplayStyle == 2){
            app.navigateToPage("/pages/pokerActivity/pokerActivity?id="+that.data.promotionId);
          }
          that.setData({
            award:prizeList,
            activityName:resData.activityName,
            activityTimeContinuity:resData.activityTimeContinuity,
            activityStartTime:resData.activityStartTime,
            activityEndTime:resData.activityEndTime,
            activityStartDate:resData.activityStartDate,
            activityEndDate:resData.activityEndDate,
            residueNum:resData.currentUserJoinTimes,
            missionInfoList:resData.missionInfoList,
            
          });
          var c_html = decodeURIComponent(resData.activityRulesDesc);

          WxParse.wxParse("commodityIntroduce", 'html', c_html, that, 5);
          var mList = resData.missionInfoList;
          if(mList != null){
            for(var i=0;i<mList.length;i++){
              if(mList[i].activityMissionType == 5 || mList[i].activityMissionType == 6){
                that.setData({
                  isShow:true
                })
                return;
              }
            }
          }
        } else {
          wx.showToast({
            title: res.data.errormsg,
            icon: 'none',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                wx.switchTab({
                  url: "/pages/index/index"
                });
              }, 2000);
            }
          })
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  closePrizeBindTap:function(){
    var that = this;
    that.setData({
      prizeShow:true,
      turntableSwitch: false
    })
  },
  getPrizeBindTap:function(){
        var that = this;
        var data = {};
        data["tm"] = "/luckdraw/retailClient/singleShipment";
        data["storeId"] = app.getExtStoreId();
        data["companyId"] = app.getExtCompanyId();
        data["promotionId"] = that.data.promotionId;
        data["userId"] = app.getUserId(),
        data["loginId"] = app.getLoginId(),
        data["userRole"] = app.getUserRole(),
        data["odbtoken"] = app.getodbtoken(),
        data["loginToken"] = app.getloginToken()
        /*获取当前用户获得的奖品*/
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/simpleRouter/stickness',
          data: data,
          success: function (res) {
            wx.hideLoading();
            if (res.data.code == 1) {
              var rData = JSON.parse(res.data.data).resultList
              if(rData != null && rData.length>0){
                that.setData({
                  prizeData:rData,
                  prizeShow:false,
                  turntableSwitch: true
  
                })
              }
              else{
                wx.showToast({
                  title: '您没有获得任何奖品~',
                  icon: 'none'
                })
              }

            } else {

            }
          },
          fail: function () {
            wx.hideLoading();
          }
        })
  },
  getRecordBindTap:function(){
    var that = this;
    var data = {};
    data["tm"] = "/luckdraw/retailClient/promotionShipment";
    data["storeId"] = app.getExtStoreId();
    data["companyId"] = app.getExtCompanyId();
    data["promotionId"] = that.data.promotionId;
    data["userId"] = app.getUserId(),
    data["loginId"] = app.getLoginId(),
    data["userRole"] = app.getUserRole(),
    data["odbtoken"] = app.getodbtoken(),
    data["loginToken"] = app.getloginToken()
    /*获取当前用户获得的奖品*/
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/simpleRouter/stickness',
      data: data,
      success: function (res) {
        wx.hideLoading();
        if (res.data.code == 1) {
          var rData = JSON.parse(res.data.data).resultList
          if(rData != null && rData.length>0){
            console.log(rData);
            that.setData({
              recordData : rData

            })
          }
          else{
          }

        } else {

        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
},
  getGameInfo: function (id, state) {  //获取中奖信息
    var that = this;
    var data;
    if (state == 0) {
      data = {
        "merchantId": app.getExtCompanyId(),
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "sceneType": id,
      }
    } else if (state == 1) {
      data = {
        "merchantId": app.getExtCompanyId(),
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "gameId": id,
      }
    }
    wx.showLoading({
      title: '正在加载活动...',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.gameProjectName + '/game/gameInfoController/getGameInfo',
      data: data,
      success: function (res) {
        wx.hideLoading();
        if (res.data.errorcode == 1000) {
          if (res.data.result.gamePrizeList.length > 0) {
            for (var i = 0; i < res.data.result.gamePrizeList.length; i++) {
              if (res.data.result.gamePrizeList[i].prizeType == 2 || res.data.result.gamePrizeList[i].prizeType == 4) {
                that.setData({
                  returnFlag: true
                })
              }
              if (res.data.result.gamePrizeList[i].isselect == 1) {
                that.setData({
                  isselectData: res.data.result.gamePrizeList[i],
                  isselectIndex: i
                })
              }
            }
            that.setData({
              gameExplain: res.data.result.gameExplain,
              award: res.data.result.gamePrizeList,
              // lotteryNum: that.data.sceneType == 3 ? res.data.result.residueNum : 1,
              lotteryNum: that.data.isFirst ? res.data.result.residueNum : that.data.lotteryNum,
              residueNum: res.data.result.residueNum,
              gameId: res.data.result.gameId,
              selectNum: res.data.result.selectNum,
              integralNum: res.data.result.integralNum
            })
            that.getRecordInfo(res.data.result.gameId);
          } else {
            wx.showToast({
              title: res.data.errormsg,
              icon: 'none',
              duration: 2000,
              mask: true,
              success: function () {
                setTimeout(function () {
                  wx.switchTab({
                    url: "/pages/index/index"
                  });
                }, 2000);
              }
            })
          }
        } else {
          wx.showToast({
            title: res.data.errormsg,
            icon: 'none',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                wx.switchTab({
                  url: "/pages/index/index"
                });
              }, 2000);
            }
          })
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  getRecordInfo: function (gameId) {   //获取用户中奖纪录列表展示
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "GET",
      url: app.gameProjectName + '/game/gameWinninglogController/showWinLogList?gameId=' + gameId,
      success: function (res) {
        if (res.data.errorcode == 1000) {
          that.setData({
            prizeInfoList: res.data.result
          })
        }
      }
    })
  },
  reduceScore: function () {
    var that = this;
    wx.request({   //积分抽奖
      method: "POST",
      url: app.gameProjectName + '/game/gameWinninglogController/insertIntegralGamelog',
      data: {
        "prizeId": that.data.isselectData.prizeId,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userName": app.getLoginName(),
        "userHead": app.getHeadImage(),
        "storeId": app.getExtStoreId(),
        "vipCardNo": that.data.vipCardNo
      },
      success: function (res) {
        if (res.data.errorcode != 1000) {
          setTimeout(function () {
            wx.showToast({
              title: res.data.errormsg,
              icon: 'none'
            })
          }, 500);
        }
        else {
          var num = 360 / that.data.award.length;
          that.setData({
            angel: (that.data.isselectIndex + 1) * num - Math.random() * num
          })
        }
        that.getRecordInfo(that.data.gameId);
        var childObj = that.selectComponent('#gameGable');
        childObj.setData({
          triggerNum: true
        });
      }
    })
  },
  getUserCard: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          that.setData({
            integral: cardList[0].integral,
            vipCardNo: cardList[0].cardId,
            cardFlag: true
          })
        } else {

        }
      }
    })
  },
  getAngel(e){
    var that = this;
    var data = {};
    if(that.data.residueNum<1){
      wx.showToast({
        title: '暂无抽奖机会啦~',
        icon: 'none'
      })
      return;
    }

    data["tm"] = "/luckdraw/retailClient/join";
    data["storeId"] = app.getExtStoreId();
    data["companyId"] = app.getExtCompanyId();
    data["promotionId"] = that.data.promotionId;
    data["userId"] = app.getUserId(),
    data["loginId"] = app.getLoginId(),
    data["userRole"] = app.getUserRole(),
    data["odbtoken"] = app.getodbtoken(),
    data["loginToken"] = app.getloginToken()
    /*获取当前抽奖活动的奖品*/
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/simpleRouter/stickness',
      data: data,
      success: function (res) {
        wx.hideLoading();
        if (res.data.code == 1) {
           /*获奖的prizeId*/
           var prizeId = JSON.parse(res.data.data).activityPrizeId;
           var prizeShowName = JSON.parse(res.data.data).activityPrizeShowName;
           var awardList = that.data.award;
           var num = 360 / awardList.length;
           that.setData({
              prizeShowName:prizeShowName
           })
           for(var i=0;i<awardList.length;i++){
             if(prizeId == awardList[i].activityPrizeId){
               that.setData({
                isselectData:awardList[i],
                isselectIndex: i,
                angel: (i + 1) * num - Math.random() * num
               })
             }
           }
           console.log(that.data.angel);
           that.getPromotionDetail();
           
          
        } else {
            /*wx.showToast({
              title: res.data.msg,
              icon: 'none'
            })*/
            if(res.data.code == 1){
              var resData = res.data.msg;
              var resMsg = resData.split("标")[0];
              var resCode = resData.split("标")[1].split("：")[1];
              app.showModal({
                title:"提示("+resCode+")",
                content: resMsg
              });
            }
            else{
              app.showModal({
                title:"提示信息",
                content: res.data.msg
              });
            }
            /*that.setData({
              tipShow:false,
              resCode:resCode
            })
            that.setData({
              tipShow:false,
              resMsg:resMsg,
              resCode:resCode
            })*/
            
            
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })

  },
  getPrize(e) {
    var that = this;
    console.log();
    if (that.data.isselectData.activityPrizeType == 1) {
      that.setData({
        noPopupHidden: true,
        prizePopupHidden:false
      })


    } else {
      that.setData({
        noPopupHidden: false,
        prizePopupHidden:true
      })
    }
    that.setData({
      turntableSwitch: true,
    })

  },
  getAngel1(e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    /*if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      app.navigateToPage('/pages/vipCard/vipCard');
      return;
    }*/
    if (that.data.returnFlag && !that.data.cardFlag) { /*代表奖品有积分余额等且没有绑定会员卡*/
      wx.showModal({
        content: "您暂未绑定会员卡无法抽奖,是否前往绑定？",
        confirmText: "确定",
        cancelText: "取消",
        success: function (res) {
          if (res.confirm) {
            app.navigateToPage('/pages/vipCard/vipCard');
            var childObj = that.selectComponent('#gameGable');
            childObj.setData({
              triggerNum: true
            });
            return;
          }
          else {
            var childObj = that.selectComponent('#gameGable');
            childObj.setData({
              triggerNum: true
            });
            return;
          }
        }
      })
    }
    else {
      let lotteryNum = that.data.lotteryNum;
      if (lotteryNum > 0 && that.data.residueNum > 0) {
        if (that.data.selectNum == 3) {/*代表使用积分去抽奖*/
          wx.showModal({
            content: "是否确认使用" + that.data.integralNum + "积分抽奖",
            confirmText: "确定",
            cancelText: "取消",
            success: function (res) {
              if (res.confirm) {
                that.reduceScore();
              }
              else {
                var childObj = that.selectComponent('#gameGable');
                childObj.setData({
                  triggerNum: true
                });
              }
            },
            complete: function (res) {
            }

          })
        }
        else {
          var num = 360 / that.data.award.length;
          that.setData({
            angel: (that.data.isselectIndex + 1) * num - Math.random() * num
          })
        }
        that.setData({
          isFirst: false
        })
      }
      else {
        wx.showToast({
          title: '暂无抽奖机会啦~',
          icon: 'none'
        })
        return
      }
    }
  },
  getPrize1(e) {
    var that = this;
    var aa = 360 / that.data.award.length
    let index = parseInt(that.data.angel / aa);
    let lotteryNum = that.data.lotteryNum;
    lotteryNum--;
    if (that.data.isselectData.prizeType != 1) {
      that.setData({
        prizePopupHidden: true,
        turntableSwitch: true
      })


    } else {
      that.setData({
        noPopupHidden: true,
        turntableSwitch: true
      })
    }
    that.setData({
      index: index,
      lotteryNum: lotteryNum
    })
    if (that.data.selectNum != 3) {
      wx.request({   //提交中奖信息
        method: "POST",
        url: app.gameProjectName + '/game/gameWinninglogController/insertGameWinninglog',
        data: {
          "prizeId": that.data.isselectData.prizeId,
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "userName": app.getLoginName(),
          "userHead": app.getHeadImage()
        },
        success: function (res) {
        }
      })
    } else {
    }

  },
  //查看规则
  seeRuleBindTap: function () {
    this.setData({
      ruleHidden: true,
      turntableSwitch: true
    })
  },
  //关闭规则弹窗
  hideRuleBindTap: function () {
    this.setData({
      ruleHidden: false,
      turntableSwitch: false
    })
  },
  prizeShowBindTap: function () {
    var that = this;
    that.setData({
      prizePopupHidden: false,
      turntableSwitch: false
    })
    if (that.data.sceneType != null && that.data.sceneType != '') {
      that.getGameInfo(that.data.sceneType, 0);
    }
    else if (that.data.gameId != null && that.data.gameId != '') {
      that.getGameInfo(that.data.gameId, 1);
    }

  },
  noPrizeHideBindTap: function () {
    var that = this;
    that.setData({
      noPopupHidden: false,
      turntableSwitch: false
    })
    if (that.data.sceneType != null && that.data.sceneType != '') {
      that.getGameInfo(that.data.sceneType, 0);
    }
    else if (that.data.gameId != null && that.data.gameId != '') {
      that.getGameInfo(that.data.gameId, 1);
    }

  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/turntableActivity/turntableActivity',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})
