// pages/myvipCard/myvipCard.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
    cardBean: [],
    entity_card_image: app.imageUrl + 'entity_card_image.png',
    integralValue: 0,
    balanceVlaue: 0,
    scoreHidden: true,
    turnIntoIntegral: 0,
    cardNo: "",
    isBlack: false,
    storeValueHidden: true,
    cardQrcode: "",
    cardBarcode: "",
    cardbg: app.imageUrl + 'cardbg.png',
    isUpgradeVipCard: false
  },
  /**
   * 升级会员卡
   */
  upgradeVipCardBindTap: function () {
    var that = this;
    var cardBean = that.data.cardBean;
    var cardNo = cardBean.cardId;
    app.navigateToPage("/pages/upgradeOpenonecard/upgradeOpenonecard?vipCardNo=" + cardNo);
  },
  /**
   * 充值详情
   **/
  goChargeDetailBind: function () {
    var that = this;
    var cardBean = that.data.cardBean;
    var spareCash = cardBean.spareCash;
    var cardNo = cardBean.cardId;
    //app.navigateToPage("/pages/chargeDetail/chargeDetail?cardNo=" + cardNo);
    app.navigateToPage("/pages/chargeItem/chargeItem?cardNo=" + cardNo);
  },
  /**
   * 去充值
   */
  goChargeBind: function () {
    var that = this;
    var cardBean = that.data.cardBean;
    var spareCash = cardBean.spareCash;
    var cardNo = cardBean.cardId;
    app.navigateToPage("/pages/chargeAccount/chargeAccount?cardNo=" + cardNo + "&spareCash=" + spareCash);
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var cardNo = options.cardNo;
    this.setData({
      cardNo: cardNo,
      cardQrcode: this.data.cardBean.cardQrcode,
      cardBarcode: this.data.cardBean.cardBarcode
    });
    this.getMyVipCardInfo(cardNo);
    this.querySupplierSettingVipFunction();
  },
  /**
   * 领取会员卡到微信卡包
   */
  receiveVipCardToWehcatBindTap: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId()
      },
      url: app.projectName + '/vipCard/receiveVipCardToWehcat',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          var timestamp = res.data.timestamp;
          var signature = res.data.signature;
          var nonce_str = res.data.nonce_str;
          var card_id = res.data.card_id;
          var code = res.data.code;
          wx.addCard({
            cardList: [
              {
                cardId: card_id,
                cardExt: '{"code":"' + code + '","timestamp":' + timestamp + ',"signature":"' + signature + '","nonce_str":"' + nonce_str + '"}'
              }
            ],
            complete: function (res) {
              if (res.errMsg == "addCard:ok") {
                that.activateWechatVipCard();
              }
            }
          })
        } else {
          wx.showToast({
            title: '领取失败，请联系商家处理',
            icon: 'none',
            duration: 2000,
            mask: true
          })
        }
      }
    })
  },
  activateWechatVipCard: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId()
      },
      url: app.projectName + '/vipCard/activateWechatVipCard',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: '已激活',
            icon: 'success',
            duration: 2000,
            mask: true
          })
        } else {
          wx.showToast({
            title: '激活失败，请联系商家处理',
            icon: 'none',
            duration: 2000,
            mask: true
          })
        }
      }
    })
  },
  querySupplierSettingVipFunction: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/querySupplierSettingVipFunction',
      success: function (res) {
        var configEntity = res.data.configEntity;
        var isIntegralTransfer = res.data.isIntegralTransfer;
        var isUpgradeVipCard = res.data.isUpgradeVipCard;
        if (configEntity != null) {
          var storedValue = configEntity.storedValue;
          that.setData({
            storeValueHidden: storedValue == 0 ? false : true
          })
        }
        that.setData({
          isUpgradeVipCard: isUpgradeVipCard,
          isIntegralTransfer: isIntegralTransfer
        })
      }
    })
  },
  onShow: function () {
    if (this.data.isBlack) {
      this.getMyVipCardInfo(this.data.cardNo);
    } else {
      this.setData({
        isBlack: true
      });
    }
  },
  /**
   * 积分转线上
   **/
  switchScoreBind: function () {
    this.setData({
      scoreHidden: false
    });
  },
  cancelScoreBindTap: function () {
    this.setData({
      scoreHidden: true
    });
  },
  integralBindInput: function (e) {
    this.setData({
      turnIntoIntegral: e.detail.value
    });
  },
  /**
   * 转入积分
   */
  transferBindTap: function () {
    var that = this;
    var turnIntoIntegral = that.data.turnIntoIntegral.replace(/\s+/g, '');
    if (turnIntoIntegral == "" || turnIntoIntegral == 0) {
      wx.showToast({
        title: '请输入积分',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return;
    }
    var cardBean = that.data.cardBean;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "vipCardNo": cardBean.cardId,
        "integralTotal": turnIntoIntegral
      },
      url: app.projectName + '/integralService/retailUserOuterConvertAccountMenu',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "转入成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1000);
            }
          })
        } else {
          wx.showToast({
            title: '转入失败',
            icon: 'fail',
            duration: 1000,
            mask: true
          })
        }
      }
    })
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function (cardNo) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "cardNo": cardNo
      },
      url: app.projectName + '/vipCard/getVipCardInfoByCardNo',
      success: function (res) {
        var cardBean = res.data.cardBean;
        var wechatPackage = res.data.wechatPackage;
        that.setData({
          wechatPackage: wechatPackage,
          cardBean: cardBean,
          turnIntoIntegral: cardBean.integral,
          cardQrcode: cardBean.cardQrcode,
          cardBarcode: cardBean.cardBarcode
        });
      }
    })
  },
  /**
   * 去支付
   */
  // gopayBind: function() {
  //   var cardBean = this.data.cardBean;
  //   var cardQrcode = cardBean.cardQrcode;
  //   var cardBarcode = cardBean.cardBarcode;
  //   app.navigateToPage("/pages/gopayBill/gopayBill?cardQrcode=" + cardQrcode + "&cardBarcode=" + cardBarcode);
  // }
  goCoupon: function () {
    app.navigateToPage("/pages/person_coupon/person_coupon");
  },
    /**
   * 积分抽奖
   */
  toLuckdraw:function(){
    var that=this;
    app.navigateToPage("/pages/turntableActivity/turntableActivity?sceneType=3&vipCardNo="+that.data.cardBean.cardId);
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})