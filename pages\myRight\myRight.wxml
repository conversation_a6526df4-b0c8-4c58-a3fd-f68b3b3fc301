<view>
    <block wx:if="{{rightsList.length>0}}">
        <block wx:for="{{rightsList}}" wx:key="item">
            <view style="padding:20rpx;align-items:center;justify-content:space-between;width:660rpx;margin:0 auto;background:#fff;display:flex;font-size:28rpx;">
                <label>{{item.activityName}}</label>
                <view style="align-items:center;display:flex;flex:1;justify-content:flex-end;">
                    <view>
                        <view>可用次数</view>
                        <view style="text-align:center;">{{item.activityJoinTimes}}</view>
                    </view>
                    <view style="margin-left:50rpx;">
                        <view data-drawid="{{item.activityId}}" bindtap="goDrawClick" style="height:60rpx;line-height:60rpx;border-radius:60rpx;">去使用</view>
                    </view>
                </view>
            </view>
        </block>
    </block>
</view>
