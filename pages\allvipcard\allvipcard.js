// pages/allvipcard/allvipcard.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    makeList: [],
    isFromBack: false
  },
  /**
   * 领取单张会员卡
   */
  getOneCardBind: function(e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var cardBean = that.data.makeList[index];
    app.navigateToPage('/pages/onevipcard/onevipcard?cardBean=' + JSON.stringify(cardBean));
  },
  /**
   * 领取单张会员卡
   */
  offLineCardBind: function() {
    app.navigateToPage('/pages/bindofflineCard/bindofflineCard');
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    that.getOnLineVipCardMessage(); //获取线上会员卡
  },
  /**
   * 获取线上会员卡
   */
  getOnLineVipCardMessage: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "state": 0,
        "page": 1,
        "pagesize": 20,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/queryAllStoreVipCardInfo',
      success: function(res) {
        var makeList = res.data.makeList;
        if (makeList != null && makeList.length > 0) {
          that.setData({
            makeList: makeList
          });
        } else {
          wx.showToast({
            title: "暂无卡片",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function() {
              setTimeout(function() {
                app.turnBack();
              }, 1000);
            }
          })
        }
      }
    })
  },
  /**
   * 获取百威线下卡券
   */
  getUnderTheLineVipCardMessage: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "vipTelephone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function(res) {
        var vipList = res.data.vipList;
        that.setData({
          vipList: vipList
        });
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    var that = this;
    if (that.data.isFromBack) {
      that.getOnLineVipCardMessage(); //获取线上会员卡
      //that.getUnderTheLineVipCardMessage(); //获取百威线下卡券
    } else {
      that.setData({
        isFromBack: true
      });
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})