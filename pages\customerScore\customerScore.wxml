<view style="font-size:29rpx;">
  <view class="reabateTop">
    <view style="display:flex;align-items:center;padding-left:30rpx;">
      <image src="{{headLogo}}" style="width:120rpx;border-radius:50%;" mode="widthFix"></image>
      <view style="color:#fff;font-size:30rpx;margin-left:30rpx;">
        <view>{{userName}}</view>
      </view>
    </view>
  </view>
  <view class="rebate_total">
    <view class="total_d">
      <label>可用积分</label>
      <label>{{accountValidQuota}}</label>
    </view>
    <view class="total_d">
      <label>待入账积分</label>
      <label>{{accountWincreaseQuota}}</label>
    </view>
  </view>
  <view style="text-align:center;" bindtap="goChange">
    <image style="width:700rpx;margin-top:30rpx;" mode="widthFix" src="{{goMall}}"></image>
  </view>
  <view class="accountDetail">
    <view style="font-size:32rpx;font-weight:bold;padding:30rpx 30rpx 0rpx;">积分明细</view>
    <view class="detailTop">
      <view class="{{billType==1?'active':''}}" bindtap="queryRetailUserBillBindTap" data-type="1">收入</view>
      <view class="{{billType==2?'active':''}}" bindtap="queryRetailUserBillBindTap" data-type="2">支出</view>
    </view>
    <scroll-view style="height:{{windowHeight}}" scroll-y class="r_wrap">
      <block wx:if="{{billList.length>0}}">
        <block wx:key="unique" wx:for="{{billList}}" wx:for-item="bill" wx:for-index="billIndex">
          <view class="r_detail">
            <view class="r_detail_m">
              <label>{{bill.accountMenuName}}</label>
              <label class="r_detail_t">{{bill.journaTime}}</label>
            </view>
            <view class="detail_income">{{bill.journaIO?"-":"+"}}{{bill.journaQuota}}</view>
          </view>
        </block>
      </block>
      <block wx:else>
        <image class='order_none' src='{{order_none}}' mode='widthFix'></image>
        <view style="text-align:center;font-size:30rpx;color:#666;">暂无数据</view>
      </block>
    </scroll-view>
  </view>
</view>