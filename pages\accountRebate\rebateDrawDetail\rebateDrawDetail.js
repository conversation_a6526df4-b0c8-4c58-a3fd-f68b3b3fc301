var app = getApp();
var TimeUtil = require('../../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    selected: app.imageUrl + 'selected.png',
    unselected: app.imageUrl + 'unselected.png',
    date1: '开始时间',
    date2: '结束时间',
    bgHidden: true,
    checkedUnder: app.imageUrl + 'checkedUnder.png',
    checkedSuccess: app.imageUrl + 'checkedSuccess.png',
    checkedFail: app.imageUrl + 'checkedFail.png',
    checkedDown: app.imageUrl + 'checkedDown.png',
    checkedUp: app.imageUrl + 'checkedUp.png',
    checkedPay: app.imageUrl + 'checkedPay.png',
    noJournal: app.imageUrl + 'noJournal.png',
    bgHidden: true,
    orderList: [],
    type: 0,
    currentPage: 1,
    pageSize: 10,
    isPage: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.userQueryApplyOrder();
  },
  switchOrderTypeBindTap: function (e) {
    var that = this;
    that.setData({
      type: e.currentTarget.dataset.type,
      orderList: [],
      currentPage: 1,
      isPage: true,
      date1: '开始时间',
      date2: '结束时间'
    })
    that.userQueryApplyOrder();
  },
  switchMoreBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var orderList = that.data.orderList;
    for (var i = 0; i < orderList.length; i++) {
      if (orderList[i].orderId == id) {
        orderList[i].showDetail = !orderList[i].showDetail;
        break;
      }
    }
    that.setData({
      orderList: orderList
    });
  },
  /**
   * 提现单查询
   */
  userQueryApplyOrder: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/extractServer/userQueryApplyOrder',
      data: {
        "type": that.data.type,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "currentPage": that.data.currentPage,
        "pageSize": that.data.pageSize,
        "startTime": that.data.date1,
        "endTime": that.data.date2
      },
      success: function (res) {
        var total = res.data.total;
        var new_orderList = res.data.orderList;
        if (new_orderList != null && new_orderList.length > 0) {
          for (var i = 0; i < new_orderList.length; i++) {
            new_orderList[i].withdrawCreateTime = TimeUtil.getSmpFormatDateByLong(new_orderList[i].withdrawCreateTime, true);
            new_orderList[i].withdrawCardNo = new_orderList[i].withdrawCardNo.substr(0, 4) + "********" + new_orderList[i].withdrawCardNo.substr(-4);
          }
        }
        var old_orderList = that.data.orderList;
        if (old_orderList != null && old_orderList.length > 0) {
          new_orderList = old_orderList.concat(new_orderList);
        }
        var isPage = true;
        if (new_orderList != null && new_orderList.length >= total) {
          isPage = false;
        }
        that.setData({
          isPage: isPage,
          orderList: new_orderList
        })
      }
    })
  },
  selectWithdrawalOrderByWhereBindTap: function () {
    var that = this;
    var startTime = that.data.date1;
    var endTime = that.data.date2;
    if (startTime == "开始时间" && endTime == "结束时间") {
      app.showModal({
        title: '提示',
        content: "请输入检索时间"
      });
      return;
    }
    if (startTime != "开始时间" && endTime != "结束时间") {
      var start_date = new Date(startTime.replace(/-/g, "-"));
      var end_date = new Date(endTime.replace(/-/g, "-"));
      //转成毫秒数，两个日期相减
      var ms = end_date.getTime() - start_date.getTime();
      //转换成天数
      var day = parseInt(ms / (1000 * 60 * 60 * 24));
      if (day <= 0) {
        app.showModal({
          title: '提示',
          content: "结束时间不能小于等于开始时间"
        });
        return;
      }
    }
    that.setData({
      bgHidden: true,
      orderList: [],
      currentPage: 1,
      type: 0,
      isPage: true
    });
    that.userQueryApplyOrder();
  },
  selectBillClick: function () {
    var that = this;
    that.setData({
      bgHidden: false
    })
  },
  closeBlackBgBindTap: function () {
    this.setData({
      bgHidden: true
    })
  },
  closeBgHiddenBindTap: function (e) {
    var type = e.currentTarget.dataset.type;
    if (type < 5) {
      this.setData({
        type: type,
        orderList: [],
        currentPage: 1,
        isPage: true,
        date1: '开始时间',
        date2: '结束时间'
      })
      this.userQueryApplyOrder();
      this.setData({
        bgHidden: true
      })
    } else {
      this.setData({
        bgHidden: true
      })
    }
  },
  bindStartDateChange: function (e) {
    this.setData({
      date1: e.detail.value
    })
  },
  bindEndDateChange: function (e) {
    this.setData({
      date2: e.detail.value
    })
  },
  resetDateBindTap: function () {
    this.setData({
      date1: '开始时间',
      date2: '结束时间'
    })
  },
  copyOrderNo: function (e) {
    var that = this;
    var orderNo = e.currentTarget.dataset.no;
    wx.setClipboardData({
      data: orderNo,
      success(res) {
        wx.getClipboardData({
          success(res) {
            wx.showToast({
              title: '复制成功'
            })
          }
        })
      }
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (that.data.isPage) {
      that.setData({
        currentPage: that.data.currentPage + 1
      })
      that.userQueryApplyOrder();
    }
  },
})