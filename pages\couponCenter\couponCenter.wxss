page {
  color: #333;
  background: #F2F2F2;
}

.oneDiscount {
  position: relative;
  margin: 10rpx auto;
  width: 700rpx;
  border: 1rpx solid #dedede;
  border-radius: 10rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  background: #FF7E00;
  height: 180rpx;

}

.discount_l {
  float: left;
  height: 150rpx;
  border: none;
  width: 77%;
  padding-top: 30rpx;
}

.discount_r {
  float: left;
  height: 180rpx;
  font-size: 28rpx;
  text-align: center;
  color: #fff;
  width: 22%;
  border-right: 1rpx dashed #fff;
  line-height: 180rpx;
}

.l_title {
  padding-left: 20rpx;
  padding-top: 20rpx;
}

.l_desc {
  padding-left: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.l_date {
  padding: 10rpx 0;
  padding-left: 20rpx;
  position: relative;
}

.desc_detail {
  height: 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title_more {
  font-size: 26rpx;
  margin-left: 20rpx;
  color: #666;
}

.title_left {
  font-size: 26rpx;
  color: #FF7E00;
}

.title_right {
  font-size: 40rpx;
  color: #fff;
}

.oneBuy {
  font-size: 26rpx;
  position: absolute;
  right: 10rpx;
  top: -2rpx;
  margin-right: 10rpx;
  padding: 6rpx 14rpx;
  border: 1px solid #fff;
  border-radius: 6rpx;
  background: #fff;
  color: #000;
}

.buy_append {
  color: #fff;
  background: none;
  border: none;
}

/**/
.coin_back {
  width: 750rpx;
  height: 100%;
  z-index: 9;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.pop_coin {
  z-index: 10;
  border-radius: 20rpx;
  width: 600rpx;
  position: fixed;
  top: 220rpx;
  left: 75rpx;
  z-index: 30;
}

.coin_info {
  color: #fff;
  margin-top: 30rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom: 30rpx;
}

.fullAmount {
  font-size: 24rpx;
  margin-top: 20rpx;
  vertical-align: bottom;
}

.useNow {
  margin-left: 98rpx;
  margin-top: 10rpx;
  width: 380rpx;
  height: 78rpx;
  line-height: 78rpx;
  border-radius: 40rpx;
  color: #534930;
  font-size: 24rpx;
  display: inline-block;
  text-align: center;
  background: #FFED8E;
  border: 1px solid #FF7E00;

}

.deleteIcon {
  position: fixed;
  top: 160rpx;
  left: 680rpx;
  z-index: 11;
}

.lunckyWrap {
  width: 540rpx;
  margin: 0 auto;
  max-height: 180rpx;
  overflow-y: auto;
  padding-top: 20rpx;

}

.lunckyName {
  float: left;
  width: 60%;
  font-size: 24rpx;
  color: #fff;
  margin-left: 20rpx;
}

.lunckyAmount {
  float: right;
  font-size: 24rpx;
  color: #fff;
}

.luckyShow {
  font-size: 26rpx;
  text-align: center;
  color: #fff;
  margin-top: 20rpx;
}

.mark_pay {
  position: absolute;
  top: 0;
  left: 0
}

/* 新的样式 */
.coupon-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overflowMore {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;

}

.couponBox {
  width: 710rpx;
  background-color: #fff;
  display: flex;
}

.jagged {
  position: relative;
  width: 480rpx;
  background: #EC1B2E;
  display: inline-block;
  color: #FFFFFF;
}

.jagged:after {
  content: "";
  position: absolute;
  top: -20px;
  display: block;
  width: 10px;
  height: 100%;
  margin-top: 20px;
  background-size: 20px 10px;
}

.jagged:after {
  right: -10px;
  background-color: #EC1B2E;
  background-position: 100% 15%;
  background-image: linear-gradient(-45deg, #fff 25%, transparent 25%, transparent),
    linear-gradient(-135deg, #fff 25%, transparent 25%, transparent),
    linear-gradient(-45deg, transparent 75%, #fff 75%),
    linear-gradient(-135deg, transparent 75%, #fff 75%);
}

.couponRight {
  width: 200rpx;
  margin-left: 20rpx;
  font-size: 50rpx;
  color: #EC1B2E;
}

.couponRight .noType {
  width: 100rpx;
  margin: 60rpx auto 0;
}

.couponRight .lType {
  width: 100rpx;
  margin: 50rpx auto;
}

.ruleBox {
  font-size: 22rpx;
  padding: 20rpx;

}

.ruleBox image {
  width: 24rpx;
  height: 12rpx;
  margin-left: 10rpx;
}

.couponOut {}

.couponImg {
  width: 580rpx;
  height: 670rpx;
  position: fixed;
  top: 200rpx;
  left: 85rpx;
  z-index: 20;
}

.black_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}