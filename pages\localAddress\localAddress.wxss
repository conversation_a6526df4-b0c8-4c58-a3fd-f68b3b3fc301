/* pages/storechain/storechain.wxss */
page{
  background:#f9f9f9;
  
}
/*搜索框
.search_box {
  height: 26px;
  background: #fff;
  color: #848489;
  font-size:28rpx;
  padding:24rpx 20rpx 28rpx;
}

.search_box icon {
  float: left;
  position: absolute;
  top: 40rpx;
  left: 18px;
  z-index: 10;
  line-height: 26px;
}

.search_box input {
  width: 80%;
  height: 62rpx;
  line-height: 62rpx;
  background: #f6f6f6;
  color: #272727;
  font-size:26rpx;
  padding-left: 30px;
  float: left;
  border-radius: 14rpx;
}

.search_box text {
  line-height: 60rpx;
  float: right;
  font-size: 28rpx;
}
.search_box .locate_add{
  width:62%;
}*/
/*搜索end*/
.chainTop{
  text-align:center;
  margin:20rpx 0;
  margin-bottom:30rpx;
}
.chainTop image{
  width:100rpx;
  height:100rpx;
  border-radius:50%;
}
.chainTop view{
  text-align:center;
  color:#999;
  font-size:24rpx;
  margin-top:10rpx;
}
/*onechain*/
.chainWrap{
  margin-bottom:20rpx;
  padding-top:20rpx;
  height:100%;
}
.onechain{
  padding-bottom:20rpx;
  padding-right:50rpx;
  position:relative;
}
.chainIcon{
  width:30rpx;
  position:absolute;
  right:20rpx;
  top:60rpx;
}
.onechain label{
  display:block;
  padding:0 20rpx
}
.chainPic{
  width:30rpx;
  margin-right:10rpx;
  margin-bottom:-4rpx;
}
.chainName{
  font-size:28rpx;
  color:#333;
}
.chainDistance{
  float:right;
  color:#999;
  font-size:26rpx;
}
.chainAddress{
  padding:20rpx;
  color:#666;
  font-size:26rpx
}
/*onechain end*/
.d_wrap{
  position:absolute;
  right:0;
  top:0;
  z-index:10;
}
.d_wrap image{
  float:right;
  margin-left:10rpx;
  width:36rpx;
}