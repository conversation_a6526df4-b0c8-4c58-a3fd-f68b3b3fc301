<!--<label class="current_account">
  当前登录账号
</label>-->

<view class='account_detail'>
  <label>
    <text>姓名</text>
    <input placeholder="用户昵称" bindinput='userNameBindInput' value='{{userName}}' type='text'></input>
  </label>
  <label>
    <text>银行卡号</text>
    <input placeholder="银行卡号" bindinput='bankCardBindInput' value='{{cardNo}}' type='text'></input>
  </label>
  <label>
    <text>开户银行</text>
    <input placeholder="开户银行" disabled='true' value='{{bankName}}' type='text'></input>
  </label>
  <label>
    <text>手机号</text>
    <input placeholder="手机号" maxlength='11' bindinput='telephoneBindInput' value='{{telephone}}' type='text'></input>
  </label>
  <label>
    <text>开户地区</text>
    <picker mode="region" bindchange="bindRegionChange" value="{{pac}}" custom-item="{{customItem}}">
      <view class="picker">
        <block wx:for="{{pac}}" wx:for-item="region" wx:for-index="addressIndex" wx:key="">
          {{region}}
        </block>
      </view>
    </picker>
  </label>
  <label>


    <picker  bindchange="bindPickerChange" value="{{index}}" range="{{branchNameList}}">
      <view class="picker">
        选择支行 \t{{branch}}
      </view>
    </picker>
    
  </label>
  
  <view class='tip_box'>
    <text class='tip'>注：每个月的推广奖励自动转入绑定的银行卡中。</text>
  </view>

</view>

<view class='add_address'>
  <button bindtap='boundBankInfo'>
    保存
  </button>
</view>