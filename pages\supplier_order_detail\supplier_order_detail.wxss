/* pages/orderDetail/orderDetail.wxss */

/*new*/

.single_box {
  background: #fff;
  height: 100%;
  margin-bottom: 20rpx;
}

.single_box image {
  width: 200rpx;
  height: 200rpx;
  float: left;
  border-radius:10rpx;
}

.product_detail {
  padding-left: 220rpx;
  height: 100%;
}

.product_detail >view >text {
  float: right;
}

.single_title {
  /*height: 20px;*/
  display: block;
  /*overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;*/
  width:74%;
  float: left;
  color: #3c3c3c;
  font-size: 28rpx;
  font-weight: bold;
}

.single_title text {
  float: right;
  text-align: right;
  display: block;
}

.goods_desc {
  font-size: 26rpx;
  margin-top: 8rpx;
}

.newPrice {
  font-size: 28rpx;
}

.classify_box {
  color: #888;
  font-size: 12px;
  margin-top: 4rpx;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.single_price {
  color: #333;
  font-size: 24rpx;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.order_operate {
  margin-top: 80rpx;
}

.order_operate view {
  float: left;
  text-align: center;
  width: 25%;
  font-size: 28rpx;
  color: #fff;
}

.order_operate view text {
  padding: 8rpx 20rpx;
  background: #FF7E00;
  border-radius: 10rpx;
}

.info_detail {
  font-size: 26rpx;
  color: #717071;
  margin: 10rpx 0;
}

.info_detail text {
  margin-left: 20rpx;
  color: #FF7E00;
}

.goods_detail {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #c6c7c8;
}

.goods_detail view {
  padding: 8rpx 0;
  font-size: 28rpx;
}

.detailWrap {
  padding-bottom: 20rpx;
  font-size: 30rpx;
  border-bottom: 1rpx solid #c6c7c8;
}

.address_icon {
  float: left;
  width: 80rpx;
}

.address_info {
  padding-left: 100rpx;
  font-size: 26rpx;
}

.acutal_pay {
  padding: 20rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid #c6c7c8;
}

.info_title {
  font-size: 28rpx;
  margin-top: 10rpx;
}
