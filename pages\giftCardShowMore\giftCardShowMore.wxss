/* pages/wholesaleDetail/wholesaleDetail.wxss */
page{
  background: #f5f5f5;
}
.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}
.wholeDetail{
  margin-top:10px;
  font-size:14px;
  color:#757575;
  background:#fff;
  padding-bottom:58px;
}
.orderMode{
  padding:6px 15px;
  border-bottom:1px solid #ececec;
}
.mode_l{
  float:left;
}
.mode_r{
  float:right;
}
.icondirect{
  transform:rotate(90deg);
}
.footer{
  font-size:15px;
  color:#000;
  width:100%;
  position:fixed;
  bottom:0;
  background:#ececec;
  text-align:center;
}
.footer label{
  width:80%;
  height:40px;
  line-height:40px;
  text-align:center;
}
.goPay{
  display:inline-block;
  color:#fff;
  padding:4px 12px;
  background:#FF7E00;
  border-radius:4px;
  font-size:13px;
}
.color_append{
  color:#FF7E00
}


.detailBox{
  width:670rpx;
  margin:20rpx auto;
  background-color: #fff;
  border-radius: 20rpx;
  padding:10rpx 20rpx;
  font-size:28rpx;
  color:#333333;
}
.detailLi{
  height:60rpx;
  line-height: 60rpx;
  display: flex;
  justify-content: space-around;

}
.detailLi .liLeft{
  width:170rpx;
}
.detailLi .liright{
  width:450rpx;
  text-align: right;
}

/* 商品样式 */
.allProducts{
  margin-top:10px;
  background:#fff;
  height:100%;
  margin-bottom:50px;
}
.allProducts .oneProduct{
  border-bottom:1px solid #ececec;
  padding:10px 15px 10px;
}
.leftPart{
  padding:4px 0px;
  width:15%;
  float:left;
}
.imgPart{
  width:100%;
  height:50px;
}
.rightPart{
  width:80%;
  float:right;
  font-size:13px;
  color:#757575;
}
.productName{
  padding:2px 0px;
}
.productDesc{
  padding:2px 0px;
  font-size:12px;
}
.productPrice{
  padding:2px 0px;
}
.productPrice label:first-child{
  float:left;
}
.productPrice label:last-child{
  float:right;
}
.footer{
  font-size:15px;
  color:#fff;
  width:100%;
  position:fixed;
  bottom:0;
  background:#FF7E00;
  text-align:center;
}
.footer label{
  width:80%;
  height:40px;
  line-height:40px;
  text-align:center;
}
.classify_box{
  color: #888;
  font-size: 12px;
  margin-top: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.moreWrap {
  border-bottom: 1rpx solid #eee;
  background: #fff;
  text-align: center;
  padding: 10rpx 0;
  font-size: 26rpx;
}
.icondirect {
  transform: rotate(90deg);
  padding-top: 10px;
}
/* 最新状态 */

.new_state, .old_state {
  position: relative;
  border-left: 1px solid #ccc;
  width: 100%;
  height: 80px;
  font-size: 14px;
}

.new_state {
  color: #FF7E00;
}

.old_state {
  color: #666;
}

.new_state text, .old_state text {
  width: 8px;
  height:8px;
  border-radius: 50%;
  position: absolute;
}

.new_state text {
  background: #FF7E00;
  border: 3px solid #ff8997;
  left: -8px;
  top: 0px;
}

.old_state text {
  background: #666;
  left: -5px;
  top: 5px;
}

.new_state label, .old_state label {
  padding-left: 15px;
  display: block;
  line-height: 40rpx;
}

.new_state label:last-child, .old_state label:last-child {
  font-size: 12px;
}
.icondirect{
  transform:rotate(90deg);
}