const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchHistory: [],
    searchValue: "",
    isShowHistory: false,
    isSHowGoods: true,
    page: 1,
    pageSize: 20,
    isNoGoods: true,
    cbList: [],
    parameter: "",
    searchLoading: false,
    secondWindowHidden: true,
    secondCbList: []
  },


  /**
   * 生命周期函数--监听页面加载
   */

  searchBindTap: function() {
    var that = this;
    var parameter = that.data.parameter;
    if (parameter != "") {
      that.setData({
        cbList: []
      });
      that.queryDirectSubordinate();
    } else {
      app.showModal({
        content: '请输入你需要查询的关键字'
      });
      return;
    }
  },
  searchBindInput: function(e) {
    var that = this;
    var parameter = e.detail.value;
    if (parameter != "") {
      that.setData({
        parameter: parameter
      });
    }
  },
  showSecondWindowBindTap: function(e) {
    var that = this;
    var userId = e.target.dataset.id;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/consumption/queryDirectSubordinate',
      data: {
        "userId": userId,
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "type": 3,
        "page": 1,
        "pageSize": 500,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
      },
      success: function(res) {
        var newCbList = res.data.cbList;
        if (newCbList != null && newCbList.length > 0) {
          that.setData({
            secondCbList: newCbList,
            secondWindowHidden: false
          });
        } else {
          app.showModal({
            title: '提示',
            content: "暂无下级"
          });
          that.setData({
            secondWindowHidden: true
          });
        }
      }
    })
  },
  closeSecondConsumptionBindTap: function() {
    this.setData({
      secondWindowHidden: true
    });
  },
  onLoad: function() {
    this.queryDirectSubordinate();
  },
  queryDirectSubordinate: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/consumption/queryDirectSubordinate',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "type": 3,
        "parameter": that.data.parameter,
        "page": that.data.page,
        "pageSize": that.data.pageSize
      },
      success: function(res) {
        var newCbList = res.data.cbList;
        if (newCbList != null && newCbList.length > 0) {
          var oldCbList = that.data.cbList;
          var cbList = oldCbList.concat(newCbList);
          that.setData({
            cbList: cbList,
            searchLoading: true
          });
        } else {
          that.setData({
            searchLoading: false
          });
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        page: that.data.page + 1
      });
      that.queryDirectSubordinate();
    }
  }
})