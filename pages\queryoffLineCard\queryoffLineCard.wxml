<view style="margin-top:60rpx;">
	<block wx:if="{{telephone.length>0}}">
		<view class="cardTip">点击领取需要绑定的会员卡</view>
		<block wx:key="unique" wx:for="{{cardList}}" wx:for-item="card" wx:for-index="index">
			<!--单张卡-->
			<view class="cardWrap" bindtap="offLineCardBind" data-index="{{index}}">
				<image src="{{card_bg}}" mode="widthFix"></image>
				<view class="card_title">{{companyName}}
				</view>
				<view class="card_id">No.{{card.cardId}}</view>
				<view class="amount_wrap">
					<label class="remain_amount">余额：
						<text>{{card.spareCash}}</text>
					</label>
					<label class="remain_score">积分：
						<text>{{card.integral}}</text>
					</label>
				</view>
				<view class="validDate">有效期：{{card.validityEndDate.length>0?card.validityEndDate:'长期'}}</view>
			</view>
			<!--单张卡-->
		</block>
	</block>
	<block wx:else>
		<view class="cardTip">请您先绑定手机号码</view>
		<view style="text-align:center;">
			<button style="background-color:#FF7E00;color:#fff;margin:0 auto;line-height:60rpx;height:60rpx;font-size:28rpx;border-radius:35rpx;width:300rpx;" class='confirm_btn' open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">获取手机号 </button>
		</view>
	</block>
</view>