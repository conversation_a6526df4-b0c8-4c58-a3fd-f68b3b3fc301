.tele{
  margin-bottom:40rpx;
}
.tele input{
  border-bottom:1rpx solid #c6c7c8;
  padding-bottom:10rpx;
  font-size:30rpx;
  padding-left:20rpx;
}
.smsMessage{
  position:relative;
  margin-bottom:40rpx;
}
.smsMessage input{
  border-bottom:1rpx solid #c6c7c8;
  padding-bottom:10rpx;
  font-size:30rpx;
  padding-left:20rpx;
}
.loginWrap{
  width:90%;
  margin: 0 auto;
  height:100%;
}
.smsButton{
  border:1rpx solid #717071;
  padding:4rpx 8rpx;
  position:absolute;
  right:0;
  bottom:16rpx;
  border-radius:8rpx;
  z-index:10;
}
.passwordLog{
  text-align:right;
  margin-bottom:180rpx;
}
.log{
  color:#fff;
  font-size:30rpx;
  padding:10rpx 0;
  margin-top:30rpx;
}
.forgetPassword{
  bottom:10%;
  position:absolute;
  text-align:center;
  width:90%;
  font-size:30rpx;
}
/**黑色背景**/

.black_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

/**协议弹框**/

.protocol_box {
  width: 80%;
  height: 80%;
  background: #fff;
  border-radius:16rpx;
  position: absolute;
  top: 10%;
  left: 10%;
  z-index: 20;
}
.close_btn {
  width: 30rpx;
  height: 30rpx;
  position: fixed;
  top: 10%;
  right: 10%;
  z-index: 22;
  background: #FF7E00;
  padding: 16rpx 30rpx;
  border-radius: 0 16rpx 0 0;
}

.protocol_title {
  display: block;
  text-align: center;
  line-height: 72rpx;
  font-weight: bold;
  color: #333;
  font-size: 30rpx;
  position: fixed;
  top: 10%;
  width: 80%;
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.protocolContent {
  padding-top: 72rpx;
}

.protocolContent label {
  width: 90%;
  margin: 10rpx 5%;
  display: block;
  text-indent: 2em;
  line-height: 44rpx;
  color: #666;
  font-size: 26rpx;
}
