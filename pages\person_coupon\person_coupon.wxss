page{
  background-color: #F2F2F2;
}
.tabWrap{
  position:fixed;
  top:0;
  width:100%;
  height:80rpx;
  line-height:80rpx;
  background: #F2F2F2;
  z-index: 1000;

}
.tabWrap view{
  float:left;
  width:23%;
  text-align:center;
  font-size:28rpx;
  margin:0 0.5%;
}
.tabWrap .tab_active{
  border-bottom:2rpx solid #FF7E00;
}
.oneDiscount{
  position:relative;
  margin:10rpx auto;
  width:700rpx;
  border: 1rpx solid #dedede;
  border-radius: 10rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  background:#FF7E00;
  height:180rpx;

}
.discount_l{
  float:left;
  height:150rpx;
  border:none;
  width:77%;
  padding-top:15rpx;
}
.discount_r{
  float:left;
  height:180rpx;
  font-size:28rpx;
  text-align:center;
  color:#fff;
  width:22%;
  border-right:1rpx dashed #fff;
  line-height:180rpx;
}
.l_title{
  padding-left:20rpx;
  padding-top:20rpx;
}
.l_desc{
  padding-left:20rpx;
  font-size:24rpx;
  color:#fff;
}
.l_date{
  padding:10rpx 0;
  padding-left:20rpx;
  position:relative;
}
.desc_detail{
  height:30rpx;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.title_more{
  font-size:26rpx;
  margin-left:20rpx;
  color:#666;
}
.title_left{
  font-size:26rpx;
  color:#FF7E00;
}
.title_right{
  font-size:40rpx;
  color:#fff;
}
.oneBuy{
  font-size:26rpx;
  position:absolute;
  right:10rpx;
  top:-2rpx;
  margin-right:10rpx;
  padding:6rpx 14rpx;
  border:1px solid #fff;
  border-radius:6rpx;
  background:#fff;
  color:#000;
  margin-top: 20rpx;
}
.validBuy{
  font-size:26rpx;
  position:absolute;
  right:10rpx;
  top:-2rpx;
  margin-right:10rpx;
  padding:6rpx 14rpx;
  border-radius:6rpx;
  color:#000;
}
.buy_append{
  color:#fff;
  background:none;
  border:none;
}
/**/
.coin_back{
  width:750rpx;
  height:100%;
  z-index:9;
  position:fixed;
  top:0;
  left:0;
  right:0;
}
.pop_coin{
  z-index:10;
  background:#FF7E00;
  border-radius:20rpx;
  width:600rpx;
  position:fixed;
  top:380rpx;
  left:75rpx;
}
.coin_info{
  color:#fff;
  margin-top:30rpx;
  padding-left:30rpx;
  padding-right:30rpx;
  padding-bottom:30rpx;
}
.fullAmount{
  float:right;
  font-size:24rpx;
  margin-top:20rpx;
  vertical-align:bottom;
}
.useNow{
  margin-left:30rpx;
  width:540rpx;
  border-radius:10rpx;
  color:#FF7E00;
  font-size:24rpx;
  padding:15rpx 0;
  display:inline-block;
  text-align:center;
  background:#fff;
  border:1px solid #FF7E00;
}
.deleteIcon{
  position:fixed;
  top:220rpx;
  left:680rpx;
  z-index:11;
}
.lunckyWrap{
  width:540rpx;
  margin:0 auto;
  max-height:330rpx;
  overflow-y:auto;
  padding-top:20rpx;
}
.lunckyName{
  float:left;
  width:60%;
  font-size:24rpx;
  color:#fff;
  margin-left:20rpx;
}
.lunckyAmount{
  float:right;
  font-size:24rpx;
  color:#fff;
}
.luckyShow{
  font-size:26rpx;
  text-align:center;
  color:#fff;
  margin-top:20rpx;
}
.mark_pay{
  position:absolute;
  top:0;
  left:0
}

/* 新的样式 */
.couponBox{
  width: 670rpx;
  margin: 20rpx auto;
  padding: 20rpx;
  border-radius: 8rpx;
  min-height: 200rpx;
  background-color: #fff;
}
.couponBox .couponTop{
  min-height: 200rpx;
  border-bottom:1px solid #F2F2F2;
  display: flex;
}
.couponLeft{
  position: relative;
  width: 170rpx;
  height: 170rpx;
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}
.couponLeft image{
  width: 170rpx;
  height: 170rpx;
  position: absolute;
  left: 0;
}
.couponLeft view{
  width: 170rpx;
}
.couponBox .couponRight{
  font-size: 28rpx;
  color:#333333;
  margin-left: 20rpx;
}
.couponCenter{
  min-height:60rpx;
  padding-top:20rpx;
  font-size: 26rpx;
}
.solidBtn{
  width: 156rpx;
  height: 56rpx;
  line-height: 56rpx;
  border-radius: 28rpx;
  text-align: center;
  background-color: #FF7E00;
  color: #fff;
  font-size: 26rpx;
}
.emptyBtn{
  width: 156rpx;
  height: 56rpx;
  line-height: 56rpx;
  border-radius: 28rpx;
  text-align: center;
  border:1px solid #FF7E00;
  color: #FF7E00;
  font-size: 26rpx;
}
.invalidBtn{
  width: 156rpx;
  height: 56rpx;
  line-height: 56rpx;
  border-radius: 28rpx;
  text-align: center;
  border:1px solid #686868;
  color: #686868;
  font-size: 26rpx;
}
.ruleImg{
  width: 24rpx;
  height: 12rpx;
  margin-left:10rpx;
}
.overflowMore{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  
}
.shareBox{
  width: 710rpx;
  height: 360rpx;
  z-index:999;
  text-align:center;
  position:fixed;
  background:#fff;
  bottom:0;
  left:0;
  right:0;
  color:#333333;
  font-size: 24rpx;
  padding:20rpx;
}
.closeTemp {
  position: absolute;
  right: 30rpx;
  top: 30rpx
}
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}