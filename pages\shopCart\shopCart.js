var app = getApp()

Page({
  data: {
    editing: false,
    shopCartHidden: true,
    goodsCount: 0,
    goodsCountToPay: 0,
    priceToPay: 0.00,
    goodsList: [],
    selectAll: false,
    editSelectAll: false,
    timeout: null,
    isFromBack: false,
    isShopCartEmpty: true,
    emptyCart: app.imageUrl + "shopDetails/EmptyCart1.png",
    shop_cart: app.imageUrl + "shopDetails/zc_app_fontschecked.png",
    loginState: 1,
    updateShopCardHidden: true,
    catEdit: app.imageUrl + "catEdit.png",
    indexGoodsList:[]
  },
  initShopCart: function () {
    var that = this;
    app.init_getExtMessage().then(res => {
      var isLogin = app.isLogin();
      console.log(isLogin);
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      } else {
        that.getShoppingCartData(res.storeId, res.companyId);
      }
    });
  },
  onLoad: function (options) {
    this.initShopCart();
    this.getIndexRetailRecommend();
  },
    /** 
   * 获取首页推荐商品
   */
  getIndexRetailRecommend: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/getIndexRetailRecommend',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var wechatAppletList = res.data.wechatAppletList;
        for (var i = 0; i < wechatAppletList.length; i++) {
          wechatAppletList[i].goodsPrice = parseFloat(wechatAppletList[i].goodsPrice.toFixed(2));
          wechatAppletList[i].cutOffThePrice = parseFloat(wechatAppletList[i].cutOffThePrice.toFixed(2));
        }
        that.setData({
          indexGoodsList: wechatAppletList
        });
        wx.hideLoading();
      },
      fail: function () {
        app.showModal({
          title: "提示",
          content: "数据加载异常"
        });
        wx.hideLoading();
      }
    })
  },
  goToGoodsDetailBindTap: function (e) {
    var goodsId = e.currentTarget.dataset.id;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  imageClick:function(e){
    console.log('show data:');
    console.log(e);
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  onShow: function () {
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,
        selected: 6,
        shopCartNum: wx.getStorageSync('shopCartNum'),
        color: app.globalData.bottomBean.color,
        selectedColor: app.globalData.bottomBean.selectedColor
      })
    }
    if (this.data.isFromBack) {
      this.initShopCart();
    } else {
      this.setData({
        isFromBack: true
      });
    }
    this.setData({
      editSelectAll: false
    })
  },
  gologinBindTap: function () {
    app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
  },
  getShoppingCartData: function (storeId, companyId) {
    var that = this;
    wx.showLoading({
      title: '正在加载，请稍后',
      mask: true
    })
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/getRetailShoppingCart',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      success: function (res) {
        if(res.data.code == -99){ /*表示登录已经失效*/
          wx.removeStorageSync("odbtoken");
          wx.removeStorageSync("loginToken");
          app.init_userOpenId();
          return;
        }

        
        wx.hideLoading();
        if (res.data.list != null && res.data.list.length > 0) {
          /*临时封装大小单位*/
          var goodsList = res.data.list;
          for (var j = 0; j < goodsList.length; j++) {
            if (goodsList[j].promotionList) {
              if (goodsList[j].promotionList.length > 1) {
                var proList = goodsList[j].promotionList;
                for (var i = 0; i < proList.length; i++) {
                  if (proList[i].otPrice > 0) {
                    goodsList[j].otPrice = proList[i].otPrice;
                  }
                  else if (proList[i].omPrice > 0) {
                    goodsList[j].omPrice = proList[i].omPrice; 
                  }
                }
              }
            }
          }
          /*临时封装大小单位*/
          that.setData({
            goodsList: res.data.list,
            shopCartHidden: false,
            isShopCartEmpty: true
          });
          that.inticlickSelectAll();
        } else {
          that.setData({
            goodsList: [],
            shopCartHidden: true,
            isShopCartEmpty: false
          });
        }
        app.countRetailCartTotal();
        setTimeout(function () {
          if (typeof that.getTabBar === 'function' &&
            that.getTabBar()) {
            that.getTabBar().setData({
              shopCartNum: wx.getStorageSync('shopCartNum')
            })
          }
        }, 500)
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  switchToEdit: function () {
    this.setData({
      editing: true
    })
  },
  editComplete: function () {
    var list = this.data.goodsList;

    for (var i = list.length - 1; i >= 0; i--) {
      list[i].editSelected = false;
    }

    this.setData({
      editing: false,
      goodsList: list
    })
    this.recalculateCountPrice();
  },
  inticlickSelectAll: function () {
    var alreadySelect = this.data.selectAll,
      list = this.data.goodsList;
    for (var i = list.length - 1; i >= 0; i--) {
      list[i].selected = true;
    }
    this.setData({
      selectAll: true,
      goodsList: list
    })
    this.recalculateCountPrice();
  },
  clickSelectAll: function () {
    var alreadySelect = this.data.selectAll,
      list = this.data.goodsList;

    if (alreadySelect) {
      for (var i = list.length - 1; i >= 0; i--) {
        list[i].selected = false;
      }
    } else {
      for (var i = list.length - 1; i >= 0; i--) {
        list[i].selected = true;
      }
    }
    this.setData({
      selectAll: !alreadySelect,
      goodsList: list
    })
    this.recalculateCountPrice();
  },
  clickEditSelectAll: function () {
    var alreadySelect = this.data.editSelectAll,
      list = this.data.goodsList;

    if (alreadySelect) {
      for (var i = list.length - 1; i >= 0; i--) {
        list[i].editSelected = false;
      };
    } else {
      for (var i = list.length - 1; i >= 0; i--) {
        list[i].editSelected = true;
      };
    }

    this.setData({
      editSelectAll: !alreadySelect,
      goodsList: list
    })
  },
  clickSelectGoods: function (e) {
    var index = e.currentTarget.dataset.index,
      list = this.data.goodsList,
      selectAll = true;

    list[index].selected = !list[index].selected;
    for (var i = list.length - 1; i >= 0; i--) {
      if (!list[i].selected) {
        selectAll = false;
        break;
      }
    }
    this.setData({
      goodsList: list,
      selectAll: selectAll
    })
    this.recalculateCountPrice();
  },
  clickEditSelectGoods: function (e) {
    var index = e.currentTarget.dataset.index,
      list = this.data.goodsList,
      editSelectAll = true;

    list[index].editSelected = !list[index].editSelected;
    for (var i = list.length - 1; i >= 0; i--) {
      if (!list[i].editSelected) {
        editSelectAll = false;
        break;
      }
    }
    this.setData({
      goodsList: list,
      editSelectAll: editSelectAll
    })
  },
  recalculateCountPrice: function () {
    var list = this.data.goodsList,
      totalCount = 0,
      price = 0;

    for (var i = list.length - 1; i >= 0; i--) {
      var goods = list[i];
      if (goods.selected) {
        totalCount += +goods.otNum + (goods.omNum * goods.omTootNum);
        price += +goods.otPrice * +goods.otNum + (goods.omPrice * goods.omNum);
      }
    }

    this.setData({
      goodsCountToPay: totalCount,
      priceToPay: price.toFixed(2)
    })
  },
  deleteGoods: function () {
    var deleteIdArr = "";
    var list = this.data.goodsList;
    var that = this;
    var delGoodsList = [];

    for (var i = 0; i < list.length; i++) {
      if (list[i].editSelected) {
        deleteIdArr += list[i].orderPurchaseId + ",";
      } else {
        delGoodsList.push(list[i]);
      }
    }
    deleteIdArr = deleteIdArr.substring(0, deleteIdArr.length - 1);
    if (deleteIdArr == "") {
      app.showModal({
        title: '提示',
        content: "请选择删除的商品"
      });
      return;
    }
    app.showModal({
      content: '确定从购物车删除商品？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/shopCart/deleteRetailGoodsInShoppingCart',
          data: {
            "cart_id_arr": deleteIdArr,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              app.countRetailCartTotal();
              setTimeout(function () {
                if (typeof that.getTabBar === 'function' &&
                  that.getTabBar()) {
                  that.getTabBar().setData({
                    shopCartNum: wx.getStorageSync('shopCartNum')
                  })
                }
              }, 500)
              if (delGoodsList.length > 0) {
                that.setData({
                  goodsList: delGoodsList
                })
              } else {
                that.setData({
                  goodsList: [],
                  shopCartHidden: true,
                  isShopCartEmpty: false,
                  editing: false
                });
              }
            }
          }
        });
      }
    })
  },
  goToPay: function (e) {
    var payIdArr = "",
      list = this.data.goodsList,
      that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          for (var i = list.length - 1; i >= 0; i--) {
            var li = list[i];
            if (li.selected) {
              payIdArr += li.orderPurchaseId + ",";
            }
          }
          if (!payIdArr.length) {
            app.showModal({
              content: '请选择结算的商品'
            });
            return;
          }
          payIdArr = payIdArr.substring(0, payIdArr.length - 1);
          that.shopCartBuyStart(payIdArr);
        }
      }
    })
  },
  /**
   * 购物车结算
   */
  shopCartBuyStart: function (payIdArr) {
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + payIdArr + '&vipCardNo=&vipCardCode=';
      app.navigateToPage(pagePath);
    } else {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId(),
          "phone": app.getTelephone()
        },
        url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
        success: function (res) {
          var cardList = res.data.show_vipCardList;
          if (cardList != null && cardList.length > 0) {
            if (cardList.length == 1) { //一张会员卡自动选择
              var cardBean = cardList[0];
              var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + payIdArr + '&vipCardNo=' + cardBean.cardId + '&vipCardCode=' + cardBean.vipCardCode;
              app.navigateToPage(pagePath);
            } else if (cardList.length > 1) {
              //多张会员卡进行选择
              app.navigateToPage("/pages/chooseCard/chooseCard?payType=1&cart_arr=" + encodeURIComponent(encodeURIComponent(payIdArr)));
            }
          } else {
            //没有会员卡
            var pagePath = '/pages/waitPay/waitPay?payType=1&cart_arr=' + payIdArr + '&vipCardNo=&vipCardCode=';
            app.navigateToPage(pagePath);
          }
        }
      })
    }
  },
  clickMinusButton: function (e) {
    var index = e.currentTarget.dataset.index,
      num = this.data.goodsList[index].otNum,
      deleteId = this.data.goodsList[index].id,
      that = this;
    if (num - 1 <= 0) {
      app.showModal({
        content: '确定从购物车删除该商品？',
        showCancel: true,
        confirm: function () {
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/applet/shopCart/removeRetailShoppingCart',
            data: {
              "r_id": deleteId,
                     "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
              "loginId": app.getLoginId(),
              "userRole": app.getUserRole(),
              "storeId": app.getExtStoreId(),
              "companyId": app.getExtCompanyId()
            },
            success: function (res) {
              app.countRetailCartTotal();
              setTimeout(function () {
                if (typeof that.getTabBar === 'function' &&
                  that.getTabBar()) {
                  that.getTabBar().setData({
                    shopCartNum: wx.getStorageSync('shopCartNum')
                  })
                }
              }, 500)
              that.getShoppingCartData(app.getExtStoreId(), app.getExtCompanyId());
            }
          });
        }
      })
      return;
    }
    this.changeGoodsNum(index, 'minus');
  },
  clickPlusButton: function (e) {
    var index = e.currentTarget.dataset.index;
    this.changeGoodsNum(index, 'plus');
  },
  changeGoodsNum: function (index, type) {
    var goods = this.data.goodsList[index],
      currentNum = +goods.otNum,
      targetNum = type == 'plus' ? currentNum + 1 : currentNum - 1,
      that = this,
      data = {},
      param;
    var otmoq = goods.otmoq;
    var otName = goods.otName;
    if (parseInt(targetNum) < parseInt(otmoq)) {
      app.showModal({
        title: '提示',
        content: "商品购买数量必须满足" + otmoq + otName + "!"
      });
      return;
    }
    var skuId = goods.skuId;
    param = {
      "id": goods.id,
      "skuId": skuId,
      "num": 1,
      "type": type,
             "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
      "loginId": app.getLoginId(),
      "userRole": app.getUserRole(),
      "storeId": app.getExtStoreId(),
      "companyId": app.getExtCompanyId()
    };

    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/changeRetailCartNum',
      data: param,
      success: function (res) {
        data = {};
        data['goodsList[' + index + '].otNum'] = targetNum;
        that.setData(data);
        that.recalculateCountPrice();
        app.countRetailCartTotal();
        setTimeout(function () {
          if (typeof that.getTabBar === 'function' &&
            that.getTabBar()) {
            that.getTabBar().setData({
              shopCartNum: wx.getStorageSync('shopCartNum')
            })
          }
        }, 500)
      },
      successStatusAbnormal: function (res) {
        app.showModal({
          content: res.data
        })
      }
    })
  },
  inputGoodsCount: function (e) {
    var index = e.target.dataset.index,
      count = e.detail.value,
      data = {};

    data['goodsList[' + index + '].otNum'] = +count;
    this.setData(data);
  },
  goCouponCenterBind: function () {
    app.navigateToPage('/pages/couponCenter/couponCenter');
  },
  updateShoppingCardNum: function (e) {
    var that = this;
    var goodsList = that.data.goodsList;
    var id = e.currentTarget.dataset.id;
    for (var i = 0; i < goodsList.length; i++) {
      if (id == goodsList[i].id) {
        that.setData({
          u_index: i,
          u_goodsBean: goodsList[i],
          updateShopCardHidden: false
        })
      }
    }
  },
  cancelShopCartButtonBindTap: function () {
    this.setData({
      updateShopCardHidden: true
    })
  },
  batchSetRetailCartNum: function () {
    var that = this;
    var u_index = that.data.u_index;
    var u_goodsBean = that.data.u_goodsBean;
    if (u_goodsBean.otNum + u_goodsBean.omNum <= 0) {
      app.showModal({
        title: '提示',
        content: "购买数量不能为0"
      });
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/batchSetRetailCartNum',
      data: {
        "commodityId": u_goodsBean.id,
        "skuId": u_goodsBean.skuId,
        "otNum": u_goodsBean.otNum,
        "omNum": u_goodsBean.omNum,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        that.initShopCart();
        var flag = res.data.flag;
        if (flag) {
          var goodsList = that.data.goodsList;
          goodsList[u_index] = u_goodsBean;
          that.setData({
            updateShopCardHidden: true,
            goodsList: goodsList
          })
          that.recalculateCountPrice();
          app.countRetailCartTotal();
          setTimeout(function () {
            if (typeof that.getTabBar === 'function' &&
              that.getTabBar()) {
              that.getTabBar().setData({
                shopCartNum: wx.getStorageSync('shopCartNum')
              })
            }
          }, 500)
        } else {
          app.showModal({
            title: '提示',
            content: "操作失败"
          });
        }
      }
    })
  },
  clickMinusOmButton: function () {
    var that = this;
    var u_index = that.data.u_index;
    var u_goodsBean = that.data.u_goodsBean;
    if (u_goodsBean.omNum > 0) {
      u_goodsBean.omNum = u_goodsBean.omNum - 1;
      var goodsList = that.data.goodsList;
      goodsList[u_index] = u_goodsBean;
      that.setData({
        goodsList: goodsList,
        u_goodsBean: u_goodsBean
      })
    }
  },
  omNumBindInput: function (e) {
    var that = this;
    if (e.detail.value >= 0) {
      var u_index = that.data.u_index;
      var u_goodsBean = that.data.u_goodsBean;
      u_goodsBean.omNum = e.detail.value;
      var goodsList = that.data.goodsList;
      goodsList[u_index] = u_goodsBean;
      that.setData({
        goodsList: goodsList,
        u_goodsBean: u_goodsBean
      })
    }
  },
  clickPlusOmButton: function () {
    var that = this;
    var u_index = that.data.u_index;
    var u_goodsBean = that.data.u_goodsBean;
    u_goodsBean.omNum = u_goodsBean.omNum + 1;
    var goodsList = that.data.goodsList;
    goodsList[u_index] = u_goodsBean;
    that.setData({
      goodsList: goodsList,
      u_goodsBean: u_goodsBean
    })
  },
  clickMinusOtButton: function () {
    var that = this;
    var u_index = that.data.u_index;
    var u_goodsBean = that.data.u_goodsBean;
    if (u_goodsBean.otNum > 0) {
      u_goodsBean.otNum = u_goodsBean.otNum - 1;
      var goodsList = that.data.goodsList;
      goodsList[u_index] = u_goodsBean;
      that.setData({
        goodsList: goodsList,
        u_goodsBean: u_goodsBean
      })
    }
  },
  otNumBindInput: function (e) {
    var that = this;
    if (e.detail.value >= 0) {
      var u_index = that.data.u_index;
      var u_goodsBean = that.data.u_goodsBean;
      u_goodsBean.otNum = e.detail.value;
      var goodsList = that.data.goodsList;
      goodsList[u_index] = u_goodsBean;
      that.setData({
        goodsList: goodsList,
        u_goodsBean: u_goodsBean
      })
    }
  },
  clickPlusOtButton: function () {
    var that = this;
    var u_index = that.data.u_index;
    var u_goodsBean = that.data.u_goodsBean;
    u_goodsBean.otNum = u_goodsBean.otNum + 1;
    var goodsList = that.data.goodsList;
    goodsList[u_index] = u_goodsBean;
    that.setData({
      goodsList: goodsList,
      u_goodsBean: u_goodsBean
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/shopCart/shopCart',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})