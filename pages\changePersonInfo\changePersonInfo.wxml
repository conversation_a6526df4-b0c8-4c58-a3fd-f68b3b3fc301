<view class='account_detail'>
  <label>
    <text>姓名</text>
    <input value="测试" type='text'></input>
  </label>
  <label>
    <text>手机号</text>
    <text>2222</text>
  </label>
  <label>
    <text>性别</text>
    <radio-group class="radio-group">
      <radio value="男" />男
      <radio value="女" style='margin-left:40px;' />女
    </radio-group>
  </label>
  <label class="picker">
    <text>生日</text>
    <picker mode="date" style='height:45px;'>
      <text style=' float:none;'>2018-01-02</text>
    </picker>
  </label>

</view>
<view class='add_address'>
  <button>
    保存
  </button>
</view>