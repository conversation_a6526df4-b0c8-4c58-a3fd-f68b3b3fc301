/* pages/rebate_self/rebate_self.wxss */
.clearfix:after{
  display: block;
  clear:both;
  visibility: hidden;
  content: ".";
  height:0;
}
.rebateWrap{
  margin:0rpx 20rpx;
  /*border-bottom:1px solid #ececec;*/
  font-size:28rpx;
  color:#3d3a3a;
}
.monthRebate{
  border-bottom:1px solid #ececec;
  padding:10rpx 0rpx 10rpx 20rpx;
}
.rebateTop view{
  float:left;
}
.rebateTop view:first-child{
  width:60%;
  height:100rpx;
  
}
.rebateTop .rebateMoney{
  width:40%;
}
.rebateMoney{
  text-align:center;
  padding:10rpx 0;
}
.rebateMoney text{
  color:#d4525c;
  font-size:36rpx;
}
.moreIcon{
  width:32rpx;
  transform: rotate(90deg);
  vertical-align:middle;
  margin-left:14rpx;
  margin-top:-4rpx;
}
.more_append{
  transform: rotate(270deg);
}
.rebateStatus{
  text-align:center;
  width:100%;
  color:green;
  font-size:28rpx;
  margin-top:6rpx;
}
.rebateUser{
  padding-top:10rpx;
}
.rebateUser text{
  font-size:32rpx;
}
.rebateUser view{
  font-size:24rpx;
  margin-top:20rpx;
}
.rebate_time{
  font-size:24rpx;
  margin-top:20rpx;
}
.productDetail{
  float:left;
  width:80%;
  height:100%;
}
.oneProduct{
  margin-left:20rpx;
  margin-top:20rpx;
}
.oneProduct image{
  float:left;
  width:30%;
  border-radius:10rpx;
}
.oneProduct .productInfo{
  margin-left:5%;
  text-align:left;
  float:left;
  width:50%;
}
.rebate_r{
  text-align:center;
  color:#d4525c;
  float:left;
  width:15%;
  height:100%;
  transform:translateY(50%);
}
.actual_pay{
  margin-top:20rpx;
  float:right;
}