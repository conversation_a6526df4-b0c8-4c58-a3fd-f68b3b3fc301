page {
  background: #f5f5f5;
}

.delvierWay {
  padding: 10rpx 34rpx;
  border-radius: 40rpx;
  display: inline-block;
  color: #666;
}

.selfWay {
  /*border:1px solid #FF7E00;*/
  padding: 10rpx 34rpx;
  border-radius: 40rpx;
  display: inline-block;
  color: #666;
}

.active {
  background: #FF7E00;
  color: #fff;
}

.single_box {
  background: #fff;
  height: 100%;
  border-bottom: 1px solid #eee;
  padding: 10px 10px;
}

.single_box image {
  width: 60px;
  height: 60px;
  float: left;
  border-radius: 8rpx;
}

.product_detail {
  padding-left: 68px;
  height: 100%;
}

.single_title {
  height: 20px;
  display: block;
  overflow: hidden;
  font-size: 14px;
  color: #444;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 75%;
  float: left;
}

.classify_box {
  color: #888;
  font-size: 12px;
  margin-top: 3px;
  margin-bottom: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.single_price {
  color: #333;
  font-size: 13px;
  float: left;
  line-height: 26px;
  text-decoration: line-through;
}

/**加减数量**/

.plus_minus {
  width: 100%;
  height: 26px;
  /**float: right;margin-top: 3px;**/
}

.minus_box {
  width: 26px;
  height: 26px;
  color: #333;
  border: 1px solid #ccc;
  display: block;
  line-height: 22px;
  text-align: center;
  font-size: 18px;
  float: left;
  border-radius: 50%;
}

.plus_box {
  width: 26px;
  height: 26px;
  color: #333;
  border: 1px solid #ccc;
  display: block;
  line-height: 24px;
  text-align: center;
  font-size: 18px;
  float: left;
  border-radius: 50%;
}

.goods_number {
  width: 160px;
  height: 26px;
  line-height: 26px;
  float: right;
  border: none;
  color: #FF7E00;
  font-size: 14px;
  text-align: right;
  /**background: #e4e4e4;**/
}

.goods_profit {
  min-height: 18px;
  padding: 2px 10px;
  line-height: 18px;
  font-size: 12px;
  background: #fff;
  color: #666;
}

.goods_profit label {
  float: right;
  color: #666;
  font-size: 12px;
}

/**留言**/

.leave_word {
  padding: 10px;
  border-bottom: 1px solid #eee;
  background: #fff;
}

.leave_word textarea {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  line-height: 26px;
  font-size: 14px;
  color: #333;
  padding: 8px;
  box-sizing: border-box;
}

.add_address, .goods_sum, .freight {
  min-height: 28px;
  padding: 6px 10px;
  line-height: 28px;
  font-size: 14px;
  background: #fff;
  color: #444;
}

.add_address {
  margin-bottom: 10px;
}

.add_address label, .goods_sum label, .freight label {
  float: right;
  padding-right: 10px;
}

.goods_sum, .freight {
  border-bottom: 1px solid #eee;
}

.goods_sum label {
  color: #FF7E00;
}

/**支付方式**/

.pay_way {
  padding: 0 10px;
  line-height: 30rpx;
  background: #fff;
  margin-bottom: 60px;
}

/*.pay_way label {
  font-size: 14px;
  width: 100%;
  display: block;
}*/

/**底部**/

.foot_box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 46px;
  border-top: 1px solid #eee;
  background: #fff;
}

.settle_accounts {
  width: 80px;
  height: 100%;
  float: right;
  background: #FF7E00;
  text-align: center;
  color: #fff;
  line-height: 46px;
  font-size: 14px;
}

.total_box {
  line-height: 46px;
  float: left;
  font-size: 14px;
  padding-left: 10px;
}

.total_box label {
  color: #FF7E00;
}

/**超值换购**/

.changeView {
  padding: 6px 10px;
  background: #fff;
  margin-bottom: 10px;
}

.changeLable {
  font-size: 14px;
  display: block;
  border-bottom: 1px solid #f1f1f1;
  line-height: 28px;
  padding-left: 5px;
}

.changeGoodsView {
  width: 100%;
  height: 70px;
  padding: 10px 0;
  border-bottom: 1px solid #f1f1f1;
}

.changeGoodsImage {
  width: 70px;
  height: 70px;
  float: left;
}

.changeGoodschildView {
  padding-left: 80px;
  height: 100%;
}

.changeGoodsName {
  width: 100%;
  font-size: 14px;
  color: #444;
  display: block;
  line-height: 22px;
  height: 40rpx;
  overflow: hidden;
}

.changeGoodsNumber {
  width: 100%;
  font-size: 12px;
  color: #999;
  display: block;
  line-height: 20px;
}

.changeGoodsPrice {
  color: #fa5468;
  font-size: 14px;
  line-height: 30px;
}

.changeGoodsPrice1 {
  text-decoration: line-through;
  font-size: 12px;
  color: #999;
  margin-left: 5px;
  line-height: 30px;
}

.goodsreduce {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #FF7E00;
  text-align: center;
  background: #fff;
  color: #FF7E00;
  float: left;
  line-height: 20px;
  margin-top: 4px;
}

.goodsNumber {
  width: 30px;
  text-align: center;
  float: left;
  line-height: 30px;
  display: block;
}

.goodsplus {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid grey;
  text-align: center;
  background: grey;
  color: #fff;
  float: left;
  line-height: 20px;
  margin-top: 4px;
}

.changeGoodsBtn {
  width: 60px;
  line-height: 28px;
  background: #FF7E00;
  color: #fff;
  font-size: 13px;
}

.planView {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  font-size: 12px;
  text-align: right;
}

.planLabel {
  font-weight: blod;
  font-size: 16px;
  margin-left: 5px;
}

.payWay {
  width: 90%;
  display: inline-block;
  padding-left: 3px;
}

.add_customer {
  margin-top: 10px;
  min-height: 28px;
  padding: 6px 10px;
  line-height: 28px;
  font-size: 14px;
  background: #fff;
  color: #444;
  margin-bottom: 10px;
}

.add_customer label {
  float: right;
  padding-right: 10px;
}

.icondirect {
  transform: rotate(90deg);
  padding-top: 10px;
}

.selectArea {
  width: 50%;
  display: inline-block;
  height: 28px;
  float: left;
}

/*卡券*/

.noCoupon {
  height: 40px;
  font-size: 14px;
  color: #666;
  line-height: 40px;
  background: #fff;
  padding: 0 10px;
}

.oneCoupon {
  margin: 20rpx 0;
}

.couponInfo {
  width: 90%;
  margin: 0 auto;
  height: 180rpx;
  border-radius: 20rpx;
}

.couponInfo .couponValue {
  text-align: center;
  color: #fff;
  float: left;
  width: 22%;
  border-right: 1px dashed #fff;
  line-height: 180rpx;
  font-size: 40rpx;
}

.couponValue text {
  font-size: 32rpx;
}

.couponInfo .couponDiscount {
  height: 130rpx;
  float: left;
  width: 50%;
  font-size: 26rpx;
  padding-top: 50rpx;
  color: #fff;
  margin-left: 36rpx;
}

.couponDiscount view:last-child {
  margin-top: 8rpx;
}

.couponStatus {
  width: 20%;
  text-align: center;
  float: left;
  line-height: 180rpx;
}

.undrawStatus {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  background: #fff;
  border-radius: 10rpx;
}

.drawStatus {
  font-size: 26rpx;
  color: #fff;
}

.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.way_checked {
  float: right;
  margin-right: 10px;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1px solid #FF7E00;
  background: #FF7E00;
  margin-top: 20rpx;
}

.way_checked image, .way_unchecked image {
  width: 80%;
  height: 70%;
  margin-left: 4rpx;
  margin-bottom: 24rpx;
  margin-top: 7rpx;
}

.way_unchecked {
  float: right;
  margin-right: 10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background: #fff;
  margin-top: 28rpx;
}

/*swtich整体大小*/

.wx-switch-input {
  width: 92rpx !important;
  height: 50rpx !important;
}

/*白色样式（false的样式）*/

.wx-switch-input::before {
  width: 90rpx !important;
  height: 46rpx !important;
}

/*绿色样式（true的样式）*/

.wx-switch-input::after {
  width: 50rpx !important;
  height: 46rpx !important;
}

/*swtich样式end*/

.de_checked {
  margin-right: 10px;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  border: 1px solid #FF7E00;
  background: #FF7E00;
  margin-top: 20rpx;
  display: inline-block;
  font-weight: normal;
}

.de_checked image, .de_unchecked image {
  width: 70%;
  height: 60%;
  margin-left: 4rpx;
  margin-bottom: 24rpx;
  margin-top: 7rpx;
}

.de_unchecked {
  margin-right: 10px;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  border: 1rpx solid #717071;
  background: #fff;
  margin-top: 20rpx;
  display: inline-block;
  font-weight: normal;
}

.allPrice {
  min-height: 28px;
  border-top: 1px solid #ececec;
  padding: 6px 10px;
}

.view_total {
  float: right;
  margin-right: 15px;
}

.view_total :first-child {
  font-size: 13px;
  margin-right: 10px;
}

.view_total :last-child {
  font-size: 16px;
  color: #FF7E00;
}
