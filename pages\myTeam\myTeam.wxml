<!--pages/person_directAgent/person_directAgent.wxml-->
<view class="agentWrap">
  <view class="totalAgent">
    <label>团队总人数</label>
    <label class="agentNum">{{chainStoreUserNumber}}
      <text>人</text>
    </label>
  </view>
  <view class="directAgent">
    <view class="levelone" bindtap="queryMySuperior" data-type='1' style="margin-bottom:40rpx;">
      <image src="{{direct_crown}}" mode="widthFix"></image>
      <label class="direct_title">我的邀请人</label>
      <image class="moreAgent" src="{{more}}" mode="widthFix"></image>
    </view>
    <block wx:if="{{fristLevelUserNumber>0}}">
      <view class="levelone" bindtap="levelAgentBind" data-type='2'>
        <image src="{{direct_crown}}" mode="widthFix"></image>
        <text class="agent_level">1</text>
        <label class="direct_title">直属一级团队</label>
        <label class="level_num">{{fristLevelUserNumber}}
          <text>人</text>
        </label>
        <image class="moreAgent" src="{{more}}" mode="widthFix"></image>
      </view>
    </block>
    <block wx:else>
      <view class="levelone">
        <image src="{{direct_crown}}" mode="widthFix"></image>
        <text class="agent_level">1</text>
        <label class="direct_title">直属一级团队</label>
        <label class="level_num">{{fristLevelUserNumber}}
          <text>人</text>
        </label>
      </view>
    </block>
    <view class="levelone agent_append">
      <image src="{{direct_crown}}" mode="widthFix"></image>
      <text class="agent_level">2</text>
      <label class="direct_title">直属二级团队</label>
      <label class="level_num">{{secondLevelUserNumber}}
        <text>人</text>
      </label>
    </view>
  </view>
</view>
<!--邀请人弹出层-->
<view class="black_bg" hidden="{{inviteShow}}"></view>
<view class="myInviate" hidden="{{inviteShow}}">
  <icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='hiddeInvite' />
  <view class="inviate_t">我的邀请人</view>
  <view class="inviate_info">
    <view>姓名：{{agent.userName}}</view>
    <view>电话：{{agent.telephone}}</view>
    <view>加入时间：{{agent.joinTime}}</view>
  </view>
</view>