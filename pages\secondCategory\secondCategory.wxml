<view class='location_box' style='height:50rpx;'>
    <image  src="{{scan}}" bindtap='sweepCodeBindTap' mode="widthFix"
        style="width:24px;margin-right:20rpx;"></image>
    <view class="s_inner" bindtap="searchBindFocus">
      <icon type="search" size='16' color='#666' />
      <text>请输入关键字</text>
    </view>
</view>
<scroll-view scroll-y="true" style="margin-top:120rpx;height:1200rpx;" bindscrolltolower="searchScrollLower">
  <!--大分类下的小分类-->
<view class="catagory_introduction clearfix" style="margin-bottom:20rpx;padding:30rpx 0;" hidden="{{secondType.length>0?false:true}}">
  <block>
    <block wx:key="unique" wx:for="{{secondType}}" wx:for-item="type" wx:for-index="index">
      <label bindtap='secondCatogroyTap' data-id='{{type.categoryId}}'>
        <image src="{{type.categoryPic}}" lazy-load='true'></image>
        <text>{{type.categoryName}}</text>
      </label>
    </block>
  </block>
</view>
<!--大分类下的小分类-->
  <view class="section">
    <!--上下结构-->
    <view class="flex-wrp clearfix">
      <block wx:key="unique" wx:for="{{commodityBaseList}}" wx:for-item="goods">
        <view class="flex-item" style="position:relative;">
          <view class="goods_pic">
            <block wx:if="{{goods.commoditySaleWay==1}}">
              <view class='soonlist'>即将上市</view>
            </block>
            <block wx:elif="{{goods.commoditySaleWay==4}}">
              <view class='soonlist'>已售馨</view>
            </block>
            <block wx:key="unique" wx:for="{{goods.promotionList}}" wx:for-item="promotion">
              <view class="eventWrap">
                <image lazy-load='true' src="{{event}}"></image>
                <block wx:if="{{promotion.promotionType=='MIAOSHA'}}">
                  <text>秒杀</text>
                </block>
                <block wx:if="{{promotion.promotionType=='TUANGOU'}}">
                  <text>团购</text>
                </block>
                <block wx:if="{{promotion.promotionType=='TEJIA'}}">
                  <text>特价</text>
                </block>
                <block wx:if="{{promotion.promotionType=='QUDUAN'}}">
                  <text>区段</text>
                </block>
                <block wx:if="{{promotion.promotionType=='NYH'}}">
                  <text>优惠</text>
                </block>
              </view>
            </block>
            <!--3人成团-->
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 1 && goods.commodityAdTag.length>0}}">
              <template is="mark1"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commodityAdTagStyle.showStyle == 2 && goods.commodityAdTag.length>0}}">
              <template is="mark2"
                data="{{name:goods.commodityAdTag,pos:goods.commodityAdTagStyle.showPosition}}"></template>
            </block>
            <block wx:if="{{goods.commoditySideDescStyle.showStyle == 3 && goods.commoditySideDesc.length>0}}">
              <template is="mark3"
                data="{{name:goods.commoditySideDesc,nameCircle:goods.commoditySideDescStyle.specialHead}}"></template>
            </block>
            <image lazy-load='true' bindtap='imageClick' data-storeId='{{goods.commoditySupplierId}}' data-commodityId='{{goods.commodityId}}' src="{{goods.commodityMainPic}}"></image>
          </view>
          <label class="goods_title">{{goods.commodityName}}</label>
          <label class="goods_adv">{{goods.commodityAdContent}}</label>
          <view style="height:50rpx;" hidden="{{goods.commodityUnitOtDefault==1?false:true}}">
            <label class="goods_price price_append" hidden='{{goods.cutOffThePrice>0&&(goods.cutOffThePrice - goods.goodsPrice > 0)?false:true}}'>￥{{goods.cutOffThePrice}}</label>
            <label class="goods_price">￥{{goods.goodsPrice}}</label>
          </view>
          <view style="height:50rpx;" hidden="{{(goods.commodityUnitOtDefault==0 &&goods.commodityUnitOmDefault==1)?false:true}}">
            <label class="goods_price">￥{{goods.goodsOmPrice}}</label>
          </view>
        </view>
      </block>
    </view>
  </view>
</scroll-view>
<!--嵌套的模块-->
<template name="mark3">
  <view class="mark1">
    <view class="mark1_l">
      <view>{{nameCircle}}</view>
    </view>
    <view class="mark1_r">
      {{name}}
    </view>
  </view>
</template>
<template name="mark2">
  <view class="mark2_{{pos}}">
    {{name}}
  </view>
</template>
<template name="mark1">
  <view class="mark_{{pos}}">
    {{name}}
  </view>
</template>