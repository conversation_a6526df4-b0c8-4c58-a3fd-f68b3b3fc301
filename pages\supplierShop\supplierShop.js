var app = getApp();
var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    storeName: "",
    pagesize: 10,
    currentPage: 1,
    searchLoading: false,
    showReturnStoreList: [],
    localtionHidden: true,
    colorHidden: true,
    navi: app.imageUrl + 'mapNav.png',
    phone: app.imageUrl + 'cellPhone.png',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var index = options.index;
    var addressLat = options.addressLat;
    var addressLon = options.addressLon;
    var settingDistance = options.settingDistance;
    this.setData({
      index: index,
      addressLat: addressLat,
      addressLon: addressLon,
      settingDistance: settingDistance
    })
    this.getUserLocation();
    this.queryStoreInfo(0, 0);
  },
  changeColorBind: function () {
    var that = this;
    that.setData({
      colorHidden: false,
    });
  },
  /**
   * 开始导航
   */
  startNavigationBindTap: function (e) {
    var that = this;
    var name = e.currentTarget.dataset.name;
    var address = e.currentTarget.dataset.address;
    var latitude = e.currentTarget.dataset.latitude;
    var longitude = e.currentTarget.dataset.longitude;
    wx.openLocation({
      latitude: Number(latitude),
      longitude: Number(longitude),
      name: address,
      address: name,
      scale: 18
    })
  },
  makePhoneBindTap: function (e) {
    var phone = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phone //仅为示例，并非真实的电话号码
    })
  },
  getUserLocation: function () {
    let vm = this;
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取门店地理位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             vm.queryStoreInfo(0, 0);
    //             vm.setData({
    //               localUserAddress: "未定位"
    //             })
    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })
    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {
    //                   vm.setData({
    //                     localUserAddress: "未定位"
    //                   })
    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                   vm.queryStoreInfo(0, 0);
    //                 }
    //               }
    //             })
    //           }
    //         },
    //         fail: function () {
    //           vm.setData({
    //             localUserAddress: "未定位"
    //           })
    //           vm.queryStoreInfo(0, 0);
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     } else {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     }
    //   }
    // })
  },
  storeNameBindInput: function (e) {
    this.setData({
      storeName: e.detail.value
    })
  },
  searchBindTap: function () {
    var that = this;
    var storeName = that.data.storeName;
    if (storeName == null || storeName == "" || storeName.length < 1) {
      wx.showToast({
        title: '请输入检索条件',
        duration: 1500
      })
    } else {
      that.queryStoreInfo(that.data.latitude, that.data.longitude);
    }
  },
  getUserLocation: function () {
    let vm = this;
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取门店信息',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             vm.setData({
    //               localtionHidden: false
    //             })
    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })
    //                   vm.setData({
    //                     localtionHidden: true
    //                   })
    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {
    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                   vm.setData({
    //                     localtionHidden: false
    //                   })
    //                 }
    //               }
    //             })
    //           }
    //         },
    //         fail: function () {
    //           vm.setData({
    //             localtionHidden: false
    //           })
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     } else {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     }
    //   }
    // })
  },
  // 微信获得经纬度
  getLocation: function () {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // wx.getLocation({
    //   type: 'gcj02',
    //   altitude: true,
    //   complete: function (res) {
    //     if (res.errMsg == "getLocation:ok") {
    //       var latitude = res.latitude;
    //       var longitude = res.longitude;
    //       that.setData({
    //         latitude: latitude,
    //         longitude: longitude
    //       });
    //       that.queryStoreInfo(latitude, longitude);
    //     } else {
    //       that.setData({
    //         latitude: 0,
    //         longitude: 0
    //       });
    //       that.queryStoreInfo(latitude, longitude);
    //     }
    //   }
    // })
  },
  /**
   * 查询我的客户信息
   */
  queryStoreInfo: function (latitude, longitude) {
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryNewSelfStoreInfo',
      data: {
        "companyId": app.getExtCompanyId(),
        "storeName": that.data.storeName
      },
      success: function (res) {
        wx.hideLoading();
        var returnStoreList = res.data.returnStoreList;
        for (var i = 0; i < returnStoreList.length; i++) {
          returnStoreList[i].storeDistance = that.distance(latitude, longitude, returnStoreList[i].latitude, returnStoreList[i].longitude);
        }
        returnStoreList = returnStoreList.sort((el1, el2) =>
          el1.storeDistance - el2.storeDistance
        );
        that.setData({
          showReturnStoreList: returnStoreList
        });
      }
    })
  },
  distanceSubmit: function (returnStoreList, latitude, longitude) {
    var that = this;
    var fromArray = [];
    for (var i = 0; i < returnStoreList.length; i++) {
      var fromBean = {};
      fromBean.latitude = returnStoreList[i].latitude;
      fromBean.longitude = returnStoreList[i].longitude;
      fromArray.push(fromBean);
    }
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // qqmapsdk.calculateDistance({
    //   //mode: 'driving',//可选值：'driving'（驾车）、'walking'（步行），不填默认：'walking',可不填
    //   //from参数不填默认当前地址
    //   //获取表单提交的经纬度并设置from和to参数（示例为string格式）
    //   from: { "latitude": latitude, "longitude": longitude }, //若起点有数据则采用起点坐标，若为空默认当前地址
    //   to: fromArray, //终点坐标
    //   success: function (res) {//成功后的回调
    //     var elements = res.result.elements;
    //     for (var i = 0; i < returnStoreList.length; i++) {
    //       returnStoreList[i].storeDistance = elements[i].distance / 1000;
    //     }
    //     that.setData({
    //       showReturnStoreList: returnStoreList
    //     });
    //   }
    // });
  },
  /**
   * 选择客户进行下单
   */
  selectStoreBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var name = e.currentTarget.dataset.name;
    var address = e.currentTarget.dataset.address;
    var latitude = e.currentTarget.dataset.latitude;
    var longitude = e.currentTarget.dataset.longitude;
    if (parseFloat(that.data.settingDistance) > 0 && that.data.index == 1) {
      if (parseFloat(latitude) > 0 && parseFloat(that.data.addressLat) > 0) {
        var returnS = that.distance(latitude, longitude, that.data.addressLat, that.data.addressLon);
        if (parseFloat(returnS) > parseFloat(that.data.settingDistance)) {
          app.showModal({
            content: '您选择的配送门店已超出收货地址配送范围'
          });
          return;
        }
      }
    }

    var selectStoreInfo = {
      "storeId": id,
      "storeName": name,
      "storeAddress": address
    };
    app.setStorage({
      key: 'selectStoreInfoKey',
      data: selectStoreInfo
    });
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      chooseStoreValue: name,
      pickOrderStoreId: id,
      storeAddress: address
    });
    app.turnBack();
  },
  /**
   * 由经纬度计算两点之间的距离，la为latitude缩写，lo为longitude
   * @param {*} 第一个坐标点的纬度 
   * @param {*} 第一个坐标点的经度 
   * @param {*} 第二个坐标点的纬度 
   * @param {*} 第二个坐标点的经度 
   * @return (int)s   返回距离(单位千米或公里)
   */
  distance: function (la1, lo1, la2, lo2) {
    var La1 = la1 * Math.PI / 180.0;
    var La2 = la2 * Math.PI / 180.0;
    var La3 = La1 - La2;
    var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    s = s.toFixed(2);
    return s;
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        currentPage: that.data.currentPage + 1
      });
      that.queryStoreInfo(that.data.latitude, that.data.longitude);
    }
  }
})