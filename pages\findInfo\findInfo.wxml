<!--引入模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<!--引入WXS方法-->
<wxs module="filters" src="../../wxs/addmul.wxs" />
<view style="background:#fff">
  <!--投票-->
  <view wx:if="{{result.voteList}}" style="padding-top:20rpx; width:690rpx;margin:0px auto;">
    <view wx:if="{{!result.isVote}}" wx:for="{{result.voteList}}" wx:for-item="vote" wx:key="index" bindtap="setVote"
      data-id="{{vote.id}}"
      style="border:2rpx solid #707070;background: #FFF;width:99%;height:88rpx;border-radius:50rpx;margin-bottom:20rpx;line-height:88rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 500;color: #333333;">
      <span style="padding-left:20rpx;">{{vote.optionIndex}}.</span> {{vote.optionComment}}
    </view>
    <progress wx:if="{{result.isVote}}" wx:for="{{result.voteList}}" wx:for-item="vote1" wx:key="index" bindtap="setVote1"
      style="position: relative;margin-bottom:20rpx;height:88rpx" percent="{{filters.toFix1(vote1.voteNum/result.allVoteNum*100)}}"
      font-size="{{fontSize}}" duration="10" show-info border-radius="{{borderRadius}}" stroke-width="{{strokeWidth}}" active
      activeColor="{{vote1.activeColor}}"><text
        style="position: absolute;top:28rpx;left:10rpx;font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #333333;">{{vote1.optionIndex}}.{{vote1.optionComment}}</text></progress>
    <view style="color:grey;font-size:22rpx;">{{result.allVoteNum}}人参与投票</view>
  </view>
  <!--图片轮播-->
  <view wx:if="{{result.imagesList}}">
    <swiper style="height:750rpx;margin-bottom:10rpx;" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}"
      interval="{{interval}}" duration="{{duration}}" circular="{{circular}}">
      <block wx:for="{{result.imagesList}}" wx:key="index">
        <swiper-item>
          <image lazy-load='true' src="{{item.imagesUrl}}" style="width:{{imgwidth}}rpx;height:750rpx;"
            bindtap="previewImage" mode="widthFix" bindload="imageLoad" data-list="{{result.imagesList}}"
            data-src="{{item.imagesUrl}}" />
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!--视频-->
  <view wx:elif="{{result.findVideo}}">
    <video style="width:100%;height:650rpx;" src='{{result.findVideo.videoUrl}}' autoplay='true' loop='true'
      muted='true' object-fit='contain' show-mute-btn='true' vslide-gesture='true' title='{{result.findInfo.content}}'
      enable-play-gesture='true'></video>
  </view>
  <view style="width:690rpx;margin:0px auto;">
    <!-- 推荐栏 -->
    <view class="find_five" wx:if="{{result.relationList.length>0}}" style="height:120rpx;">
      <scroll-view wx:if="{{result.relationList}}" class="tab" scroll-x scroll-with-animation="true">
        <block wx:for-item="relation" wx:for="{{result.relationList}}" wx:key="index">
          <view class="tab-item" data-current="{{index}}">
            <view bindtap="imageClick" data-commodityid="{{relation.relationId}}"
              style="display:flex; align-items: center;justify-content:center;">
              <view
                style="width:30%;height:120rpx;border:0px solid red;display:flex; align-items: center;justify-content:center;">
                <image lazy-load='true' style="width:80rpx;height:80rpx" src="{{relation.relationImageurl}}"
                  mode="widthFill"></image>
              </view>
              <view style="width:60%;border:0px solid red;height:80rpx;">
                <view style="height:50%;font-family: PingFang SC;font-weight: 500;color: #333333;font-size:28rpx;">
                  {{filters.sub(relation.relationTitle,10)}}</view>
                <view style="height:50%;font-size: 24rpx;font-family: PingFang SC;font-weight: 500;color: #FF7E00;">
                  {{filters.subPre(relation.relationExplain)}}</view>
              </view>
              <view
                style="width:10%;height:120rpx;border:0px solid red;display:flex; align-items: center;justify-content:center">
                <image lazy-load='true' style="width:45rpx;height:auto" src="{{find_arrow}}" mode="widthFix"></image>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
    <view class="find_five"
      style="margin-top:10rpx;font-size: 30rpx;font-family: PingFang SC;line-height: 50rpx;color: #000; ">
      <view class="textFour_box" bindtap="toFindinfo" data-findinfo="{{result}}">
        {{result.findInfo.content}}
      </view>
    </view>
    <view class="find_five" style="height:80rpx;display:flex; align-items: center;justify-content:center">
      <view
        style="width:50%;border:0px solid red;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;color: #525252;">
        {{time}}</view>
      <view style="width:50%;border:0px solid red;display:flex; align-items: center;justify-content:center;">
        <!-- <view style="width:34%;border:0px solid red;display:flex;">
          <image lazy-load='true' wx:if="{{result.isAgree}}" style="width:39rpx;height:39rpx;" bindtap="likeOrCancel"
            data-isragree="0" data-id="{{result.findInfo.id}}" src="{{find_agree}}"></image>
          <image lazy-load='true' wx:else style="width:39rpx;height:39rpx;" bindtap="likeOrCancel" data-isragree="1"
            data-id="{{result.findInfo.id}}" src="{{find_notagree}}"></image>
          <span
            style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{result.findInfo.agreeNum}}</span>
        </view> -->
        <view wx:if="{{result.isAgree}}"
            style="width:50%;border:0px solid red;display:flex; align-items: center;justify-content:center;"
            catchtap="likeOrCancel" data-isragree="0"  data-id="{{result.findInfo.id}}">
            <view style="border:0px solid red;display:flex;">
              <image  lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_agree}}"  ></image>        
              <span
                style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{result.findInfo.agreeNum}}</span>
            </view>
          </view>

          <view wx:else style="width:50%;border:0px solid red;display:flex; align-items: center;justify-content:center;"
            catchtap="likeOrCancel" data-isragree="1" data-index="{{index1}}" data-id="{{result.findInfo.id}}">
            <view style="border:0px solid red;display:flex;">
              <image lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_notagree}}"></image>
              <span
                style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{result.findInfo.agreeNum}}</span>
            </view>
          </view>
        <view style="width:34%;border:0px solid blue;display:flex;" bindtap="toComment" bindtouchstart="bindTouchStart"
          bindtouchend="bindTouchEnd" data-findinfoid="{{result.findInfo.id}}" data-index="{{index}}">
          <image lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_comment}}">
          </image>
          <span
            style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{result.findInfo.commentNum}}</span>
        </view>
        <view style="width:34%;display:flex;">
          <button open-type="share"
            style="width:45rpx;font-size: 28rpx;background-color: #fff;border: none;padding: 0;margin: 0;line-height: 1;"
            data-findid="{{result.findInfo.id}}" data-time="{{time}}">
            <image lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_share}}"></image>
          </button>
          <view
            style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;color: #333333;padding-top:-20rpx;">
            {{result.findInfo.forwardNum}}</view>
        </view>
        <!--<span
            style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{item.findInfo.forwardNum}}</span> -->
      </view>
    </view>
  </view>
</view>
<view wx:if="{{result.commentList.length>0}}"
  style="margin-top:40rpx;border-radius: 32rpx 32rpx 0px 0px;background:#fff;">
  <view
    style="width:100%;height:150rpx;text-align:center;line-height:150rpx;font-size: 32rpx;font-family: PingFang SC;font-weight: 500;color: #323334;">
    全部评论</view>
  <view style="width:690rpx;display:flex; justify-content: center;margin:0px auto;margin-bottom:40rpx;"
    wx:for="{{result.commentList}}" wx:key="index" wx:if="{{item.isDelete!=1}}">
    <view style="width:10%;margin-top:10rpx;">
      <image lazy-load='true' style="width:60rpx;height:60rpx;border-radius:50%" src="{{item.userHead}}"
        mode="widthFix"></image>
    </view>
    <view style="width:80%; " bindtouchstart="bindTouchStart" bindtouchend="bindTouchEnd" bindtap="toComment"
      bindlongtap="toReportOrdel" data-comment="{{item}}" data-index="{{index}}">
      <view style="font-size: 28rpx;font-family: PingFang SC;font-weight: 400;color: #525252;">{{item.userName}}
      </view>
      <view style="font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #919398;">{{item.createTime}}
      </view>
      <view wx:if="{{!item.replyId}}">
        <view style="font-size: 28rpx;font-family: PingFang SC;font-weight: 400;color: #525252;">{{item.commentText}}
        </view>
      </view>
      <view wx:if="{{item.replyId}}">
        <text class="noblueColor"> 回复 </text> <text class="blueColor">{{item.replyUserName}}:</text><text
          style="font-size: 28rpx;font-family: PingFang SC;font-weight: 400;color: #525252;">{{item.commentText}}
        </text>
      </view>
    </view>
    <view style="margin-top:20rpx;width:10%;">
      <view style="display:flex;" wx:if="{{item.userList==0}}" bindtap="commentLike" data-commentid="{{item.id}}"
        data-isragree="1" data-index="{{index}}">
        <view style="width:34rpx;height:34rpx;">
          <image lazy-load='true' style="width:34rpx;height:34rpx;" mode="widthFix" src="{{comnotagree}}" />
        </view>
        <view
          style="padding-left:10rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 400;color: #919398;line-height:35rpx;">
          赞</view>
      </view>
      <view style="display:flex;" wx:if="{{item.userList==1}}" bindtap="commentLike" data-commentid="{{item.id}}"
        data-isragree="0" data-index="{{index}}">
        <view style="width:34rpx;height:34rpx;">
          <image lazy-load='true' style="width:34rpx;height:34rpx;" mode="widthFix" src="{{comagree}}" />
        </view>
        <view
          style="padding-left:10rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 400;color: #919398;line-height:35rpx;">
          {{item.agreeNum}}</view>
      </view>
    </view>
  </view>
  <view style="height:20rpx;"></view>
</view>
<template wx:if="{{state1}}" is="comment" data="{{...item}}" />
<template wx:if="{{state2}}" is="reportOrdel" data="{{...item}}" />
<load-more id="loadMoreView" bindloadMoreListener='loadMoreListener' bindclickLoadMore='clickLoadMore'></load-more>