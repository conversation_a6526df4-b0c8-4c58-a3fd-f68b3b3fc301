var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var wxMarkerData = [];
Page({
  data: {
    second_height: 1050,
    classify_banner: app.imageUrl + 'activity/classify_banner.png',
    categoryBean: [],
    commodityList: [],
    pic_bg: app.imageUrl + 'classify_bg.png',
    categoryBean: [],
    isFromBack: false,
    currpage: 1,
    pageSize: 8,
    categoryId: "",
    searchLoading: true,
    defaultGoodsMainImage: app.shareImageUrl,
    showBall: false,
    classifyShowType: 1,
    skuShowHidden: true,
    cart: app.imageUrl + 'order/shopcart.png',
    soldOut: app.imageUrl + 'soldOut.png',
    tmpCompany: '',
    scan: app.imageUrl + 'scan.png',
    emptyClassify: app.imageUrl + 'emptyClassify.png',
    emptyGoods: app.imageUrl + 'emptyGoods.png',
    buyOmNum: 0,
    buyOtNum: 0,
    skuId: "",
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
    shop_cart1: app.imageUrl + 'shop_cart1.png',
    shop_cart2: app.imageUrl + 'shop_cart2.png',
    disLikeGoods: app.imageUrl + 'disLikeGoods.png',
    casualGoods: app.imageUrl + 'casualGoods.png',
    popularGoods: app.imageUrl + 'popularGoods.png',
    cheapGoods: app.imageUrl + 'cheapGoods.png',
    tagImg5: app.imageUrl + 'tagImg5.png',
    tagImg6: app.imageUrl + 'tagImg6.png',
    tagImg7: app.imageUrl + 'tagImg7.png',
    tagImg8: app.imageUrl + 'tagImg8.png',
    c_disLikeGoods: app.imageUrl + 'c_disLikeGoods.png',
    c_casualGoods: app.imageUrl + 'c_casualGoods.png',
    c_popularGoods: app.imageUrl + 'c_popularGoods.png',
    c_cheapGoods: app.imageUrl + 'c_cheapGoods.png',
    c_tagImg5: app.imageUrl + 'c_tagImg5.png',
    c_tagImg6: app.imageUrl + 'c_tagImg6.png',
    c_tagImg7: app.imageUrl + 'c_tagImg7.png',
    c_tagImg8: app.imageUrl + 'c_tagImg8.png',
    tagList: [],
    topLeft: '',
    topPage: '',
    tagHidden: true,
    currentTag: 0,
    currentPage:1
  },
  fastSelectGoodsTypeBindTap: function (e) {
    var that = this;
    //1:按分类查找 2：综合排序 3：销量倒序排序 4：价格倒序 5：价格正序 6:全部分类 7.库存倒序 8.库存顺序
    var type = e.currentTarget.dataset.type;
    var fastType = that.data.fastType;
    if (fastType == 4 && type == 4) {
        type = 5;
    }
    if (fastType == 5 && type == 5) {
        type = 4;
    }
    if (fastType == 7 && type == 7) {
      type = 8;
    }
    if (fastType == 8 && type == 8) {
        type = 7;
    }
    switch (parseInt(type)) {
      case 2: //2：综合排序
          wx.reportEvent("dbb_fast_looking_for_goods_comprehensive", {
              "user_id": app.getUserId(),
              "telephone": app.getTelephone(),
              "supplier_id": app.getExtCompanyId(),
              "supplier_name": app.getExtStoreName()
          })
          var fastCategoryId = that.data.categoryId;
          that.setData({
              fastType: 2,
              commodityList: [],
              currentPage: 1,
          })
          that.fastSelectGoodsType(fastCategoryId, "", "", "", "", "");
          break;
        case 3: //3：销量倒序排序
            wx.reportEvent("dbb_fast_looking_for_goods_sales_volume", {
                "user_id": app.getUserId(),
                "telephone": app.getTelephone(),
                "supplier_id": app.getExtCompanyId(),
                "supplier_name": app.getExtStoreName()
            })
            var fastCategoryId = that.data.categoryId;
            that.setData({
                fastType: 3,
                commodityList: [],
                currentPage: 1,
            })
            that.fastSelectGoodsType(fastCategoryId, 1, 2, "", "", "");
            break;
        case 4: //4：价格倒序
            wx.reportEvent("dbb_fast_looking_for_goods_price_desc", {
                "user_id": app.getUserId(),
                "telephone": app.getTelephone(),
                "supplier_id": app.getExtCompanyId(),
                "supplier_name": app.getExtStoreName()
            })
            var fastCategoryId = that.data.categoryId;
            that.setData({
                fastType: 4,
                commodityList: [],
                currentPage: 1,
            })
            that.fastSelectGoodsType(fastCategoryId, 2, 2, "", "", "");
            break;
        case 5: //5：价格正序
            wx.reportEvent("dbb_fast_looking_for_goods_price_asc", {
                "user_id": app.getUserId(),
                "telephone": app.getTelephone(),
                "supplier_id": app.getExtCompanyId(),
                "supplier_name": app.getExtStoreName()
            })
            var fastCategoryId = that.data.categoryId;
            that.setData({
                fastType: 5,
                commodityList: [],
                currentPage: 1,
            })
            that.fastSelectGoodsType(fastCategoryId, 2, 1, "", "", "");
            break;
        case 7: //7：库存倒序
            wx.reportEvent("dbb_fast_looking_for_goods_stock_dasc", {
                "user_id": app.getUserId(),
                "telephone": app.getTelephone(),
                "supplier_id": app.getExtCompanyId(),
                "supplier_name": app.getExtStoreName()
            })
            var fastCategoryId = that.data.categoryId;
            that.setData({
                fastType: 7,
                commodityList: [],
                currentPage: 1,
            })
            that.fastSelectGoodsType(fastCategoryId, 3, 2, "", "", "");
            break;
        case 8: //8：库存顺序
            wx.reportEvent("dbb_fast_looking_for_goods_stock_dasc", {
                "user_id": app.getUserId(),
                "telephone": app.getTelephone(),
                "supplier_id": app.getExtCompanyId(),
                "supplier_name": app.getExtStoreName()
            })
            var fastCategoryId = that.data.categoryId;
            that.setData({
                fastType: 8,
                commodityList: [],
                currentPage: 1,
            })
            that.fastSelectGoodsType(fastCategoryId, 3, 1, "", "", "");
            break;
        case 6: //6：全部分类
            wx.reportEvent("dbb_fast_looking_for_goods_all_type", {
                "user_id": app.getUserId(),
                "telephone": app.getTelephone(),
                "supplier_id": app.getExtCompanyId(),
                "supplier_name": app.getExtStoreName()
            })
            var goodsTypeList = that.data.goodsTypeList;
            goodsTypeList.forEach(e => {
                e.select = false;
            });
            that.setData({
                goodsTypeList: goodsTypeList,
                commodityList: [],
                currentPage: 1,
                fastType: 6,
                fastCategoryId: "",
                selectAllType: true
            });
            that.fastSelectGoodsType("", "", "", "", "", "");
            break;
        default:
            break;
    }
},
/**
     * 快速找货条件查询
     * @param {*} categoryId 商品类别ID
     * @param {*} sortAttr 1：按销量 2：按零售价
     * @param {*} sortDirection 1：升序 2：倒序 默认倒序
     * @param {*} tagArray 客户设置的商品标记码
     * @param {*} saleStateArray 销售品态码
     */
    fastSelectGoodsType: function (categoryId, sortAttr, sortDirection, tagArray, saleStateArray, commodityName) {
      var that = this;
      console.log('sortAttr: ' + sortAttr + ' / sortDirection: ' + sortDirection);
      wx.request({
          header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/orderGoods/fastSelectGoodsType',
          data: {
              "localcompanyId": app.getExtCompanyId(),
              "localstoreId": app.getExtStoreId(),
              "pageSize": that.data.pageSize,
              "currpage": that.data.currentPage,
              "categoryId": categoryId,
              "sortAttr": sortAttr,
              "sortDirection": sortDirection,
              "tagArray": tagArray,
              "saleStateArray": saleStateArray,
              "commodityName": commodityName
          },
          success: function (res) {
              wx.hideLoading();
              var commodityList = res.data.commodityBaseList;
              var totalRecords = res.data.totalRecords;
              var oldCommodityBaseList = [];
              if (that.data.currentPage > 1) {
                  oldCommodityBaseList = that.data.commodityList;
              }
              // 计算已显示的总数据量
              var showTotal = that.data.currentPage * that.data.pageSize;

              if (commodityList != null && commodityList.length > 0) {
                  commodityList = oldCommodityBaseList.concat(commodityList);
                  commodityList.forEach(e => {
                      if (parseFloat(e.omNum) + parseFloat(e.otNum) <= 0) {
                          e['empty'] = true;
                      } else {
                          e['empty'] = false;
                      }
                  });
              }
              else{
                //查找下一个ID
                that.getNextId(categoryId)                
                console.log(categoryId+"shishsissi");
                /*setTimeout(function(){
                  that.fastSelectGoodsType(that.data.nextId,sortAttr, sortDirection, tagArray, saleStateArray, commodityName);
                },1500)*/
                return;
              }
              that.setData({
                  commodityList: commodityList,
                  totalHidden: showTotal < totalRecords ? true : false //检查是否已加载所有数据
              });
          }
      })
  },
  getNextId:function(id){
    var that = this;
    var flag = false;
    //that.fastSelectGoodsType("", "", "", "", "", "");
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtStoreId(),
      },
      url: app.projectName + '/applet/goods/queryGoodsType',
      success: function (res) {
        var categoryBean = res.data.categoryBean;
        for(var i=0; i<categoryBean.length; i++){
          // 检查一级分类
          if(categoryBean[i]. categoryId == id){
            if(categoryBean[i+1]){
              flag = true;
              that.selectGoodsTypeBindTapMore(categoryBean[i+1].categoryId);
            }
            break;
          }

          // 检查二级分类
          if (categoryBean[i].children) {
            for (var j = 0; j < categoryBean[i].children.length; j++) {
              if (categoryBean[i].children[j].categoryId == id) {
                // 当前分类有下一个二级分类
                if (j + 1 < categoryBean[i].children.length) {
                  flag = true;
                  that.selectGoodsTypeBindTapMore(categoryBean[i].children[j + 1].categoryId);
                } 
                // 当前分类是最后一个二级分类，则跳转到下一个一级分类
                else if (i + 1 < categoryBean.length && categoryBean[i + 1].children) {
                  flag = true;
                  that.selectGoodsTypeBindTapMore(categoryBean[i + 1].children[0].categoryId);
                }
                break;
              }
            }
            if (flag) break;
          }
        }
        
        // 如果没有找到下一个分类，跳转到第一个一级分类
        if(!flag){
          that.selectGoodsTypeBindTapMore(categoryBean[0].categoryId);
        }
      }
    })
  },
  selectGoodsTypeBindTapMore: function (obj) {
    var that = this;
    var id = obj;
    var categoryBean = that.data.categoryBean;

    // 设置选中的分类
    for (var i = 0; i < categoryBean.length; i++) {
      categoryBean[i].select = false;
      // 检查一级分类
      if (categoryBean[i].categoryId == id) {
        categoryBean[i].select = true;
        that.setData({
          adPic: that.data.categoryBean[i].adPic
        })
        break;
      }

      // 检查二级分类
      if (categoryBean[i].children) {
        for (var j = 0; j < categoryBean[i].children.length; j++) {
          categoryBean[i].children[j].select = false;
          if (categoryBean[i].children[j].categoryId == id) {
            // 一级分类被选中
            categoryBean[i].select = true;
            // 当前二级分类被选中
            categoryBean[i].children[j].select = true;
            that.setData({
              adPic: categoryBean[i].adPic
            });
            break;
          }
        }
      }
    }
    that.setData({
      currpage: 1,
      currentPage:1,
      categoryBean: categoryBean,
      commodityList: [],
      categoryId:id,
      fastType:1
    });
    //that.queryGoodsByTypeId(id);
    that.fastSelectGoodsType(id, "", "", "", "", "");
  },
  imageClick:function(e){
    var goodsId = e.currentTarget.dataset.commodityid;
    var name = e.currentTarget.dataset.name;
    var pic = e.currentTarget.dataset.pic;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  getTagPress: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var tag = e.currentTarget.dataset.tag;
    that.setData({
      topLeft: e.detail.x,
      topPage: e.detail.y,
      tagHidden: false,
      tagCommodityId: id,
      currentTag: tag
    })
  },
  clientSignCommodityBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var code = e.currentTarget.dataset.code;
    var tagCommodityId = that.data.tagCommodityId;
    app.clientSignCommodity(tagCommodityId, code).then(res => {
      if (res) {
        that.setData({
          commodityList: [],
          currpage: 1
        })
        that.queryGoodsByTypeId(that.data.categoryId);
      }
      that.setData({
        tagHidden: true
      })
    });
  },
  hiddenTagHiddenBindTap: function () {
    this.setData({
      tagHidden: true
    })
  },
  initGoodsClassify: function () {
    var that = this;
    app.init_getExtMessage().then(res => {
      that.setData({
        tmpCompany: res.companyId,
      })
      app.getSupplierSetting(res.companyId);
      that.getSecondHeight();
      that.queryClassifyTempalte(res.storeId, res.companyId);
      that.queryGoodsType(res.storeId, res.companyId);
    });
  },
  onLoad: function (option) {
    var that = this;
    that.initGoodsClassify();
  },
  onShow: function (option) {
    var that = this;
    if (that.data.isFromBack) {
      if (app.classifyData.classifyId != null) {
        that.initGoodsClassify();
      }
    } else {
      that.setData({
        isFromBack: true
      })
    }
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,
        selected: 5,
        shopCartNum: wx.getStorageSync('shopCartNum'),
        color: app.globalData.bottomBean.color,
        selectedColor: app.globalData.bottomBean.selectedColor
      })
    }
  },
  /**
   * 扫码识别商品
   */
  sweepCodeBindTap: function () {
    var that = this;
    wx.scanCode({
      success(res) {
        var filterName = res.result;
        app.navigateToPage('/pages/searchPage/searchPage?filterName=' + filterName);
      }
    })
  },
  /**
   * 查询分类模版
   */
  queryClassifyTempalte: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      //url: app.projectName + '/classifyTemplate/queryClassifyTempalte',
      //url: app.projectName + '/classifyTemplate/queryWechatClassAdvertisementManager',
      url: app.projectName + '/classifyTemplate/queryClassifyTempalteNew',
      success: function (res) {
        var showType = res.data.templateManagerEntity.classStyle;
        var adOpen = res.data.templateManagerEntity.adOpen;
        var scan = res.data.templateManagerEntity.scan;
        var shopStyle = res.data.templateManagerEntity.shopcartStyle
        that.setData({
          classifyShowType: showType,
          adOpen: adOpen,
          scanShow: scan,
          shopStyle: shopStyle
        });
      }
    })
  },
  getSecondHeight: function () {
    var that = this
    // 获取系统信息
    wx.getSystemInfo({
      success: function (res) {
        // 计算主体部分高度,单位为px
        that.setData({
          // second部分高度 = 利用窗口可使用高度 - first部分高度（这里的高度单位为px，所有利用比例将300rpx转换为px）
          //second_height: res.windowHeight - res.windowWidth / 750 * 80
          //second_height: res.windowHeight * 750 / res.windowWidth - 20 * 750 / res.windowWidth
        })
      }
    })
    console.log(that.data.second_height+"iiiiii");
  },
  /**
   * 点击搜索框跳转
   */
  searchBindFocus: function () {
    app.navigateToPage('/pages/searchPage/searchPage');
  },
  /**
   * 跳转到二级分类商品详情
   */
  secondCategoryBind: function (e) {
    var categoryId = e.currentTarget.dataset.id;
    app.navigateToPage("/pages/secondCategory/secondCategory?categoryId=" + categoryId);
  },
  //
  /**
   * 跳转到商品详情
   */
  imageClickBind: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId + "&storeId=" + app.storeId);
  },
  /**
   * 查询商品分类
   */
  queryGoodsType: function (storeId, companyId) {
    var that = this;
    //that.fastSelectGoodsType("", "", "", "", "", "");
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      url: app.projectName + '/applet/goods/queryGoodsType',
      success: function (res) {
        var categoryBean = res.data.categoryBean;
        that.setData({
          overallStock: res.data.overallStock,
          stockBean: res.data.stockBean,
          categoryBean: categoryBean,
          categoryId:categoryBean[0].categoryId,
          fastType:1
        });
        that.queryClassifyAd(companyId, categoryBean);
        var typeId = app.classifyData.classifyId;
        if (typeId != null && typeId != '') {
          that.queryFirstClassify(typeId);
        } else {
          //that.queryGoodsByTypeId(categoryBean[0].categoryId);
          that.fastSelectGoodsType(categoryBean[0].categoryId, "", "", "", "", "");
        }
      }
    })
  },
  /*根据分类查询对应的广告图片*/
  queryClassifyAd: function (companyId, categoryBean) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": companyId
      },
      url: app.projectName + '/classifyTemplate/queryWechatClassAdvertisementManager',
      success: function (res) {
        var adList = res.data.returnList;
        for (var j = 0; j < categoryBean.length; j++) {
          for (var i = 0; i < adList.length; i++) {
            if (adList[i].applyGroup == 0) { /*表示应用在全部分类*/
              categoryBean[j].adPic = adList[i].adImage;
            }
            else if (adList[i].applyGroup == 1) {
              var adItem = adList[i].groupId;
              var itemList = adItem.split("#");
              for (var k = 0; k < itemList.length; k++) {
                if (itemList[k] == categoryBean[j].categoryId) {
                  categoryBean[j].adPic = adList[i].adImage;
                }
              }
            }
          }
        }
        that.setData({
          categoryBean: categoryBean
        });
      }
    })
  },
  /*首页选中一级分类*/
  queryFirstClassify: function (id) {
    var that = this;
    var categoryBean = that.data.categoryBean;
    for (var i = 0; i < categoryBean.length; i++) {
      if (categoryBean[i].categoryId == id) {
        if (i == 0) {
          categoryBean[i].select = categoryBean[i].select;
        } else {
          categoryBean[i].select = !categoryBean[i].select;
        }
      } else {
        categoryBean[i].select = false;
      }
    }
    that.setData({
      categoryBean: categoryBean,
      commodityList: [],
      fastType:1
    });
    that.queryGoodsByTypeId(id);
  },
  /**
   * 选择一级分类
   */
  selectGoodsTypeBindTap: function (e) {
    var that = this;
    var id = e.target.dataset.id;
    var categoryBean = that.data.categoryBean;
    for (var i = 0; i < categoryBean.length; i++) {
      if (categoryBean[i].categoryId == id) {
        categoryBean[i].select = !categoryBean[i].select;
        that.setData({
          adPic: that.data.categoryBean[i].adPic
        })
      } else {
        categoryBean[i].select = false;
      }
    }
    that.setData({
      currpage: 1,
      currentPage:1,
      categoryBean: categoryBean,
      commodityList: [],
      categoryId:id,
      fastType:1
    });
    //that.queryGoodsByTypeId(id);
    that.fastSelectGoodsType(id, "", "", "", "", "");
  },
  /**
   * 选择二级分类
   */
  selectTwoGoodsTypeBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var categoryBean = that.data.categoryBean;
    for (var i = 0; i < categoryBean.length; i++) {
      var childCateBean = categoryBean[i].children;
      for (var j = 0; j < childCateBean.length; j++) {
        if (childCateBean[j].categoryId == id) {
          childCateBean[j].select = true;
        } else {
          childCateBean[j].select = false;
        }
      }
    }
    that.setData({
      categoryBean: categoryBean,
      commodityList: [],
      currpage: 1,
      currentPage:1,
      categoryId:id,
      fastType:1
    });
    //that.queryGoodsByTypeId(id);
    that.fastSelectGoodsType(id, "", "", "", "", "");
  },
  addGoodsToShopCartBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var commodityId = e.currentTarget.dataset.commodityid;
    var sku = e.currentTarget.dataset.sku;

    var commodityBaseList = that.data.commodityList;
    for (var i = 0; i < commodityBaseList.length; i++) {
      if (commodityBaseList[i].commodityId == commodityId) {
        var commodityName = commodityBaseList[i].commodityName;
        var otPrice = commodityBaseList[i].goodsPrice;
        var omPrice = commodityBaseList[i].goodsOmPrice;
        var otUnit = commodityBaseList[i].commodityWeightUnit;
        var omUnit = commodityBaseList[i].commodityWeightUnitOm;
        var goodsStock = commodityBaseList[i].commodityVirtualStore;
        var commodityMainPic = commodityBaseList[i].commodityMainPic;
        var mySkuList = commodityBaseList[i].mySkuList;
        var commodityMultiple = commodityBaseList[i].commodityMultiple;
        that.setData({
          skuList: commodityBaseList[i].skuList,
          showskuAllAttrList: mySkuList,
          s_commodityName: commodityName,
          s_otPrice: otPrice,
          s_omPrice: omPrice,
          s_otUnit: otUnit,
          s_omUnit: omUnit,
          s_goodsStock: goodsStock,
          s_commodityMainPic: commodityMainPic,
          s_commodityId: commodityId,
          s_commodityMultiple: commodityMultiple,
          s_commodityUnitOtDefault: commodityBaseList[i].commodityUnitOtDefault,
          s_commodityUnitOmDefault: commodityBaseList[i].commodityUnitOmDefault,
          skuGoodsImage: commodityBaseList[i].commodityMainPic,
          skuShowHidden: false,
          skuId: ""
        });
        return;
      }
    }
    // if (sku > 0) {
    //   var commodityBaseList = that.data.commodityBaseList;
    //   for (var i = 0; i < commodityBaseList.length; i++) {
    //     if (commodityBaseList[i].commodityId == commodityId) {
    //       var mySkuList = commodityBaseList[i].mySkuList;
    //       that.setData({
    //         skuCommodityWeightUnit: commodityBaseList[i].commodityWeightUnit,
    //         skuList: commodityBaseList[i].skuList,
    //         showskuAllAttrList: mySkuList,
    //         skuGoodsName: commodityBaseList[i].commodityName,
    //         skuGoodsPrice: commodityBaseList[i].goodsPrice,
    //         skuGoodsImage: commodityBaseList[i].commodityMainPic,
    //         skuCommodityId: commodityBaseList[i].commodityId,
    //         commodityMoq: commodityBaseList[i].commodityMoq,
    //         skuShowHidden: false
    //       });
    //       return;
    //     }
    //   }
    // } else {
    //   var count = 1;
    //   var moq = e.currentTarget.dataset.moq;
    //   if (parseInt(moq) > 0) {
    //     count = moq;
    //   }
    //   that.addGoodsToShopCart(commodityId, count);
    // }
  },
  hiddeSkuAddGoodsShopCartBindTap: function () {
    var that = this;
    that.setData({
      skuShowHidden: true
    });
  },
  omMinusNumBindTap: function () {
    var that = this;
    if (that.data.buyOmNum > 0) {
      that.setData({
        buyOmNum: that.data.buyOmNum - 1
      });
    }
  },
  omPlusNumBindTap: function () {
    var that = this;
    that.setData({
      buyOmNum: that.data.buyOmNum + 1
    });
  },
  omNumBindInput: function (e) {
    var that = this;
    that.setData({
      buyOmNum: e.detail.value >= 0 ? e.detail.value : 0
    });
  },
  otMinusNumBindTap: function () {
    var that = this;
    if (that.data.buyOtNum > 0) {
      that.setData({
        buyOtNum: that.data.buyOtNum - 1
      });
    }
  },
  otPlusNumBindTap: function () {
    var that = this;
    that.setData({
      buyOtNum: that.data.buyOtNum + 1
    });
  },
  otNumBindInput: function (e) {
    var that = this;
    that.setData({
      buyOtNum: e.detail.value >= 0 ? e.detail.value : 0
    });
  },
  /**
   * 添加SKU商品到购物车
   */
  addSkuGoodsToShopCart: function () {
    var that = this;
    var updateSkuList = that.data.showskuAllAttrList;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (updateSkuList != null && updateSkuList.length > 0) {
      if (that.data.skuId == "" || that.data.skuId == null) {
        app.showModal({
          title: '提示',
          content: "请选择规格"
        });
        return;
      }
    }
    var otBuyNum = that.data.buyOtNum;
    var omBuyNum = that.data.buyOmNum;
    
    if (otBuyNum + omBuyNum == 0) {
      app.showModal({
        title: '提示',
        content: "购买数量不能为0"
      });
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/addRetailShppingCart',
      data: {
        "a_num": otBuyNum,
        "omNum": omBuyNum,
        "a_id": that.data.s_commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "skuId": that.data.skuId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "recommendUserId": app.recommendData.recommendUserId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          that.setData({
            skuShowHidden: true,
            buyOtNum: 0,
            buyOmNum: 0
          })
          app.countRetailCartTotal();
          setTimeout(function(){
            if (typeof that.getTabBar === 'function' &&
              that.getTabBar()) {
              that.getTabBar().setData({
                shopCartNum: wx.getStorageSync('shopCartNum')
              })
            }
          },500)
          wx.showToast({
            title: '添加成功',
            duration: 1000
          })
        } else {
          wx.showToast({
            title: '添加失败',
            duration: 1000
          })
        }
      }
    })
  },
  /*控制库存时,检查商品是否超过了库存数量*/
  checkStock: function () {
    var that = this;
    var openStock = that.data.stockBean.openStock;
    var overallStock = that.data.overallStock;
    var buyNum = that.data.buyOtNum + (that.data.buyOmNum * that.data.commodityMultiple);
    var commodityVirtualStore = that.data.commodityVirtualStore;
    var flag = true;
    console.log('777777'+commodityVirtualStore);
    if (openStock || overallStock == 1) {
      if (parseInt(buyNum) > parseInt(commodityVirtualStore)) {
        app.showModal({
          title: '提示',
          content: "购买数量超过库存"
        });
        flag = false;
      }
    }
    return flag;
  },
  changeSKUBindTap: function (e) {
    var that = this;
    var name = e.target.dataset.name;
    var childname = e.target.dataset.childname;
    var updateSkuList = that.data.showskuAllAttrList;
    for (var i = 0; i < updateSkuList.length; i++) {
      if (name == updateSkuList[i].skuAttrName) {
        for (var j = 0; j < updateSkuList[i].skuAttrValueList.length; j++) {
          if (childname == updateSkuList[i].skuAttrValueList[j].skuAttrName) {
            updateSkuList[i].skuAttrValueList[j].isSelect = true;
          } else {
            updateSkuList[i].skuAttrValueList[j].isSelect = false;
          }
        }
      }
    }
    that.setData({
      showskuAllAttrList: updateSkuList
    });
    that.bachGoodsPrice();
  },
  bachGoodsPrice: function () {
    var that = this;
    that.setData({
      skuId: ''
    })
    var selectSKUArray = [];
    var skuList = that.data.showskuAllAttrList;
    for (var i = 0; i < skuList.length; i++) {
      for (var j = 0; j < skuList[i].skuAttrValueList.length; j++) {
        if (skuList[i].skuAttrValueList[j].isSelect) {
          var skuBean = {};
          skuBean.name = skuList[i].skuAttrName;
          skuBean.value = skuList[i].skuAttrValueList[j].skuAttrName;
          selectSKUArray.push(skuBean);
        }
      }
    }

    var skuGoodsPrice = "";
    var skuGroup = that.data.skuList;
    for (var i = 0; i < skuGroup.length; i++) {
      var skuAttrList = skuGroup[i].skuAttrList;
      var count = 0;
      for (var j = 0; j < skuAttrList.length; j++) {
        var skuName = skuAttrList[j].skuName;
        var skuValue = skuAttrList[j].skuValue;
        var flag = false;
        for (var z = 0; z < selectSKUArray.length; z++) {
          var skuBean = selectSKUArray[z];
          if (skuName == skuBean.name && skuValue == skuBean.value) {
            flag = true;
            break;
          }
        }
        if (flag) {
          count++;
        } else {
          count--;
        }
      }
      if (count == selectSKUArray.length) {
        if (skuGroup[i].skuPrice != null && skuGroup[i].skuPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPrice;
        }
        if (skuGroup[i].skuPromotionPrice != null && skuGroup[i].skuPromotionPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPromotionPrice;
        }
        if (skuGroup[i].regionPriceList != null && skuGroup[i].regionPriceList.length > 0) {
          var goodsCount = that.data.buyCount + (that.data.buyOmCount * that.data.commodityMultiple);
          for (var k = 0; k < skuGroup[i].regionPriceList.length; k++) {
            var beginRegion = skuGroup[i].regionPriceList[k].beginRegion;
            var endRegion = skuGroup[i].regionPriceList[k].endRegion;
            var commoditySalePrice = skuGroup[i].regionPriceList[k].commoditySalePrice;
            if (goodsCount >= beginRegion && goodsCount <= endRegion) {
              skuGoodsPrice = commoditySalePrice;
              break;
            }
          }
        }
        var selectSkuStr = "";
        for (var m = 0; m < skuGroup[i].skuAttrList.length; m++) {
          selectSkuStr += skuGroup[i].skuAttrList[m].skuName + ":" + skuGroup[i].skuAttrList[m].skuValue + ";";
        }
        selectSkuStr = selectSkuStr.substring(0, selectSkuStr.length - 1);
        that.setData({
          selectSkuStr: selectSkuStr,
          s_otPrice: skuGoodsPrice,
          skuId: skuGroup[i].skuId,
          commodityVirtualStore: skuGroup[i].skuInventory
        });
        break;
      }
    }
  },
  /**
   * 购物车中增加商品
   */
  addGoodsToShopCart: function (commodityId, count) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "a_id": commodityId,
        "a_num": count,
        "skuId": "",
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "a_supplierId": app.getExtStoreId(),
        "recommendUserId": app.recommendData.recommendUserId
      },
      url: app.projectName + '/applet/shopCart/addRetailShppingCart',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: '添加成功',
            duration: 1000
          })
          app.countRetailCartTotal();
          setTimeout(function () {
            if (typeof this.getTabBar === 'function' &&
              this.getTabBar()) {
              this.getTabBar().setData({
                shopCartNum: wx.getStorageSync('shopCartNum')
              })
            }
            console.log(typeof this.getTabBar === 'function' &&
              this.getTabBar());
          }, 1000)
        } else {
          wx.showToast({
            title: '增加商品失败',
            duration: 1500
          })
        }
      }
    })
  },
  /**
   * 查询商品根据分类Id
   */
  queryGoodsByTypeId: function (categoryId) {
    wx.showLoading({
      title: '加载数据中',
      mask: true
    })
    var that = this;
    var oldCateId = that.data.categoryId;
    if (oldCateId != categoryId) {
      that.setData({
        currpage: 1
      });
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "categoryId": categoryId,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "pageSize": that.data.pageSize,
        "currpage": that.data.currpage,
      },
      url: app.projectName + '/applet/goods/queryGoodsByTypeId',
      success: function (res) {
        wx.hideLoading();
        for (var i = 0; i < that.data.categoryBean.length; i++) {
          var id = that.data.categoryBean[i].categoryId;
          if (id == categoryId) {
            that.setData({
              adPic: that.data.categoryBean[i].adPic
            })
          }
         
        }
        var newCommodityBaseList = res.data.commodityBaseList;
        var oldCommodityBaseList = that.data.commodityList;
        if (oldCateId == categoryId) {
          if (newCommodityBaseList != null && newCommodityBaseList.length > 0) {
            newCommodityBaseList = oldCommodityBaseList.concat(newCommodityBaseList);
          } else {
            newCommodityBaseList = oldCommodityBaseList;
          }
        }
        if (newCommodityBaseList != null && newCommodityBaseList.length > 0) {
          for (var i = 0; i < newCommodityBaseList.length; i++) {
            newCommodityBaseList[i].goodsPrice = parseFloat(newCommodityBaseList[i].goodsPrice.toFixed(2));
            newCommodityBaseList[i].cutOffThePrice = parseFloat(newCommodityBaseList[i].cutOffThePrice.toFixed(2));
          }
        }
        that.setData({
          commodityList: newCommodityBaseList,
          categoryId: categoryId
        });
        if (res.data.commodityBaseList == null || res.data.commodityBaseList.length == 0) {
          that.setData({
            searchLoading: false
          });
        } else {
          that.setData({
            searchLoading: true
          });
        }
        app.classifyData.classifyId = null;
      }
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  searchScrollLower: function () {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        currpage: that.data.currpage + 1,
        currentPage:that.data.currentPage + 1,
      });
      var fastType = that.data.fastType;
            switch (parseInt(fastType)) {
                case 1: //1:按分类查找
                    var fastCategoryId = that.data.categoryId;
                    that.fastSelectGoodsType(fastCategoryId, "", "", "", "", "");
                    break;
                case 2: //2：综合排序
                    var fastCategoryId = that.data.categoryId;
                    that.fastSelectGoodsType(fastCategoryId, "", "", "", "", "");
                    break;
                case 3: //3：销量倒序排序
                    var fastCategoryId = that.data.categoryId;
                    that.fastSelectGoodsType(fastCategoryId, 1, 2, "", "", "");
                    break;
                case 4: //4：价格倒序
                    var fastCategoryId = that.data.categoryId;
                    that.fastSelectGoodsType(fastCategoryId, 2, 2, "", "", "");
                    break;
                case 5: //5：价格正序
                    var fastCategoryId = that.data.categoryId;
                    that.fastSelectGoodsType(fastCategoryId, 2, 1, "", "", "");
                    break;
                case 6: //6：全部分类
                    that.fastSelectGoodsType("", "", "", "", "", "");
                    break;
                default:
                    break;
            }
      
    }
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.getSecondHeight();
    this.queryGoodsType(app.getExtStoreId(), app.getExtCompanyId());
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/goods_classify/goods_classify',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})
