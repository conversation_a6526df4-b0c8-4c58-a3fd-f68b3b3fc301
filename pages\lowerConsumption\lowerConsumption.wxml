<view class="contant_box">

  <!-- 搜索框 -->
  <view class='search_box'>
    <icon type="search" size='16' />
    <input placeholder='请输入关键字' bindinput='searchBindInput'></input>
    <text bindtap='searchBindTap'>搜索</text>
  </view>
  <!-- 搜索框 -->
  <scroll-view class='contant-box'>

    <view class='contantBox contantBox2'>
      <label>姓名</label>
      <label>本月消费</label>
      <label>历史消费</label>
      <label>下级消费</label>
    </view>
    <block wx:for="{{cbList}}" wx:for-item="cb" wx:key="">
      <view class='contantBox'>
        <label>{{cb.userName}}</label>
        <label class='price-box'>{{cb.actualConsumption}}元</label>
        <label class='price-box'>{{cb.userTotalConsumption}}元</label>
        <label class='price-box' data-id='{{cb.userId}}' bindtap='showSecondWindowBindTap'>查看</label>
      </view>
    </block>

  </scroll-view>

  <!--弹出黑色弹窗-->
  <view class="black_bg" hidden='{{secondWindowHidden}}'>
  </view>

  <!--二级消费数据-->
  <view class="second_consumption" hidden='{{secondWindowHidden}}'>
    <icon class="page-dialog-close" bindtap='closeSecondConsumptionBindTap' type="clear" size='20' color='#666' />
    <view class='s_contantBox s_contantBox2'>
      <label>姓名</label>
      <label>本月消费</label>
      <label>历史消费</label>
    </view>
    <block wx:for="{{secondCbList}}" wx:for-item="cb" wx:key="">
      <view class='s_contantBox'>
        <label>{{cb.userName}}</label>
        <label class='price-box'>{{cb.actualConsumption}}元</label>
        <label class='price-box'>{{cb.userTotalConsumption}}元</label>
      </view>
    </block>
  </view>

</view>