var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    giftCardNo:"",
    telephone: "",
    smsCode: "", //短信验证码
    second: 60, //倒计时秒数
    secondDesc: "获取验证码",
    isSend: false,
  },
  bindGiftCardInput:function(e){
    var that = this;
    that.setData({
      giftCardNo: e.detail.value
    })
  },
  bindTelephoneInput: function (e) {
    var that = this;
    that.setData({
      telephone: e.detail.value
    })
  },
  smsCodeInput:function(e){
    var that = this;
    that.setData({
      smsCode: e.detail.value
    })
  },
  getsmsCodeBindtap:function(){
    var that = this;
    var telephone = that.data.telephone.replace(/\s+/g, '');
    if (telephone == "" || telephone == null) {
      app.showModal({
        title: '提示',
        content: "请输入手机号码获取"
      });
      return;
    }
    else if (telephone.length < 11) {
      wx.showToast({
        icon: 'none',
        title: '手机号长度有误',
        duration: 1500
    })
      return;
    }
    if (!that.data.isSend) {
      wx.request({
        url: app.giftCardProjectName + '/api/querySMSCode',
        data: {
          "telephone":telephone,
          "companyId":app.getExtCompanyId()
        },
        success: function (res) {
          var code = res.data.errorcode;
          var message = res.data.errormsg;
          if (code == 1000) {
            wx.showToast({
              icon: 'none',
              title: '短信获取成功',
              duration: 1500
            })
            that.setData({
              isSend: true
            })
            that.countdown(that);
          } else {
            app.showModal({
              title: '提示',
              content: message == "" ? "系统异常，稍后在试" : message
            });
            return;
          }
        }
      })
    }
  },
  /*获取openId*/
  init_userOpenId: function () {
    var that = this;
    return new Promise(function (resolve, reject) {
      wx.login({
        success: function (res) {
          if (res.code) {
            that.init_getExtMessage().then(result => {
              wx.request({
                header: {
                  'content-type': 'application/x-www-form-urlencoded' // 默认值
                },
                method: "POST",
                url: that.projectName + '/applet/jscode2session',
                data: {
                  "code": res.code,
                  "companyId": result.companyId,
                  "storeId": result.storeId,
                  "withEncryption":1
                },
                success: function (res) {
                  var openid = res.data.openid;
                  that.getUserId(openid);
                }
              })
            })
          }
        }
      })
    });

  },
  /**
  * 倒计时开始
  */
  countdown: function (that) {
    var second = that.data.second;
    if (second == 0) {
      that.setData({
        secondDesc: "获取验证码",
        second: 60,
        isSend: false
      });
      return;
    }
    var time = setTimeout(function () {
      that.setData({
        second: second - 1,
        secondDesc: second + "秒后重新获取"
      });
      that.countdown(that);
    }, 1000)
  },
  /**
   * 查询当前输入的卡券
   */
  verfiyBindTap: function () {
    var that = this;
    var telephone = that.data.telephone.replace(/\s+/g, '');
    var smsCode = that.data.smsCode.replace(/\s+/g, '');
    var giftCardNo = that.data.giftCardNo.replace(/\s+/g, '');
    if (giftCardNo == null || giftCardNo == "" || giftCardNo.length == 0) {
      wx.showToast({
        icon: 'none',
        title: '请输入礼券兑换码',
        duration: 1500
      })
      return;
    }
    if (telephone == null || telephone == "" || telephone.length == 0) {
      wx.showToast({
        icon: 'none',
        title: '请输入手机号码',
        duration: 1500
      })
      return;
    }
    if (smsCode == null || smsCode == "" || smsCode.length == 0) {
      wx.showToast({
        icon: 'none',
        title: '请输入短信验证码',
        duration: 1500
      })
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.giftCardProjectName + '/api/writeOffCardQuery',
      data: {
        "companyId": app.getExtCompanyId(),
        "cardPwd": giftCardNo,
        "smscode": smsCode,
        "phone": telephone
      },
      success: function (res) {
        var errorcode = res.data.errorcode;
        var errormsg = res.data.errormsg;
        if (errorcode == 1000){
          var result = res.data.result;
          var cardName = result.cardName;
          var cardImg = result.imageUrl;
          var cardNo = result.cardNo;
          var remark = result.remark;
          app.navigateToPage('/pages/giftCardDetail/giftCardDetail?cardName=' + cardName + "&cardImg=" + cardImg + "&cardNo=" + cardNo + "&remark=" + remark + "&cardPwd=" + that.data.giftCardNo + "&phone=" + that.data.telephone);
        }
        else{
          wx.showToast({
            icon:'none',
            title: errormsg,
            duration: 1500
          })
          return;
        }
      }
    })
  },
  /**
   * 查询卡券历史记录信息
   */
  historyBindTap: function () {
    app.navigateToPage('/pages/giftCardList/giftCardList');
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    } else {
      app.init_getExtMessage().then(res => {
        that.showGiftBg(res.storeId, res.companyId);
      });
    }
  },
  showGiftBg:function(storeId,companyId){
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      url: app.projectName + '/personalCenterTemplate/queryNewPersonalCenterTempalte',
      success: function (res) {
        var openStoreTime = res.data.openStoreTime;
        that.setData({
          openStoreTime: openStoreTime
        });
        var returnResult = res.data.returnResult;
        var floorContent = returnResult.floorContent;
        for (var i = 0; i < floorContent.length; i++) {
          if (floorContent[i].floorNum == 7) {/*代表的是头部颜色*/
            var giftCover = floorContent[i].giftCover;
            that.setData({
              giftCover:giftCover
            })
          }
        }
       
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  /**
 * 用户点击右上角分享
 */
  onShareAppMessage: function () {

  }
})