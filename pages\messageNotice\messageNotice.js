const app = getApp();
var WxParse = require('../../components/wxParse/wxParse.js');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        order_none: app.imageUrl + 'order/order_none.png',
        messageList:[],
        isShow:true,
        avatarUrl:'',
        currentPage:1,
        pageSize:10,
        cList:[],
        eList:[],
        commentImage:app.imageUrl + 'find_comment.png',
        chatMessage:app.imageUrl + 'chatMessage.png',
        loveMessage:app.imageUrl + 'loveMessage.png',
        evalueData:'',
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this;
        that.setData({
            avatarUrl: app.getExtStoreImage(),
        })
        that.getSystemMessage();
        that.queryUnread();
        that.queryRichText();
    },
    /*获取所有的系统消息*/
    getSystemMessage:function(){
      var that = this;
      var data = {};
      data["tm"] = "/terminalRetailInfo/noticeList";
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      data["busiType"] = 1;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/openservice',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
            console.log(res.data.data);
            var resData = JSON.parse(res.data.data);
            var resultList = resData.resultList;
            that.setData({
              messageList:resultList,
              
            });
          } 
        },
        fail: function () {
          wx.hideLoading();
        }
      })
    },
    queryUnread:function(){
      var that = this;
      var data = {};
      data["tm"] = "/terminalRetailInfo/noticeNewNum";
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["busiType"] = 1;
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/openservice',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
            var resData = JSON.parse(res.data.data);
            that.setData({
              unreadData:resData,
              
            });
          } 
        },
        fail: function () {
          wx.hideLoading();
        }
      })

    },
    formatRichText:function(html){
      let newContent= html.replace(/]*>/gi,function(match,capture){
        match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
        match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
        match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
        return match;
      });
      newContent = newContent.replace(/style="[^"]+"/gi,function(match,capture){
        match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;');
        return match;
      });
      newContent = newContent.replace(/]*\/>/gi, '');
      newContent = newContent.replace(/\<img/gi, '<img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"');
      return newContent;
    },
    queryRichText:function(){
      var that = this;
      var data = {};
      data["tm"] = "/terminalRetailInfo/chanceList";
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["busiType"] = 1;
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/openservice',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
            var cResult = [];
            var resData = JSON.parse(res.data.data);
            var resultList = resData.resultList;
            for(let i=0;i<resultList.length;i++){
              that.queryRichEvaluate(resultList[i].messageId);
              var c_html = decodeURIComponent(resultList[i].messageContent).replaceAll("+"," ");
              cResult.push(that.formatRichText(c_html));
              that.setData({
                cList:cResult
              })
            }
            that.setData({
              richData:resultList
            })
          } 
        },
        fail: function () {
          wx.hideLoading();
        }
      })
    },
    lovePressClick:function(){
      var that = this;
      var data = {};
    },
    queryRichEvaluate:function(mId){
      var that = this;
      var data = {};
      data["tm"] = "/terminalRetailInfo/chanceEvaluateList";
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["busiType"] = 1;
      data["msgId"] = mId;
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/openservice',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
              console.log(res.data.data);
              var resData = JSON.parse(res.data.data);
              var resultList = resData.resultList;
              var rData = that.data.richData;
              for(var i=0;i<rData.length;i++){
                  if(rData[i].messageId == mId){
                    rData[i]["eValue"] = resultList
                  }
                  console.log(rData);
                  that.setData({
                    richData:rData
                  })
              }
          } 
        },
        fail: function () {
          wx.hideLoading();
        }
      })    
    },
    addRichEvaluate:function(e){
      var that = this;
      var data = {};
      data["tm"] = "/terminalRetailInfo/chanceEvaluate";
      data["storeId"] = app.getExtStoreId();
      data["userId"] = app.getUserId();
      data["loginId"] = app.getLoginId();
      data["userRole"] = app.getUserRole();
      data["busiType"] = 1;
      data["msgId"] = e.currentTarget.dataset.mid;
      data["content"] = e.detail.value;
      data["odbtoken"] = app.getodbtoken();
      data["loginToken"] = app.getloginToken();
      data["companyId"] = app.getExtCompanyId();
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/simpleRouter/openservice',
        data: data,
        success: function (res) {
          wx.hideLoading();
          if (res.data.code == 1) {
             that.queryRichText(e.currentTarget.dataset.mid);
             that.setData({
                evalueData:''
             })
          } 
        },
        fail: function () {
          wx.hideLoading();
        }
      })    
    },
    storeMessageClick:function(){
        var that = this;
        that.setData({
            isShow:true
        })

    },
    systemMessageClick:function(){
        var that = this;
        that.setData({
            isShow:false
        })

    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})