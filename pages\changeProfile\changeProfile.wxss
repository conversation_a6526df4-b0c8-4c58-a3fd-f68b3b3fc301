@import '../popupTemplate/popupTemplate.wxss';
page {
  background: #f5f5f5;
}

/**
.current_account {
  width: 100%;
  height: 44px;
  line-height: 44px;
  border-bottom: 1px solid #ececec;
  display: block;
  font-size: 14px;
  padding: 0 15px;
  font-weight: bold;
  background: #fff;
}**/

.account_head {
  /**border-bottom: 1px solid #ececec;**/
  padding: 15px 0 10px 0;
  background: #fff;
  margin-bottom: 20px;
}

.head_box {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}

.head_box image {
  width: 100%;
  height: 100%;
}

.account_tips {
  display: block;
  text-align: center;
  width: 100%;
  height: 30px;
  line-height: 30px;
  color: #7f7f7f;
  font-size: 15px;
  margin-top: 10px;
}

.account_detail {
  background: #fff;
  padding-bottom: 60px;
}

.account_detail label {
  height: 44px;
  line-height: 44px;
  border-bottom: 1px solid #ececec;
  padding: 0 15px;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  display: block;
}

.account_detail label:last-child {
  border-bottom: none;
}

.account_detail label text {
  float: left;
  /*width: 80px;*/
}
.d_r{
  width: 80px;
}

.account_detail label input {
  height: 45px;
  line-height: 45px;
}

.radio {
  float: left;
  padding: 0 0 !important;
  margin-right: 40px;
}

textarea {
  padding: 14px 0px;
  display: block;
  line-height: 20px;
  font-size: 14px;
  height: 60px;
  margin-bottom: 20px;
  width: auto;
  color: #333;
}

.add_address {
  height: 36px;
  width: 100%;
  background: #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding: 10px 0;
}

.add_address button {
  margin: 0 2%;
  height: 100%;
  background: #FF7E00;
  color: #fff;
  font-size: 14px;
  line-height: 36px;
  width:46%;
  display:inline-block;
}
.bl_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.topWrap {
  text-align: center;
  margin: 15px 0;
  margin-top: 40rpx;
}

.topWrap image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}

.topWrap view {
  font-size: 12px;
  color: #999;
}
.fillCode{
  position: relative;
  height:68rpx;
  line-height:68rpx;
  border-bottom:2rpx solid #717071;
}
.fillCode input{
  padding:0 20rpx;
  height: 68rpx;
  line-height: 68rpx;

}
.fillCode .smsButton {
  border: 1rpx solid #717071;
  padding: 4rpx 8rpx;
  position: absolute;
  right: 0;
  bottom: 8rpx;
  border-radius: 8rpx;
  z-index: 10;
  line-height:52rpx;
  height:52rpx;
  font-size:26rpx;

}
.confirm_btn {
  display: inline-block;
  width: 35%;
  margin: 20px auto;
  height: 70rpx;
  line-height: 70rpx;
  border: none;
  margin-top: 60rpx;
  margin-right: 6%;
  margin-left: 6%;
  font-size: 26rpx;
}
.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.container {
  display: flex;
}