var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    personConsumption: 0,
    dvm: 0,
    consumptionBill: 0, //客户消费提成
    isFromBack: false,
    year: "",
    month: "",
    date: '',
    localStr: "本"
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    } else {
      var that = this;
      wx.showLoading({
        title: '正在加载数据。。。',
        mask: true
      })
      that.queryConsumptionBill();
    }
  },
  /**
   * 绑定银行卡
   * */
  bindcard: function () {
    app.navigateToPage('/pages/bindCard/bindCard');
  },
  /**
   * 查询返利流水
   * */
  goReportDetail: function () {
    app.navigateToPage('/pages/reportDetail/reportDetail');
  },
  /**
   * 查询客户消费提成
   */
  bindDateChange: function (e) {
    var that = this;
    var ymd = e.detail.value;
    var year = ymd.substring(0, 4);
    var month = ymd.substring(5, 7);
    var fMonth = month.substring(0, 1);
    if (fMonth == 0) {
      month = month.substring(1, 2);
    }
    this.setData({
      year: year,
      month: month,
      localStr: month
    })
    that.queryConsumptionBill();
  },
  queryConsumptionBill: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/rebateAccountServer/userQueryRebateJourna',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "year": that.data.year,
        "month": that.data.month,
        "currentPage": 1,
        "pageSize": 10
      },
      success: function (res) {
        wx.hideLoading();
        var totalRebateMoney = res.data.totalRebateMoney;
        var fFrozenRebateMoney = res.data.fFrozenRebateMoney;
        var fValidRebateMoney = res.data.fValidRebateMoney;
        var sFrozenRebateMoney = res.data.sFrozenRebateMoney;
        var sValidRebateMoney = res.data.sValidRebateMoney;
        that.setData({
          totalRebateMoney: totalRebateMoney,
          fFrozenRebateMoney: fFrozenRebateMoney,
          fValidRebateMoney: fValidRebateMoney,
          sFrozenRebateMoney: sFrozenRebateMoney,
          sValidRebateMoney: sValidRebateMoney
        });
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.queryConsumptionBill();
    } else {
      that.setData({
        isFromBack: true
      });
    }
  }

})