<!--引入模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<!--引入WXS方法-->
<wxs module="filters" src="../../wxs/addmul.wxs" />
<test id="test"></test>
<view wx:if="{{items.length>0}}">
  <block wx:for='{{items}}' wx:key='index' wx:for-index='index1'>
    <view style="background:#FFF">
      <view class="find_one" bindtap="toFindinfo" data-index="{{index1}}" data-time="{{timeArry[index1]}}"
        data-findid="{{item.findInfo.id}}">
        <image lazy-load='true' class="find_nine" src="{{storeImage}}" mode="widthFix"></image>
        <view class="find_ten">
          <view class="find_six find_seven">{{storeName}}</view>
          <view class="find_six find_eight">{{timeArry[index1]}}</view>
        </view>
        <!-- <view class="find_two" >
      <view class="find_three">
        <view wx:if="{{item.findInfo.isTop==1}}" style=" display: flex; align-items: center;">
          <image src="{{find_thumbtack}}" style="width:35rpx;height:auto;" mode="widthFix"></image>
        </view>
      </view>
      <view class="find_three" style="text-align:right;">{{item.findInfo.readNum}}阅读</view>
    </view> -->
      </view>
      <view class="find_four">
        <view class="find_five" bindtap="toFindinfo" data-index="{{index1}}" data-time="{{timeArry[index1]}}"
        data-findid="{{item.findInfo.id}}">
          <view class="textFour_box">
            <span wx:if="{{item.findInfo.label!=''&&item.findInfo.labelColor!=''}}" class="find_label"
              style="background:{{item.findInfo.labelColor}};letter-spacing:5rpx">{{item.findInfo.label}}</span>
            <text class="find_content">{{filters.sub(item.findInfo.content,65)}}</text>
            <span wx:if="{{item.findInfo.content.length>65}}"
              style="font-size: 30rpx;font-family: PingFang SC;font-weight: 400;color: #4A79E5;">查看全文</span>
          </view>
        </view>
        <view class="find_five">
          <!-- 九宫格布局预览图片 -->
          <view wx:if="{{item.imagesList.length>0}}" class="gallery">
            <view class="item" wx:for-item="image" wx:for="{{item.imagesList}}" wx:key="index"
              style="height:{{item.imagesList.length==1?550:218}}rpx;">
              <image lazy-load='true'
                style="width: {{item.imagesList.length==1?650:218}}rpx; height: {{item.imagesList.length==1?550:218}}rpx"
                src=" {{image.imagesUrl}}" catchtap="previewImage" mode="widthFill" data-list="{{item.imagesList}}"
                data-src="{{image.imagesUrl}}" />
            </view>
          </view>
          <!--投票-->
          <view wx:elif="{{item.voteList.length>0}}">
            <!--<view wx:if="{{!item.isVote}}" wx:for="{{item.voteList}}" wx:for-item="vote" wx:key="index"
              catchtap="setVote" data-index="{{index1}}" data-id="{{vote.id}}"
              style="border:2rpx solid grey;background: #FFF;width:99%;height:88rpx;border-radius:50rpx;margin-bottom:20rpx;line-height:88rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 500;color: #333333;">
              <span style="padding-left:20rpx;">{{vote.optionIndex}}.</span> {{vote.optionComment}}
            </view>-->
            <progress wx:if="{{!item.isVote}}" wx:for="{{item.voteList}}" wx:for-item="vote1" wx:key="index"
              catchtap="setVote" data-id="{{vote1.id}}" data-index="{{index1}}" style="position: relative;margin-bottom:20rpx;height:88rpx;"
              percent="{{filters.toFix1(vote1.voteNum/item.allVoteNum*100)}}" font-size="{{fontSize}}" duration="10"
              show-info border-radius="{{borderRadius}}" stroke-width="{{strokeWidth}}" active
              activeColor="{{vote1.activeColor}}"><text
                style="position: absolute;top:28rpx;left:10rpx;font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #333333;">{{vote1.optionIndex}}.{{vote1.optionComment}}</text></progress>
            <progress wx:if="{{item.isVote}}" wx:for="{{item.voteList}}" wx:for-item="vote1" wx:key="index"
              catchtap="setVote1" style="position: relative;margin-bottom:20rpx;height:88rpx;"
              percent="{{filters.toFix1(vote1.voteNum/item.allVoteNum*100)}}" font-size="{{fontSize}}" duration="10"
              show-info border-radius="{{borderRadius}}" stroke-width="{{strokeWidth}}" active
              activeColor="{{vote1.activeColor}}"><text
                style="position: absolute;top:28rpx;left:10rpx;font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #333333;">{{vote1.optionIndex}}.{{vote1.optionComment}}</text></progress>
            <view style="color:grey;font-size:22rpx;">{{item.allVoteNum}}人参与投票</view>
          </view>

          <!--视频-->
          <view wx:elif="{{item.findVideo}}">
            <video style="width:100%;height:450rpx" src='{{item.findVideo.videoUrl}}' autoplay='true' loop='true'
              muted='true' object-fit='contain' show-mute-btn='true' title='{{item.findInfo.content}}'
              enable-play-gesture='true' bindplay="bofang" bindended="endVideo"></video>
          </view>
        </view>
        <view class="find_five" wx:if="{{item.relationList.length>0}}">
          <!-- 推荐栏 -->
          <scroll-view wx:if="{{item.relationList}}" class="tab" scroll-x scroll-with-animation="true">
            <block wx:for-item="relation" wx:for="{{item.relationList}}" wx:key="index">
              <view class="tab-item" data-current="{{index}}">
                <view catchtap="imageClick" data-commodityid="{{relation.relationId}}"
                  data-relationtype="{{relation.relationType}}"
                  style="display:flex; align-items: center;justify-content:center;">
                  <view
                    style="width:30%;height:120rpx;border:0px solid red;display:flex; align-items: center;justify-content:center;">
                    <image lazy-load='true' style="width:80rpx;height:80rpx;" src="{{relation.relationImageurl}}"
                      mode="widthFill"></image>
                  </view>
                  <view style="width:60%;border:0px solid red;height:80rpx;">
                    <view style="height:50%;font-family: PingFang SC;font-weight: 500;color: #333333;font-size:28rpx;">
                      {{filters.sub(relation.relationTitle,10)}}</view>
                    <view style="height:50%;font-size: 24rpx;font-family: PingFang SC;font-weight: 500;color: #FF7E00;">
                      {{filters.subPre(relation.relationExplain)}}</view>
                  </view>
                  <view
                    style="width:10%;height:120rpx;border:0px solid red;display:flex; align-items: center;justify-content:center">
                    <image lazy-load='true' style="width:45rpx;height:auto" src="{{find_arrow}}" mode="widthFix">
                    </image>
                  </view>
                </view>
              </view>
            </block>
          </scroll-view>
        </view>
        <view class="find_five" style="height:80rpx;display:flex; align-items: center;justify-content:center">
          <view style="width:50%;font-size: 26rpx;font-family: PingFang SC;font-weight: 400;color: #919398;">
            {{item.findInfo.readNum}}阅读</view>

          <view wx:if="{{item.isAgree}}"
            style="width:50%;border:0px solid red;display:flex; align-items: center;justify-content:center;"
            catchtap="likeOrCancel" data-isragree="0" data-index="{{index1}}" data-id="{{item.findInfo.id}}">
            <view style="border:0px solid red;display:flex;">
              <image  lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_agree}}"  ></image>        
              <span
                style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{item.findInfo.agreeNum}}</span>
            </view>
          </view>

          <view wx:else style="width:50%;border:0px solid red;display:flex; align-items: center;justify-content:center;"
            catchtap="likeOrCancel" data-isragree="1" data-index="{{index1}}" data-id="{{item.findInfo.id}}">
            <view style="border:0px solid red;display:flex;">
              <image lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_notagree}}"></image>
              <span
                style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{item.findInfo.agreeNum}}</span>
            </view>
          </view>

          <view style="width:34%;border:0px solid blue;display:flex;" catchtap="toComment"
            bindtouchstart="bindTouchStart" bindtouchend="bindTouchEnd" data-findinfoid="{{item.findInfo.id}}"
            data-index="{{index1}}">
            <image lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_comment}}">
            </image>
            <span
              style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{item.findInfo.commentNum}}</span>
          </view>
          <view style="width:34%;display:flex;">
            <button open-type="share"
              style="width:45rpx;font-size: 28rpx;background-color: #fff;border: none;padding: 0;margin: 0;line-height: 1;"
              data-findid="{{item.findInfo.id}}" data-time="{{timeArry[index1]}}">
              <image lazy-load='true' style="width:39rpx;height:39rpx;" src="{{find_share}}"></image>
            </button>
            <view
              style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;color: #333333;padding-top:-20rpx;">
              {{item.findInfo.forwardNum}}</view>
          </view>
          <!--<span
            style="margin-left:20rpx;font-size: 28rpx;font-family: PingFang SC;font-weight: 300;line-height: 39rpx;color: #333333;">{{item.findInfo.forwardNum}}</span> -->
        </view>
        <view class="find_five" style="border-radius:6rpx;background: #F6F7F9;">
          <!-- 点赞 -->
          <view wx:if="{{item.agreeList.length>0}}">
            <view class="zan">
              <image lazy-load='true' style="border-radius:50%" wx:for="{{item.agreeList}}" wx:for-item="agree"
                wx:key="index" class="love-icon" src="{{agree.userHead}}" />
            </view>
          </view>

          <view wx:if="{{item.commentList.length>0}}" class="comments" wx:for="{{item.commentList}}"
            wx:for-item="comment" wx:key="index" bindtouchstart="bindTouchStart" bindtouchend="bindTouchEnd"
            catchtap="toComment" bindlongtap="toReportOrdel" data-comment="{{comment}}" data-index="{{index1}}">
            <view style="width:64rpx;height:64rpx;">
              <image lazy-load='true' style="width: 64rpx;height: 64rpx;border-radius: 4px;" src="{{comment.userHead}}"
                mode="widthFix" />
            </view>
            <view wx:if="{{!comment.replyId}}">
              <view class="blueColor">{{comment.userName}}</view>
              <view class="noblueColor">{{comment.commentText}}</view>
            </view>
            <view wx:if="{{comment.replyId}}">
              <!-- <image style="width: 64rpx;height: 64rpx;border-radius: 4px;" src="{{comment.userHead}}" mode="widthFix" /> -->
              <view class="blueColor">{{comment.userName}}</view>
              <view class="noblueColor">回复<text style="color: #4A79E5;"> {{comment.replyUserName}}</text> {{comment.commentText}}</view>
            </view>
          </view>
        </view>
        <view style="background:#FFF;height:10rpx;"></view>
      </view>
    </view>
  </block>
  <template wx:if="{{state1}}" is="comment" data="{{...item}}" />
  <template wx:if="{{state2}}" is="share" data="{{...item}}" />
  <template wx:if="{{state3}}" is="reportOrdel" data="{{...item}}" />
  <load-more id="loadMoreView" bindloadMoreListener='loadMoreListener' bindclickLoadMore='clickLoadMore'></load-more>
</view>
<view wx:else style="">
  <image class='order_none' src='{{order_none}}' mode='widthFix' style="margin-top:300rpx;"></image>
  <view style="text-align:center;font-size:30rpx;color:#666;">暂无发现</view>
</view>