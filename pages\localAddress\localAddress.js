//  pages/localAddress/localAddress.js
var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    more: app.imageUrl + 'accountManager/personal_more.png',
    storeImgerSrc: app.imageUrl + "store.png",
    showReturnStoreList: [],
    storeName: "",
    pagesize: 10,
    currentPage: 1,
    searchLoading: false,
    latitude: '',
    longitude: '',
    navi: app.imageUrl + 'mapNav.png',
    authorizationHidden: false,
    phone: app.imageUrl + 'cellPhone.png',
    localUserAddress: "",
    localUserCity: ""
  },

  /**
   * 跳转到用户地址
   **/
  toLocalAddress: function () {
    app.navigateToPage('/pages/toCity/toCity?localUserCity=' + this.data.localUserCity + "&localUserAddress=" + this.data.localUserAddress + "&latitude=" + this.data.latitude + "&longitude=" + this.data.longitude);
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var latitude = options.latitude;
    var longitude = options.longitude;
    var localUserAddress = options.localUserAddress;
    var localUserCity = options.localUserCity;
    this.setData({
      latitude: latitude,
      longitude: longitude,
      localUserAddress: localUserAddress,
      localUserCity: localUserCity
    });
    if (latitude == 0 || latitude == undefined) {
      this.getUserLocation();
    } else {
      this.queryStoreInfo(latitude, longitude);
    }
  },
  getUserLocation: function () {
    let vm = this;
    // wx.getSetting({
    //   success: (res) => {
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取门店地理位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             vm.queryStoreInfo(0, 0);
    //             vm.setData({
    //               localUserAddress: "未定位"
    //             })
    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })
    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {
    //                   vm.setData({
    //                     localUserAddress: "未定位"
    //                   })
    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                   vm.queryStoreInfo(0, 0);
    //                 }
    //               }
    //             })
    //           }
    //         },
    //         fail: function () {
    //           vm.setData({
    //             localUserAddress: "未定位"
    //           })
    //           vm.queryStoreInfo(0, 0);
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     } else {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     }
    //   }
    // })
  },
  /**
   * 获取定位
   */
  getLocation: function () {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // wx.getLocation({
    //   type: 'gcj02',
    //   altitude: true,
    //   complete: function (res) {
    //     if (res.errMsg == "getLocation:ok") {
    //       var latitude = res.latitude;
    //       var longitude = res.longitude;
    //       qqmapsdk.reverseGeocoder({
    //         location: {
    //           latitude: latitude,
    //           longitude: longitude
    //         },
    //         success: function (res) { //成功后的回调
    //           var res = res.result;
    //           that.setData({
    //             localUserAddress: res.address,
    //             localUserCity: res.address_component.city
    //           })
    //           qqmapsdk.geocoder({
    //             //获取表单传入地址
    //             address: res.address, //地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
    //             success: function (res) { //成功后的回调
    //               var res = res.result;
    //               var latitude = res.location.lat;
    //               var longitude = res.location.lng;
    //               that.setData({
    //                 latitude: latitude,
    //                 longitude: longitude
    //               });
    //               that.queryStoreInfo(latitude, longitude);
    //             },
    //             fail: function (error) {
    //               console.error(error);
    //             },
    //             complete: function (res) {
    //             }
    //           })
    //         },
    //         fail: function (error) {
    //           console.error(error);
    //         },
    //         complete: function (res) {
    //         }
    //       })
    //     } else {
    //       that.setData({
    //         latitude: 0,
    //         longitude: 0
    //       });
    //       that.queryStoreInfo(latitude, longitude);
    //     }
    //   }
    // })
  },
  queryStoreInfo: function (latitude, longitude) {
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    var that = this;
    app.init_getExtMessage().then(res => {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/applet/queryNewSelfStoreInfo',
        data: {
          "companyId": res.companyId,
          "storeName": that.data.storeName,
          "currentPage": that.data.currentPage,
          "pagesize": that.data.pagesize,
          "latitude": latitude,
          "longitude": longitude,
          "localUserAddress": that.data.localUserAddress,
          "localUserCity": that.data.localUserCity
        },
        success: function (res) {
          wx.hideLoading();
          var returnStoreList = res.data.returnStoreList;
          var oldReturnStoreList = that.data.showReturnStoreList;
          if (returnStoreList != null && returnStoreList.length > 0) {
            returnStoreList = oldReturnStoreList.concat(returnStoreList);
            that.setData({
              searchLoading: true
            });
          } else {
            returnStoreList = oldReturnStoreList;
            that.setData({
              searchLoading: false
            });
          }
          that.setData({
            showReturnStoreList: returnStoreList
          });
        }
      })
    });
  },
  makePhoneBindTap: function (e) {
    var phone = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phone //仅为示例，并非真实的电话号码
    })
  },
  selectStoreInfoBindTap: function (e) {
    var that = this;
    var name = e.currentTarget.dataset.name;
    var id = e.currentTarget.dataset.id;
    var selectStoreInfo = {
      "storeId": id,
      "storeName": name
    };
    app.setStorage({
      key: 'selectStoreInfoKey',
      data: selectStoreInfo
    });
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      storeName: name
    });
    app.turnBack();
  },
  /**
   * 开始导航
   */
  startNavigationBindTap: function (e) {
    var that = this;
    var name = e.currentTarget.dataset.name;
    var address = e.currentTarget.dataset.address;
    var latitude = e.currentTarget.dataset.latitude;
    var longitude = e.currentTarget.dataset.longitude;
    wx.openLocation({
      latitude: Number(latitude),
      longitude: Number(longitude),
      name: address,
      address: name,
      scale: 18
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (that.data.searchLoading) {
      that.setData({
        currentPage: that.data.currentPage + 1
      });
      that.queryStoreInfo(that.data.latitude, that.data.longitude);
    }
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})