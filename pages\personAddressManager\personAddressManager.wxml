<view class="addressBox">
	<block wx:for="{{ addressList }}" wx:for-item="address" wx:for-index="addressIndex" wx:key="">
		<view class='address_box'>
			<view>
				<view class='address'>
					<block wx:if="{{address.isdefault==1}}">
						<label class="title">[默]</label>
					</block>
					<label class="addressCo">{{address.province+address.city+address.area+address.address+address.houseNumber}}</label>

				</view>
				<view class='clearfix contacts_phone'>
					<label class='contacts'>{{address.username}}</label>
					<label class='phone'>{{address.telephone}}</label>
				</view>

			</view>
			<view class='clearfix operate_box' style="position:relative">
				<!-- <lable class="remove_box" data-id="{{address.id}}" bindtap='deleteAddressBindTap'>
					<image src='{{remove}}'></image>删除
				</lable> -->
				<view style="position:absolute;bottom:8rpx;right:0">
					<view class="edit_box" data-id="{{address.id}}" bindtap='updateAddressBindTap'>
						<label>
							<image src='{{edit}}'></image><text style="font-size: 26rpx;">编辑</text>
						</label>
					</view>
					<block wx:if="{{address.isdefault==2}}">
						<view class="default_address" data-id="{{address.id}}" bindtap='setDefaultAddressBindTap'>设置默认地址</view>
					</block>
				</view>
			</view>
		</view>
	</block>
</view>
<view class='add_address'>
	<button bindtap='addNewAddressBindTap'>
		添加新地址
	</button>
</view>