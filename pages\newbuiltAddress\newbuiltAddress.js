var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userName: "", //姓名
    telephone: "", //电话
    address: "", //地址
    isDefault: false,
    houseNumber: "", //门牌号
    latitude: 0,
    longitude: 0,
    province: "",
    city: "",
    area: "",
    pickOrderStoreId: 0,
    region: ["请选择省市区"],
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      pickOrderStoreId: options.pickOrderStoreId
    })
  },
  bindRegionChange: function (e) {
    this.setData({
      region: e.detail.value
    })
  },
  selectAddressBindTap: function () {
    app.navigateToPage('/pages/addressToCity/addressToCity');
  },
  /**
   * 保存新建地址
   */
  saveNewAddressBindTap: function () {
    var that = this;
    var userName = that.data.userName;
    var telephone = that.data.telephone;
    var address = that.data.address;
    var houseNumber = that.data.houseNumber;
    var flag = that.checkTelephone(telephone, userName, address);
    var pac = that.data.province + that.data.city;
    if (that.data.area != null && that.data.area != "" && that.data.area != undefined) {
      pac += that.data.area;
    }
    address = address.substring(pac.length);
    if (!flag) {
      return;
    }
    var jsonStr = {
      "latitude": that.data.latitude,
      "longitude": that.data.longitude,
      "houseNumber": houseNumber,
      "username": userName,
      "telephone": telephone,
      "province": that.data.province,
      "city": that.data.city,
      "area": that.data.area,
      "address": address,
      "isDefault": that.data.isDefault == true ? 1 : 2,
      "loginId": app.getUserId(),
      "loginRole": 1,
      "storeId": app.getExtStoreId(),
      "companyId": app.getExtCompanyId(),
      "odbtoken":app.getodbtoken(),
      "loginToken":app.getloginToken(),
    };
    jsonStr = encodeURIComponent(encodeURIComponent(JSON.stringify(jsonStr)));
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/ios/addDeliveryAddress',
      data: {
        "jsonStr": jsonStr,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
      },
      success: function (res) {
        var pages = getCurrentPages(),
          prevPage = pages[pages.length - 2];
        prevPage.initAddress(that.data.pickOrderStoreId);
        app.turnBack();
      }
    })
  },
  checkTelephone: function (telephone, userName, address) {
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(19[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (userName == "") {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    } else if (userName.length > 20) {
      wx.showToast({
        title: '姓名长度过长',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    }
    if (telephone == "") {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    } else if (telephone.length < 11) {
      wx.showToast({
        title: '手机号长度有误',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    } else if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    }
    if (address.length == 0) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none',
        duration: 1500,
        mask: true
      })
      return false;
    }
    return true;
  },
  checkboxChange: function (e) {
    this.setData({
      isDefault: e.detail.value == 1 ? true : false
    })
  },
  saveUserNameBindInput: function (e) {
    this.setData({
      userName: e.detail.value
    })
  },
  saveTelephoneBindInput: function (e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  saveAddressBindInput: function (e) {
    this.setData({
      address: e.detail.value
    })
  },
  houseNumberBindInput: function (e) {
    this.setData({
      houseNumber: e.detail.value
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})