<wxs src="../../wxs/subutil.wxs" module="tools" />
<view class="shoppingCart-wrap page" id="shoppingCart" style="padding-bottom:180rpx;">
	<image lazy-load='true' class='tips_pic' src='{{emptyCart}}' bindtap='' hidden='{{isShopCartEmpty}}' mode='widthFix'>
	</image>
	<view hidden='{{shopCartHidden}}'>
		<view class="shoppingCart-top-nav" style="font-size:14px;">
			<view class="top-nav-left">共
				<text style="color:#FF7E00;margin:0 4rpx;font-weight:bold;">{{goodsList.length}}</text>件商品
			</view>
			<view class="top-nav-right" style="display:flex;width:160rpx;">
				<view>
					<view bindtap="goCouponCenterBind" style="color:#FF7E00;margin-right:20rpx;">领券</view>
				</view>
				<view>
					<view class="shoppingCart-edit-btn" hidden="{{ editing }}" bindtap="switchToEdit">管理</view>
					<view class="shoppingCart-edit-complete" hidden="{{ !editing }}" bindtap="editComplete">完成</view>
				</view>
			</view>
		</view>
	</view>
	<view hidden='{{shopCartHidden}}'>
		<view class="shoppingCart-bottom-nav" hidden="{{ editing }}">
			<label class="shoppingCart-check-box" bindtap="clickSelectAll">
				<label class="shoppingCart-select-all check-box {{ selectAll ? 'checked' : '' }}">
					<image lazy-load='true' src="{{shop_cart}}"></image>
				</label> 全选
			</label>
			<form report-submit="true" bindsubmit="goToPay">
				<button formType="submit" class="pull-right shoppingCart-goto-pay">结算({{ goodsCountToPay }})</button>
			</form>
			<label class="pull-right" style="margin-right:10px;">合计:
				<text class="shoppingCart-all-price">¥ {{priceToPay}}</text>
			</label>
		</view>
	</view>
	<view class="shoppingCart-edit-bar" hidden="{{!editing}}">
		<label class="shoppingCart-check-box" bindtap="clickEditSelectAll">
			<label class="shoppingCart-select-all check-box {{ editSelectAll ? 'checked' : '' }}">
				<image lazy-load='true' src="{{shop_cart}}"></image>
			</label>全选</label>
		<text class="shoppingCart-delete-goods btn btn-red pull-right" bindtap="deleteGoods">删除所选</text>
	</view>

	<view class="shoppingCart-list-wrap {{ editing ? 'editing-list' : '' }}">
		<view class="shoppingCart-goods-list">
			<view wx:for="{{ goodsList }}" wx:for-item="goods" wx:key="unique" style="position:relative; overflow:hidden;">
				<label hidden="{{ editing }}" data-index="{{ index }}" class="shoppingCart-check-box"
					bindtap="clickSelectGoods">
					<label class="check-box pull-left {{ goods.selected ? 'checked' : '' }}">
						<image lazy-load='true' src="{{shop_cart}}"></image>
					</label>
				</label>
				<label hidden="{{ !editing }}" data-index="{{ index }}" class="shoppingCart-check-box"
					bindtap="clickEditSelectGoods">
					<label class="check-box pull-left {{ goods.editSelected ? 'checked' : '' }}">
						<image lazy-load='true' src="{{shop_cart}}"></image>
					</label>
				</label>
				<view class="shoppingCart-goods-content">
					<image lazy-load='true' class="shoppingCart-goods-cover" src="{{ goods.goodsImage }}" data-id="{{goods.id}}"
						bindtap="goToGoodsDetailBindTap"></image>
					<view class="ellipsis shoppingCart-goods-title">{{ goods.name }}</view>
					<view class='classify_box'>
						<block wx:for="{{ goods.skuUnitList }}" wx:for-item="sku" wx:key="unique" wx:for-index="skuIdx">
							<block wx:if="{{goods.skuUnitList.length-1==skuIdx}}">
								{{sku.skuName}}:{{sku.skuValue}}
							</block>
							<block wx:else>
								{{sku.skuName}}:{{sku.skuValue}};
							</block>
						</block>
					</view>
					<view class="shoppingCart-goods-price">
						<view style="float:left;">
							<view style="font-size:28rpx" hidden="{{goods.omUnitDefault==1?false:true}}">¥{{
								tools.sub.formatAmount(goods.omPrice,2) }}<block wx:if="{{goods.skuUnitList.length==0}}">
									/{{goods.omName}}</block>
							</view>
							<view style="font-size:28rpx" hidden="{{goods.otUnitDefault==1?false:true}}">¥{{
								tools.sub.formatAmount(goods.otPrice,2) }}<block wx:if="{{goods.skuUnitList.length==0}}">
									/{{goods.otName}}</block>
							</view>
						</view>
						<view class="shoppingCart-goods-right">
							<view class="quantity" hidden="{{ editing }}" style="display: flex;padding-top:6rpx">
								<view style="font-size:26rpx;color:#666">数量:
									<block wx:if="{{goods.omUnitDefault==1}}">
										{{goods.omNum}}{{goods.omName}}
									</block>
									<block wx:if="{{goods.otUnitDefault==1}}">
										{{goods.otNum}}{{goods.otName}}
									</block>
								</view>
								<image lazy-load='true' src="{{catEdit}}"
									style="margin-left:20rpx;width:50rpx;margin-right:20rpx;height:50rpx;" mode="widthFix"
									data-id="{{goods.id}}" bindtap="updateShoppingCardNum"></image>
							</view>
						</view>
						<!-- 数量加减 -->
						<!-- <view class="shoppingCart-goods-right">
							<view class="quantity" hidden="{{ editing }}">
								<label class="minus {{ goods.omNum <= 0 ? 'disabled' : '' }}"></label>
								<input type="text" class="txt" style="color:#FF7E00" value="{{ goods.omNum }}" data-index="{{ index }}" disabled="true" bindinput="inputGoodsCount" />
								<label class="plus"></label>
								<view class="response-area response-area-minus" bindtap="clickMinusButton" data-index="{{ index }}"></view>
								<view class="response-area response-area-plus" bindtap="clickPlusButton" data-index="{{ index }}"></view>{{goods.omName}}
							</view>
								<view class="quantity" hidden="{{ editing }}">
								<label class="minus {{ goods.otNum <= 0 ? 'disabled' : '' }}"></label>
								<input type="text" class="txt" style="color:#FF7E00" value="{{ goods.otNum }}" data-index="{{ index }}" disabled="true" bindinput="inputGoodsCount" />
								<label class="plus"></label>
								<view class="response-area response-area-minus" bindtap="clickMinusButton" data-index="{{ index }}"></view>
								<view class="response-area response-area-plus" bindtap="clickPlusButton" data-index="{{ index }}"></view>{{goods.otName}}
							</view>
						</view> -->
						<!-- 数量加减 -->
					</view>
				</view>

			</view>
		</view>
	</view>

	<!-- <mp-dialog title="test" show="{{showOneButtonDialog}}" bindbuttontap="tapDialogButton" buttons="{{buttons}}">
        <view>test content</view>
        <view>23224</view>
  </mp-dialog> -->
</view>
<view hidden="{{indexGoodsList.length>0?false:true}}" class="r_wrap" style="background:#f5f5f5;">
	<view style="height:80rpx;line-height:80rpx;padding-left:25rpx;">推荐商品</view>
	<view class="pic_two">
		<block wx:key="unique" wx:for="{{indexGoodsList}}" wx:for-item="goods" wx:for-index="goodsIndex">
			<view class="commodity_box3" style="border-radius:20rpx">
				<image lazy-load='true' style="border-top-left-radius:20rpx;border-top-right-radius:20rpx;" bindtap="imageClick"
					data-commodityId="{{goods.commodityId}}" src="{{goods.commodityMainPic}}"></image>
				<view class="goods_desc">
					<view class="desc_title">
						{{goods.commodityName}}
					</view>
					<view class="desc_price" style="display:flex;justify-content:space-between">
						<view class="price_l">
							<label class="price_tag">￥</label>
							<label class="price_inner"
								hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0)?false:true}}">{{tools.sub.formatAmount(goods.omPrice,1)}}</label>
							<label class="price_inner">{{tools.sub.formatAmount(goods.goodsPrice,1)}}</label>
							<block wx:if="{{goods.cutOffTheOmPrice>0&&goods.cutOffTheOmPrice>goods.omPrice}}">
								<label
									hidden="{{(goods.commodityOMUnitDefault==1&&goods.commodityOtUnitDefault==0&&indexConfig.goodsBean.shopCartStyle != 3)?false:true}}"
									class="line_price">￥{{tools.sub.formatAmount(goods.cutOffTheOmPrice,1)}}</label>
							</block>
							<block wx:if="{{goods.cutOffThePrice>0&&goods.cutOffThePrice>goods.goodsPrice}}">
								<label class="line_price">￥{{tools.sub.formatAmount(goods.cutOffThePrice,1)}}</label>
							</block>
							<block
								wx:if="{{goods.promotionName != null && goods.promotionName != '' && goods.promotionName.length>0&& indexConfig.goodsBean.shopCartHidden==2}}">
								<label>
									<text class="promotionDesc">{{goods.promotionList[0].promotionName}}</text>
								</label>
							</block>
						</view>
					</view>
				</view>
			</view>
		</block>
	</view>
</view>
<!--修改购物车数量弹出层-->
<view class='black_bg' hidden="{{updateShopCardHidden}}"></view>
<view class='scroll_block' hidden="{{updateShopCardHidden}}">
	<icon class="page-dialog-close" type="clear" size='20' color='#666' bindtap='cancelShopCartButtonBindTap' />
	<view class='append_title clearfix'>
		<label class='limited_quantity' style="color:#333;">商品：{{u_goodsBean.name}}</label>
		<label class='limited_quantity'>修改购买数量</label>
	</view>
	<view class="orderWrap">
		<block wx:if="{{u_goodsBean.omUnitDefault==1}}">
			<view style="display:flex;justify-content:center">
				<label class="minus_box" bindtap="clickMinusOmButton">-</label>
				<input class="numBox" type='number' bindinput="omNumBindInput" value="{{u_goodsBean.omNum}}"></input>
				<label class="plus_box" bindtap='clickPlusOmButton'>+</label>
				<text class="unit_num">{{u_goodsBean.omName}}</text>
			</view>
		</block>

		<block wx:if="{{u_goodsBean.otUnitDefault==1}}">
			<view style="display:flex;justify-content:center">
				<label class="minus_box" bindtap='clickMinusOtButton'>-</label>
				<input class="numBox" type='number' bindinput="otNumBindInput" value="{{u_goodsBean.otNum}}"></input>
				<label class="plus_box" bindtap='clickPlusOtButton'>+</label>
				<text class="unit_num">{{u_goodsBean.otName}}</text>
			</view>
		</block>
		<label class="confirmButton" bindtap="batchSetRetailCartNum">确定</label>
	</view>
</view>
<!--修改购物车数量弹出层-->