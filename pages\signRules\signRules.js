const app = getApp();
const http = require('../../utils/http')
var WxParse = require('../../components/wxParse/wxParse.js');
Page({
  data:{
    coupon:app.imageUrl + 'sign_coupon.png',
    integral:app.imageUrl + 'sign_integral.png',
    spmoney:app.imageUrl + 'sign_spmoney.png',
    game:app.imageUrl + 'sign_game.png'
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that=this;
    var promotionId = options.promotionId;
    var that = this;
    var data = {};
    data["tm"] = "/signin/retailClient/detail";
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/simpleRouter/stickness',
      data: data,
      success: function (res) {
        if(res.data.code == 1){
          var resData = JSON.parse(res.data.data);
          var c_html = decodeURIComponent(resData.activityRulesDesc);

          WxParse.wxParse("commodityIntroduce", 'html', c_html, that, 5);
        }
        else{
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })

  }

})