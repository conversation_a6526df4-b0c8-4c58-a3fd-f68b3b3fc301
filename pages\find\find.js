// pages/find/find.js
const app = getApp();
const http = require('../../utils/http');
const ui = require('../../utils/ui');
const popup = require('../popupTemplate/popupTemplate.js');
const { utf16toEntities, entitiesToUtf16 } = require('../../utils/emojis')
var w = wx.getSystemInfoSync().windowWidth;
var h = wx.getSystemInfoSync().windowHeight;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    state: false,
    msg: '',
    find_agree: app.imageUrl + 'find_agree.png',
    find_arrow: app.imageUrl + 'find_arrow.png',
    find_comment: app.imageUrl + 'find_comment.png',
    find_emoji: app.imageUrl + 'find_emoji.png',
    find_image: app.imageUrl + 'find_image.png',
    find_jianpan: app.imageUrl + 'find_jianpan.png',
    find_notagree: app.imageUrl + 'find_notagree.png',
    find_share: app.imageUrl + 'find_share.png',
    find_thumbtack: app.imageUrl + 'find_thumbtack.png',
    find_wechat: app.imageUrl + 'find_wechat.png',
    order_none: app.imageUrl + 'order/order_none.png',
    storeImage: app.getExtStoreImage(),
    storeName: app.getExtStoreName(),
    fontSize: h / w > 1.23 ? 12 : 24,
    borderRadius: h / w > 1.23 ? 22 : 50,
    strokeWidth: h / w > 1.23 ? 44 : 100,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    app.getSupplierSetting(app.getExtCompanyId());
    that.getFindInfo(1, -1);
  },
  /**
   * 查询发现接口
   */
  //startPage：哪一页 index:下标 state：0是其他刷新数据，1：点赞刷新数据
  getFindInfo: function (startPage, index) {
    var that = this;
    var userId = app.getUserId();
    var data;
    if (userId == undefined) {
      data = {
        merchantId: app.getExtCompanyId(),
        startPage: startPage
      }
    } else if (userId != undefined) {
      data = {
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        startPage: startPage
      }
    }

    http.get({
      urlName: 'find',
      url: 'find/FindInfoAllVoPage',
      showLoading: false,
      data: data,
      success: (res) => {
        var items = that.data.items;
        var timeArry = [];
        // var numArray = [];
        var date;
        var time;
        // var  comments=[];
        if (startPage == 1) {
          items = res.resultList;
        } else {
          if (index == -1) {
            items = items.concat(res.resultList);
          } else {
            items[index] = res.resultList[index % res.pageSize]
          }
        }
        items.forEach(result => {
          date = new Date(result.findInfo.createTime.replace(/\-/g, "/")).getTime();
          time = that.parseTime(date);
          timeArry.push(time);
          //result.findInfo.createTime=time
          // result.findInfo.auto = false;
          // result.findInfo.seeMore = false;
          var allVoteNum = 0;
          if (result.voteList) {
            result.voteList.forEach(vote => {
              allVoteNum += vote.voteNum
              if (vote.userList == "1") {
                vote.activeColor = "skyblue"
              } else if (vote.userList == "0") {
                vote.activeColor = "#CCCCCC"
              }
            });
            result.allVoteNum = allVoteNum
          }
          result.commentList.forEach(comment => {
            comment.commentText = entitiesToUtf16(comment.commentText);
            comment.userName = entitiesToUtf16(comment.userName);
            comment.replyUserName = entitiesToUtf16(comment.replyUserName);
          });
        });
        that.setData({
          result: res,
          items: items,
          startPage: startPage,
          nextPage: res.nextPage,
          lastPage: res.lastPage,
          timeArry: timeArry,
          pageSize: res.pageSize,
          storeImage: app.getExtStoreImage(),
          storeName: app.getExtStoreName()
          // numArray: numArray,
          // comments:comments
        })
        //调用加载更多组件加载方法
        if (items.length > 0) {
          this.selectComponent("#loadMoreView").loadMoreComplete(res);
        }

      },
      fail: () => {
        //调用加载更多组件加载失败方法
        this.selectComponent("#loadMoreView").loadMoreFail();
      }
    })
  },
  loadMoreListener: function (e) {
    this.getFindInfo(this.data.nextPage, -1);
  },
  clickLoadMore: function (e) {
    this.getFindInfo(this.data.nextPage, -1);
  },
  /**
   * 图片预览
   */
  previewImage: function (e) {
    var src = e.currentTarget.dataset.src;
    var list = e.currentTarget.dataset.list;
    var imgList = [];
    for (var i = 0; i < list.length; i++) {
      imgList.push(list[i].imagesUrl)
    }
    //图片预览
    wx.previewImage({
      current: src, // 当前显示图片的http链接
      urls: imgList // 需要预览的图片http链接列表
    })
  },
  /**
   * 时间戳转换标注
   */
  parseTime(dateTimeStamp) { //dateTimeStamp是一个时间毫秒，注意时间戳是秒的形式，在这个毫秒的基础上除以1000，就是十位数的时间戳。13位数的都是时间毫秒。
    var minute = 1000 * 60; //把分，时，天，周，半个月，一个月用毫秒表示
    var hour = minute * 60;
    var day = hour * 24;
    var week = day * 7;
    var halfamonth = day * 15;
    var month = day * 30;
    var now = new Date().getTime(); //获取当前时间毫秒
    var diffValue = now - dateTimeStamp; //时间差

    if (diffValue < 0) {
      return;
    }
    var minC = diffValue / minute; //计算时间差的分，时，天，周，月
    var hourC = diffValue / hour;
    var dayC = diffValue / day;
    var weekC = diffValue / week;
    var monthC = diffValue / month;
    var result = "23分钟前"
    if (monthC >= 1 && monthC <= 2) {
      result = " " + parseInt(monthC) + "月前"
    } else if (weekC >= 1 && weekC <= 3) {
      result = " " + parseInt(weekC) + "周前"
    } else if (dayC >= 1 && dayC <= 7) {
      result = " " + parseInt(dayC) + "天前"
    } else if (hourC >= 1 && hourC <= 23) {
      result = " " + parseInt(hourC) + "小时前"
    } else if (minC >= 1 && minC <= 59) {
      result = " " + parseInt(minC) + "分钟前"
    } else if (diffValue >= 0 && diffValue <= minute) {
      result = "刚刚"
    } else {
      var datetime = new Date();
      datetime.setTime(dateTimeStamp);
      var Nyear = datetime.getFullYear();
      var Nmonth = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
      var Ndate = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
      var Nhour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
      var Nminute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
      var Nsecond = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
      result = Nyear + "-" + Nmonth + "-" + Ndate
    }
    return result;
  },
  /**
   * 点赞或取消
   */
  likeOrCancel: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var that = this;
    var isRagree = e.currentTarget.dataset.isragree;//是否点赞1：点赞，0未点赞
    var id = e.currentTarget.dataset.id;//infoid 
    var index = e.currentTarget.dataset.index;//当前点赞内容的index
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    http.get({
      urlName: 'find',
      url: 'find/setUseragree',
      showLoading: false,
      data: {
        infoId: id,
        userId: app.getUserId(),
        isRagree: isRagree,
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName(),
        userHead: app.getHeadImage()
      },
      success: (res) => {
        that.getFindInfo(currentPage, index);
      }
    })
  },
  /**
   * 视频播放
   */
  bofang() {
    var that = this
    that.setData({
      autoplay: false
    })
  },
  /**
   * 结束播放触发的事件
   */
  endVideo() {
    var that = this
    that.setData({
      autoplay: true
    })
  },
  /**
 * 跳转到商品详情(点击image)
 */
  imageClick: function (e) {
    var id = e.currentTarget.dataset.commodityid;
    var relationType = e.currentTarget.dataset.relationtype;
    if (relationType == 1) {
      app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + id);
    } else if (relationType == 2) {
      app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
    } else if (relationType == 3) {
      app.navigateToPage('/pages/couponCenter/couponCenter');
    }

  },
  bindTouchStart: function (e) {
    this.startTime = e.timeStamp;
  },
  bindTouchEnd: function (e) {
    this.endTime = e.timeStamp;
  },
  /**
   * 调用评论弹窗
   */
  toComment: function (e) {
    if (this.endTime - this.startTime < 350) {
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        return;
      }
      var that = this;
      var index = e.currentTarget.dataset.index;//当前点赞内容的index
      // var currentPage = (index+1)/5<=1?1:Math.ceil((index+1)/5)//当前是第几页
      var comment = e.currentTarget.dataset.comment;
      var findinfoId = e.currentTarget.dataset.findinfoid == undefined ? comment.findinfoId : e.currentTarget.dataset.findinfoid;
      that.setData({
        findinfoId: findinfoId,
        index: index,
        comment: comment,
        msg: '',
        item: {
          msg: ''
        }
      })
      that.comment();
    }

  },
  /**
   * 调用删除或举报评论弹窗
   */
  toReportOrdel: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var that = this;
    var comment = e.currentTarget.dataset.comment;
    var index = e.currentTarget.dataset.index;//当前要删除评论的内容index
    // var currentPage = (index+1)/5<=1?1:Math.ceil((index+1)/5)//当前是第几页
    if (comment.userId == app.getUserId()) {
      that.setData({
        reportOrdelText: "删除",
        commentId: comment.id,
        index: index,
        findinfoId: comment.findinfoId
      })
    } else {
      that.setData({
        reportOrdelText: "举报",
        commentId: comment.id,
      })
    }
    var that = this;
    that.reportOrdel();
  },
  /**
   * 删除或举报评论
   */
  reportOrdelComment: function (e) {
    var that = this;
    var reportordeltext = e.currentTarget.dataset.reportordeltext;
    var commentId = e.currentTarget.dataset.commentid;
    var index = e.currentTarget.dataset.index;//当前要删除评论的内容index
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    if (reportordeltext == "删除") {
      http.post({
        urlName: 'find',
        url: 'find/delMyComment',
        showLoading: false,
        data: {
          commentId: commentId
        },
        success: (res) => {
          that.setData({
            findinfoId: e.currentTarget.dataset.findinfoid
          })
          that.hiddenFloatView();
          that.getCommentList(currentPage, index);
        }
      })
    } else if (reportordeltext == "举报") {
      http.post({
        urlName: 'find',
        url: 'find/setCommentReport',
        showLoading: false,
        data: {
          commentId: commentId
        },
        success: (res) => {
          that.hiddenFloatView();
          ui.showToast("举报成功");
        }
      })
    }
  },
  /**
   * 去分享
   */
  toShare: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var that = this;
    that.share();
  },
  //调用评论模板弹窗
  comment: function () {
    var that = this;
    that.setData({
      state1: true,
      state2: false,
      state3: false,
      which: "find"
    })
    popup.animationEvents(that, 0, true);
  },
  //调用分享模板弹窗
  share: function () {
    var that = this;
    that.setData({
      state1: false,
      state2: true,
      state3: false,
      which: "find"
    })
    popup.animationEvents(that, 0, true);
  },
  //调用删除或举报弹窗
  reportOrdel: function () {
    var that = this;
    that.setData({
      state1: false,
      state2: false,
      state3: true,
      which: "find"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
  //获取输入的评论
  inputComment: function (e) {
    popup.inputComment(this, e)
  },
  //emoji表情评论
  emojiComment() {
    popup.changeHeight(this)
  },
  clickEmoji(e) {
    popup.clickEmoji(this, e)
  },
  //发表评论
  releaseEvent(e) {
    var that = this
    var index = e.currentTarget.dataset.index;//当前要删除评论的内容index
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    if (that.data.msg == undefined || that.data.msg == '') {
      ui.showToast("请输入评论内容")
      return
    }

    app.msg_sec_check(that.data.msg).then(res => {
      if (res) {
        that.setInfoComment(currentPage, index);
      } else {
        app.showModal({
          content: "评论内容违规，请重新修改"
        });
      }
    })
  },
  setInfoComment: function (currentPage, index) {
    var that = this;
    var jsonData = {};
    var comment = that.data.comment
    jsonData.merchantId = app.getExtCompanyId();
    jsonData.findinfoId = that.data.findinfoId;
    jsonData.userId = app.getUserId();
    jsonData.userHead = app.getHeadImage();
    jsonData.userName = app.getLoginName() == "" ? "匿名" : utf16toEntities(app.getLoginName());
    jsonData.commentText = utf16toEntities(that.data.msg);
    if (comment != undefined) {
      jsonData.replyId = comment.userId;
      jsonData.replyUserHead = comment.userHead;
      jsonData.replyUserName = utf16toEntities(comment.userName);
    }
    http.post({
      urlName: 'find',
      url: 'find/setInfoComment',
      header: "application/json",
      data: JSON.stringify(jsonData),
      success: (res) => {
        this.getCommentList(currentPage, index)
        this.hiddenFloatView()
        ui.showToast("评论成功")
      }
    })
  },
  //刷新评论过的区域
  getCommentList(currentpage, index) {
    var that = this
    http.post({
      urlName: 'find',
      showLoading: false,
      url: 'find/queryInfoCommentPage',
      data: {
        infoId: that.data.findinfoId,
        userId: app.getUserId(),
        startPage: 1
      },
      success: (res) => {
        that.getFindInfo(currentpage, index)
      }
    })
  },
  /**
   * 用户投票
   */
  setVote: function (e) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    var that = this;
    var voteId = e.currentTarget.dataset.id;
    var index = e.currentTarget.dataset.index;
    var pageSize = that.data.pageSize;//每一页展示的条数
    var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
    http.post({
      urlName: 'find',
      url: 'find/setVote',
      showLoading: false,
      data: {
        voteId: voteId,
        userId: app.getUserId()
      },
      success: (res) => {
        that.getFindInfo(currentPage, index)
      }
    })
  },
  setVote1: function () {
    ui.showToast("您已经投过票啦")
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this;
    if (!that.data.lastPage) {
      that.getFindInfo(that.data.nextPage, -1)
    } else {
      that.selectComponent("#loadMoreView").noMore()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (e) {
    var findId = e.target.dataset.findid;
    http.post({
      urlName: 'find',
      url: 'find/setforward',
      showLoading: false,
      data: {
        infoId: findId
      },
      success: (res) => {
      }
    })
    return {
      title: '发现分享',
      path: '/pages/findInfo/findInfo?findId=' + findId + "&time=" + e.target.dataset.time
    };
  },
  /**
  * 生命周期函数--监听页面显示
  */
  onShow: function () {
    var that = this;
    var childObj = that.selectComponent('#test');
    childObj.setData({
      list: app.globalData.bottomBean.wechatAppletIndexBottomContentEntity,
      selected: 17,
      color: app.globalData.bottomBean.color,
      selectedColor: app.globalData.bottomBean.selectedColor
    });
    let flag = true;
    app.globalData.bottomBean.wechatAppletIndexBottomContentEntity.forEach(item => {
      console.log(item.pagePath);
      if (item.pagePath == 17) {
        flag = false
      }
    })
    childObj.setData({
      showFlag: flag
    });
    that.getFindInfo(1, -1)
    /*var that = this;
    var index = that.data.index;
    if (index != undefined) {
      var pageSize = that.data.pageSize;//每一页展示的条数
      var currentPage = (index + 1) / pageSize <= 1 ? 1 : Math.ceil((index + 1) / pageSize)//当前是第几页
      that.getFindInfo(currentPage, index);
    }*/
  },
  /**
   * 去发现详情
   */
  toFindinfo: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    that.setData({
      index: index
    })
    app.navigateToPage("/pages/findInfo/findInfo?findId=" + e.currentTarget.dataset.findid + "&time=" + e.currentTarget.dataset.time)
  },
  /**
  * 页面相关事件处理函数--监听用户下拉动作
  */
  onPullDownRefresh: function () {
    // 展示加载logo(小圈圈图片)
    wx.showNavigationBarLoading()
    setTimeout(() => {
      this.getFindInfo(1, -1)
      // 防止下拉刷新不恢复
      wx.stopPullDownRefresh()
      // 隐藏logo(小圈圈图片)
      wx.hideNavigationBarLoading()
    }, 1000)
  },

})