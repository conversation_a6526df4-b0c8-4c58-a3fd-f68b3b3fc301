var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
var wxMarkerData = [];
var mstimeoutId;
Page({
  data: {
    img1: app.imageUrl + 'tongyong.png',
    img2: app.imageUrl + 'xinren.png',
    img3: app.imageUrl + 'integral.png',
    img4: app.imageUrl + 'xianjin.png',
    // img5: app.imageUrl + 'youhui1.png',
    img5: app.imageUrl + 'PopupBackground.png',
    img6: app.imageUrl + 'shengri.png',
    img7: app.imageUrl + 'zhifu.png',
    img8: app.imageUrl + 'hongbao.png',
    img9: app.imageUrl + 'hongbao2.png',
    img10: app.imageUrl + 'goVipCard.png',
    goodsList: [],
    imgUrls: [
      app.imageUrl + 'activity/activity.png'
    ],
    bottom_logo: app.imageUrl + 'bottom.png',
    announce: app.imageUrl + 'announcement.png',
    three_icon: app.imageUrl + 'three_icon.png',
    newvip: app.imageUrl + 'icon1.png',
    material: app.imageUrl + 'icon2.png',
    scan: app.imageUrl + 'newscan.png',
    vipCode: app.imageUrl + 'newvipCode.png',
    brand: app.imageUrl + 'icon4.png',
    vip: app.imageUrl + 'icon5.png',
    item: app.imageUrl + 'activity/activity.png',
    newgoods_banner: app.imageUrl + 'activity/newgoods_banner.png',
    limit_banner: app.imageUrl + 'activity/limit_banner.png',
    fire: app.imageUrl + 'activity/fire.png',
    goods_mfy: app.imageUrl + 'production/mfy.png',
    goods_hlmm: app.imageUrl + 'production/hlmm.png',
    more: app.imageUrl + 'more.png',
    index_bg: app.imageUrl + 'index/index_bg.png',
    test_bg: '../../image/test11.jpg',
    event: app.imageUrl + 'event.png',
    autoplay: true, //是否自动切换
    //interval: 3000, //自动切换时间间隔
    duration: 500, //滑动动画时长
    circular: true, //是否采用衔接滑动
    tj_indicatorDots: true, //是否显示面板指示点
    xsqg_indicatorDots: true, //是否显示面板指示点
    xpss_indicatorDots: true, //是否显示面板指示点
    hgq_indicatorDots: true, //是否显示面板指示点
    indicatorColor: "rgba(0, 0, 0, .3)", //指示点颜色
    indicatorActiveColor: "#fa6a85", //当前选中的指示点颜色
    mode: 'widthFix',
    isFromBack: false,
    localCity: "", //当前地理位置
    winHeight: "", //窗口高度
    currentTab: 0, //预设当前项的值
    scrollLeft: 0, //tab标题的滚动条位置
    imgheights: [],
    current: 0,
    imgwidth: 750,
    xsqgGoods: [],
    hgqGoods: [],
    contentHidden: true,
    navigateList: [], //中部导航图标
    headnavList: [], //头部导航图标
    navigateNum: 0,
    navigateW: "20%",
    cardBanner: [],
    indexBanner: [],
    appletPromotionList: [],
    appletPromotionEvent: [],
    filterName: '',
    showGoodsList: [],
    isShowClassify: true,
    eventList: [],
    flag: false,
    shopperGift: app.imageUrl + 'shopperGift.png',
    cancelMark: app.imageUrl + 'cancelMark.png',
    vipGift: app.imageUrl + 'vipGift.png',
    vip_shopperGift: app.imageUrl + 'vipshopperGift.png',
    cardImageSrc: "",
    putCardHidden: true,
    giftBox: app.imageUrl + 'giftBox.png',
    online_service: app.imageUrl + 'online_service.png',
    indexListConfig: [],
    announctList: [],
    cardList: [],
    goodsTypeList: [],
    categoryIdList: [],
    commodityList: [],
    myAddress: app.imageUrl + 'locate.png',
    timeId: null,
    latitude: 0,
    longitude: 0,
    isShowStoreHidden: true,
    storeName: "",
    localUserAddress: "",
    localUserCity: "",
    addToShoppingCartHidden: true,
    liveImg: app.imageUrl + 'liveImg.png',
    isOnlineService: false,
    isOnLive: true,
    content_t: '', //内容
    size: 14, //宽度即文字大小
    marqueeW: 0,
    moveTimes: 12, //一屏内容滚动时间为8s
    allT: "0s",
    pickOrderStoreId: '',
    storeAddress: '',
    promotionIndex: 0,
    promotionJson: [],
    exchange_icon: app.imageUrl + 'exchange_icon.png',
    countDownList: [],
    actEndTimeList: [],
    mainLeftColor: '',
    mainRightColor: '',
    isVipCardSwitch: false,
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
  },
  switchSelectStoreBindTap: function () {
    var that = this;
    var locationObj = wx.getStorageSync('locationKey');
    var latitude = "";
    var longitude = "";
    var localUserAddress = "";
    var localUserCity = "";
    if (locationObj) {
      latitude = locationObj.latitude;
      longitude = locationObj.longitude;
      localUserAddress = locationObj.address;
      localUserCity = locationObj.localUserCity;
    } else {
      latitude = that.data.latitude;
      longitude = that.data.longitude;
      localUserAddress = that.data.localUserAddress;
      localUserCity = that.data.localUserCity;
    }
    app.navigateToPage('/pages/localAddress/localAddress?latitude=' + latitude + "&longitude=" + longitude + "&localUserAddress=" + localUserAddress + "&localUserCity=" + localUserCity);
  },
  goCouponCenterBind: function () {
    app.navigateToPage('/pages/couponCenter/couponCenter');
  },
  imageLoad: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight
    var imgheights = this.data.imgheights
    //把每一张图片的高度记录到数组里  
    imgheights.push(imgheight)
    this.setData({
      imgheights: imgheights,
      current: 0
    })
  },
  imageLoadAll: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight;
    var allimgheights = imgheight;
    //把每一张图片的高度记录到数组里  
    this.setData({
      allimgheights: allimgheights
    })
  },
  // 滚动切换标签样式
  switchTab: function (e) {
    this.setData({
      currentTab: e.detail.current
    });
    this.checkCor();
  },
  // 点击标题切换当前页时改变样式
  swichNav: function (e) {
    var cur = e.target.dataset.current;
    if (this.data.currentTab == cur) {
      return false;
    } else {
      this.setData({
        currentTab: cur
      })
    }
  },
  /**
   * 更多分类点击事件
   */
  moreTypeBindTap: function () {
    wx.switchTab({
      url: "/pages/goods_classify/goods_classify"
    });
  },
  /**
   * 首页商品分类点击事件
   */
  goCatogroyTap: function (e) {
    app.classifyData.classifyId = e.currentTarget.dataset.id;
    wx.switchTab({
      url: "/pages/goods_classify/goods_classify"
    });
  },
  /**
   * 扫码识别商品
   */
  sweepCodeBindTap: function () {
    var that = this;
    wx.scanCode({
      success(res) {
        var filterName = res.result;
        app.navigateToPage('/pages/searchPage/searchPage?filterName=' + filterName);
      }
    })
  },
  //判断当前滚动超过一屏时，设置tab标题滚动条。
  checkCor: function () {
    if (this.data.currentTab > 4) {
      this.setData({
        scrollLeft: 300
      })
    } else {
      this.setData({
        scrollLeft: 0
      })
    }
  },
  onLoad: function (options) {
    var that = this;
    that.setData({
      currentTab: that.data.currentTab
    });
    //  高度自适应
    wx.getSystemInfo({
      success: function (res) {
        var clientHeight = res.windowHeight,
          clientWidth = res.windowWidth,
          rpxR = 750 / clientWidth;
        var calc = clientHeight * rpxR;
        that.setData({
          winHeight: calc
        });
      }
    });
    that.initPage(options);
  },
  initPage: function (options) {
    var that = this;
    that.init_getExtMessage(options);
  },
  init_getExtMessage: function (options) {
    var that = this;
    if (wx.getExtConfig) {
      wx.getExtConfig({
        success(res) {
          var extObj = {
            "storeId": res.extConfig.storeId,
            "companyId": res.extConfig.companyId,
            "appId": res.extConfig.appId,
            "enterpriseCode": res.extConfig.enterpriseCode
          };
          wx.removeStorageSync("extStorageKey");
          app.setStorage({
            key: 'extStorageKey',
            data: extObj
          });
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/newSupplierSetting/querySupplierState',
            data: {
              "companyId": res.extConfig.companyId
            },
            success: function (result) {
              var code = result.data.code;
              if (code == -1 || code == 2 || code == 5) {
           //     app.reLaunchToPage("/pages/expireAccout/expireAccout");
              } else {
                //异步处理请求
                that.queryIndexTemplate(res.extConfig.companyId, res.extConfig.storeId)
                  .then(that.queryIndexPutCardInfo(res.extConfig.storeId, res.extConfig.companyId))
                  .then(that.queryNearestStoreInfo(res.extConfig.storeId))
                  .then(app.getSupplierSetting(res.extConfig.companyId))
                  .then(app.countRetailCartTotal())
                  .then(that.queryStoreInfoByStoreId(res.extConfig.storeId));
              }
            }
          })
        }
      })
    }
  },
  goLiveCast: function () {
    app.navigateToPage("/pages/onlineCast/onlineCast");
  },
  /**
   * 查询距离最近的店铺信息
   */
  queryNearestStoreInfo: function (storeId) {
    var that = this;
    var selectStoreInfoKey = wx.getStorageSync("selectStoreInfoKey");
    if (selectStoreInfoKey != "" && selectStoreInfoKey != undefined) {
      var storeName = selectStoreInfoKey.storeName;
      that.setData({
        storeName: storeName
      });

    } else {
      let vm = this;
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
            wx.showModal({
              title: '请求授权当前位置',
              content: '需要获取您的地理位置，请确认授权',
              success: function (res) {
                if (res.cancel) {
                  wx.showToast({
                    title: '拒绝授权,将无法获取门店地理位置',
                    icon: 'none',
                    duration: 1000
                  })
                  vm.queryStoreInfo(0, 0);

                } else if (res.confirm) {
                  wx.openSetting({
                    success: function (dataAu) {
                      if (dataAu.authSetting["scope.userLocation"] == true) {
                        wx.showToast({
                          title: '授权成功',
                          icon: 'success',
                          duration: 1000
                        })
                        //再次授权，调用wx.getLocation的API
                        vm.getLocation();
                      } else {

                        wx.showToast({
                          title: '授权失败',
                          icon: 'none',
                          duration: 1000
                        })
                        vm.queryStoreInfo(0, 0);
                      }
                    }
                  })
                }
              },
              fail: function () {

                vm.queryStoreInfo(0, 0);
              }
            })
          } else if (res.authSetting['scope.userLocation'] == undefined) {
            //调用wx.getLocation的API
            vm.getLocation();
          } else {
            //调用wx.getLocation的API
            vm.getLocation();
          }
        }
      })
    }
  },
  /**
   * 查询首页模版配置信息
   */
  queryIndexTemplate: function (companyId, storeId) {
    var that = this;

    return new Promise(function () {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/indexTemplate/queryIndexTemplate',
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": storeId,
          "companyId": companyId,
          "identity": 1
        },
        success: function (res) {
          wx.hideLoading();
          var indexListConfig = res.data.indexListConfig;
          var announctList = [];
          var cardList = [];
          var goodsTypeList = [];
          var categoryIdList = [];
          var commodityList = [];
          let promotionList = []
          var screenW = wx.getSystemInfoSync().windowWidth; //获取屏幕宽度
          allT = allT < 8 ? 8 : allT; //不够一平-----最小滚动一平时间
          if (indexListConfig != null && indexListConfig.length > 0) {
            for (var i = 0; i < indexListConfig.length; i++) {
              var floorType = indexListConfig[i].floorType;
              if (floorType == 2) {
                announctList = res.data.announctList;
                if (announctList != null && announctList.length > 0) {
                  var content_t = announctList[0].contentTitle + announctList[0].contentText;
                  var contentW = content_t.length * that.data.size; //获取文本宽度（大概宽度）
                  var allT = (contentW / screenW) * that.data.moveTimes; //文字很长时计算有几屏
                  that.setData({
                    content_t: content_t,
                    marqueeW: -contentW + "px",
                    allT: allT + "s"
                  })
                }
              } else if (floorType == 4) {
                cardList = res.data.cardList;
              } else if (floorType == 6) {
                goodsTypeList = res.data.goodsTypeList;
              } else if (floorType == 7) {
                categoryIdList = res.data.categoryIdList;
                commodityList = res.data.commodityList;
              } else if (floorType == 8) {

                for (var m = 0; m < indexListConfig[i].goodsTemplateList.length; m++) {
                  promotionList.push({ actEndTime: indexListConfig[i].goodsTemplateList[m].promotionEndDate })
                }
                let endTimeList = [];
                // 将活动的结束时间参数提成一个单独的数组，方便操作
                promotionList.forEach(o => { endTimeList.push(o.actEndTime) })
                that.setData({ actEndTimeList: endTimeList });
                // 执行倒计时函数
                // that.countDown();


              } else if (floorType == 12) {
                if (indexListConfig[i].showContent != '') {
                  var showContent = indexListConfig[i].showContent.split('@');
                  if (showContent[0] == '#ffffff' && showContent[1] == '#ffffff') {
                    wx.setNavigationBarColor({
                      frontColor: '#000000', // 必写项
                      backgroundColor: '#ffffff', // 传递的颜色值
                    })
                    that.setData({
                      mainLeftColor: '#ffffff',
                      mainRightColor: '#ffffff',
                      myAddress: app.imageUrl + 'locate.png',
                      scan: app.imageUrl + 'newscan.png',
                      vipCode: app.imageUrl + 'newvipCode.png'
                    })
                  } else {
                    wx.setNavigationBarColor({
                      frontColor: '#ffffff', // 必写项
                      backgroundColor: showContent[0], // 传递的颜色值
                    })
                    that.setData({
                      mainLeftColor: showContent[0],
                      mainRightColor: showContent[1],
                      myAddress: app.imageUrl + 'locate_white.png',
                      scan: app.imageUrl + 'newscan_white.png',
                      vipCode: app.imageUrl + 'newvipCode_white.png'
                    })
                  }

                }

              }
            }

          }
          that.setData({
            isOnlineService: res.data.isOnlineService,
            indexListConfig: indexListConfig,
            announctList: announctList,
            cardList: cardList,
            goodsTypeList: goodsTypeList,
            categoryIdList: categoryIdList,
            commodityList: commodityList
          });
        },
        fail: function () {
          wx.hideLoading();
        }
      })
    })
  },
  timeFormat(param) {//小于10的格式化函数
    return param < 10 ? '0' + param : param;
  },
  countDown() {//倒计时函数
    // 获取当前时间，同时得到活动结束时间数组
    var that = this;
    let newTime = new Date().getTime();
    let endTimeList = this.data.actEndTimeList;
    let countDownArr = [];
    // 对结束时间进行处理渲染到页面
    endTimeList.forEach(o => {
      let endTime = o;
      let obj = null;
      // 如果活动未结束，对时间进行处理
      if (endTime - Date.parse(new Date()) > 0) {
        let time = (endTime - newTime) / 1000;
        // 获取天、时、分、秒
        let day = parseInt(time / (60 * 60 * 24));
        let hou = parseInt(time % (60 * 60 * 24) / 3600);
        let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
        let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
        obj = {
          day: this.timeFormat(day),
          hou: this.timeFormat(hou),
          min: this.timeFormat(min),
          sec: this.timeFormat(sec),
          actEndTime: endTime
        }
      } else {//活动已结束，全部设置为'00'
        obj = {
          day: '00',
          hou: '00',
          min: '00',
          sec: '00',
          actEndTime: '0'
        }
      }
      countDownArr.push(obj);
    })
    // 渲染，然后每隔一秒执行一次倒计时函数

    var hash = {};    //去重
    countDownArr = countDownArr.reduce(function (item, next) {
      hash[next.actEndTime] ? '' : hash[next.actEndTime] = true && item.push(next);
      return item
    }, [])
    this.setData({ countDownList: countDownArr })
    clearTimeout(mstimeoutId)
    mstimeoutId = setTimeout(this.countDown, 1000);
  },
  /**
   * 查询商家是否推送卡券给用户
   */
  queryIndexPutCardInfo: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newPushCard/queryIndexPutCardInfo',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "cardMarketingType": "4,5",
        "companyId": companyId
      },
      success: function (res) {
        var indexFlag = res.data.indexFlag;
        if (indexFlag) {
          var storeCardList = res.data.storeCardList;
          that.setData({
            indexFlag: indexFlag,
            storeCardList: storeCardList,
            putCardHidden: false
          });
        } else {
          that.setData({
            indexFlag: indexFlag,
            putCardHidden: true
          });
        }
      }
    })
  },
  queryCardBagBindTap: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
    } else {
      app.navigateToPage('/pages/person_coupon/person_coupon');
    }

  },
  closeCardBgBindTap: function () {
    this.setData({
      putCardHidden: true,
      indexFlag: false
    });
  },
  /**
   * 微信小程序获取店铺信息
   */
  queryStoreInfoByStoreId: function (storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryStoreInfoByStoreId',
      data: {
        "storeId": storeId
      },
      success: function (res) {
        var storeName = res.data.storeName;
        var storeImage = res.data.storeImage;
        var storePhone = res.data.storePhone;
        var storeInfo = {
          "storeName": storeName,
          "storeImage": storeImage,
          "storePhone": storePhone
        };
        wx.setNavigationBarTitle({
          title: storeName
        });
        wx.removeStorageSync("storeInfoStorageKey");
        app.setStorage({
          key: 'storeInfoStorageKey',
          data: storeInfo
        });
      }
    })
  },
  /**
   * 关闭
   * */
  cancelButtonBindTap: function () {
    this.setData({
      contentHidden: true
    });
  },
  showAnnounceBindTap: function (e) {
    var index = e.currentTarget.dataset.index;
    var announctList = this.data.announctList;
    this.setData({
      announceTitle: announctList[index].contentTitle,
      announceContent: announctList[index].contentText,
      contentHidden: false
    });
  },
  /**
   * 首页banner图点击事件
   */
  bannerBindTap: function (e) {
    var id = e.target.dataset.id;
    var type = e.target.dataset.type;
    var click = e.target.dataset.click;
    if (click == 2) {
      if (type == 1) { //关联商品
        var pid = e.target.dataset.pid;
        var idArray = id.split(",");
        if (idArray.length == 1) {
          app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + id);
        } else {
          app.navigateToPage('/pages/moreGoods/moreGoods?type=3&id=' + pid);
        }
      } else if (type == 2) { //关联活动
        app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
      } else if (type == 3) { //关联积分商城
        app.navigateToPage('/pages/scoreMall/scoreMall');
      } else if (type == 6) { //关联转盘
        app.navigateToPage('/pages/turntableActivity/turntableActivity?sceneType=3');
      }
    }
  },
  /**
   * 跳转到商品详情(点击image)
   */
  imageClick: function (e) {
    var goodsId = e.target.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 跳转到商品详情(点击view)
   */
  imageClick1: function (e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 点击搜索框跳转
   */
  searchBindFocus: function () {
    app.navigateToPage('/pages/searchPage/searchPage');
  },
  onHide: function () {
    var that = this;
    clearTimeout(that.data.timeId);
    clearTimeout(mstimeoutId)
    if (that.selectComponent("#slideItem") != null) {
      that.selectComponent("#slideItem").stopSlide();
    }
  },
  // 下拉刷新
  // onPullDownRefresh: function () {
  //   var that = this;
  //   this.initPage()
  // },
  onShow: function (options) {
    var that = this;
    if (that.data.timeId != null) {
      if (that.selectComponent("#slideItem") != null) {
        that.selectComponent("#slideItem").ontest();
      }
    } else {
      var timeId = setTimeout(function () {
        if (that.selectComponent("#slideItem") != null) {
          that.selectComponent("#slideItem").ontest();
        }
      }, 5000);
      that.setData({
        timeId: timeId
      })
    }
    if (this.data.isFromBack) {
      this.initPage(options);
    } else {
      this.setData({
        isFromBack: true
      });
    }
    this.setData({
      currentTab: this.data.currentTab
    });
  },
  /**
   * 首页中部导航点击事件
   */
  indexNavigateBindTap: function (e) {
    var id = e.currentTarget.dataset.id;
    var type = e.currentTarget.dataset.type;
    var name = e.currentTarget.dataset.name;
    if (type == 1) { //文字图片素材页面
      app.navigateToPage('/pages/imageDesc/imageDesc?id=' + id + "&name=" + name);
    } else if (type == 2) { //活动商品页面onePromotion
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
          "id": id
        },
        url: app.projectName + '/indexTemplate/queryNavigationDetail',
        success: function (res) {
          var navigationEntity = res.data.navigationEntity;
          if (navigationEntity != null) {
            var content = navigationEntity.content;
            var promotionId = "";
            if (content.indexOf("@") > -1) {
              promotionId = content.substring(0, content.indexOf("@"));
            } else {
              promotionId = content;
            }
            app.navigateToPage('/pages/onePromotion/onePromotion?id=' + promotionId);
          }
        }
      })
    }
  },
  /**
   * 首页活动版块点击事件
   */
  indexMoveBlockBindTap: function (e) {
    var relation = e.target.dataset.relation;
    var type = e.target.dataset.type;
    var content = e.target.dataset.content;
    var pK_id = e.target.dataset.id;
    if (relation == 1) { //关联
      var id = "";
      if (content != null && content.length > 0) {
        if (content.indexOf("@") > -1) {
          id = content.substring(0, content.indexOf("@"));
        } else {
          id = content;
        }
        if (type == 1) { //关联商品
          var idArray = id.split(",");
          if (idArray.length == 1) {
            app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + id);
          } else {
            app.navigateToPage('/pages/moreGoods/moreGoods?type=2&id=' + pK_id);
          }
        } else if (type == 2) { //关联活动
          app.navigateToPage('/pages/onePromotion/onePromotion?id=' + id);
        } else if (type == 3) { //关联积分商城
          app.navigateToPage('/pages/scoreMall/scoreMall');
        } else if (type == 4) { //关联卡券信息
          app.navigateToPage('/pages/couponTypeCenter/couponTypeCenter');
        } else if (type == 5) { //关联直播间
          app.navigateToPage("/pages/onlineCast/onlineCast");
        } else if (type == 6) { //关联转盘
          app.navigateToPage("/pages/turntableActivity/turntableActivity?sceneType=3");
        }
      }
    }
  },
  /**
   * 商品模块更多点击事件
   */
  goodsMoreBindTap: function (e) {
    var id = e.currentTarget.dataset.id;
    app.navigateToPage('/pages/moreGoods/moreGoods?type=1&id=' + id);
  },
  /**
   * 免费领取券
   */
  userGetCardBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.showLoading({
      title: '卡券领取中...',
      mask: true
    })
    var cardId = e.currentTarget.dataset.id;
    that.checkCardIsHave(cardId);
  },
  /**
   * 检查卡券库存是否充足
   */
  checkCardIsHave: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailUserCard/checkCardIsHave',
      data: {
        "cardId": cardId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          var storeCardList = that.data.cardList;
          for (var i = 0; i < storeCardList.length; i++) {
            if (storeCardList[i].cardId == cardId) {
              if (storeCardList[i].isFree == 1) {
                that.freeGetCardBindTap(cardId);
              }
              break;
            }
          }
        } else {
          wx.hideLoading();
        }
      }
    })
  },
  /**
   * 免费领取卡券
   */
  freeGetCardBindTap: function (cardId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailUserCard/addUserCard',
      data: {
        "cardId": cardId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "userName": app.getLoginName()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: '领取成功',
            duration: 1500
          })
          that.queryIndexCardDetail();
        } else {
          wx.showToast({
            title: '领取失败',
            duration: 1500
          })
        }
      }
    })
  },
  /**
   * 查询首页卡券信息
   */
  queryIndexCardDetail: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/indexTemplate/queryIndexCardDetail',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var cardList = res.data.cardList;
        that.setData({
          cardList: cardList
        })
      }
    })
  },
  /**
   * 查询当前会员是否有会员卡，如果有则跳转到会员卡页面，如果没有跳转到个人中心页面
   */
  goToVipCardBindTap: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    }
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      app.navigateToPage('/pages/vipCard/vipCard');
    } else {
      that.getMyVipCardInfo();
    }
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          var cardNo = cardList[0].cardId;
          app.navigateToPage('/pages/myvipCard/myvipCard?cardNo=' + cardNo);
        } else {
          app.navigateToPage('/pages/vipCard/vipCard');
        }
      }
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    app.commonShareApplet(res);
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  getLocation: function () {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    /*wx.getLocation({
      type: 'gcj02',
      altitude: true,
      complete: function (res) {
        if (res.errMsg == "getLocation:ok") {
          var latitude = res.latitude;
          var longitude = res.longitude;
          qqmapsdk.reverseGeocoder({
            location: {
              latitude: latitude,
              longitude: longitude
            },
            success: function (res) { //成功后的回调
              var res = res.result;
              that.setData({
                localUserAddress: res.address,
                localUserCity: res.address_component.city
              })
              qqmapsdk.geocoder({
                //获取表单传入地址
                address: res.address, //地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
                success: function (res) { //成功后的回调
                  var res = res.result;
                  var latitude = res.location.lat;
                  var longitude = res.location.lng;
                  that.setData({
                    latitude: latitude,
                    longitude: longitude
                  });
                  that.queryStoreInfo(latitude, longitude);
                },
                fail: function (error) {
                  console.error(error);
                },
                complete: function (res) {
                }
              })
            },
            fail: function (error) {
              console.error(error);
            },
            complete: function (res) {
            }
          })
        } else {
          that.setData({
            latitude: 0,
            longitude: 0
          });
          that.queryStoreInfo(latitude, longitude);
        }
      }
    })*/
  },
  /**
   * 查询店铺信息
   */
  queryStoreInfo: function (latitude, longitude) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryNewSelfStoreInfo',
      data: {
        "companyId": app.getExtCompanyId(),
        "storeName": "",
        "currentPage": 1,
        "pagesize": 1,
        "latitude": latitude,
        "longitude": longitude,
        "localUserAddress": that.data.localUserAddress,
        "localUserCity": that.data.localUserCity
      },
      success: function (res) {
        var returnStoreList = res.data.returnStoreList;
        if (returnStoreList != null && returnStoreList.length > 0) {
          var pickOrderStoreId = returnStoreList[0].storeId;
          var chooseStoreValue = returnStoreList[0].storeName;
          var storeAddress = returnStoreList[0].storeAddress;
          that.setData({
            pickOrderStoreId: pickOrderStoreId,
            storeName: chooseStoreValue,
            storeAddress: storeAddress
          })
          var extObj = {
            "storeId": returnStoreList[0].storeId,
            "storeName": returnStoreList[0].storeName
          };

          wx.removeStorageSync("selectStoreInfoKey");
          app.setStorage({
            key: 'selectStoreInfoKey',
            data: extObj
          });
        } else {

        }
      }
    })
  },
  closeVipCardBindTap: function () {

    this.setData({
      isVipCardSwitch: false
    });
  },
  goVipCard: function () {
    app.navigateToPage('/pages/vipCard/vipCard');
  }
})