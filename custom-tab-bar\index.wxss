.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 96rpx;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index:9999;
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position:relative;
}

.tab-bar-item cover-image {
  width: 27px;
  height: 27px;
}

.tab-bar-item cover-view {
  font-size: 10px;
}
.tab-bar-dot{
  color: #fff;
  position: absolute;
  top: 12rpx;
  right:50rpx;
  min-width: 18rpx;
  height: 18rpx;
  padding: 8rpx;
  border-radius: 34rpx;
  background: #ef4c5a;
  font-size: 24rpx;
  line-height: 18rpx;
}
