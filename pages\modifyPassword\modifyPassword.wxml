<view class="list-block">
  <view>
    <input type="number" bindinput="bindKeyInput" maxlength="11" placeholder='输入手机号码'></input>
  </view>
  <view>
    <input type="password" bindinput='passwordInput' placeholder='设置登录密码'></input>
  </view>
  <view>
    <input type="password" bindinput='againPasswordInput' placeholder='再次输入密码'></input>
  </view>
  <view>
    <input class='sms_verification' bindinput='smsCodeInput' type="number" maxlength="6" placeholder='请输入验证码'></input>
    <label bindtap='smsBindTap' class='sms_btn'>{{secondDesc}}</label>
  </view>
</view>
<button class="confirm_btn" bindtap='registerBindTap'>
  提交
</button>
<label class='existing_account' bindtap='goToLogin'>已有账号></label>