// pages/sign/sign.js
const app = getApp();
const popup = require('../popupTemplate/popupTemplate.js');
const http = require('../../utils/http');
const ui = require('../../utils/ui');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //跑马灯文字参数
    marqueePace: 1, //滚动速度
    marqueeDistance: 0, //初始滚动距离
    marqueeDistance2: 0,
    marquee2copy_status: false,
    marquee2_margin: 60,
    size: 26,
    orientation: 'left', //滚动方向
    interval: 15, // 时间间隔 ,
    disabled: false, //签到按钮状态
    calendar: app.imageUrl + 'sign_calendar.png',
    reward: app.imageUrl + 'sign_reward.png',
    signature: app.imageUrl + 'sign_signature.png',
    notice: app.imageUrl + 'sign_notice.png',
    signed: app.imageUrl + 'sign_signed.png',
    notsigned: app.imageUrl + 'sign_notsigned.png',
    signAD: app.imageUrl + 'sign_signAD.jpg',
    noticeHeight: 185,
    isFill: false,
    cardFlag: false,
    isFromBack: false,
    joinSet: 0,
    turntable_bj: app.imageUrl + 'turntable_bj.png',
    prizePopupHidden:false
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.getPromotionDetail();
  },
  getShowList:function(obj){
     var that = this;
     var scheduleDetailList = that.data.scheduleDetailList;
     var resultList = [];
     for(var i=obj;i<obj+7;i++){
        resultList.push(scheduleDetailList[i]);
     }
     return resultList
  },
  getPromotionDetail:function(){
    var that = this;
    var data = {};
    data["tm"] = "/signin/retailClient/detail";
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/simpleRouter/stickness',
          data: data,
          success: function (res) {
            if(res.data.code == 1){
              if(!res.data.data){
                wx.showModal({
                  content: '暂无签到活动',
                  confirmText: "确定",
                  cancelText: "取消"})
                return;
              }
              var resData = JSON.parse(res.data.data);
              var promotionId = resData.recordId;
              that.setData({
                resData:resData
              })
              that.setData({
                promotionId:promotionId
              })
              var activityTimeContinuity = resData.activityTimeContinuity; // 代表的是执行的类型 1:每周执行 2:每月执行 3:执行一次 4.中断
              var scheduleDetailList = resData.scheduleDetailList
              that.setData({
                scheduleDetailList:scheduleDetailList
              })
              var showList = [];
              var isSignFlag = false;
              var currenDayFlag = false;
              var currentDayPos = resData.currentDayPos;
              for(var i=0;i<scheduleDetailList.length;i++){
                  if(scheduleDetailList[i].curpos == currentDayPos){ /*代表当天签到的数据*/
                    currenDayFlag = true; 
                    that.setData({
                      currentDayPos:currentDayPos,
                      joinSet:scheduleDetailList[i].joinSeat
                    })
                  }
                  if(showList.length >=7){
                    that.setData({
                      showList:showList
                    })
                    console.log(showList);
                    return;
                  }
                  if(activityTimeContinuity != 4){
                    if(scheduleDetailList[i].curpos == currentDayPos){
                      if(i>=3){
                        showList = that.getShowList(i-3)
                      }
                      else{
                        showList = that.getShowList(0)
                      }
                      console.log(showList);
                    }
                  }
                  if(activityTimeContinuity == 4){
                    if(scheduleDetailList[i].joinSeat == 1){/*代表的是已经签到*/
                      isSignFlag = true
                    }
                    if(isSignFlag){
                      showList.push(scheduleDetailList[i]);
                    }
                    if(!isSignFlag && currenDayFlag){
                      showList.push(scheduleDetailList[i]);
                    }
                  }

                  
              }

            }
            else{
            }
          },
          fail: function () {
            wx.hideLoading();
          }
        })
  },
  prizeShowBindTap:function(){
    var that = this;
    that.setData({
      prizePopupHidden:false
    })

  },
  toSignBind:function(){
    console.log("11")
    var that = this;
    var data = {};
    data["tm"] = "/signin/retailClient/join";
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["userId"] = app.getUserId();
    data["loginId"] = app.getLoginId();
    data["userRole"] = app.getUserRole();
    data["odbtoken"] = app.getodbtoken();
    data["loginToken"] = app.getloginToken();
    data["companyId"] = app.getExtCompanyId();
    data["storeId"] = app.getExtStoreId();
    data["promotionId"] = that.data.promotionId;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/simpleRouter/stickness',
      data: data,
      success: function (res) {
        if(res.data.code == 1){ /*代表签到成功*/
           var showList = that.data.showList;
           for(var i=0;i<showList.length;i++){
             if(showList[i].curpos == that.data.currentDayPos){
              showList[i].joinSeat = 1;
             }
           }
           var resData = JSON.parse(res.data.data);
           var tipsType = resData.tipsType;
           var showContent = "";
           if(tipsType == 4){ /*代表有奖励*/
            showContent = resData.prizeContentList[0].prizeContent;
           }
           else{
            showContent = resData.tipsContent
           }
           that.setData({
            prizePopupHidden:true,
            showContent:showContent
           })
          that.getPromotionDetail();
        }
        else{
            wx.showModal({
              content: res.data.msg,
              confirmText: "确定",
              cancelText: "取消"})
        }
      },
      fail: function () {
        wx.hideLoading();
      }
    })
  },
  imageClick: function (e) {
    var goodsId = e.target.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /** 
   * 获取首页推荐商品
   */
  getIndexRetailRecommend: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/getIndexRetailRecommend',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var wechatAppletList = res.data.wechatAppletList;
        for (var i = 0; i < wechatAppletList.length; i++) {
          wechatAppletList[i].goodsPrice = parseFloat(wechatAppletList[i].goodsPrice.toFixed(2));
          wechatAppletList[i].cutOffThePrice = parseFloat(wechatAppletList[i].cutOffThePrice.toFixed(2));
        }
        that.setData({
          indexGoodsList: wechatAppletList
        });
        wx.hideLoading();
      },
      fail: function () {
        app.showModal({
          title: "提示",
          content: "数据加载异常"
        });
        wx.hideLoading();
      }
    })
  },
  /**
  * 查询获奖配置
  */
  getPrize: function (configId) {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.errorcode == '1000') {
          wx.showToast({
            title: '恭喜您获得一张补签卡！',
            icon: 'success',
            duration: 2000
          })
        }
      }
    })
  },
  /**
   * 查询商户签到配置信息
   */
  goSupplyClick: function () {
    var that = this;
    that.signature();
  },
  /**
 * 查询用户是否有会员卡
**/
  getUserCard: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          that.setData({
            cardFlag: true
          })
        } else {

        }
      }
    })
  },


  /**
   * 补签
   */
  toSignature: function () {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'signin/userSigninByCard',
      showLoading: false,
      data: {
        userSigninId: that.data.usersignid,
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res == true) {
          ui.showToast("补签成功");
          that.hiddenFloatView();
          that.onLoad();
        } else {
          ui.showToast(res.errormsg);
        }
      }
    })
  },
  /**
   * 我的奖励
   */
  toMyreward: function () {
    var that = this;
    console.log(that.data.promotionId)
    app.navigateToPage('/pages/myReward/myReward?promotionId='+that.data.promotionId);
  },
  /**
   * 签到规则
   */
  toSignRules: function () {
    app.navigateToPage('/pages/signRules/signRules');
  },
  /**
 * 去购物
 */
  toIndexClick: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
* 去评价页面
*/
  toOrderClick: function () {
    app.navigateToPage("/pages/indexThree/indexThree?currentTab=7");
  },
  /**
* 去成为会员
*/
  toCardClick: function () {
    app.navigateToPage('/pages/openonecard/openonecard');
  },
  /**
* 去完善资料
*/
  toProfileClick: function () {
    app.navigateToPage('/pages/changeProfile/changeProfile');
  },
  /**
   * 去我的券包
   */
  toCoupons: function () {
    var that = this;
    app.navigateToPage('/pages/person_coupon/person_coupon');
    that.hiddenFloatView();
  },
  /**
   * 去我的会员卡
   */
  toVipCard: function () {
    var that = this;
    app.navigateToPage('/pages/vipCard/vipCard');
    that.hiddenFloatView();
  },
  /**
   * 去抽奖
   */
  toLuckdraw: function () {
    var that = this;
    app.navigateToPage("/pages/turntableActivity/turntableActivity?sceneType=4");
    that.hiddenFloatView();
  },
  cancellottery: function () {
    var that = this;
    app.showModal({
      content: '放弃后将无法获得奖励',
      showCancel: true,
      confirm: function () {
        that.hiddenFloatView();
      }
    })
  }
})
