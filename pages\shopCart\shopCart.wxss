.confirm_btn {
  width: 88%;
  margin: 20px auto;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  border: none;
  background-color: #FF7E00;
  color: #fff;
}
.check-box.checked {
  background-color: #FF7E00;
  
  }
  
page {
  background: #f4f4f4;
}

.price_append {
  text-decoration: line-through;
  font-size: 12px;
}

.shoppingCart-top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 2.625rem;
  line-height: 2.625rem;
  padding: 0 0.625rem;
  margin-bottom: 0.625rem;
  /**box-shadow: 0 0.0625rem 0.125rem #eee;**/
  text-align: center;
  z-index: 9;
  background-color: #fff;
}

.top-nav-left {
  text-align: left;
  padding-left: 20rpx;
}

.top-nav-right {
  position: absolute;
  top: 0;
  right: 0.625rem;
  height: 100%;
}

.top-nav-right view {
  font-size: 14px;
}

.shoppingCart-edit-bar {
  position: fixed;
  top: 42px;
  left: 0;
  right: 0;
  margin-bottom: 0.625rem;
  padding: 0.3125rem 0.625rem;
  line-height: 1.9375rem;
  background: #fff;
  z-index: 20;
  border-top: 1px solid #ececec;
  box-shadow: 0 0.0625rem 0.125rem #eee;
}

.shoppingCart-list-wrap {
  margin-top: 2.8125rem;
  margin-bottom: 2.8125rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.shoppingCart-list-wrap.editing-list {
  margin-top: 90px;
  margin-bottom: 0;
}

.shoppingCart-goods-list {
  background-color: #fff;
  margin-bottom: 0.625rem;
}

.shoppingCart-goods-list:last-child {
  margin-bottom: 0;
}

.shoppingCart-goods-list>view {
  position: relative;
  border-bottom: 1px solid #e5e5e5;
}

/* .shoppingCart-goods-list > view:first-child {
  border-top: 1px solid #e5e5e5;
} */

.shoppingCart-goods-content {
  padding: 0.625rem 10px 0.625rem 0;
  height: 150rpx;
}

.shoppingCart-goods-right {
  /* position: absolute;
  bottom: 0.75rem;
  right: 0.75rem; */
  line-height: 1.25rem;
  text-align: right;
  float: right;
}

.shoppingCart-goods-list .shoppingCart-check-box {
  margin: 60rpx 0.625rem 0;
  padding: 0.3rem 0;
  float: left;
}

.shoppingCart-goods-list .check-box {
  margin: 0;
}

.shoppingCart-goods-cover {
  float: left;
  width: 150rpx;
  height: 150rpx;
  margin-right: 22rpx;
  border-radius: 10rpx;
}

.shoppingCart-goods-title {
  color: #666;
  font-size: 14px;
}

.shoppingCart-goods-model,
.shoppingCart-goods-original-price {
  margin-top: 6rpx;
  color: #818181;
  font-size: 24rpx;
  line-height: 1.3em;
}

.shoppingCart-goods-original-price>text {
  text-decoration: line-through;
}

.shoppingCart-bottom-nav {
  position: fixed;
  left: 0;
  right: 0;
  bottom: calc(96rpx + env(safe-area-inset-bottom));
  padding-left: 10px;
  line-height: 100rpx;
  z-index: 9;
  background-color: #fff;
  box-shadow: 0 -2rpx 5rpx #eee;
}

.shoppingCart-check-box,
.pull-right {
  font-size: 14px;
}

.shoppingCart-all-price {
  color: #FF7E00;
}

.shoppingCart-goto-pay {
  min-width: 6rem;
  background-color: #FF7E00;
  border-color: #FF7E00;
  color: #fff;
  height: 80rpx;
  border-radius: 0;
  line-height: 80rpx;
  padding: 0 20px;
  font-size: 28rpx;
  border-radius: 45rpx;
  margin: 10rpx 10rpx;

}

.shoppingCart-pay-dialog {
  text-align: center;
}

.shoppingCart-pay-dialog .shoppingCart-check-box {
  margin-top: 0.375rem;
}

.shoppingCart-dialog-detail>view {
  line-height: 2.125rem;
}

.shoppingCart-payment {
  text-align: left;
  padding: 0 0.9375rem;
}

.shoppingCart-pay-directly {
  margin: 0 0.9375rem;
}

.classify_box {
  color: #888;
  font-size: 12px;
  margin-top: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shoppingCart-goods-price {
  color: #FF7E00;
  margin-top: 3px;
  line-height: 26px;
}

.shoppingCart-goods-price>text {
  font-size: 15px;
}

#shoppingCart .minus,
#shoppingCart .plus {
  position: relative;
  display: inline-block;
  font-size: 22px;
  outline: 0 !important;
  text-indent: -9999px;
  overflow: hidden;
  vertical-align: middle;
  width: 34px;
  height: 34px;
  line-height: 34px;
  font-weight: normal;
  background-color: #f1f1f1;
  color: #878787;
  border-radius: 2px;
  border: none;
}

#shoppingCart .minus.disabled,
#shoppingCart .plus.disabled {
  background-color: #f9f9f9;
  color: #cacaca;
}

#shoppingCart .minus:before,
#shoppingCart .plus:before,
#shoppingCart .plus:after {
  position: absolute;
  width: 8px;
  height: 2px;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: #878787;
  bottom: 0;
  content: '';
}

#shoppingCart .plus:after {
  width: 2px;
  height: 8px;
}

#shoppingCart .minus.disabled:before,
#shoppingCart .plus.disabled:before,
#shoppingCart .plus.disabled:after {
  background-color: #cacaca;
}

.quantity input {
  border: none;
  height: 25px;
  background-color: #f1f1f1;
  padding: 0;
  margin: 0 1px;
  border-radius: 0;
  width: 40px;
  min-height: 34px;
  line-height: 34px;
  font-size: 16px;
}

.tips_pic {
  display: block;
  margin: 0 auto;
  width: 180px;
}

/**热销单品**/

.section {
  line-height: 26px;
  margin-top: 0px;
}

.section_title {
  text-align: center;
  font-size: 16px;
  background: #f4f4f4;
  line-height: 50px;
  height: 50px;
  margin-top: 0px;
  color: #FF7E00;
  letter-spacing: 1px;
  position: relative;
}

.section_title image {
  width: 18px;
  height: 18px;
  position: absolute;
  margin-top: 15px;
}

.flex-wrp {
  flex-direction: row;
  background: #f0f0f0;
}

.flex-item {
  width: 49.2%;
  float: left;
  background: #fff;
  margin-bottom: 6px;
}

.flex-item:nth-child(2n+1) {
  margin-right: 1.6%;
}

.goods_tips {
  color: #958353;
  border: 1px solid #bcb396;
  top: 10px;
  left: 10px;
  width: 12px;
  height: 62px;
  line-height: 16px;
  display: block;
  position: absolute;
  font-size: 12px;
  word-wrap: break-word;
  padding: 4px;
  border-radius: 3px;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.soonlist {
  font-size: 14px;
  writing-mode: vertical-lr;
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 2;
  color: #988250;
  border: 1px solid #988250;
  border-radius: 5px;
  padding: 5px 0;
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.goods_adv {
  padding: 0 5px;
  height: 36px;
  line-height: 36px;
  background: #f0ece1;
  display: block;
  color: #948358;
  font-size: 12px;
  overflow: hidden;
}

.goods_title {
  display: block;
  /**height: 28px;
  line-height: 28px;**/
  overflow: hidden;
  font-size: 30rpx;
  margin: 0 5px;
  height: 70rpx;
  line-height: 34rpx;
  margin-top: 12rpx;
}

.goods_price {
  color: #FF7E00;
  font-size: 15px;
  margin-left: 5px;
}

.price_append {
  text-decoration: line-through;
  font-size: 12px;
  color: #666;
}

.shoppingCart-goods-right {
  /* position: absolute;
  bottom: 0.75rem;
  right: 0.75rem; */
  line-height: 1.25rem;
  text-align: right;
  float: right;
}
.quantity input {
  border: none;
  height: 25px;
  background-color: #f1f1f1;
  padding: 0;
  margin: 0 1px;
  border-radius: 0;
  width: 40px;
  min-height: 34px;
  line-height: 34px;
  font-size: 16px;
}
/* 修改弹窗 */
.scroll_block {
  width: 80%;
  position: fixed;
  top: 20%;
  left: 10%;
  z-index: 130;
  padding-bottom: 46px;
  border-radius: 10px;
  background: #fff;
}

.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.append_title {
  margin-top: 40px;
}
.unit_num {
  vertical-align: top;
  margin-left: 10rpx;
  font-size: 28rpx;
  line-height: 60rpx;
  height: 60rpx;
  width: 30rpx;
}
.limited_quantity {
  font-size: 14px;
  color: #666;
  display: block;
  width: 100%;
  text-align: center;
  margin-bottom: 10rpx;
}
.orderLimit {
  text-align: center;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.orderWrap {
  margin-top: 10px;
  
}
.confirmButton {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  width: 66%;
  background: #FF7E00;
  color: #fff;
  text-align: center;
  margin-left: 18%;
  margin-top: 15px;
  border-radius: 20px;
}
.numBox {
  height: 60rpx;
  border: 1px solid #ddd;
  text-align: center;
  display: inline-block;
  width: 25%;
  margin: 0 10rpx 20rpx;
  background-color: #FAFBFA;
}
.plus_box{
  border: 1px solid #ddd;
  font-size: 46rpx;
  color:#CBCBCB;
  width: 60rpx;
  height: 60rpx;
  line-height: 58rpx;
  text-align: center;
  background-color: #FAFBFA;
}
.minus_box{
  border: 1px solid #ddd;
  font-size: 46rpx;
  color:#CBCBCB;
  width: 60rpx;
  height: 60rpx;
  line-height: 58rpx;
  text-align: center;
  background-color: #FAFBFA;
}
.r_wrap{
  padding-bottom: env(safe-area-inset-bottom);
  margin-bottom:100rpx;
}
.pic_two {
  display: flex;
  width: 700rpx;
  margin: 0 auto;
  flex-wrap: wrap;
}

.pic_two>view:nth-child(2n+1) {
  margin-right: 20rpx;
}

.pic_two .pic_two_goods {
  width: 340rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.pic_two .pic_two_goods image {
  width: 340rpx;
  height: 340rpx;
}

.commodity_box3 {
  width: 340rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.commodity_box3 image {
  width: 340rpx;
  height: 340rpx;
}

.line_price {
  font-size: 18rpx;
  text-decoration: line-through;
  color: #666;
  margin-left: 6rpx;
}

.desc_title {
  font-size: 28rpx;
  height: 80rpx;
  overflow: hidden;
}

.desc_price {
  margin-top: 20rpx;
}

.price_tag {
  color: #FF7E00;
  font-size: 22rpx;
}

.price_inner {
  color: #FF7E00;
  font-size: 32rpx;
}

.line_price {
  font-size: 18rpx;
  text-decoration: line-through;
  color: #666;
  margin-left: 6rpx;
}

.goods_desc {
  padding: 14rpx 10rpx;
}