<wxs src="../../../wxs/subutil.wxs" module="tools" />
<view style="font-size:29rpx;">
  <view class="reabateTop">
    <view class="rebate_m">
      <view class="m_l">
        <view>可用金额（元）</view>
        <view class="widthdraw_m">￥<label>{{tools.sub.formatAmount(validRebateMoney,2)}}</label></view>
      </view>
      <label class="m_r" bindtap="withdrawalBindTap">提 现</label>
    </view>
    <view class="rebate_b">
      <label class="widthdraw_d" bindtap="extractDetailBindTap">提现详情</label>
      <label style="margin-left:20rpx;" class="widthdraw_d" bindtap="ruleClickBindTap">提现规则</label>
    </view>
  </view>
  <view class="rebate_total">
    <view class="total_d">
      <label>￥{{tools.sub.formatAmount(accountWincreaseQuota,2)}}</label>
      <label>即将到账金额</label>
    </view>
    <view class="total_d">
      <label>￥{{tools.sub.formatAmount(accountWdeductionQuota,2)}}</label>
      <label>即将扣除金额</label>
    </view>
  </view>
  <view class="accountDetail">
    <view class="detailTop">
      <view class="{{type==0?'active':''}}" data-type="0" bindtap="selectAccountTypeBindTap">全部</view>
      <view class="{{type==1?'active':''}}" data-type="1" bindtap="selectAccountTypeBindTap">收入</view>
      <view class="{{type==2?'active':''}}" data-type="2" bindtap="selectAccountTypeBindTap">支出</view>
      <view bindtap="selectBillClick">筛选<image class="selectImage" src="{{unselected}}" mode="widthFix"></image>
      </view>
    </view>
    <scroll-view style="height:{{windowHeight}}" scroll-y class="r_wrap" bindscrolltolower="scrolltolowerTip">
      <block wx:if="{{ajList.length>0}}">
        <block wx:key="unique" wx:for-index="ajIndex" wx:for="{{ajList}}" wx:for-item="aj">
          <block wx:if="{{aj.journaType==1}}">
            <view class="r_detail">
              <image src="{{income}}" mode="widthFix"></image>
              <view class="r_detail_m">
                <label>{{aj.journaComment}}</label>
                <label class="r_detail_t">{{aj.journaTime}}</label>
              </view>
              <view class="detail_income">+{{tools.sub.formatAmount(aj.rebateMoney,2)}}</view>
            </view>
          </block>
          <block wx:elif="{{aj.journaType==2}}">
            <view class="r_detail">
              <image src="{{outcome}}" mode="widthFix"></image>
              <view class="r_detail_m">
                <label>{{aj.journaComment}}</label>
                <label class="r_detail_t">{{aj.journaTime}}</label>
              </view>
              <view class="detail_outcome">-{{tools.sub.formatAmount(aj.rebateMoney,2)}}</view>
            </view>
          </block>
        </block>
      </block>
      <block wx:else>
        <!--未查询到流水展示-->
        <view class="no_journal">
          <image mode="widthFix" src="{{noJournal}}"></image>
        </view>
      </block>
    </scroll-view>
  </view>
</view>
<view class="black_bg" hidden="{{bgHidden}}" bindtap="closeBlackBgBindTap"></view>
<view hidden="{{bgHidden}}" class="popWrap">
  <view class="detailTop">
    <view bindtap="closeBgHiddenBindTap" data-type="0">全部</view>
    <view bindtap="closeBgHiddenBindTap" data-type="1">收入</view>
    <view bindtap="closeBgHiddenBindTap" data-type="2">支出</view>
    <view bindtap="closeBgHiddenBindTap" data-type="3" class="selectItem">筛选<image src="{{selected}}"
        class="selectImage" mode="widthFix">
      </image>
    </view>
  </view>
  <view class="timeSelect">按时间筛选</view>
  <view class="timePicker">
    <view class="timeBox">
      <picker mode="date" value="{{date1}}" bindchange="bindStartDateChange">
        <view class="picker">
          {{date1}}
        </view>
      </picker>
    </view>
    <view style="padding-top: 20rpx;">~</view>
    <view class="timeBox">
      <picker mode="date" value="{{date2}}" bindchange="bindEndDateChange">
        <view class="picker">
          {{date2}}
        </view>
      </picker>
    </view>
  </view>
  <view class="popBtn">
    <label bindtap="resetDateBindTap">重置</label>
    <label bindtap="selectRebateWhereBindTap">确定</label>
  </view>
</view>
<view bindtap="closeRuleBind" class="black_bg" hidden="{{ruleShow}}"></view>
<view bindtap="closeRuleBind" class="ri_wrap"><image mode="widthFix"  hidden="{{ruleShow}}" style="width:600rpx;" src="{{d_rule}}"></image></view>