var app = getApp();
var TimeUtil = require('../../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    income: app.imageUrl + 'income.png',
    outcome: app.imageUrl + 'outcome.png',
    selected: app.imageUrl + 'selected.png',
    unselected: app.imageUrl + 'unselected.png',
    noJournal: app.imageUrl + 'noJournal.png',
    d_rule: app.imageUrl + 'd_rule.png',
    date1: '开始时间',
    date2: '结束时间',
    bgHidden: true,
    validRebateMoney: 0.00,//可用余额
    accountWincreaseQuota: 0.00,//即将到账金额
    accountWdeductionQuota: 0.00,//即将扣除越
    minExtractMoney: 0,//最低提现金额
    pageSize: 10,
    currentPage: 1,
    type: 0,
    ajList: [],
    isFromBack: false,
    isPage: true,
    ruleShow: true
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#ff6600'
    })
    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          windowHeight: res.windowHeight * 2 - 600 + "rpx",
        })
      }
    })
    this.initPage();
  },
  initPage: function () {
    var that = this;
    that.userQueryRebateAccount();
    that.userQueryRebateJourna();
  },
  /**
   * 用户查询自己的返利账目信息
   */
  userQueryRebateAccount: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/rebateAccountServer/userQueryRebateAccount',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var rebateAccountEntity = res.data.rebateAccountEntity;
        if (rebateAccountEntity != null) {
          that.setData({
            minExtractMoney: rebateAccountEntity.minExtractMoney,
            validRebateMoney: rebateAccountEntity.validRebateMoney,
            accountWincreaseQuota: rebateAccountEntity.accountWincreaseQuota,
            accountWdeductionQuota: rebateAccountEntity.accountWdeductionQuota
          });
        }
      }
    })
  },
  /**
   * 用户分页查询返利流水记录
   */
  userQueryRebateJourna: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/rebateAccountServer/userQueryRebateJourna',
      data: {
        "type": that.data.type,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "currentPage": that.data.currentPage,
        "pageSize": that.data.pageSize,
        "startTime": that.data.date1,
        "endTime": that.data.date2
      },
      success: function (res) {
        var new_ajList = res.data.ajList;
        if (new_ajList != null && new_ajList.length > 0) {
          for (var i = 0; i < new_ajList.length; i++) {
            new_ajList[i].journaTime = TimeUtil.getSmpFormatDateByLong(new_ajList[i].journaTime, true);
          }
        }
        var old_ajList = that.data.ajList;
        if (old_ajList != null && old_ajList.length > 0) {
          new_ajList = old_ajList.concat(new_ajList);
        }
        var total = res.data.total;
        var isPage = true;
        if (new_ajList != null && new_ajList.length >= total) {
          isPage = false;
        }
        that.setData({
          ajList: new_ajList,
          isPage: isPage
        })
      }
    })
  },
  extractDetailBindTap: function () {
    app.navigateToPage('/pages/accountRebate/rebateDrawDetail/rebateDrawDetail');
  },
  /**
   * 提现
   */
  withdrawalBindTap: function () {
    var that = this;
    var validRebateMoney = that.data.validRebateMoney;
    var minExtractMoney = that.data.minExtractMoney;
    if (minExtractMoney > 0) {
      if (validRebateMoney >= minExtractMoney) {
        app.navigateToPage('/pages/accountRebate/rebateGoDraw/rebateGoDraw');
      } else {
        app.showModal({
          title: '提示',
          content: "您的提现金额未达到商家设置的提现额度"
        });
      }
    } else {
      if (validRebateMoney > 2) {
        app.navigateToPage('/pages/accountRebate/rebateGoDraw/rebateGoDraw');
      } else {
        app.showModal({
          title: '提示',
          content: "您的提现金额未达到商家设置的提现额度"
        });
      }
    }
  },
  selectRebateWhereBindTap: function () {
    var that = this;
    var startTime = that.data.date1;
    var endTime = that.data.date2;
    if (startTime == "开始时间" && endTime == "结束时间") {
      app.showModal({
        title: '提示',
        content: "请输入检索时间"
      });
      return;
    }
    if (startTime != "开始时间" && endTime != "结束时间") {
      var start_date = new Date(startTime.replace(/-/g, "-"));
      var end_date = new Date(endTime.replace(/-/g, "-"));
      //转成毫秒数，两个日期相减
      var ms = end_date.getTime() - start_date.getTime();
      //转换成天数
      var day = parseInt(ms / (1000 * 60 * 60 * 24));
      if (day <= 0) {
        app.showModal({
          title: '提示',
          content: "结束时间不能小于等于开始时间"
        });
        return;
      }
    }
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#ff6600'
    })
    that.setData({
      bgHidden: true,
      ajList: [],
      currentPage: 1,
      type: 0,
      isPage: true
    });
    that.userQueryRebateJourna();
  },
  selectAccountTypeBindTap: function (e) {
    var that = this;
    that.setData({
      type: e.currentTarget.dataset.type,
      ajList: [],
      currentPage: 1,
      isPage: true,
      date1: '开始时间',
      date2: '结束时间'
    })
    that.userQueryRebateJourna();
  },
  selectBillClick: function () {
    var that = this;
    that.setData({
      bgHidden: false
    })
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    })
  },
  closeBgHiddenBindTap: function (e) {
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#ff6600'
    })
    var type = e.currentTarget.dataset.type;
    if (type < 3) {
      this.setData({
        type: e.currentTarget.dataset.type,
        ajList: [],
        currentPage: 1,
        isPage: true,
        bgHidden: true,
        date1: '开始时间',
        date2: '结束时间'
      })
      this.userQueryRebateJourna();
    } else {
      this.setData({
        bgHidden: true
      })
    }
  },
  closeBlackBgBindTap: function () {
    this.setData({
      bgHidden: true
    })
  },
  bindStartDateChange: function (e) {
    this.setData({
      date1: e.detail.value
    })
  },
  bindEndDateChange: function (e) {
    this.setData({
      date2: e.detail.value
    })
  },
  resetDateBindTap: function () {
    this.setData({
      date1: '开始时间',
      date2: '结束时间'
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.setData({
        type: 0,
        ajList: [],
        currentPage: 1,
        isPage: true,
        date1: '开始时间',
        date2: '结束时间'
      })
      that.initPage();
    } else {
      that.setData({
        isFromBack: true
      })
    }
  },
  ruleClickBindTap: function () {
    var that = this;
    that.setData({
      ruleShow: false
    })
  },
  closeRuleBind: function () {
    var that = this;
    that.setData({
      ruleShow: true
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  scrolltolowerTip: function () {
    var that = this;
    if (that.data.isPage) {
      that.setData({
        currentPage: that.data.currentPage + 1
      })
      that.userQueryRebateJourna();
    }
  }
})