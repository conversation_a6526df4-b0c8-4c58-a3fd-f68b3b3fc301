page{
  background:#f5f5f5;
}
.search_box {
  height: 26px;
  padding:5px 10px;
  background: #fff;
  color: #848489;
  position: relative;
}

.search_box icon {
  float: left;
  position: absolute;
  top: 10px;
  left: 18px;
  z-index: 10;
  line-height: 26px;
}

.search_box input {
  width: 80%;
  height: 100%;
  line-height: 26px;
  background: #e7e8ea;
  font-size: 13px;
  padding-left: 30px;
  border-radius: 26px;
  float: left;
}

.search_box text {
  line-height: 26px;
  float: right;
  font-size: 12px;
}
.oneShop{
  color:#666;
  text-align:left;
  padding:20rpx;
  border-bottom:1px solid #ececec;
  margin:0 auto;
  background:#fff;
}
.shopWrap{
  margin:0px 10px 0px 10px;
  text-align:center;
  font-size:14px;
}

.store_wrap{
  width: 100%;
  margin-top:55px;
  font-size:15px;
}
.store_wrap .eachstore{
  margin-left:5%;
  margin-right:5%;
  padding-bottom:20px;
  padding-top:10px;
  border-bottom:1px solid #ddd;
}
.substore_logo{
  float: left !important;
  width: 60px !important;
  height: 60px !important;
  margin-top:4px !important;
  margin-right: 10px !important;
}
.storeDetail{
  font-size:12px;
  color:#000;
  margin-top:4px;
  width:100%;
  display:block;
  }
  .detail_append{
    color:#666;
    display:inline-block;
    font-size:12px;
    margin-top:4px;
    margin-right:4px;
  }
.store_name{
  width:80%;
  overflow:hidden;
  font-size:15px;
  color:#272727;
}
.store_des{
  padding:10px 0;
  border-bottom:1px solid #ddd;
  padding-left:10%;
}
.store_des label{
  margin-right:10px;
}
.black_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}
.store_info{
  border:1px solid #ddd;
  left:10%;width:80%;
  background:#fff;
  position:absolute;
  top:10%;
  overflow:hidden;
  font-size:14px;
  padding-top:20px;
  color:#272727;
  height:350px;
  z-index:999;
}
.clearfix:after{
  content:".";
  display:block;
  height:0;
  clear:both;
  visibility:hidden
  }
  .disposition{
    float:right;
    font-size:11px;
    color:#666;
  }
  .color_append{
    color:#FF7E00;
  }
  .orderDistance{
    font-size:28rpx;
    margin-left:60rpx;
    margin-top:10rpx;
    color:#333;
  }
  .location_txt{
    display:inline-block;
    padding:10rpx 30rpx;
    font-size:28rpx;
    text-align:center;
    margin-top:40rpx;
    border-radius:10rpx;
    background:#FF7E00;
    color:#fff;
    margin-left:25%;
  }


