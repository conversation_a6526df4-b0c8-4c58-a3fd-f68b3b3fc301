const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    date1: '2020-10-23',
    date2: '2020-10-23'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var cardNo = options.cardNo;
    that.setData({
      cardNo: cardNo,
      date1: that.getLocalDate(),
      date2: that.getLocalDate()
    });
    that.queryVipCardConsumptionWaterDetail(cardNo);
  },
  timeSearchFun: function () {
    this.setData({
      consumptionList: []
    });
    this.queryVipCardConsumptionWaterDetail(this.data.cardNo);
  },
  getLocalDate: function () {
    var timestamp = Date.parse(new Date());
    var date = new Date(timestamp);
    //获取年份  
    var Y = date.getFullYear();
    //获取月份  
    var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
    //获取当日日期 
    var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    return Y + "-" + M + "-" + D;
  },
  queryVipCardConsumptionWaterDetail: function (cardNo) {
    var that = this;
    wx.request({
      url: app.projectName + '/vipCard/queryVipCardConsumptionWaterDetail',
      data: {
        "page": that.data.offSet,
        "pagesize": that.data.pageSize,
        "cardNo": cardNo,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "startTime": that.data.date1,
        "endTime": that.data.date2
      },
      success: function (res) {
        var consumptionList = res.data.consumptionList;
        that.setData({
          consumptionList: consumptionList
        })
      }
    })
  },
  bindDateChange1: function (e) {
    this.setData({
      date1: e.detail.value
    })
  },
  bindDateChange2: function (e) {
    this.setData({
      date2: e.detail.value
    })
  }
})