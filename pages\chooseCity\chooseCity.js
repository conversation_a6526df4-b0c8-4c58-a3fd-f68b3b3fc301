const app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    arrowDown: app.imageUrl + "arrowDown.png",
    whiteLocation: app.imageUrl + "whiteLocation.png",
    locateMark: app.imageUrl + "locateMark.png",
    localCity: "开启定位获取精准位置"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    wx.getSystemInfo({
      success: function (res) {
        var clientHeight = res.windowHeight;
        that.setData({
          winHeight: clientHeight
        });
      }
    });
    var selectCityKey = wx.getStorageSync("selectCityKey");
    if (selectCityKey) {
      this.setData({
        localCity: selectCityKey
      })
    }
    this.getUserLocation();
    that.queryStoreInfo(0, 0);
  },
  openLocationBindTap: function () {
    if (this.data.localCity == "开启定位获取精准位置") {
      this.getUserLocation();
    }
  },
  goToLoginBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var showReturnStoreList = that.data.showReturnStoreList;
    var storeAddress;
    var storeName;
    for (var i = 0; i < showReturnStoreList.length; i++) {
      if (showReturnStoreList[i].id == id) {
        storeAddress = showReturnStoreList[i].province + showReturnStoreList[i].city + showReturnStoreList[i].area + showReturnStoreList[i].storeAddress;
        storeName = showReturnStoreList[i].storeName;
        break;
      }
    }
    var extObj = {
      "storeId": id,
      "storeName": storeName,
      "storeAddress": storeAddress
    };
    wx.removeStorageSync("selectStoreInfoKey");
    app.setStorage({
      key: 'selectStoreInfoKey',
      data: extObj
    });
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  getUserLocation: function () {
    let vm = this;
    // wx.getSetting({
    //   success: (res) => {
    //     console.log(res)
    //     if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
    //       wx.showModal({
    //         title: '请求授权当前位置',
    //         content: '需要获取您的地理位置，请确认授权',
    //         success: function (res) {
    //           console.log(res)
    //           if (res.cancel) {
    //             wx.showToast({
    //               title: '拒绝授权,将无法获取门店精准位置',
    //               icon: 'none',
    //               duration: 1000
    //             })
    //             vm.getLocation();
    //           } else if (res.confirm) {
    //             wx.openSetting({
    //               success: function (dataAu) {
    //                 console.log(4444)
    //                 if (dataAu.authSetting["scope.userLocation"] == true) {
    //                   wx.showToast({
    //                     title: '授权成功',
    //                     icon: 'success',
    //                     duration: 1000
    //                   })

    //                   //再次授权，调用wx.getLocation的API
    //                   vm.getLocation();
    //                 } else {
    //                   wx.showToast({
    //                     title: '授权失败',
    //                     icon: 'none',
    //                     duration: 1000
    //                   })
    //                 }
    //               }, complete: function (res) {
    //                 console.log(res)
    //               }
    //             })
    //           }
    //         },
    //         fail: function () {
    //           vm.setData({
    //             localtionHidden: false
    //           })
    //         }
    //       })
    //     } else if (res.authSetting['scope.userLocation'] == undefined) {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     } else {
    //       //调用wx.getLocation的API
    //       vm.getLocation();
    //     }
    //   }
    // })
  },
  // 微信获得经纬度
  getLocation: function () {
    var that = this;
    // wx.getLocation({
    //   type: 'gcj02',
    //   altitude: true,
    //   complete: function (res) {
    //     if (res.errMsg == "getLocation:ok") {
    //       var latitude = res.latitude;
    //       var longitude = res.longitude;
    //       that.setData({
    //         latitude: latitude,
    //         longitude: longitude
    //       });
    //       that.getLocationDesc(latitude, longitude);
    //       that.queryStoreInfo(latitude, longitude);
    //     } else {
          that.setData({
           latitude: 0,
          longitude: 0
          
    //     }
    //   }
    })
    that.queryStoreInfo(0, 0);
  },

  getLocationDesc: function (latitude, longitude) {
    var that = this;
    // var qqmapsdk = new QQMapWX({
    //   key: 'O5XBZ-QNPC4-NDEUL-FCDYR-BSXO3-XYFIW' // 必填
    // });
    // qqmapsdk.reverseGeocoder({
    //   location: {
    //     latitude: latitude,
    //     longitude: longitude
    //   },
    //   success: function (res) {//成功后的回调
    //     that.setData({
    //       localCity: res.result.address
    //     })
    //   },
    //   fail: function (error) {
    //     console.error(error);
    //   }
    // })
  },
  /**
  * 查询我的客户信息
  */
  queryStoreInfo: function (latitude, longitude) {
    wx.showLoading({
      title: '数据加载中...',
      mask: true
    })
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryLoginBreakStoreInfo',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var returnStoreList = res.data.storeList;
        if (returnStoreList != null && returnStoreList.length > 0) {
          for (var i = 0; i < returnStoreList.length; i++) {
            returnStoreList[i].storeDistance = that.distance(latitude, longitude, returnStoreList[i].latitude, returnStoreList[i].longitude);
          }
          //按照距离排序
          returnStoreList = returnStoreList.sort((el1, el2) =>
            el1.storeDistance - el2.storeDistance
          );
        }
        var authorList = res.data.authorList;
        that.setData({
          authorList: authorList,
          showReturnStoreList: returnStoreList
        });
      }
    })
  },
  /**
 * 由经纬度计算两点之间的距离，la为latitude缩写，lo为longitude
 * @param {*} 第一个坐标点的纬度 
 * @param {*} 第一个坐标点的经度 
 * @param {*} 第二个坐标点的纬度 
 * @param {*} 第二个坐标点的经度 
 * @return (int)s   返回距离(单位千米或公里)
 */
  distance: function (la1, lo1, la2, lo2) {
    var La1 = la1 * Math.PI / 180.0;
    var La2 = la2 * Math.PI / 180.0;
    var La3 = La1 - La2;
    var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    s = s.toFixed(2);
    return s;
  }
})