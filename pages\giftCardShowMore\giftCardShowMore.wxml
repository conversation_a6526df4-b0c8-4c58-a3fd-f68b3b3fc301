<view style="font-size:52rpx;padding:0 20rpx;"></view>
<view class="detailBox">
	<view class="detailLi">
		<view class="liLeft">礼券号：</view>
		<view class="liright">{{cardDetail.cardNo}}</view>
	</view>
	<view class="detailLi">
		<view class="liLeft">兑换时间：</view>
		<view class="liright">{{cardDetail.createTime}}</view>
	</view>
	<!--<view class="detailLi">
		<view class="liLeft">配送方式：</view>
		<view class="liright">
			<block wx:if="{{orderBean.orderDistributionWay=='SELF_DISTRIBUTION'}}">
				自配送
			</block>
			<block wx:elif="{{orderBean.orderDistributionWay=='SELF_EXTRACT'}}">
				自取
			</block>
			<block wx:else>
				第三方配送
			</block>
		</view>
	</view>
	<view class="detailLi">
		<view class="liLeft">付款方式：</view>
		<view class="liright">
			<block wx:if="{{orderBean.orderBillType==3}}">
				积分支付
			</block>
			<block wx:else>
				<block wx:if="{{orderBean.orderPayWay=='PREPAY'}}">
					预付款支付
				</block>
				<block wx:elif="{{orderBean.orderPayWay=='PAYBEFORE'}}">
					微信支付
				</block>
				<block wx:elif="{{orderBean.orderPayWay=='PAYAFTER'}}">
					货到付款
				</block>
				<block wx:elif="{{orderBean.orderPayWay=='MONTHAUDIT'}}">
					月结
				</block>
			</block>
		</view>
	</view>-->
</view>

<view class="detailBox">
	<!--<view class="detailLi">
		<view class="liLeft">客户名称：</view>
		<view class="liright">{{orderBean.orderUserName}}</view>
	</view>
	<view class="detailLi" style="border-bottom:1px solid #F7F8F9;padding-bottom:10rpx;">
		<view class="liLeft">联系方式：</view>
		<view class="liright">{{orderBean.orderUserPhone}}</view>
	</view>-->
	<view class="detailLi" style="padding-top:10rpx;">
		<view class="liLeft">收货人：</view>
		<view class="liright">{{cardDetail.consigneeName}}</view>
	</view>
	<view class="detailLi" style="padding-top:10rpx;">
		<view class="liLeft">联系方式：</view>
		<view class="liright">{{cardDetail.consigneePhone}}</view>
	</view>
	<view class="detailLi" style="padding-top:10rpx;height:auto">
		<view class="liLeft">收货地址：</view>
		<view class="liright">{{cardDetail.province}}{{cardDetail.city}}{{cardDetail.area}}{{cardDetail.address}}</view>
	</view>
</view>

<view class="detailBox">
	<view class="detailLi" style="height:auto;">
		<view class="liLeft">备注：</view>
		<view class="liright">{{cardDetail.remarks}}</view>
	</view>
</view>
<view class="detailBox">
	<view class="detailLi" style="height:auto;">
    <block wx:if="{{cardDetail.expressType != ''}}">
      <view class="liLeft">{{cardDetail.expressType}}快递</view>
      <view class="liright">{{cardDetail.expressCode}}</view>
    </block>
    <block wx:if="{{cardDetail.expressType == ''}}">
       <view class="liLeft">快递信息</view>
       <view class="liright">暂无</view>
    </block>
	</view>
  <view>
    <block wx:key="unique" wx:for="{{logisticsList}}" wx:for-item="logistics" wx:for-index="logisticsIndex">
      <block wx:if="{{logisticsIndex==0}}">
        <view class='new_state'>
          <text></text>
          <label style="font-size:24rpx;">{{logistics.status}}</label>
          <label>{{logistics.time}}</label>
        </view>
      </block>
      <block wx:else>
        <block wx:if="{{logisticsIndex<=3}}">
          <view class="old_state">
            <text></text>
            <label style="font-size:24rpx;">{{logistics.status}}</label>
            <label>{{logistics.time}}</label>
          </view>
        </block>
        <block wx:if="{{logisticsIndex>3}}">
          <view class="old_state" hidden="{{logHidden}}">
            <text></text>
            <label style="font-size:24rpx;">{{logistics.status}}</label>
            <label>{{logistics.time}}</label>
          </view>
        </block>
      </block>
    </block>
    <block wx:if="{{logisticsList.length>4}}">
      <view hidden="{{!logHidden}}" bindtap="queryMoreLogsBindTap" style="text-align:center;">
        <label style="vertical-align:top;font-size:24rpx;margin-right:8rpx;">查看更多物流详情</label>
        <label class="icondirect" style="vertical-align:bottom;transform:rotate(180deg);margin-right:20rpx;display:inline-block;margin-top:10rpx;font-size:24rpx;">
          ︿
        </label>
      </view>
    </block>
  </view>
</view>