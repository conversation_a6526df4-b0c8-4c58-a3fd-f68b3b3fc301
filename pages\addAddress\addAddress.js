var app = getApp();
//var QQMapWX = require('../../libs/qqmap-wx-jssdk.js');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        array: [{
            mode: 'aspectFit',
        }],
        edit: app.imageUrl + 'edit.png',
        remove: app.imageUrl + 'remove.png',
        isFromBack: false,
        latitude: 0,
        longitude: 0,
        distributionDistance: 0,
        pickOrderStoreId: 0
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var pickOrderStoreId = options.pickOrderStoreId;
        this.setData({
            pickOrderStoreId: pickOrderStoreId
        })
        this.initAddress(pickOrderStoreId);
    },
    /**
     * 初始化收货地址
     */
    initAddress: function (pickOrderStoreId) {
        var that = this;
        wx.showLoading({
            title: '正在加载，请稍后',
            mask: true
        })
        wx.request({
            header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/ios/queryDeliveryAddressByOrder',
            data: {
                "loginId": app.getUserId(),
                "loginRole": 1,
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId(),
                "pickOrderStoreId": pickOrderStoreId,
                "latitude": that.data.latitude,
                "longitude": that.data.longitude,
                "odbtoken":app.getodbtoken(),
                "loginToken":app.getloginToken(),
            },
            success: function (res) {
                var distributionDistance = res.data.distributionDistance;
                var addressList = res.data.addressList;
                var normalAddressList = []; //正常范围
                var outOfRangeAddressList = []; //超出范围
                if (parseFloat(distributionDistance) > 0) {
                    for (var i = 0; i < addressList.length; i++) {
                      console.log('+++++++++++++'+parseFloat(addressList[i].addressDistance))
                        if (parseFloat(addressList[i].addressDistance) > 0) {
                          console.log('===================='+parseFloat(addressList[i].addressDistance) / 1000);
                          console.log('----------------'+distributionDistance);
                            if (parseFloat(addressList[i].addressDistance) / 1000 > parseFloat(distributionDistance)) {
                                outOfRangeAddressList.push(addressList[i]);
                            } else {
                                normalAddressList.push(addressList[i]);
                            }
                        } else {
                            normalAddressList.push(addressList[i]);
                        }
                    }
                } else {
                    normalAddressList = addressList;
                }
                that.setData({
                    normalAddressList: normalAddressList,
                    outOfRangeAddressList: outOfRangeAddressList
                });
                wx.hideLoading();
            },
            fail: function () {
                wx.hideLoading();
            }
        })
    },
    selectAddressBindTap: function (e) {
        var addressId = e.currentTarget.dataset.id;
        var adList = this.data.normalAddressList;
        var selectAddress = "";
        var username = "";
        var telephone = "";
        var addressLat = 0;
        var addressLon = 0;
        for (var i = 0; i < adList.length; i++) {
            if (adList[i].id == addressId) {
                if (adList[i].province == "" || adList[i].city == "") {
                    wx.showToast({
                        title: '收货地址不完整',
                        duration: 1500
                    })
                    return;
                }
                selectAddress = adList[i].province + adList[i].city + adList[i].area + adList[i].address + adList[i].houseNumber;
                username = adList[i].username;
                telephone = adList[i].telephone;
                if (adList[i].latitude != null && adList[i].latitude.length > 0) {
                    addressLat = adList[i].latitude;
                    addressLon = adList[i].longitude;
                }
                break;
            }
        }
        if (selectAddress != "") {
            var pages = getCurrentPages(),
                prevPage = pages[pages.length - 2];
            prevPage.setData({
                receiveAddress: selectAddress,
                username: username,
                telephone: telephone,
                addressLat: addressLat,
                addressLon: addressLon,
                addressId:addressId
            });
            app.turnBack();
        }
    },
    /**
     * 添加新地址
     */
    addNewAddressBindTap: function () {
        app.navigateToPage('/pages/newbuiltAddress/newbuiltAddress?pickOrderStoreId=' + this.data.pickOrderStoreId);
    },
    /**
     * 设置默认地址
     */
    setDefaultAddressBindTap: function (e) {
        var id = e.currentTarget.dataset.id;
        var that = this;
        app.showModal({
            content: '确定设置为默认收货地址？',
            showCancel: true,
            confirm: function () {
                wx.request({
                    header: {
                        'content-type': 'application/x-www-form-urlencoded' // 默认值
                    },
                    method: "POST",
                    url: app.projectName + '/ios/updateDefaultAddress',
                    data: {
                        "loginId": app.getUserId(),
                        "loginRole": 1,
                        "addressId": id,
                        "storeId": app.getExtStoreId(),
                        "companyId": app.getExtCompanyId(),
                        "odbtoken":app.getodbtoken(),
                        "loginToken":app.getloginToken(),
                    },
                    success: function (res) {
                        that.initAddress();
                    }
                })
            }
        })
    },
    /**
     * 更新地址
     */
    updateAddressBindTap: function (e) {
        var id = e.currentTarget.dataset.id;
        app.navigateToPage('/pages/editAddress/editAddress?id=' + id + "&pickOrderStoreId=" + this.data.pickOrderStoreId);
    }
})