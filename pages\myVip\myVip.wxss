page {
  background: #f4f4f4;
}

.top_title {
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: #FF7E00;
  color: #fff;
  text-align: center;
  font-size: 14px;
  position: fixed;
  top: 0;
  left: 0;
}

/* 定位 搜索 */

.location_box {
  height: 26px;
  position: fixed;
  top: 40px;
  left: 0;
  font-size: 12px;
  line-height: 26px;
  width: 100%;
  padding: 5px 0;
  z-index: 100;
  background: #fff;
  margin-bottom: 10px;
}

.location_box text {
  line-height: 26px;
  height: 26px;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #a6a6a6;
  color: #fff;
  border-radius: 15px !important;
  padding: 0 2%;
  overflow: hidden;
}

.location_box icon {
  float: left;
  position: absolute;
  top: 9px;
  left: 9%;
  z-index: 10;
  line-height: 34px;
}

.location_box input {
  line-height: 26px;
  height: 26px;
  width: 66%;
  display: block;
  float: left;
  margin:0 2% 0 6%;
  background: #ededed;
  color: #272727;
  border-radius: 15px;
  padding-left: 8%;
  padding-right: 4%;
}

.content_box {
  width: 100%;
  padding-top: 88px;
}

.single_box {
  padding: 20px 4%;
  height: 90px;
  background: #fff;
  margin-bottom: 10px;
}

.single_box image {
  width: 70px;
  height: 70px;
  /**float: left;**/
  margin:0 10px;

}

.message_box {
  padding-left: 100px;
  height: 80px;
}

.message_box label {
  width: 100%;
  display: block;
  height: 26px;
  border-bottom: 1px dashed #ccc;
  font-size: 13px;
  color: #555;
  line-height: 26px;
}

.message_box label text {
  width: 56px;
  display: block;
  float: left;
}

/**.message_box label:first-child {
  margin-top: 5px;
}**/

.message_box label:last-child {
  border-bottom: none;
}
