page{
  background:#f5f5f5;
}
.reabateTop{
  background:#ff6600;
  height:340rpx;
}
.rebate_m{
  align-items:center;
  display:flex;
  justify-content:space-between;
  margin:0 40rpx;
}
.m_l{
  display:flex;
  flex-direction:column;
  color:#fff;
}
.m_r{
  background:#fff;
  color:#ff6600;
  padding:8rpx 50rpx;
  border:1px solid #fff;
  border-radius:30rpx
}
.widthdraw_m{
  margin:20rpx 0
}
.widthdraw_m label{
  font-size:48rpx;
  margin-left:10rpx;
}
.widthdraw_d{
  text-align:center;
  padding:8rpx 10rpx;
  border:1px solid #fff;
  border-radius:30rpx

}
.rebate_total{
  border-radius:40rpx;
  background:#fff;
  margin-top:-80rpx;
  height:160rpx;
  width:700rpx;
  margin-left:25rpx;
  margin-right:25rpx;
  display:flex;
  justify-content:space-around;
  align-items:center;
}
.total_d{
  display:flex;
  flex-direction:column;
  align-items:center;
}
.total_d label:first-child{
  margin-bottom:16rpx;
}
.accountDetail{
  background:#ffffff;
  margin-top:30rpx;
  padding-bottom:30rpx;
}
.detailTop{
  display:flex;
  justify-content:space-around;
  padding:20rpx 0;
}
.detailTop view{
  padding:16rpx 0;
}
.detailTop .active{
  border-bottom:4rpx solid #ff6600;
  color:#ff6600;
}
.selectImage{
  width:20rpx;
  margin-left:8rpx;
  vertical-align:middle;
}
.r_wrap{
  width:700rpx;
  margin:0 auto;
}
.r_detail{
  display:flex;
  align-items:center;
  padding:20rpx 0;
  border-bottom:1rpx solid #f2f2f2;
}
.r_detail image{
  width:40rpx;
  margin-right:20rpx;
}
.r_detail_m{
  display:flex;
  flex-direction:column;
  flex:1
}
.r_detail_m lable:first-child{
  margin-bottom:8rpx;
}
.r_detail_m .r_detail_t{
  font-size:25rpx;
  margin-top:4rpx;
  color:#666;
}
.detail_income{
  color:#ff6600;
}
.detail_outcome{
  color:green;
}
.black_bg{
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}
.popWrap{
  position:fixed;
  top:0;
  left:0;
  right:0;
  z-index:99999;
  background:#fff;
  font-size:29rpx;
  padding-bottom:30rpx;
}
.popWrap .detailTop{
  display:flex;
  justify-content:space-around;
}
.timeSelect{
  margin-top:40rpx;
  padding-left:30rpx;
}
.timePicker{
  height: 80rpx;
  display: flex;
  padding-left:20rpx;
  margin-top:20rpx;
}
.timeBox{
  width: 300rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 8rpx;
  margin:10rpx;
}
.picker{
  border:1px solid #ddd;
}
.popBtn{
  display:flex;
  justify-content:space-around;
  margin-top:30rpx;
}
.popBtn label{
  background:#ff6600;
  color:#fff;
  width:40%;
  text-align:center;
  height:70rpx;
  line-height:70rpx;
  border-radius:10rpx;
}
.selectItem{
  color:#ff6600;
}
.no_journal{
  text-align:center;
}
.no_journal image{
  margin-top:150rpx;
  width:240rpx;
}
.rebate_b{
  align-items:center;
  display:flex;
  margin:0 40rpx;
}
.rebate_b label{
  padding:8rpx 20rpx;
  color:#fff;
}
.black_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}
.ri_wrap{
  position:fixed;
  top:200rpx;
  left:75rpx;
  z-index: 11;
}