var app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    qrCodeUrl: "",
    saveUrl: [],
    windowWidth: '',
    posterHeight: '',
    invitationCode: '',
    imageHeight: 1050,
    imageWidth: 750,
    qrCodeImageHeight: 140,
    qrCodeImageWidth: 140,
    shareQrCodeImageUrl: app.imageUrl + "/shareQrcodeBtn.png",
    localPostImage: [],
    localqrcodeImage: "",
    announctList: [app.imageUrl + "/share_1.png", app.imageUrl + "/share_2.png", app.imageUrl + "/share_3.png"],
    swiperDotIn: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    } else {
      that.queryUserIsDistribution();
    }
  },
  /**
   * 查询是否有分享权限
   */
  queryUserIsDistribution: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/newDistrution/queryUserIsDistribution',
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showLoading({
            title: '数据加载中...',
            mask: true
          })
          const systemInfo = wx.getSystemInfoSync()
          let windowWidth = systemInfo.windowWidth;
          let windowHeight = systemInfo.windowHeight;
          let posterHeight = parseInt((windowWidth / that.data.imageWidth) * that.data.imageHeight);
          that.setData({
            windowWidth: windowWidth,
            posterHeight: posterHeight
          })
          that.getQRCode();
        } else {
          wx.showToast({
            title: "暂无分享权限，请联系商家开通",
            icon: 'none',
            duration: 3000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 3000);
            }
          })
        }
      }
    })
  },
  savePosetImageToLocal: function (qrCodeUrl, postUrl, invitationCode, urlIndex) {
    var that = this;
    wx.getImageInfo({
      src: postUrl, //服务器返回的图片地址
      success: function (res) {
        //res.path是网络图片的本地地址
        let Path = res.path;

        var localPostImage = that.data.localPostImage;
        localPostImage[urlIndex] = Path;
        that.setData({
          localPostImage: localPostImage
        })
        wx.getImageInfo({
          src: qrCodeUrl, //服务器返回的图片地址
          success: function (res) {
            //res.path是网络图片的本地地址
            let Path = res.path;
            that.setData({
              localqrcodeImage: Path
            })
            that.initCanvas(invitationCode, urlIndex);

          },
          fail: function (res) {
          }
        });
      },
      fail: function (res) {
        //失败回调
      }
    });
  },
  saveqrCodeImageToLocal: function (qrCodeUrl, invitationCode) {
    var that = this;
    wx.getImageInfo({
      src: qrCodeUrl, //服务器返回的图片地址
      success: function (res) {
        //res.path是网络图片的本地地址
        let Path = res.path;
        that.setData({
          localqrcodeImage: Path
        })
        //that.initCanvas(invitationCode);
      },
      fail: function (res) {
        //失败回调
      }
    });
  },
  initCanvas: function (invitationCode,index) {
    this.setData({
      index:index
    })
    const query = wx.createSelectorQuery()
    query.select('#poster'+index)
      .fields({
        id: true,
        node: true,
        size: true
      })
      .exec(this.init.bind(this));
     //生成微信临时模板文件path
  },
  init:function(res){
    let canvas = res[0].node;
    let ctx = canvas.getContext('2d');
    const dpr = wx.getSystemInfoSync().pixelRatio;
    //新接口需显示设置画布宽高；
    canvas.width = res[0].width * dpr
    canvas.height = res[0].height* dpr;
    this.setData({
      canvas:canvas
    });
    ctx.scale(dpr, dpr);
    ctx.font = '15px';
    ctx.fillStyle = '#000';
    let img = canvas.createImage();
    img.onload = () =>{
      ctx.drawImage(img, 0, 0, res[0].width, res[0].height); 
     
    var qrcodeY = this.data.posterHeight - this.data.posterHeight / 4 - 100;
    var qrcodeX = res[0].width/2  - this.data.qrCodeImageWidth/2 ;
    let img1 = canvas.createImage();
    img1.onload = () =>{
      ctx.drawImage(img1, qrcodeX, qrcodeY, this.data.qrCodeImageWidth, this.data.qrCodeImageHeight); //画用户头像
     
      ctx.textAlign = 'center';
      ctx.fillStyle = '#fff';
      ctx.font = "18px";
      var textX = this.data.windowWidth / 2;
      var textY = this.data.posterHeight - this.data.posterHeight / 5 + this.data.qrCodeImageWidth;
      ctx.stroke();
      this.save(this.data.index,canvas);
    }
    img1.src=this.data.localqrcodeImage;
    
    }
    var imageSrc = this.data.localPostImage[this.data.index];
    img.src=imageSrc;
  },
  save: function (index,canvas) {
    var that = this;
    setTimeout(function () {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: that.data.imageWidth,
        height: that.data.imageHeight,
        destWidth: that.data.imageWidth,
        destHeight: that.data.imageHeight,
        canvas:canvas,
        success: function (res) {
          var saveUrl = that.data.saveUrl;
          saveUrl[index] = res.tempFilePath;
          that.setData({
            saveUrl: saveUrl
          })
          //that.data.saveUrl = res.tempFilePath //保存临时模板文件路径
        },
        fail: function (res) {
          return;
        }
      })
    }, 500)
  },
  getQRCode: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newAppletQR/getWechatAppletPersonalPosterQRcode',
      data: {
        "companyId": app.getExtCompanyId(),
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        //"recommendUserId": app.getClientOpenId(),
        
        "type": 2
      },
      success: function (res) {
        var returnUrl = res.data.returnUrl;
        var posterUrl = res.data.posterUrl;
        if (returnUrl != null && returnUrl != "") {
          for (let i = 0; i < that.data.announctList.length; i++) {
            that.savePosetImageToLocal(returnUrl, that.data.announctList[i], "", i);
          }
        }
        wx.hideLoading();
      }
    })
  },
  saveImageToPhotosAlbum: function () {
    var that = this;
    if (!wx.saveImageToPhotosAlbum) {
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
      })
      return;
    }
    wx.getSetting({
      success(res) {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          // 接口调用询问  
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              that.saveImageToPhotosAlbum1();
            },
            fail() {
              wx.showModal({
                title: '提示',
                content: '检测到您保存到相册权限未开启，是否开启?',
                success: function (res) {
                  if (res.confirm) {
                    wx.openSetting({
                      success: function (data) {
                      },
                      fail: function (data) {
                      }
                    });
                  }
                }
              })
              // 用户拒绝了授权  
              // 打开设置页面  
            }
          })
        } else {
          that.saveImageToPhotosAlbum1();
        }
      },
      fail(res) {
      }
    })
  },
  saveImageToPhotosAlbum1: function () {

    var that = this;
    if (that.data.saveUrl.length == 0) {
      return;
    }
    wx.showLoading({
      title: '下载中...'
    })
    wx.saveImageToPhotosAlbum({
      filePath: that.data.saveUrl[that.data.swiperDotIn],
      success(result) {
        wx.hideLoading();
        wx.showToast({
          title: '已保存至相册',
          icon: 'none',
        })
      },
      fail(result) {
        wx.hideLoading()
        wx.showToast({
          title: '下载失败',
          icon: 'none',
        })
      }
    })

  },
  swiperDotIndex: function (e) {
    this.setData({
      swiperDotIn: e.detail.current
    })
  }
})