var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    defaultHead: app.imageUrl + 'team/defaultHead.png',
    queryType: "",
    agentList: [],
    title: "",
    parameter: ""
  },
  makePhoneBindTap: function (e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.telephone //仅为示例，并非真实的电话号码
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var queryType = options.queryType;
    var title = "";
    if (queryType == 1) {
      title = "我的直属上级";
    } else if (queryType == 2) {
      title = "我的直属钻石店主";
    } else if (queryType == 3) {
      title = "我的直属VIP店主";
    }
    this.setData({
      queryType: queryType,
      title: title
    })
    this.queryUserInfo();
  },
  queryUserInfo: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "type": that.data.queryType,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "parameter": that.data.parameter,
        "storeId": app.storeId,
        "companyId": app.companyId
      },
      url: app.projectName + '/ios/agent/queryAgentInfo',
      success: function (res) {
        var agentList = res.data.agentList;
        if (agentList != null && agentList.length > 0) {
          that.setData({
            agentList: agentList
          });
        } else {
          that.setData({
            agentList: []
          });
        }
      }
    })
  },
  parameterBindInput: function (e) {
    this.setData({
      parameter: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    app.commonShareApplet(res);
  }
})