//index.js
//获取应用实例
const app = getApp()

Page({

    /**
     * 页面的初始数据
     */
    data: {
        telephone: "", //手机号码
        loginPassWord: "", //登录密码
        againLoginPassword: "", //再次输入登录密码
        smsCode: "", //短信验证码
        second: 60, //倒计时秒数
        secondDesc: "获取短信验证码"
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function(options) {

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function() {

    },
    bindKeyInput: function(e) {
        this.setData({
            telephone: e.detail.value
        })
    },
    passwordInput: function(e) {
        this.setData({
            loginPassWord: e.detail.value
        })
    },
    againPasswordInput: function(e) {
        this.setData({
            againLoginPassword: e.detail.value
        })
    },
    smsCodeInput: function(e) {
        this.setData({
            smsCode: e.detail.value
        })
    },
    /**
     * 点击获取短信验证码
     */
    smsBindTap: function() {
        var that = this;
        var telephone = that.data.telephone;
        if (telephone == "" || telephone == null) {
            app.showModal({
                title: '提示',
                content: "手机号码不能为空"
            });
            return;
        }
        wx.showLoading({
            title: '正在获取。。。',
            mask: true
        })
        wx.request({
            url: app.projectName + '/applet/querySMSCode',
            data: {
                "type": "3",
                "telephone": telephone,
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId()
            },
            success: function(res) {
                wx.hideLoading();
                var flag = res.data.flag;
                if (flag) {
                    that.countdown(that);
                } else {
                    app.showModal({
                        title: '提示',
                        content: "获取短信验证失败，请稍后在试"
                    });
                    return;
                }
            }
        })
    },
    /**
     * 倒计时开始
     */
    countdown: function(that) {
        var second = that.data.second;
        if (second == 0) {
            that.setData({
                secondDesc: "获取短信验证码",
                second: 60
            });
            return;
        }
        var time = setTimeout(function() {
            that.setData({
                second: second - 1,
                secondDesc: second + "秒后重新获取"
            });
            that.countdown(that);
        }, 1000)
    },
    registerBindTap: function() {
        var telephone = this.data.telephone;
        var loginPassWord = this.data.loginPassWord;
        var againLoginPassword = this.data.againLoginPassword;
        var smsCode = this.data.smsCode;
        if (telephone == null || telephone == "" || telephone.length <= 0) {
            app.showModal({
                title: '提示',
                content: "手机号码不能为空"
            });
            return;
        }
        if (loginPassWord == null || loginPassWord == "" || loginPassWord.length <= 0) {
            app.showModal({
                title: '提示',
                content: "登录密码不能为空"
            });
            return;
        }
        if (againLoginPassword == null || againLoginPassword == "" || againLoginPassword.length <= 0) {
            app.showModal({
                title: '提示',
                content: "确认登录密码不能为空"
            });
            return;
        }
        if (loginPassWord != againLoginPassword) {
            app.showModal({
                title: '提示',
                content: "两次登录密码输入不一致"
            });
            return;
        }
        if (smsCode == null || smsCode == "" || smsCode.length <= 0) {
            app.showModal({
                title: '提示',
                content: "短信验证码不能为空"
            });
            return;
        }
        wx.showLoading({
            title: '正在校验。。。',
            mask: true
        })
        wx.request({
            url: app.projectName + '/applet/updateForgetLoginPassword',
            data: {
                "telephone": telephone,
                "newPassword": loginPassWord,
                "code": smsCode,
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId()
            },
            success: function(res) {
                wx.hideLoading();
                var resultCode = res.data.resultCode;
                var message = res.data.message;
                if (resultCode == 0) { //修改密码成功
                    app.turnToPage("/pages/login/login?loginAccount=" + telephone);
                } else {
                    app.showModal({
                        title: '提示',
                        content: message
                    });
                    return;
                }
            }
        })
    },
    goToLogin: function() {
        app.turnToPage("/pages/login/login");
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function(res) {
        var that = this;
        if (res.from === 'button') {
            // 来自页面内转发按钮
        }
        return {
            title: app.storeName,
            path: '/pages/index/index?recommendId=' + app.getUserId() + '&recommendAccount=' + app.getLoginAccount() + '&recommendName=' + app.getLoginName() + '&identity=' + app.getIdentity(),
            imageUrl: app.shareImageUrl,
            success: function(res) {
                // 转发成功
            },
            fail: function(res) {
                // 转发失败
            }
        }
    }
})