var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userName: '',
    telephone: '',
    sex: '1',
    birthday: '',
    address: '',
    interest: '',
    like: '',
    occupation: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
  },
  submitMethod: function () {   //提交
    var that = this;
    if (that.data.userName == '') {
      wx.showToast({
        title: '用户姓名不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (that.data.telephone == '') {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.login({
      success: function (res) {
        if (res.code) {
          var code = res.code;
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            data: {
              "code": code,
              "companyId": app.getExtCompanyId(),
              "storeId": app.getExtStoreId(),
                     "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
              "id": that.data.id,
              "userName": that.data.userName,
              "telephone": that.data.telephone,
              "sex": that.data.sex
            },
            url: app.projectName + '/vipCard/activateWechatVipCardToNoVip',
            success: function (res) {
              var flag = res.data.flag;
              if (flag) {
                wx.showToast({
                  title: "激活成功",
                  icon: 'success',
                  duration: 1000,
                  mask: true,
                  success: function () {
                    setTimeout(function () {
                      wx.switchTab({
                        url: "/pages/index/index"
                      });
                    }, 1000);
                  }
                })
              } else {
                wx.showToast({
                  title: '激活失败，请联系商家处理',
                  icon: 'none',
                  duration: 2000,
                  mask: true
                })
              }
            }
          })
        }
      }
    })
  },
  userNameBindInput: function (e) {
    this.setData({
      userName: e.detail.value
    })
  },
  telephoneBindInput: function (e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  sexBindInput: function (e) {
    this.setData({
      sex: e.detail.value
    })
  },
  addressBindInput: function (e) {
    this.setData({
      address: e.detail.value
    })
  },
  interestBindInput: function (e) {
    this.setData({
      interest: e.detail.value
    })
  },
  likeBindInput: function (e) {
    this.setData({
      like: e.detail.value
    })
  },
  occupationBindInput: function (e) {
    this.setData({
      occupation: e.detail.value
    })
  },
  bindDateChange: function (e) {
    this.setData({
      birthday: e.detail.value
    })
  }
})