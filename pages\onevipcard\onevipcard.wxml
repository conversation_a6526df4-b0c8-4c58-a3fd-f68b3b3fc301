<view style="font-size:28rpx;">
  <view style="margin:20rpx 25rpx;border-left:3px solid red;">
    <text style="margin-left:10rpx;">会员卡权益</text>
  </view>
  <view style="margin-left:25rpx;margin-right:25rpx;">{{cardBean.vipDesc.length>0?cardBean.vipDesc:'暂无'}}</view>
  <view style="margin:20rpx 25rpx;border-left:3px solid red;">
    <text style="margin-left:10rpx;">领卡人信息</text>
  </view>
  <view style="margin:0 30rpx;">
    <view class='account_detail'>
      <label>
        <text>姓名</text>
        <input placeholder="请输入会员姓名" bindinput='userNameBindInput' type='text'></input>
      </label>
      <label>
        <text>手机号</text>
        <input placeholder="请输入绑定手机号" maxlength='11' bindinput='telephoneBindInput' disabled='true' value='{{telephone}}' type='number'></input>
      </label>
      <label>
        <text>性别</text>
        <radio-group class="radio-group" bindchange="radioChange">
          <radio value="1" checked="{{sex==1?true:false}}" />男
          <radio value="0" checked="{{sex==0?true:false}}" style='margin-left:40px;' />女
        </radio-group>
      </label>
      <label class="picker">
        <picker mode="date" value="{{date}}" bindchange="bindDateChange" style='height:45px;'> 生日
          <text style=' float:none; margin-left:24px;'>{{birthday}}</text>
        </picker>
      </label>
      <label>
        <text>支付密码</text>
        <input placeholder="请输入支付密码" bindinput='passwordBindInput' type='password'></input>
      </label>
    </view>
  </view>
  <view class='add_address' bindtap='receiveVipCardBindTap'>
    确认并领取
  </view>
</view>