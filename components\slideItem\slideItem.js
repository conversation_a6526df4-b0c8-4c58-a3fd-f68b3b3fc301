// components/slideItem/slideItem.js
var app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    itemData:{
      type:Array,
      value:[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    announce: app.imageUrl + 'announcement.png',
    text: '',
    marqueePace: 1, //滚动速度
    marqueeDistance: 0, //初始滚动距离
    marqueeDistance2: 0,
    marquee2copy_status: false,
    marquee2_margin: 60,
    size: 14,
    orientation: 'left', //滚动方向
    inter_val: 30, // 时间间隔
    intervarID:null,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    stopSlide:function(){
      var that =this;
      clearInterval(that.data.intervarID);
    },

    ontest: function () {

      // 页面显示

      var vm = this;
      vm.setData({
        itemData: vm.properties.itemData,
      })
      var tmpArray = vm.properties.itemData;
      vm.setData({
        text:tmpArray[0].contentTitle + tmpArray[0].contentText
      })
      var length = vm.data.text.length * vm.data.size; //文字长度

      var windowWidth = wx.getSystemInfoSync().windowWidth; // 屏幕宽度

      vm.setData({

        length: length,

        windowWidth: windowWidth,

        marquee2_margin: length < windowWidth ? windowWidth - length : vm.data.marquee2_margin //当文字长度小于屏幕长度时，需要增加补白

      });
      vm.run2(); // 第一个字消失后立即从右边出现
    },

    run2: function () {
      var vm = this;
      vm.data.intervarID = setInterval(function () {
        if (-vm.data.marqueeDistance2 < vm.data.length) {
          // 如果文字滚动到出现marquee2_margin=30px的白边，就接着显示

          vm.setData({

            marqueeDistance2: vm.data.marqueeDistance2 - vm.data.marqueePace,

            marquee2copy_status: vm.data.length + vm.data.marqueeDistance2 <= vm.data.windowWidth + vm.data.marquee2_margin,

          });

        } else {

          if (-vm.data.marqueeDistance2 >= vm.data.marquee2_margin) { // 当第二条文字滚动到最左边时

            vm.setData({

              marqueeDistance2: vm.data.marquee2_margin // 直接重新滚动

            });

            clearInterval(vm.data.intervarID);

            vm.run2();

          } else {

            clearInterval(vm.data.intervarID);

            vm.setData({

              marqueeDistance2: -vm.data.windowWidth

            });

            vm.run2();

          }

        }

      }, vm.data.inter_val);

    },
  },
})
