const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cityArray: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L",
      "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
    ],
    toView: 'inToView01',
    localCity: "",
    searchName: "",
    isKeyword: true
  },
  scrollToViewFn: function (e) {
    var _id = e.target.dataset.id;
    this.setData({
      toView: 'inToView' + _id
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      localCity: options.localCity
    })
    this.queryAllCity();
  },
  queryAllCity: function () {
    var that = this;
    wx.request({
      url: app.projectName + '/applet/queryAllCityName',
      success: function (res) {
        var cityMap = res.data.cityMap;
        that.setData({
          cityMap: cityMap
        });
      }
    })
  },
  selectCityBindTap: function (e) {
    var name = e.target.dataset.name;
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      localUserCity: name
    });
    app.turnBack();
  },
  localCityBindTap: function () {
    var that = this;
    var pages = getCurrentPages(),
      prevPage = pages[pages.length - 2];
    prevPage.setData({
      localUserCity: that.data.localCity,
    });
    app.turnBack();
  },
  cityBindInput: function (e) {
    var that = this;
    var cityName = e.detail.value;
    if (cityName != "") {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/applet/queryLikeCity',
        data: {
          "cityName": cityName
        },
        success: function (res) {
          var cityList = res.data.city_list;
          if (cityList != null && cityList.length > 0) {
            that.setData({
              cityList: res.data.city_list,
              isKeyword: false
            });
          } else {
            that.setData({
              isKeyword: true
            });
          }
        }
      })
    } else {
      that.setData({
        isKeyword: true
      });
    }
  }
})