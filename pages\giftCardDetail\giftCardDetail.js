var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isDeliver:true,
    region: ["请选择省市区"],
    cardName:"",
    cardImg: "",
    cardNo: "",
    remark: "",
    cardPwd:"",
    phone:"",
    userName:"",
    telephone:"",
    address:"",
    deliverremark:"",

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
      var that = this;
      var cardName = options.cardName;
      var cardImg = options.cardImg;
      var cardNo = options.cardNo;
      var remark = options.remark; 
      var cardPwd = options.cardPwd; 
      var phone = options.phone;
      that.setData({
        cardName: cardName,
        cardImg: cardImg,
        cardNo: cardNo,
        remark: remark,
        cardPwd: cardPwd,
        phone:phone
      })
  },
  bindRegionChange: function (e) {
    this.setData({
      region: e.detail.value
    })
  },
  saveUserNameBindInput:function(e){
    var that = this;
    that.setData({
      userName: e.detail.value
    })
  },
  saveTelephoneBindInput: function (e) {
    var that = this;
    that.setData({
      telephone: e.detail.value
    })
  },
  saveAddressBindInput:function(e){
    var that = this;
    that.setData({
      address: e.detail.value
    })
  },
  saveRemarkBindInput:function(e){
    var that = this;
    that.setData({
      deliverremark: e.detail.value
    })
  },
  /**
   * 是否邮寄
   */
  deliverBindtap:function(e){
    var that = this;
    if (!e.detail.value){
      that.setData({
        isDeliver: true
      })
      wx.showToast({
        icon: 'none',
        title: '线上核销礼券仅支持邮寄，如若选择非邮寄，请前往线下实体店',
        duration: 1500
      })
      return;
    }
  },
  exchangeBindtap:function(){
    var that = this;
    wx.showModal({
      title: '提示',
      content: "请确认兑换信息,提交后信息不可撤回",
      success(res) {
        if (res.confirm) {
          that.checkExchangeInfo();
        } else if (res.cancel) {
        }
      }
    });
  },
  checkExchangeInfo:function(){
    var that = this;
    var telephone = that.data.telephone.replace(/\s+/g, '');
    var userName = that.data.userName;
    var pacArray = that.data.region;
    var address = that.data.address;
    var deliverremark = that.data.deliverremark;
    var isDeliver = that.data.isDeliver;
    if(isDeliver){
      if (telephone == null || telephone == "" || telephone.length == 0) {
        wx.showToast({
          icon:'none',
          title: '请输入手机号码',
          duration: 1500
        })
        return;
      }
      if (userName.length < 1) {
        wx.showToast({
          icon: 'none',
          title: '请输入联系人',
          duration: 1500
        })
        return;
      }
      if (pacArray[0] == "请选择省市区") {
        wx.showToast({
          title: '请选择省市区',
          duration: 1500
        })
        return;
      }
      if (address.length == 0) {
        wx.showToast({
          icon: 'none',
          title: '请填写收货地址',
          duration: 1500
        })
        return;
      }
    }
    wx.request({
      url: app.giftCardProjectName + '/api/writeOffCard',
      data: {
        "companyId": app.getExtCompanyId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "cardPwd": that.data.cardPwd,
        "remark": that.data.deliverremark,
        "phone": that.data.phone,
        "consigneeName": that.data.userName,
        "consigneePhone": that.data.telephone,
        "province": pacArray[0],
        "city": pacArray[1],
        "area": pacArray[2],
        "address": that.data.address,
        "userId": app.getUserId(),
        "userId": app.getExtCompanyId(),
      },
      success: function (res) {
        var code = res.data.errorcode;
        var message = res.data.errormsg;
        if (code == 1000) {
          wx.showToast({
            title: "兑换成功",
            duration: 1500
          })
          app.navigateToPage('/pages/giftCardList/giftCardList');
        } else {
          app.showModal({
            title: '提示',
            content: message == "" ? "系统异常，稍后在试" : message
          });
          return;
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})