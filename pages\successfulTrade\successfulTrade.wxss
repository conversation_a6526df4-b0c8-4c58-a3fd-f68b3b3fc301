/* page {
  background: #f9f9f9;
}
.successful_trade {
  width: 100%;
  height: 60px;
  background: #FF7E00;
  line-height: 60px;
  text-align: center;
  color: #fff;
  font-size: 14px;
}
.successBox{
  width: 710rpx;
  height: 430rpx;
  margin:20rpx auto;
  background-color: #fff;
  border-radius:10rpx;
  text-align: center;
  color:#FF7E00;
  
}
.successBox .sucBtn{
  width: 156rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border:1px solid #FF7E00;
  border-radius: 28rpx;
  color:#FF7E00;
  font-size:26rpx;
}

.trade_box, .trade_box2 {
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
}

.trade_box {
  height: 60px;
}

.trade_1, .trade_2 {
  width: 30px;
  height: 30px;
  float: left;
}

.trade_1 {
  margin: 15px 3%;
}

.company_name {
  color: #2e2e2e;
  font-size: 15px;
  display: block;
  padding-top: 10px;
  font-weight: bold;
}

.order_time {
  color: #aeaeae;
  font-size: 13px;
}

.trade_box2 {
  height: 100px;
}

.trade_2 {
  margin: 35px 3%;
}

.trade_top {
  display: block;
  padding: 15px 0 8px 0;
}

.consignee {
  font-size: 15px;
  color: #353535;
}

.phone {
  float: right;
  margin-right: 3%;
  font-size: 15px;
  color: #3d3d3d;
}

.address {
  font-size: 14px;
  line-height: 24px;
  margin-right: 3%;
  display: block;
}

.goods_box {
  width: 100%;
  height: 100px;
  margin-bottom: 10px;
}

.goods_pic {
  width: 80px;
  height: 80px;
  margin-top: 10px;
  margin-left: 3%;
  float: left;
}

.order_all {
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: right;
  color: #2e2e2e;
  font-size: 14px;
  border-top: 1px solid #F7F8F9;
}

.right_box {
  padding-left: 110px;
  display: block;
  height: 100px;
}

.goods_name {
  display: block;
  color: #2e2e2e;
  padding-top: 20px;
  font-size: 15px;
  
}

.goods_price {
  color: #777a7f;
  padding-top: 10px;
  display: block;
}

.goods_price text {
  font-size: 14px;
  margin-right: 5px;
}

.order_all text {
  margin-right: 3%;
  margin-left: 5px;
  color: #FF7E00;
  font-size: 16px;
}
.textName{
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  width:320rpx;
  float:left;
} */
page{
  background: #F0F0F0;
}
.reward_two {
  margin: 0px auto;
  width: 690rpx;
  height: 117rpx;
  border: 0px solid blue;
  margin-top: 16rpx;
  display: flex;
  border-radius: 9px;
}

.reward_three {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 140rpx;
  border: 0px solid red
}

.reward_five {
  border: 0px solid red;
  width: 100%;
  height: 58rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 68rpx;
  color: #333333;
}

.reward_six {
  border: 0px solid green;
  width: 100%;
  height: 58rpx;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 49rpx;
  color: #686868;
}

.reward_seven {
  border: 0px solid red;
  width: 100%;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 117rpx;
  color: #333333;
  text-align: center
}

.reward_dashed {
  width: 50rpx;
  height: 50rpx;
}

.reward_img {
  width: 100%;
  height: auto;
}
.eval_one {
  background: #F0F0F0;
  width: 750rpx;
}

.eval_two {
  background: linear-gradient(180deg, #FFA200 0%, #FE8000 100%);
  height: 571rpx;
  width: 100%;
  border-radius: 0px 0px 60px 60px;
  position: relative;
  z-index: 1;
}

.eval_three {
  height: 400rpx;
  opacity: 1;
  position: relative;
  z-index: 2;
}

.eval_four {
  width: 100%;
  height: 88rpx;
  padding-top: 45rpx;
  display: flex;
  align-items: center;
}

.eval_five {
  width: 88rpx;
  height: 88rpx;
  margin: 0px auto;
}

.eval_six {
  margin-top: 20rpx;
  font-size: 40rpx;
  font-family: PingFang-SC-Bold;
  color: #FFFFFF;
  text-align: center;
  font-weight: 500
}

.eval_seven {
  display: flex;
  margin-top: 40rpx;
}

.eval_eight {
  float: right;
  width: 180rpx;
  height: 64rpx;
  border: 2rpx solid #FFFFFF;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 64rpx;
  color: #FFFFFF;
  text-align: center;
}

.eval_nine {
  width: 180rpx;
  height: 64rpx;
  border: 2rpx solid #FFFFFF;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 64rpx;
  color: #FFFFFF;
  text-align: center;
}

.eval_ten {
  width: 710rpx;
  background: #FFFFFF;
  opacity: 1;
  border-radius: 16rpx;
  margin: 0px auto;
  position: relative;
  z-index: 2;
  margin-bottom: 32rpx;
}

.eval1_one {
  height: 98rpx;
  font-size: 36rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 98rpx;
  color: #843706;
  text-align: center;
}

.eval1_two {
  background: #fff;
  border-bottom: 2rpx solid #F8F8F7;
}

.eval1_three {
  width: 35%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.eval1_four {
  border-radius: 32rpx;
  width: 130rpx;
  height: 56rpx;
  background: #FF7E00;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 56rpx;
  color: #FFFFFF;
}

.eval1_five {
  border-radius: 32rpx;
  width: 130rpx;
  height: 56rpx;
  background: #C4C4C4;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 56rpx;
  color: #FFFFFF;
}
.grey_b{
  background:#c4c4c4;
}