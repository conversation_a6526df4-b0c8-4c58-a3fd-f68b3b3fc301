page {
  background: #f9f9f9;
}


.account_head{
  width:700rpx;
  margin:0 auto;
  margin-bottom:20rpx;
  border-radius:20rpx;
}
/**
.current_account {
  width: 100%;
  height: 44px;
  line-height: 44px;
  border-bottom: 1px solid #ececec;
  display: block;
  font-size: 14px;
  padding: 0 15px;
  font-weight: bold;
  background: #fff;
}**/
.vipWrap view{
  width:33%;
  float:left;
  text-align:center;
  font-size:28rpx;
  color:#333;
  padding-top:20rpx;
  
}
.vipWrap view text{
  display:block;
  padding:10rpx 0;
}
.orderWrap view{
  width:25%;
  float:left;
  text-align:center;
  font-size:24rpx;
  color:#333;
}
.orderWrap view image{
  display:block;
  margin:0 auto;
  margin-bottom:10rpx;
}
.account_head {
  /*height: 70rpx;*/
  /*background: url('https://www.cn2b2c.com/gsf/img/wa/accountManager/top_bg.png') no-repeat 1%;*/
  position: relative;
}

.head_box image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  float: left;
  /*margin-right: 20px;*/
  margin-left:20rpx;
}

.account_head label {
  font-size: 17px;
  color: #fff;
}

.account_detail {
  background: #fff;
}

.account_detail label {
  height: 44px;
  line-height: 44px;
  /*border-bottom: 1px solid #ececec;*/
  /*padding: 0 15px;*/
  padding-right:30rpx;
  font-size: 14px;
  color: #333;
  display: block;
  border-bottom:1px solid #f6f6f6;
  margin-left:30rpx;
}

.account_detail label:last-child {
  border-bottom: none;
}

.account_detail text {
  float: right;
}
.personal_more {
  float: right !important;
  width: 14px !important;
  height: 14px !important;
  margin-top: 16px !important;
  margin-right: 0 !important;
}
.account_detail image {
  width: 36rpx;
  height: 36rpx;
  float: left;
  margin-top: 12px;
  margin-right: 20rpx;
  display: block;
}
.config_bg{
  overflow:hidden;
  position:absolute;
  top:0;
  left:0;
  right:0;
  height:280rpx;
  background:#fff;
}
.icondirect{
 transform:rotate(90deg);
 padding-top: 10px;
}
.goCard{
  z-index:99;
  line-height:80rpx;
  border-top-left-radius:16rpx;
  border-top-right-radius:16rpx;
  position:absolute;
  height:80rpx;
  bottom:0;
  left:30rpx;
  right:30rpx;
  background:#333;
}
.goCard label{
  color:#cac18f;
  font-size:26rpx;
  float:right;
  margin-right:30rpx;
}
.col_detail{
  padding-top:30rpx;
  background:#fff;
  margin-bottom:20rpx;
  width:700rpx;
  margin:0 auto;
  border-radius:20rpx;
  margin-top:20rpx;
}
.oneItem{
  float:left;
  width:160rpx;
  height:150rpx;
  margin-right:15rpx;
  text-align:center;
}
.middleIcon{
  width:100% !important;
  display:block;
  font-size:28rpx;
  text-align:center;
}
.middleText{
  width:100% !important;
  display:block;
  font-size:24rpx;
  text-align:center;
  color:#666;
  margin-top:4rpx;
}
/*强制登录*/
.topWrap {
  text-align: center;
  margin: 15px 0;
  margin-top: 100rpx;
}

.topWrap image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}

.topWrap view {
  font-size: 12px;
  color: #999;
}

.companyLog {
  float: left;
  height: 100%;
  width: 49%;
  border-right: 1px solid #ececec;
  text-align: center;
  color: #b2b2b2;
}

.personLog {
  float: left;
  height: 100%;
  width: 49%;
  text-align: center;
  color: #b2b2b2;
}

.active {
  border-bottom: 2px solid #FF7E00;
  color: #444;
}

.loginType {
  background: #fff;
  font-size: 15px;
  margin-bottom: 20px;
  height: 44px;
  line-height: 44px;
}
.confirm_btn, .confirm_btn2 {
  display: inline-block;
  width: 35%;
  margin: 20px auto;
  height: 70rpx;
  line-height: 70rpx;
  border: none;
  margin-top: 60rpx;
  margin-right: 6%;
  margin-left: 6%;
  font-size: 26rpx;
}
.bl_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}
.cartNum{
  color: #fff;
  position: absolute;
  top: -10%;
  right:20%;
  min-width: 22rpx;
  height: 22rpx;
  padding: 8rpx;
  border-radius: 30rpx;
  background: #FF7E00;
  font-size: 20rpx;
  line-height: 22rpx;
}
.orderState{
  position:relative;
}
