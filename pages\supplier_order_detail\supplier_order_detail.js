var app = getApp();
var TimeUtil = require('../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    detail_address: "https://www.cn2b2c.com/gsf/img/wa/new_3e/detail_address.png",
    orderBean: [],
    actualPayment: 0, //实付金额
    payTime: "",
    orderId: "",
    profitMoney: 0 //活动抵扣
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var orderId = options.orderId;
    that.setData({
      orderId: orderId
    });
    that.queryOrderDetailByOrderId(orderId);
  },
  verifyOrderBind: function (e) {
    var pickUpCode128 = e.currentTarget.dataset.code;
    app.navigateToPage('/pages/verifyOrder/verifyOrder?pickUpCode128=' + pickUpCode128);
  },
  copyOrderNo: function (e) {
    var that = this;
    var orderNo = e.currentTarget.dataset.no;
    wx.setClipboardData({
      data: orderNo,
      success(res) {
        wx.getClipboardData({
          success(res) {
            wx.showToast({
              title: '复制成功'
            })
          }
        })
      }
    })
  },
  queryDistributionBindTap: function (e) {
    var orderId = e.currentTarget.dataset.orderid;
    app.navigateToPage("/pages/selfDeliverInfo/selfDeliverInfo?orderId=" + orderId);
  },
  queryOrderDetailByOrderId: function (orderId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/queryOrderDetailByOrderId',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "orderId": orderId
      },
      success: function (res) {
        var orderBean = res.data.orderBean;
        if (orderBean != null) {
          orderBean.orderGenerateDate = TimeUtil.getSmpFormatDateByLong(orderBean.orderGenerateDate, true);
          orderBean.orderPayTime = TimeUtil.getSmpFormatDateByLong(orderBean.orderPayTime, true);
          orderBean.pickActualArriveTimeBegin = TimeUtil.getSmpFormatDateByLong(orderBean.pickActualArriveTimeBegin, true);
          orderBean.pickActualArriveTimeEnd = TimeUtil.getSmpFormatDateByLong(orderBean.pickActualArriveTimeEnd, true);

          var payList = orderBean.payList;
          var actualPayment = 0.0;
          var scorePayment = 0.0;
          var couponPayment = 0.0;
          var banlancePayment = 0.0;
          var payTime = "";
          if (payList != null && payList.length > 0) {
            for (var i = 0; i < payList.length; i++) {
              if (payList[i].payType == "WECHAT_PAY" && payList[i].payStatus == "PAY_SUCCESS") {
                actualPayment += parseFloat(payList[i].payMoney);
                payTime = TimeUtil.getSmpFormatDateByLong(payList[i].operateTime, true);
              }
              if (payList[i].payType == "ALIPAY" && payList[i].payStatus == "PAY_SUCCESS") {
                actualPayment += parseFloat(payList[i].payMoney);
                payTime = TimeUtil.getSmpFormatDateByLong(payList[i].operateTime, true);
              }
              if (payList[i].payType == "MEMEBER_CARD_ACCUMULATION_PAY" && payList[i].payStatus == "PAY_SUCCESS") {
                scorePayment += parseFloat(payList[i].payMoney);
              }
              if (payList[i].payType == "MEMBER_CARD_BANLANCE_PAY" && payList[i].payStatus == "PAY_SUCCESS") {
                banlancePayment += parseFloat(payList[i].payMoney);
              }
              if (payList[i].payType == "CouponPay" && payList[i].payStatus == "PAY_SUCCESS") {
                couponPayment += parseFloat(payList[i].payMoney);
              }
            }
          }
          that.setData({
            payTime: payTime,
            couponPayment: couponPayment,
            actualPayment: actualPayment,
            scorePayment: scorePayment,
            banlancePayment: banlancePayment,
            orderBean: orderBean
          });
        }
      }
    })
  },
  /**
   * 立即付款
   */
  nowPayBindTap: function (e) {
    wx.showLoading({
      title: '正在请求支付',
      mask: true
    })
    var that = this;
    var orderNo = e.currentTarget.dataset.orderno;
    wx.request({
      url: app.projectName + '/applet/buyOrder/nowPay',
      data: {
        "openId": app.getOpenId(),
        "orderNo": orderNo,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var param = res.data;
        var newOrderBean = res.data.newOrderBean;
        if (flag) {
          wx.requestPayment({
            'timeStamp': param.timeStamp,
            'nonceStr': param.nonceStr,
            'package': param.package,
            'signType': param.signType,
            'paySign': param.paySign,
            success: function (res) {
              app.showModal({
                content: '支付成功'
              })
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryOrderDetailByOrderId(that.data.orderId);
            },
            fail: function (res) {
              if (res.errMsg === 'requestPayment:fail cancel') {
                app.showModal({
                  content: '取消支付'
                })
              } else {
                app.showModal({
                  content: '支付失败'
                })
              }
              that.queryOrderDetailByOrderId(that.data.orderId);
            }
          })
        }
      }
    })
  },
  /**
   * 申请退款
   */
  appleyReturnMoneyBindTap: function (e) {
    var that = this;
    var orderId = e.currentTarget.dataset.orderid;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateIdForOrderRufund',
      data: {
        "typeId": "6",
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              that.appleyReturnMoney(orderId);
            }
          })
        } else {
          that.appleyReturnMoney(orderId);
        }
      }
    })
  },
  /**
  * 申请退款
  */
  appleyReturnMoney: function (orderId) {
    var that = this;
    app.showModal({
      content: '确定申请退款？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/applyRejectedOrderNoneCommodity',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryOrderDetailByOrderId(that.data.orderId);
            } else {
              app.showModal({
                title: '提示',
                content: "申请失败"
              });
            }
          }
        })
      }
    });
  },
  /**
   * 确认收货
   */
  receiptGoodsBindTap: function (e) {
    var that = this;
    app.showModal({
      content: '确定收货？',
      showCancel: true,
      confirm: function () {
        var orderId = e.currentTarget.dataset.orderid;
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/okReceipt',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              app.navigateToPage("/pages/evaluate/evaluate?orderId=" + orderId);
            }
          }
        })
      }
    });
  },
  /**
   * 删除订单
   */
  removeOrderBindTap: function (e) {
    var that = this;
    app.showModal({
      content: '确定删除订单？',
      showCancel: true,
      confirm: function () {
        var orderId = e.currentTarget.dataset.orderid;
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/deleteOrder',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryOrderDetailByOrderId(that.data.orderId);
            }
          }
        })
      }
    });
  },
  /**
   * 取消订单
   */
  cancelOrderformSubmit: function (e) {
    var that = this;
    var orderId = e.currentTarget.dataset.orderid;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newSubscribe/querySystemTemplateIdForOrderRufund',
      data: {
        "typeId": "6",
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var templateArray = res.data.templateArray;
        if (templateArray != null && templateArray.length > 0) {
          wx.requestSubscribeMessage({
            tmplIds: templateArray,
            success(res) {
              that.cancelOrder(orderId);
            }
          })
        } else {
          that.cancelOrder(orderId);
        }
      }
    })
  },
  /**
   * 取消订单
   */
  cancelOrder: function (orderId) {
    var that = this;
    app.showModal({
      content: '确定取消订单？',
      showCancel: true,
      confirm: function () {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/applet/buyOrder/cancelOrder',
          data: {
            "orderId": orderId,
                   "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
            "loginId": app.getLoginId(),
            "userRole": app.getUserRole(),
            "storeId": app.getExtStoreId(),
            "companyId": app.getExtCompanyId()
          },
          success: function (res) {
            var flag = res.data.flag;
            if (flag) {
              that.setData({
                offSet: 1,
                orderList: []
              });
              that.queryOrderDetailByOrderId(that.data.orderId);
            } else {
              app.showModal({
                content: '取消订单失败，请联系客服'
              });
              return;
            }
          }
        })
      }
    });
  },
  /**
   * 评价
   */
  evaluateBindTap: function (e) {
    var orderId = e.currentTarget.dataset.orderid;
    app.navigateToPage("/pages/evaluate/evaluate?orderId=" + orderId);
  },
  /**
   * 查看物流
   */
  queryLogisticsBindTap: function (e) {
    var orderId = e.currentTarget.dataset.orderid;
    var receiveAddress = e.currentTarget.dataset.address;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/queryLogisticsMessage',
      data: {
        "orderId": orderId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var logisticNo = res.data.logisticNo;
        var logisticName = res.data.logisticName;
        var receivePhoneNum = res.data.receivePhoneNum;
        app.navigateToPage("/pages/logisticDetail/logisticDetail?logisticNo=" + logisticNo + "&receivePhoneNum=" + receivePhoneNum +"&logisticName=" + logisticName + "&receiveAddress=" + encodeURIComponent(encodeURIComponent(receiveAddress)));
      }
    })
  }

})