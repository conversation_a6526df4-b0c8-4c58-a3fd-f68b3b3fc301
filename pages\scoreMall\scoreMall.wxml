<test id="test"></test>
<view style="margin-top:40rpx;padding-bottom:100rpx;">
  <view class="flex-wrp clearfix">
    <block wx:key="unique" wx:for="{{exchangeList}}" wx:for-item="exc">
      <view class="flex-item" bindtap="exchangeCommodityBindTap" data-id='{{exc.exchangeCommodityId}}'>
        <view class="goods_pic">
          <image lazy-load='true' src="{{exc.commodityBaseInfo.commodityMainPic}}"></image>
        </view>
        <label class="goods_title">{{exc.commodityBaseInfo.commodityName}}</label>
        <label class="goods_ad">{{exc.commodityBaseInfo.commodityAdContent}}</label>
        <label class="goods_price">
          <label>
            <block wx:key="unique" wx:for="{{exc.commodityPriceList[0].priceList}}" wx:for-item="pl">
              <block wx:if="{{exc.commodityPriceList[0].priceList.length>=2}}">
                <block wx:if="pl.priceType=='ACCMULATION'">
                  {{pl.priceVal}}积分
                </block>
                <block wx:elif="pl.priceType=='RMB'">
                  +{{pl.priceVal}}元
                </block>
              </block>
              <block wx:else>
                <block wx:if="pl.priceType=='ACCMULATION'">
                  {{pl.priceVal}}积分
                </block>
                <block wx:elif="pl.priceType=='RMB'">
                  {{pl.priceVal}}元
                </block>
              </block>
            </block>
          </label>
        </label>
        <label class="good_shopCart">立即兑换
        </label>
      </view>
    </block>
  </view>
</view>