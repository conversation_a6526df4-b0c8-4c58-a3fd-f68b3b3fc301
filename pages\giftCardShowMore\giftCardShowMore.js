var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    logHidden:true,
    cardDetail:'',
    logisticsList:[]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var loginId = options.loginId;
    wx.request({
      url: app.giftCardProjectName + '/api/findCouponLogDetails',
      data: {
        "companyId": app.getExtCompanyId(),
        "logId": loginId,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
      },
      success: function (res) {
        var code = res.data.errorcode;
        var message = res.data.errormsg;
        if (code == 1000) {
          var logisticNo = res.data.result.expressCode;
          var logisticName = res.data.result.expressType;
          that.getLogisticMessage(logisticNo, logisticName);
          that.setData({
            cardDetail: res.data.result,
            expressType: res.data.result.expressType,
            expressCode: res.data.result.expressCode
          })

        } else {
          app.showModal({
            title: '提示',
            content: message == "" ? "系统异常，稍后在试" : message
          });
          return;
        }
      }
    })
  },
  /**
   * 获取物流单号
   */
  getLogisticMessage: function (logisticNo, logisticName) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: 'https://www.cn2b2c.com/applet.message/template/getLogisticsMessage',
      data: {
        "logisticsNo": logisticNo,
        "logisticsName": logisticName
      },
      success: function (res) {
        wx.hideLoading();
        var logisticsList = res.data.logisticsList;
        if (logisticsList != null && logisticsList.length > 0) {
          that.setData({
            logisticsList: logisticsList
          });
        } else {
          
        }
      },
    })
  },
  /**
   * 获取更多物流详情
   */
  queryMoreLogsBindTap:function(){
    var that = this;
    that.setData({
      logHidden:false
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})