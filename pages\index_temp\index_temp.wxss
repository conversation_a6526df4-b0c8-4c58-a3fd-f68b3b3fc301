@import "../CouponTem/CouponTem.wxss";

page {
  /*background:#f0f0f0;*/
  background: #f9f9f9;
}

/*卡券**/

.cardImg {
  z-index: 11;
  width: 100%;
  position: fixed;
}

.coupon_inner {
  width: 100%;
  max-height: 500rpx;
  background: #fc6366;
  padding: 5rpx 0 40rpx;
  overflow-y: auto;
  margin-top: -25rpx;
}

.pop_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #000;
  opacity: 0.6;
}

.couponWrap {
  padding-bottom: 30px;
  margin: 0 auto;
  width: 90%;
  position: fixed;
  top: 14%;
  overflow-y: auto;
  left: 5%;
  z-index: 9999;
}

.circal_top {
  display: inline-block;
  width: 12px;
  height: 6px;
  position: absolute;
  top: -1px;
  left: calc(25% - 6px);
  background: #fc6366;
  border-radius: 0 0 6px 6px;
  border-top: none;
}

.circal_bottom {
  display: inline-block;
  width: 12px;
  height: 6px;
  position: absolute;
  bottom: -1px;
  left: calc(25% - 6px);
  background: #fc6366;
  border-radius: 6px 6px 0 0;
  border-bottom: none;
}

.couponTitle {
  color: #fff;
  text-align: center;
  height: 60px;
  line-height: 80px;
}

.onecircal {
  /*height: 80px;*/
  margin: 15px 20px;
  background: #fff;
  position: relative;
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.oneCoupon {
  height: 80px;
  float: left;
  border-right: 1rpx dashed #c7c7c7;
  width: 25%;
  line-height: 50px;
  margin: 0 auto;
}

.couponImage {
  padding: 15px 0px 0px 10px;
  display: inline-block;
  width: 80%;
  margin: 0 auto;
  text-align: center;
}

.imageShow {
  width: 50px;
  height: 50px;
}

.couponProduct {
  padding-top: 15px;
  padding-left: 10px;
  display: inline-block;
  vertical-align: top;
  overflow: hidden;
  width: 45%;
  float: left;
}

.productDetail {
  font-size: 13px;
  color: #444;
  height: 24px;
  overflow: hidden;
}

.couponMoney {
  margin-top: 17px;
  font-size: 13px;
  color: #fb6165;
  height: 24px;
  overflow: hidden;
}

.couponGift {
  font-size: 14px;
  color: #666;
}

.couponLimit {
  color: #666;
  font-size: 12px;
}

.lineNum {
  color: #444;
  font-size: 12px;
  text-decoration: line-through;
}

.couponRight {
  float: left;
  margin-top: 30px;
  width: 20%;
  padding: 10rpx 6rpx;
  border-radius: 30rpx;
  color: #fff;
  background: #FF7E00;
  text-align: center;
  font-size: 26rpx;
}

.couponText {
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  background: #fb6165;
  color: #fff;
  margin-left: 12px;
}

.coupon_append {
  color: #999;
  background: none;
  /*color:#fff;*/
}

.couponLine {
  font-size: 12px;
  color: #666;
  text-decoration: line-through;
}

.numDiscount {
  font-size: 26px;
  color: red;
}

.titleDiscount {
  font-size: 12px;
  color: red;
}

/**卡券结束*/

.tab-h {
  height: 100rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 80rpx;
  background: #fff;
  font-size: 14px;
  white-space: nowrap;
  position: fixed;
  top: 36px;
  left: 0;
  z-index: 99;
}

.tab-item {
  width: 21%;
  margin: 0 2%;
  text-align: center;
  display: inline-block;
}

.tab-item.active {
  color: #FF7E00;
  position: relative;
}

.tab-item.active:after {
  content: "";
  display: block;
  height: 2px;
  width: 100%;
  background: #FF7E00;
  position: absolute;
  bottom: 0;
  left: 5rpx;
  border-radius: 16rpx;
}

.item-ans {
  width: 100%;
  display: flex;
  flex-grow: row no-wrap;
  justify-content: space-between;
  padding: 30rpx;
  box-sizing: border-box;
  height: 180rpx;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  position: relative;
  padding-right: 30rpx;
}

.avatar .img {
  width: 100%;
  height: 100%;
}

.avatar .doyen {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  bottom: -2px;
  right: 20rpx;
}

.expertInfo {
  font-size: 12px;
  flex-grow: 2;
  color: #b0b0b0;
  line-height: 1.5em;
}

.expertInfo .name {
  font-size: 16px;
  color: #000;
  margin-bottom: 6px;
}

.askBtn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 14px;
  border-radius: 60rpx;
  border: 1px solid #4675f9;
  color: #4675f9;
}

.tab-content {
  /*background:#f0f0f0;*/
  /* background: #f9f9f9; */
}

.scoll-h {
  height: 100%;
}

/**
中部轮播
**/

.eventBanner {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.bannerWrap {
  width: 100%;
  padding: 10rpx;
}

.bannerWrap image {
  border-radius: 10rpx;
}

/*明星单品样式开始*/

page {
  background: #f4f4f4;
  font-family: arial;
}

.top_nav {
  width: 100%;
  height: 33px;
  font-size: 14px;
  line-height: 31px;
  box-sizing: border-box;
  color: #717171;
  position: fixed;
  top: 36px;
  right: 0;
  z-index: 100;
  background: #fff;
}

.top_nav label {
  width: 21%;
  margin: 0 2%;
  text-align: center;
  display: inline-block;
}

.nav_active {
  color: #FF7E00;
  border-bottom: 2px solid #FF7E00;
}

/* 定位 搜索 */

.location_box {
  height: 52rpx;
  /* position: fixed;
  top: 70rpx;
  left: 0; */
  font-size: 28rpx;
  line-height: 26px;
  width: 100%;
  padding: 10px 0 0 0;
  z-index: 100;
  position: relative;
}

.location_box text {
  line-height: 52rpx;
  height: 52rpx;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #fff;
  color: #fff;
  border-radius: 30rpx !important;
  padding: 0 2%;
  overflow: hidden;
}

.location_box icon {
  float: left;
  position: absolute;
  top: 38rpx;
  left: 30%;
  z-index: 10;
  line-height: 34px;
}

.location_box input {
  line-height: 68rpx;
  height: 68rpx;
  width: 50%;
  display: block;
  float: left;
  margin: 0 2%;
  /*margin-left: 18%;*/
  background: #fff;
  color: #272727;
  border-radius: 34rpx;
  padding-left: 8%;
  padding-right: 4%;
  margin-left: 4%;
}

/*.location_box {
  height: 52rpx;
  position: fixed;
  top: 0px;
  left: 0;
  font-size: 28rpx;
  line-height: 52rpx;
  width: 100%;
  padding: 20rpx 0 0 0;
  z-index: 100;
  background: #fff;
}

.location_box text {
  line-height: 52rpx;
  height: 52rpx;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #a6a6a6;
  color: #fff;
  border-radius: 15px !important;
  padding: 0 2%;
  overflow: hidden;
}

.location_box icon {
  float: left;
  position: absolute;
  top: 24rpx;
  left: 9%;
  z-index: 10;
  line-height: 34px;
}

.location_box input {
  line-height: 62rpx;
  height: 62rpx;
  width: 76%;
  display: block;
  float: left;
  margin: 0 6%;
  background: #ededed;
  color: #272727;
  border-radius: 14rpx;
  padding-left: 8%;
  padding-right: 4%;
}*/

.three_box {
  width: 100;
  height: 36px;
  line-height: 36px;
  background: #fff;
}

.three_box label {
  display: inline-block;
  font-size: 12px;
  color: #FF7E00;
}

.three_box label:nth-child(1) {
  width: 55%;
  margin-left: 8%;
}

.three_box label:nth-child(2) {
  width: 37%;
}

.three_box image {
  margin-top: 9px;
  width: 18px;
  height: 18px;
  float: left;
  margin-right: 5px;
}

.company_introduction {
  padding-top: 20rpx;
  width: 100%;
  height: 190rpx;
  background: #fff;
}

.company_introduction label {
  width: 25%;
  display: inline-block;
}

.company_introduction image {
  width: 112rpx;
  height: 112rpx;
  margin: 5px auto;
  display: block;
}

.company_introduction text {
  width: 100%;
  text-align: center;
  display: block;
  color: #595959;
  font-size: 14px;
}

/**热销单品**/

.section {
  line-height: 26px;
  margin-top: 0px;
}

.section_title {
  text-align: center;
  font-size: 16px;
  /*background: #f4f4f4;*/
  line-height: 50px;
  height: 50px;
  margin-top: 24rpx;
  color: #FF7E00;
  letter-spacing: 1px;
  position: relative;
}

.section_title image {
  width: 18px;
  height: 18px;
  position: absolute;
  margin-top: 15px;
}

.flex-wrp {
  width: 96%;
  margin-left: 2%;
  flex-direction: row;
  /*background: #f0f0f0;*/
}

.flex-item {
  width: 48.3%;
  /*width:45%;*/
  float: left;
  background: #fff;
  margin-bottom: 18rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  border-radius: 10rpx;
}

.flex-item:nth-child(2n+1) {
  /*margin-right: 1.6%;*/
  margin-right: 2%;
}

.goods_tips {
  color: #958353;
  border: 1px solid #bcb396;
  top: 10px;
  left: 10px;
  width: 12px;
  height: 62px;
  line-height: 16px;
  display: block;
  position: absolute;
  font-size: 12px;
  word-wrap: break-word;
  padding: 4px;
  border-radius: 3px;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.soonlist {
  font-size: 14px;
  writing-mode: vertical-lr;
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 2;
  color: #988250;
  border: 1px solid #988250;
  border-radius: 5px;
  padding: 5px 0;
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.goods_adv {
  padding: 0 5px;
  height: 36px;
  line-height: 36px;
  background: #f0ece1;
  display: block;
  color: #948358;
  font-size: 12px;
  overflow: hidden;
}

.goods_title {
  display: block;
  /**height: 28px;
  line-height: 28px;**/
  overflow: hidden;
  font-size: 30rpx;
  margin: 0 5px;
  height: 70rpx;
  line-height: 34rpx;
  margin-top: 12rpx;
}

.goods_price {
  color: #FF7E00;
  font-size: 15px;
  margin-left: 5px;
}

/*明星单品样式结束*/

/*限时限购样式开始*/

.time_limit {
  width: 100%;
  height: 38px;
  padding: 5px 0;
  background: #cfd0d4;
}

.time_limit label {
  width: 20%;
  height: 100%;
  display: block;
  float: left;
  box-sizing: border-box;
  font-size: 13px;
}

.time_limit label:first-child {
  border-right: 1px solid #b3b7bb;
}

.time_limit label:last-child {
  border-left: 1px solid #b3b7bb;
}

.time-active {
  background: #94999f;
  height: 48px !important;
  margin-top: -5px;
  border-left: 1px solid #94999f;
  position: relative;
}

.time_limit label text {
  display: block;
  text-align: center;
  line-height: 20px;
}

.time_limit label text:first-child {
  color: #2d2e2e;
  font-size: 12px;
}

.time_limit label text:last-child {
  color: #96938c;
  font-size: 13px;
}

.time-active text {
  color: #fff !important;
}

.time-active text:first-child {
  margin-top: 4px;
}

.row {
  position: absolute;
  bottom: -9px;
  left: 37%;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #94999f;
}

.countdown {
  width: 100%;
  background: #fff;
  height: 50px;
  text-align: center;
  line-height: 50px;
  font-size: 12px;
}

.countdown text {
  color: #e8e8e8;
}

.word_tips {
  margin: 0 5px 0 10px;
  color: #313131 !important;
}

.time_tips {
  margin: 0 10px 0 0px;
  color: #343434 !important;
  font-weight: bold;
}

.recommendGoods_2 {
  width: 100%;
  padding: 10px 0 10px 0;
  height: 140px;
  background: #fff;
}

.recommend_goods {
  width: 94%;
  margin: 0 3%;
  height: 100%;
  border: 1px solid #d4d4d4;
}

.recommend_goods image {
  width: 120px;
  height: 120px;
}

.recommend_box {
  width: 58%;
  float: right;
  height: 100%;
}

.recommend_box text {
  overflow: hidden;
}

.big_title {
  color: #2d2d2d;
  font-size: 15px;
  font-weight: bold;
  display: block;
  width: 100%;
  height: 24px;
}

.little_title {
  margin-top: 15px;
  color: #7e7f81;
  font-size: 15px;
  display: block;
  width: 100%;
  margin-bottom: 5px;
  height: 20px;
}

.recommend_box label {
  font-size: 12px;
  width: 100%;
  display: block;
}

.recommend_box label:last-child {
  height: 30px;
}

.limit_price {
  color: #121212;
  font-size: 16px;
  font-weight: bold;
}

.cost_price {
  color: #808080;
  margin-left: 10px;
  text-decoration: line-through;
}

.limit_number {
  border: 1px solid #acacac;
  border-radius: 12px;
  background: #fff;
  padding: 3px 8px;
  color: #2e2e2e;
  line-height: 30px;
}

.buttonBox {
  padding: 2px 16px;
  background: #FF7E00;
  color: #fff;
  float: right;
  font-size: 14px;
  margin-right: 10px;
}

/**热销单品**/

.section {
  line-height: 26px;
  background: #fff !important;
  /**margin-top: 20px;**/
}

/*限时限购样式结束*/

/*新品上市样式开始*/

.recommendGoods {
  width: 100%;
  padding: 10px 0 10px 0;
  height: 120px;
  background: #fff;
}

.right_recommend {
  width: 58%;
  float: right;
  height: 100%;
}

.right_recommend text {
  display: block;
  width: 100%;
  overflow: hidden;
  height: 24px;
}

.right_recommend text:nth-child(1) {
  color: #76797e;
  font-size: 14px;
  margin-top: 22px;
}

.right_recommend text:nth-child(2) {
  color: #2e2e2e;
  font-size: 15px;
  font-weight: bold;
}

.right_recommend text:nth-child(3) {
  color: #070707;
  font-size: 17px;
  margin-top: 6px;
  font-weight: bold;
}

.goods_title2 {
  display: block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  font-size: 14px;
  background: #d8d9de;
  margin-top: 4px;
  color: #76797e;
  text-align: center;
}

.goods_tag {
  width: 96%;
  margin: 5px 2%;
}

.goods_tag text {
  color: #fff;
  margin-left: 5px;
  padding: 2px 3px;
  font-size: 12px;
}

.goods_tag text:nth-child(1) {
  background: #94999f;
}

.goods_tag text:nth-child(2) {
  background: #af423d;
}

/*新品上市样式结束*/

/**推荐 新增商品样式**/

.recommendGoods_ad {
  width: 100%;
  background: #fff;
  /** padding-top: 10px;
  border-top: 1px solid #f4f4f4;**/
}

.recommendGoods_ad:after {
  clear: both;
  content: "";
  height: 0;
  display: block;
}

.recommendAd {
  width: 50%;
  float: left;
  border-right: 1px solid #f4f4f4;
  border-top: 1px solid #f4f4f4;
  height: 120px;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.recommendAd:nth-child(2n) {
  border-right: none;
}

.recommendAd label {
  width: 57%;
  height: 100%;
  font-size: 15px;
  color: #030303;
  position: absolute;
  top: 6px;
  left: 10%;
  z-index: 20;
  max-width: 91px;
}

.recommendAd label text {
  display: block;
  width: 100%;
}

.recommendAd label text:first-child {
  height: 25px;
  margin-top: 35%;
}

/**.recommendAd label text:last-child {
  color: #fff;
  font-size: 12px;
  background: #c7b797;
  width: 50px;
  text-align: center;
  border-radius: 4px;
  height: 24px;
  line-height: 24px;
}**/

.recommendAd image {
  width: 100px;
  position: absolute;
  bottom: 0;
  right: -2%;
}

/**通栏**/

.banner_box {
  width: 100%;
  position: relative;
}

.banner_box image {
  width: 100%;
}

.banner_word {
  position: absolute;
  top: 18%;
  right: 10%;
}

.banner_title {
  display: block;
  font-size: 17px;
  text-align: center;
  letter-spacing: 3px;
}

.banner_title text {
  display: block;
}

.banner_price {
  display: block;
  text-align: center;
  color: #000;
  font-size: 13px;
  line-height: 22px;
}

.discount_price {
  display: block;
  text-align: center;
  color: #FF7E00;
  font-size: 13px;
  line-height: 22px;
}

.banner_btn {
  background: #FF7E00;
  color: #fff;
  height: 26px;
  line-height: 26px;
  font-size: 14px;
  width: 98px;
}

.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}

.slideTwo {
  margin-top: 20px;
  height: 100px;
  box-shadow: 0px 0px 8px 3px #e8e8e8;
  border-radius: 10px;
  margin-left: 5%;
  margin-right: 5%;
}

.slideTwo_l {
  float: left;
  width: 30%;
  height: 100%;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.slideTwo_r {
  width: 65%;
  float: right;
  padding-right: 2%;
  height: 100px;
  overflow: hidden;
}

.r_title {
  height: 30px;
  line-height: 30px;
}

.r_content {
  font-size: 12px;
  color: #666;
}

.slideThree_l {
  float: left;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1px solid #ddd;
  margin-top: 5px;
  margin-left: 5px;
}

.promotionMark {
  height: 22px;
  line-height: 22px;
  padding: 0px 6px;
  border-radius: 4px;
  z-index: 999;
  position: absolute;
  background: #e57b7b;
  top: 5%;
  left: 5%;
  color: #fff;
  font-size: 12px;
}

.price_append {
  text-decoration: line-through;
  font-size: 24rpx;
  color: #666;
}

.promotionPrice {
  font-size: 15px;
  color: #e57b7b;
}

.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  content: ".";
  height: 0;
}

.announceItem {
  position: relative;
}

.announceItem label:first-child {
  position: absolute;
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #ddd;
  top: 30%;
  left: 0;
}

.announceItem label:last-child {
  color: #444;
  font-size: 13px;
  margin-left: 14px;
  display: inline-block;
  height: 20px;
  width: 250px;
}

.showAnnounce {
  vertical-align: middle;
  margin-top: 15px;
  float: left;
  width: 20px;
  height: 20px;
  margin-left: 16px;
  margin-right: 5px;
}

.announceDetail {
  float: left;
  margin-left: 10px;
  vertical-align: middle;
  margin-top: 5px;
  width: 80%;
}

.annoucnePart {
  height: 56px;
  padding: 4px 0px 0px 0px;
  background: #fff;
  /*margin: 20rpx 0;*/
}

.black_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.scroll_block {
  width: 80%;
  position: absolute;
  top: 40%;
  left: 10%;
  z-index: 130;
  padding-bottom: 46px;
  border-radius: 10px;
  background: #fff;
}

.announce_Title {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 10px;
  font-size: 15px;
  color: #444;
}

.announce_Content {
  font-size: 14px;
  color: #444;
  padding: 0px 10px;
}

.oneItem {
  margin-top: 10px;
  vertical-align: middle;
}

.eventWrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  width: 50px;
  height: 50px;
}

.eventWrap image {
  width: 50px;
  height: 50px;
  top: -2px;
  left: -2px;
}

.eventWrap text {
  z-index: 9999;
  color: #fff;
  position: absolute;
  display: inline-block;
  font-size: 12px;
  top: 3px;
  left: 2px;
  transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
}

/*首页分类*/

.catagory_introduction {
  width: 100%;
  background: #fff;
}

.catagory_introduction label {
  width: 20%;
  font-size: 13px;
  height: 84px;
  overflow: hidden;
  float: left;
}

.catagory_introduction label text {
  overflow: hidden;
  text-align: center;
  display: block;
  height: 100%;
  margin-bottom: 2px;
}

.catagory_introduction image {
  width: 56px;
  height: 56px;
  margin: 5px auto;
  display: block;
  border-radius: 5px;
}

.categoryContent {
  margin-top: 20rpx;
  background: #fff;
  padding: 20rpx;
}

.cataTitle {
  margin-bottom: 10px;
  color: #666;
  font-weight: bold;
}

.cataTitle label {
  font-weight: normal;
  float: right;
  font-size: 12px;
}

.cataTitle text {
  /*transform:rotate(90deg);*/
  display: inline-block;
}

.promotionGoods {
  padding: 10rpx 20rpx;
  background: #fff;
  margin-top: 15rpx;
}

.old_p {
  margin-top: 8rpx;
  font-size: 26rpx;
  text-align: center;
  color: #f60;
}

.new_p {
  margin-bottom: 8rpx;
  color: #666;
  font-size: 20rpx;
  text-decoration: line-through;
  text-align: center;
}

.goods_n {
  font-size: 24rpx;
  height: 30rpx;
  overflow: hidden;
  text-align: center;
  width: 180rpx;
  margin: 0 auto;
}

.goods_p {
  width: 150rpx;
  height: 150rpx;
}

.goods_w {
  display: inline-block;
  width: 160rpx;
  box-shadow: #c6c7c8 0px 0px 12rpx;
  margin-bottom: 10rpx;
  margin-top: 20rpx;
  margin-left: 15rpx;
}

.goods_w:first-child {
  margin-left: 12rpx;
}

.scroll_box {
  height: 260rpx;
  overflow: hidden;
  background: #fff;
  white-space: nowrap;
}

.example {
  display: block;
  height: 60rpx;
  background: #fff;
  padding-top: 20rpx;
}

.marquee_box {
  position: relative;
  padding-left: 50rpx;
}

.marquee_box image {
  padding-left: 30rpx;
  padding-right: 30rpx;
  background: #fff;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  width: 40rpx;
  height: 40rpx;
}

.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
  color: #FF7E00 !important;
}

/*新增mode*/

.newMode_wrap {
  background: #fff;
  padding-bottom: 20rpx;
}

.text_mode {
  color: #FF7E00;
  font-size: 32rpx;
  text-align: center;
  padding: 20rpx 0;
  background-color: #f9f9f9;
}

.line_one image {
  margin: 0 25rpx;
  width: 700rpx;
  border-radius: 10rpx;
}

.line_two {
  padding: 10rpx 0;
}

.line_two image:nth-of-type(2) {
  margin-left: 20rpx;
}

.line_two image {
  border-radius: 10rpx;
  width: 345rpx;
  height: 345rpx;
  float: left;
}

.line_three {
  width: 700rpx;
  margin: 0 auto;
  padding: 10rpx 0;
}

.line_three image {
  border-radius: 10rpx;
  float: left;
  width: 220rpx;
  height: 220rpx;
  margin-left: 20rpx;
}

.line_three image:first-child {
  margin-left: 0;
}

.line_left_one {
  width: 700rpx;
  margin: 0 auto;

}

.line_left_one .image_left {
  border-radius: 10rpx;
  width: 300rpx;
  height: 398rpx;

}

.line_left_one .image_right {
  margin-left: 20rpx;
  display: inline-block;
  width: 380rpx;

}

.line_left_one .image_right image:first-child {
  border-radius: 10rpx;
  width: 380rpx;
  height: 190rpx;
  margin-bottom: 10rpx;

}

.line_left_one .image_right image:last-child {
  border-radius: 10rpx;
  width: 380rpx;
  height: 190rpx;

}

.line_left_two {
  width: 700rpx;
  margin:0 auto;
}

.line_left_two .image_left {
  border-radius: 10rpx;
  width: 300rpx;
  height: 398rpx;
}

.line_left_two .image_right {
  margin-left: 20rpx;
  display: inline-block;
  width: 380rpx;
}

.right_top {
  border-radius: 10rpx;
  width: 380rpx;
  height: 190rpx;
  margin-bottom: 10rpx;

}

.right_bottom {
  width: 380rpx;
}

.right_bottom image:first-child {
  border-radius: 10rpx;
  width: 180rpx;
  height: 190rpx;

}

.right_bottom image:last-child {
  border-radius: 10rpx;
  width: 180rpx;
  height: 190rpx;
  margin-left: 20rpx;

}

.line_left_four {
  padding: 8rpx 0;
  width: 700rpx;
  margin: 0 auto;
}

.four_image_left {
  border-radius: 10rpx;
  width: 300rpx;
  height: 398rpx;
}

.four_image_right {
  margin-left: 20rpx;
  display: inline-block;
  width: 380rpx;
}

.four_image_right>view {
  width: 380rpx;
}

.four_image_right .image_one {
  border-radius: 10rpx;
  width: 180rpx;
  height: 190rpx;
  margin-bottom: 10rpx;
}

.four_image_right .image_two {
  border-radius: 10rpx;
  width: 180rpx;
  height: 190rpx;
  margin-left: 20rpx;
  margin-bottom: 10rpx;
}

.four_image_right .image_three {
  border-radius: 10rpx;
  width: 180rpx;
  height: 190rpx;
}

.four_image_right .image_four {
  border-radius: 10rpx;
  width: 180rpx;
  height: 190rpx;
  margin-left: 20rpx;

}

.goods_mode1 {
  margin-left: 14rpx;
  margin-right: 14rpx;
  width: 720rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  margin-bottom: 10rpx;
  border-radius: 10rpx;
  background: #fff;
  margin-top: 10rpx;

}

.goods_mode1 .activity_time {
  display: none !important;
}

.goods_mode1 .activity_time:nth-of-type(1) {
  display: flex !important;
}

.goods_mode1 image {
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.goods_desc {
  margin: 20rpx;
}

.desc_title {
  font-size: 28rpx;
  height: 40rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.desc_title1 {
  font-size: 28rpx;
  height: 80rpx;
  line-height: 40rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;

}

.desc_spec {
  color: #666;
  font-size: 24rpx;
  margin-top: 10rpx;
  height: 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.desc_price {
  color: #FF7E00;
  margin-top: 10rpx;
}

.price_tag {
  font-size: 24rpx;
}

.price_inner {
  font-size: 34rpx;
}

.goods_mode2 {
  margin: 20rpx 0;
  background-color: #f4f4f4;
}

.mode2_wrap {
  float: left;
  margin-left: 14rpx;
  width: 350rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  border-radius: 10rpx;
  background: #fff;
}

.mode2_wrap image {
  width: 350rpx;
  height: 350rpx;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.wrap_append {
  margin-left: 0;
  margin-right: 14rpx;
  float: right;
}

.goods_mode3 {
  margin: 20rpx 0;
}

.mode3_wrap {
  float: left;
  margin-left: 14rpx;
  width: 230rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  border-radius: 10rpx;
  background: #fff;
  margin-bottom: 10rpx;
}

.mode3_wrap image {
  width: 226rpx;
  height: 226rpx;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.desc_app {
  text-align: left;
}

.mode_slide {
  overflow: hidden;
  background: #fff;
  /* white-space: nowrap; */
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.slide_wrap {
  /*border:2rpx solid #ddd;*/
  width: 226rpx;
  display: inline-block;
  margin-left: 14rpx;
  box-shadow: 1px 1px 1px 1px #f8f8f8;
  border: 1rpx solid #dedede;
  border-radius: 10rpx;

}

.slide_wrap image {
  width: 226rpx;
  height: 226rpx;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.mode_slide scroll-view {
  overflow: hidden;
}

/*推荐商品一排一个*/

.recommadOne {
  width: 720rpx;
  margin: 0 15rpx;
}

.one_inner {
  margin-bottom: 20rpx;
}

.one_inner image {
  float: left;
  width: 200rpx;
  border-radius: 10rpx;
}

.right_inner {
  height: 250rpx;
  position: relative;
  float: left;
  width: 450rpx;
  margin-left: 20rpx;
}

.right_title {
  font-size: 28rpx;
}

.right_desc {
  font-size: 24rpx;
  color: #666;
  height: 40rpx;
  overflow: hidden;
}

.right_price {
  color: #FF7E00;
  position: absolute;
  left: 0;
  bottom: 0;
}

/*优惠券第二种形式*/

.coupon2 {
  overflow: hidden;
  background: #fff;
  white-space: nowrap;
  padding: 20rpx 0;
}

.card_one {
  margin-left: 10rpx;
  background: #ffe9b7;
  border-radius: 8rpx;
  width: 300rpx;
  height: 150rpx;
  display: inline-block;
  margin-right: 10rpx;
}

.card_inner {
  color: #f39343;
  text-align: center;
  vertical-align: middle;
  float: left;
  height: 150rpx;
  width: 70%;
  border-right: 1rpx dashed #fff;
  padding-top: 6%;
}

.card_amount {
  font-size: 60rpx;
}

.card_amount label {
  font-size: 26rpx;
  margin-left: 10rpx;
}

.card_con {
  font-size: 26rpx;
  margin-top: 10rpx;
}

.card_right {
  font-size: 26rpx;
  float: left;
  height: 150rpx;
  width: 28%;
  margin-top: 12%;
  text-align: center;
  vertical-align: middle;
}

.right_one {
  color: #f39343;
}

.right_two {
  margin-top: 10%;
  color: #f39343;
}

.card_append {
  background: #ddd;
}

.inner_append {
  border-right: 1rpx dashed #fff;
  color: #999;
}

.right_append {
  padding-top: 8%;
  margin-top: 0;
}

.one_inner image {
  width: 250rpx;
  height: 250rpx;
  border-radius: 8rpx;
}

.mode2_wrap {
  margin-bottom: 10rpx;
}

/*add*/

.location_store {
  line-height: 80rpx;
  height: 80rpx;

  /* position: fixed;
  top: 0;
  left: 0;
  right: 0; */
  z-index: 100;
}

.location_store image {
  width: 40rpx;
  vertical-align: middle;
  margin-left: 40rpx;
  margin-right: 16rpx;
}

.storeName {
  color: #999;
  font-size: 28rpx;
}

.storeChange {
  font-size: 28rpx;
  margin-right: 30rpx;
}

/*底部购物车*/
.shopcart_b {
  z-index: 1000;
  background: #fff;
  left: 0;
  right: 0;
  position: fixed;
  bottom: 0;
}

.shop_top {
  position: relative;
  /*min-height:100rpx;*/
}

.shop_top image {
  border: 1rpx solid #ccc;
  width: 160rpx;
  height: 160rpx;
  position: absolute;
  top: -80rpx;
  margin-left: 40rpx;
}

.shop_top .shop_goodsInfo {
  padding-top: 10rpx;
  margin-left: 250rpx;
  font-size: 26rpx;
  color: #666;
}

.shop_top .shop_goodsInfo label {
  color: #353535;
}

.shop_top .shop_goodsInfo view {
  padding: 6rpx 0;
  font-size: 28rpx;
}

.shop_goodsInfo view text {
  color: #FF7E00;
}

.goods_stock {
  margin: 20rpx 0;
  font-size: 28rpx;
  color: #353535;
  margin-left: 40rpx;
}

.shopcart_num {
  font-size: 28rpx;
  color: #353535;
  margin-bottom: 40rpx;
  margin-top: 30rpx;
}

.shop_om {
  margin-left: 140rpx;
  margin-bottom: 20rpx;
}

.shop_om text {
  width: 100rpx;
  float: left;
}

.shop_om input {
  float: left;
  border: 1rpx solid #ccc;
}

.shop_om {
  margin-left: 140rpx;
  margin-bottom: 20rpx;
}

.add_toCart {
  padding: 24rpx 0;
  background: #FF7E00;
  color: #fff;
  text-align: center;
  font-size: 30rpx;
}

.numBox {
  height: 60rpx;
  border: 1rpx solid #ddd;
  padding-left: 20rpx;
  width: 80rpx;
}

.mini_buy {
  margin: 20rpx 0 0 0;
  font-size: 28rpx;
  color: #353535;
  margin-left: 40rpx;
}

.minus_num {
  height: 60rpx;
  border: 1rpx solid #ccc;
  border-right: none;
  line-height: 60rpx;
  width: 60rpx;
  text-align: center;
  font-size: 40rpx;
  float: left;
}

.plus_num {
  height: 60rpx;
  border: 1rpx solid #ccc;
  border-left: none;
  line-height: 60rpx;
  width: 60rpx;
  text-align: center;
  font-size: 40rpx;
  float: left;
}

/*新加的样式*/
.example {
  display: block;
  height: 60rpx;
  background: #fff;
  padding-top: 20rpx;
}

.marquee_box {
  position: relative;
  padding-left: 50rpx;
}

.marquee_box image {
  padding-left: 30rpx;
  padding-right: 30rpx;
  background: #fff;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  width: 40rpx;
  height: 40rpx;
}

.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
  color: #FF7E00 !important;
}

/*加入购物车*/
.scroll_blo {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 99999;
  padding-bottom: 46px;
}

.addgoods_box {
  width: 100%;
  height: 60px;
  /* border-bottom: 1px solid #e5e5e5; */
  background: #fff;
}

.addgoods_pic {
  width: 86px;
  height: 86px;
  background: #fff;
  position: absolute;
  top: -43px;
  left: 15px;
  border: 1px solid #ccc;
  /* overflow: hidden; */
}

.addgoods_pic image {
  width: 100%;
  height: 100%;
}

.addgoods_title,
.addgoods_price {
  padding-left: 115px;
  height: 20px;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  padding-right: 30px;
  text-overflow: ellipsis;
  padding-top: 3px;
}

.addgoods_title {
  font-size: 14px;
  background: #fff;
  color: #000;
}

.addgoods_price {
  color: #FF7E00;
  font-size: 13px;
}

.goods_classify {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 10px 5px 10px;
}

.goods_classify label {
  margin-bottom: 10px;
  line-height: 28px;
  font-size: 14px;
}

.goods_classify view text {
  float: left;
  padding: 0 16px;
  color: #333;
  background: #ececec;
  font-size: 12px;
  margin-bottom: 8px;
  margin-right: 10px;
  line-height: 28px;
  border-radius: 5px;
}

.addgoods_number {
  width: 100%;
  background: #fff;
  padding-top: 15px;
  padding-bottom: 15px;
  /**border-bottom: 1px solid #e5e5e5;**/
}

.limited_quantity {
  font-size: 13px;
  float: left;
  padding-left: 15px;
  padding-top: 5px;
}

.stock {
  font-size: 13px;
  float: right;
  margin-right: 5px;
  margin-top: 4px;
}

.plus_minus {
  float: right;
  padding-right: 15px;
  margin-top: 3px;
}

.minus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 22px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 24px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_minus input {
  width: 50px;
  height: 26px;
  line-height: 10px;
  background: #e4e4e4;
  float: left;
  border: none;
  font-size: 13px;
  margin: 0 5px;
  text-align: center;
  color: #333;
}

.pay-add-to-shoppingcart {
  display: block;
  padding: 0px;
  font-size: 14px;
  line-height: 44px;
  height: 44px;
  border-radius: 0px;
  flex: 1;
  color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
}

.page-dialog-close {
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 10;
  display: block;
  width: 20px;
  height: 20px;
  /* border: 1px solid #67666f;
  color: #67666f; 
  text-align: center;
  line-height: 16px;
  border-radius: 50%;
  font-size: 26px;
  margin: 5px auto;*/
}

.code {
  width: 80%;
  margin-left: 5%;
}

.outCode {
  width: 55% !important;
}


@keyframes around {
  from {
    margin-left: 100%;
  }

  to {
    /* var接受传入的变量 */
    margin-left: var(--marqueeWidth--);
  }
}

.marquee_container {
  background-color: #fff;
  height: 40rpx;
  line-height: 40rpx;
  position: relative;
  width: 100%;
}

.a_text {
  color: #909090;
  font-size: 28rpx;
  /*display: inline-block;*/
  white-space: nowrap;
  overflow: hidden;
  animation: around var(--allTs--) infinite linear;
}

.promotionDesc {
  border-radius: 6rpx;
  padding: 6rpx;
  border: 1px solid #FF7E00;
  margin-left: 20rpx;
  font-size: 18rpx;
  color: #FF7E00;
}

.activity_time {
  width: 100%;
  /* float: right; */
  background: #FF7E00;
  height: 70rpx;
  padding-left: 10px;
  text-align: center;
  color: #583512;
}

.activity_time>view:first-child {
  color: #fff;
  text-align: center;
  width: 100%;
  display: block;
  line-height: 13px;
  margin-top: 8px;
  font-weight: bold;
  font-size: 13px;
  background: none;
  float: left;
}

.activity_time .clock {
  font-size: 14px;
  background: #fff;
  color: #FF7E00;
  padding: 0 3px;
  border-radius: 3px;
  height: 42rpx;
  line-height: 42rpx;
}

/* 团购活动start */
.groupbuy {
  width: 750rpx;
  background-color: #fff;
  font-size: 28rpx;
  margin:10rpx 0;
}

.groupbuy .groupTop {
  height: 100rpx;
  line-height: 100rpx;
  padding: 0 20rpx;

}

.groupbuy .groupCenter {
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
}

.groupbuy .groupCenter .groupGoods {
  width: 225rpx;
  font-size: 28rpx;
}

.groupbuy .groupCenter .groupGoods .goodsName {
  color: #525252;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden
}

.personal_more {
  float: right !important;
  width: 14px !important;
  height: 14px !important;
  margin-top: 16px !important;
  margin-right: 0 !important;
}
/* 团购活动end */