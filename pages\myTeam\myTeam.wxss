/* pages/person_directAgent/person_directAgent.wxss */
.agentWrap{
  color:#3d3a3a;
  margin:0rpx 20rpx;
  position:relative;
}
.totalAgent{
  background:#93a9d2;
  height:400rpx;
  text-align:center;
}
.totalAgent label:first-child{
  font-size:36rpx;
  display:block;
  padding-top:120rpx;
}
.totalAgent .agentNum{
  color:#fff;
  font-size:70rpx;
}
.agentNum text{
  font-size:28rpx;
  color:#3d3a3a;
}
.directAgent{
  font-size:28rpx;
  position:absolute;
  width:80%;
  /*height:100rpx;*/
  left:50%;
  transform: translateX(-50%);
  top:320rpx;
}
.levelone{
  width:100%;
  height:250rpx;
  border-radius:20rpx;
  /*border:1px solid #ececec;*/
  text-align:center;
  background:#fff;
  box-shadow:2px 2px 1px 0px #e8e8e8;
  position:relative;
}
.levelone image{
  width:50rpx;
  margin-top:30rpx;
}
.levelone label{
  display:block;
  margin-top:20rpx;
}
.level_num{
  font-size:40rpx;
  color:#d4525c;
}
.level_num text{ 
  font-size:28rpx;
  color:#3d3a3a;
}
.agent_append{
  margin-top:40rpx;
}
.agent_level{
  font-size:30rpx;
  color:#3d3a3a;
  margin-left:6rpx;
}
.levelone .moreAgent{
  position:absolute;
  right:8%;
  top:30%;
  width:36rpx;
}
.direct_title{
  font-size:32rpx;
}

/*邀请人*/
.black_bg{
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}
.myInviate{
  position:absolute;
  top:20%;
  z-index:11;
  background:#fff;
  border-radius:10rpx;
  width:70%;
  left:15%;
}
.myInviate{
  padding:30rpx;
}
.inviate_t{
  text-align:center;
  color:#3d3a3a;
  margin-bottom:40rpx;
}
.inviate_info view{
  margin-bottom:14rpx;
  color:#666;
  font-size:30rpx;
}
