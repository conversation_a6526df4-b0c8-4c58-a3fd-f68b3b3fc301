<wxs src="../../../wxs/subutil.wxs" module="tools" />
<view class="accountDetail">
  <view class="detailTop">
    <view class="{{type==0?'active':''}}" data-type="0" bindtap="switchOrderTypeBindTap">全部</view>
    <view class="{{type==1?'active':''}}" data-type="1" bindtap="switchOrderTypeBindTap">审核中</view>
    <view class="{{type==3?'active':''}}" data-type="3" bindtap="switchOrderTypeBindTap">待打款</view>
    <view class="{{type==4?'active':''}}" data-type="4" bindtap="switchOrderTypeBindTap">已提现</view>
    <view bindtap="selectBillClick">筛选<image class="selectImage" src="{{unselected}}" mode="widthFix"></image>
    </view>
  </view>
  <view class="r_wrap">
    <block wx:if="{{orderList.length>0}}">
      <block wx:key="unique" wx:for-index="orderIndex" wx:for="{{orderList}}" wx:for-item="order">
        <!--提现成功-->
        <block wx:if="{{order.orderStatus==4}}">
          <view class="oneItem">
            <view class="itemTop" bindtap="switchMoreBindTap" data-id="{{order.orderId}}">
              <image class="checkImg" mode="widthFix" src="{{checkedSuccess}}"></image>
              <label class="checkState">提现成功</label>
              <label class="checkTime">{{order.withdrawCreateTime}}</label>
              <image class="showImg" mode="widthFix" src="{{order.showDetail?checkedDown:checkedUp}}">
              </image>
            </view>
            <view class="itemContent">
              <view class="content_v">
                <label>申请提现金额</label><label>￥{{tools.sub.formatAmount(order.withdrawQuota,2)}}</label></view>
              <view class="content_v"><label>手续费</label><label>-
                  ￥{{tools.sub.formatAmount(order.withdrawFee,2)}}</label>
              </view>
              <view class="content_v"><label>实际到账金额</label><label class="success_c">+
                  ￥{{tools.sub.formatAmount(order.actualWithdrawQuota,2)}}</label></view>
            </view>
            <view class="itemDetail" hidden="{{order.showDetail}}">
              <view class="item_no">
                <label>订单号</label>
                <label class="order_no">{{order.orderNo}}</label>
                <label class="order_copy" bindtap='copyOrderNo' data-no='{{order.orderNo}}'>复制</label>
              </view>
              <view class="content_v">
                <label>银行卡号</label>
                <label>{{order.withdrawCardNo}}</label>
              </view>
              <view class="content_v">
                <label>手机号</label>
                <label>{{order.withdrawCardUserPhone}}</label>
              </view>
            </view>
          </view>
        </block>
        <!--待打款-->
        <block wx:if="{{order.orderStatus==3}}">
          <view class="oneItem">
            <view class="itemTop" bindtap="switchMoreBindTap" data-id="{{order.orderId}}">
              <image class="checkImg" mode="widthFix" src="{{checkedPay}}"></image>
              <label class="checkState">待打款</label>
              <label class="checkTime">{{order.withdrawCreateTime}}</label>
              <image class="showImg" mode="widthFix" src="{{order.showDetail?checkedDown:checkedUp}}">
              </image>
            </view>
            <view class="itemContent">
              <view class="content_v">
                <label>申请提现金额</label><label>￥{{tools.sub.formatAmount(order.withdrawQuota,2)}}</label></view>
              <view class="content_v"><label>手续费</label><label>-
                  ￥{{tools.sub.formatAmount(order.withdrawFee,2)}}</label>
              </view>
              <view class="content_v"><label>实际到账金额</label><label class="success_p">+
                  ￥{{tools.sub.formatAmount(order.actualWithdrawQuota,2)}}</label></view>
            </view>
            <view class="itemDetail" hidden="{{order.showDetail}}">
              <view class="item_no">
                <label>订单号</label>
                <label class="order_no">{{order.orderNo}}</label>
                <label class="order_copy" bindtap='copyOrderNo' data-no='{{order.orderNo}}'>复制</label>
              </view>
              <view class="content_v">
                <label>银行卡号</label>
                <label>{{order.withdrawCardNo}}</label>
              </view>
              <view class="content_v">
                <label>手机号</label>
                <label>{{order.withdrawCardUserPhone}}</label>
              </view>
            </view>
          </view>
        </block>
        <!--审核中-->
        <block wx:if="{{order.orderStatus==1}}">
          <view class="oneItem">
            <view class="itemTop" bindtap="switchMoreBindTap" data-id="{{order.orderId}}">
              <image class="checkImg" mode="widthFix" src="{{checkedUnder}}"></image>
              <label class="checkState">审核中</label>
              <label class="checkTime">{{order.withdrawCreateTime}}</label>
              <image class="showImg" mode="widthFix" src="{{order.showDetail?checkedDown:checkedUp}}"></image>
            </view>
            <view class="itemContent">
              <view class="content_v">
                <label>申请提现金额</label><label>￥{{tools.sub.formatAmount(order.withdrawQuota,2)}}</label></view>
              <view class="content_v"><label>手续费</label><label>-
                  ￥{{tools.sub.formatAmount(order.withdrawFee,2)}}</label></view>
              <view class="content_v"><label>实际到账金额</label><label class="under_c">+
                  ￥{{tools.sub.formatAmount(order.actualWithdrawQuota,2)}}</label></view>
            </view>
            <view class="itemDetail" hidden="{{order.showDetail}}">
              <view class="item_no">
                <label>订单号</label>
                <label class="order_no">{{order.orderNo}}</label>
                <label class="order_copy" bindtap='copyOrderNo' data-no='{{order.orderNo}}'>复制</label>
              </view>
            </view>
          </view>
        </block>
        <!--提现失败-->
        <block wx:if="{{order.orderStatus==6}}">
          <view class="oneItem">
            <view class="itemTop" bindtap="switchMoreBindTap" data-id="{{order.orderId}}">
              <image class="checkImg" mode="widthFix" src="{{checkedFail}}"></image>
              <label class="checkState">提现失败</label>
              <label class="checkTime">{{order.withdrawCreateTime}}</label>
              <image class="showImg" mode="widthFix" src="{{order.showDetail?checkedDown:checkedUp}}"></image>
            </view>
            <view class="itemContent">
              <view class="content_v">
                <label>申请提现金额</label><label>￥{{tools.sub.formatAmount(order.withdrawQuota,2)}}</label></view>
              <view class="content_v"><label>手续费</label><label>-
                  ￥{{tools.sub.formatAmount(order.withdrawFee,2)}}</label></view>
              <view class="content_v"><label>原因</label><label class="fail_c">{{order.auditFailureReason}}</label></view>
            </view>
            <view class="itemDetail" hidden="{{order.showDetail}}">
              <view class="item_no">
                <label>订单号</label>
                <label class="order_no">{{order.orderNo}}</label>
                <label class="order_copy" bindtap='copyOrderNo' data-no='{{order.orderNo}}'>复制</label>
              </view>
            </view>
          </view>
        </block>
      </block>
    </block>
    <block wx:else>
      <!--未查询到数据展示-->
      <view class="no_journal">
        <image mode="widthFix" src="{{noJournal}}"></image>
      </view>
    </block>
  </view>
</view>
<view class="black_bg" hidden="{{bgHidden}}" bindtap="closeBlackBgBindTap"></view>
<view hidden="{{bgHidden}}" class="popWrap">
  <view class="detailTop">
    <view bindtap="closeBgHiddenBindTap" data-type="0">全部</view>
    <view bindtap="closeBgHiddenBindTap" data-type="1">审核中</view>
    <view bindtap="closeBgHiddenBindTap" data-type="3">待打款</view>
    <view bindtap="closeBgHiddenBindTap" data-type="4">已提现</view>
    <view bindtap="closeBgHiddenBindTap" data-type="5" class="selectItem">筛选<image src="{{selected}}"
        class="selectImage" mode="widthFix">
      </image>
    </view>
  </view>
  <view class="timeSelect">按时间筛选</view>
  <view class="timePicker">
    <view class="timeBox">
      <picker mode="date" value="{{date1}}" bindchange="bindStartDateChange">
        <view class="picker">
          {{date1}}
        </view>
      </picker>
    </view>
    <view style="padding-top: 20rpx;">~</view>
    <view class="timeBox">
      <picker mode="date" value="{{date2}}" bindchange="bindEndDateChange">
        <view class="picker">
          {{date2}}
        </view>
      </picker>
    </view>
  </view>
  <view class="popBtn">
    <label bindtap="resetDateBindTap">重置</label>
    <label bindtap="selectWithdrawalOrderByWhereBindTap">确定</label>
  </view>
</view>