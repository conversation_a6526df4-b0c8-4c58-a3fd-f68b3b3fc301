<view class="b_wrap" style="min-height:{{winHeight}}px">
  <view class="top_wrap">
    <image class="icon_l" src="{{whiteLocation}}"></image>
    <label bindtap="openLocationBindTap">{{localCity}}</label>
  </view>
  <view class="x_title">
    欢迎使用{{authorList[0].nickName}}
  </view>
  <view class="sub_title">
    请从下方选择您的店铺所在区域
  </view>
  <view class="storeWrap">
    <block wx:key="unique" wx:for="{{showReturnStoreList}}" wx:for-item="store">
      <view class="oneStore" bindtap="goToLoginBindTap" data-id="{{store.id}}">
        <view class="imageWrap">
          <image src="{{store.storeLogo}}" mode="widthFix"></image>
        </view>
        <view class="address_detail">
          <view class="a_title">{{store.storeName}}</view>
          <view class="a_content">{{store.province+store.city+store.area+store.storeAddress}}</view>
        </view>
        <label class="address_distance">距离{{store.storeDistance}}km</label>
      </view>
    </block>
  </view>
</view>