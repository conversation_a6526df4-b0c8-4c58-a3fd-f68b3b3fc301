<view style="position:relative;padding-bottom:160rpx;">
  <view style="margin:20rpx 30rpx 0;"><image style="width:100%;border-radius:30rpx;" mode="widthFix" src="{{cardImg}}" /></view>
  <view style="margin-top:20rpx;background:#fff;padding:30rpx;">
      <view class="giftDetail">礼券号码：{{cardNo}}</view>
      <view class="giftDetail">礼券名称：{{cardName}}</view>
      <view class="giftDetail">礼券说明：{{remark}}</view>
  </view>
  <view style="margin-top:20rpx;" class="clearfix packWrap">
    <label style="float:left;">是否邮寄</label>
    <switch bindchange="deliverBindtap" checked="{{isDeliver}}" style="float:right;" color="#FF7E00"/>
  </view>
  <view style="margin-top:20rpx;" class="packWrap" hidden="{{!isDeliver}}">
      <view class="clearfix">
        <label style="width:20%;float:left;">联系人</label>
        <input style="width:80%;height:80rpx;line-height:80rpx;float:left;border-bottom:1px solid #ddd;" type='text' maxlength="20" value='{{userName}}' bindinput='saveUserNameBindInput' placeholder='请输入联系人'></input>
      </view>
      <view class="clearfix">
        <label style="width:20%;float:left;">联系电话</label>
        <input style="width:80%;height:80rpx;line-height:80rpx;float:left;border-bottom:1px solid #ddd;" type='text' value='{{telephone}}' bindinput='saveTelephoneBindInput' placeholder='请输入联系电话'></input>
      </view>
      <view class="clearfix">
        <label style="width:20%;float:left;">选择地区</label>
        <picker style="width:80%;float:left;border-bottom:1px solid #ddd;" mode="region" bindchange="bindRegionChange" value="{{region}}" custom-item="{{customItem}}">
          <view class="picker">
            <block wx:for="{{region}}" wx:for-item="region" wx:for-index="addressIndex" wx:key="">
              {{region}}
            </block>
          </view>
        </picker>
      </view>
      <view class="clearfix">
        <label style="width:20%;float:left;">详细地址</label>
        <textarea style="margin-bottom:20rpx;height:100rpx;line-height:50rpx;width:80%;float:left;border-bottom:1px solid #ddd;" type='text' bindinput='saveAddressBindInput' value='{{address}}'></textarea>
      </view>
  </view>
  <view hidden="{{!isDeliver}}" style="margin-top:20rpx;" class="clearfix packWrap">
      <label style="width:20%;float:left;">备注</label>
      <textarea style="width:80%;margin-bottom:20rpx;height:100rpx;line-height:50rpx;float:left;border-bottom:1px solid #ddd;" type='text' bindinput='saveRemarkBindInput' value='{{deliverremark}}'></textarea>
  </view>
  <view class="historyWrap" bindtap="exchangeBindtap" style="position:fixed;bottom:0;left:0;right:0;height:140rpx;font-size:28rpx;text-align:center;background:#f5f5f5;color:#FF7E00;">
    <view class="exchangeConfirm" style="margin-bottom:10rpx;">确认兑换</view>
    <view hidden="{{!isDeliver}}"style="text-align:center;font-size:24rpx;color:#666;">快递仅支持到付</view>
  </view>
</view>
