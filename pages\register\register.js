//index.js
//获取应用实例
const app = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        telephone: "", //手机号码
        loginPassWord: "", //登录密码
        againLoginPassword: "", //再次输入登录密码
        smsCode: "", //短信验证码
        second: 60, //倒计时秒数
        secondDesc: "获取短信验证码",
        close_btn: app.imageUrl + 'close_2.png',
        serverHidden: true,
        isSend: false,
        isDefault: true,
        recommendTelephone: "", //推荐人手机号
        userName: "",
        storeName: app.getExtStoreName(),
        shopperGift: app.imageUrl + 'shopperGift.png',
        cancelMark: app.imageUrl + 'cancelMark.png',
        vipGift: app.imageUrl + 'vipGift.png',
        vip_shopperGift: app.imageUrl + 'vipshopperGift.png',
        cardImageSrc: "",
        putCardHidden: true,
        giftBox: app.imageUrl + 'giftBox.png'
    },
    queryCardBagBindTap: function() {
        var isLogin = app.isLogin();
        if (!isLogin) {
            app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
        } else {
            app.navigateToPage('/pages/person_coupon/person_coupon');
        }
    },
    closeCardBgBindTap: function() {
        this.setData({
            putCardHidden: true
        });
        app.turnToPage("/pages/login/login?loginAccount=" + that.data.telephone);
    },
    defaultBindTap: function() {
        var that = this;
        that.setData({
            isDefault: !that.data.isDefault
        })
    },
    openServerBindTap: function() {
        this.setData({
            serverHidden: false,
            isDefault: this.data.isDefault
        })
    },
    hideServerBindTap: function() {
        this.setData({
            serverHidden: true,
            isDefault: this.data.isDefault
        })
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function(options) {
        var that = this;
        const scene = decodeURIComponent(options.scene)
        if (scene != null && scene != "" && scene != "undefined") {
            that.setData({
                recommendTelephone: scene
            });
        }
        that.setData({
            storeName: app.getExtStoreName()
        })
    },
    bindKeyInput: function(e) {
        this.setData({
            telephone: e.detail.value
        })
    },
    passwordInput: function(e) {
        this.setData({
            loginPassWord: e.detail.value
        })
    },
    againPasswordInput: function(e) {
        this.setData({
            againLoginPassword: e.detail.value
        })
    },
    recommendTelephoneBindInput: function(e) {
        this.setData({
            recommendTelephone: e.detail.value
        })
    },
    bindUserNameInput: function(e) {
        this.setData({
            userName: e.detail.value
        })
    },
    smsCodeInput: function(e) {
        this.setData({
            smsCode: e.detail.value
        })
    },
    /**
     * 点击获取短信验证码
     */
    smsBindTap: function() {
        var that = this;
        var telephone = that.data.telephone.replace(/\s+/g, '');
        var userName = that.data.userName.replace(/\s+/g, '');
        var loginPassWord = that.data.loginPassWord;
        var againLoginPassword = that.data.againLoginPassword;
        if (telephone.length == 0) {
            wx.showToast({
                title: '手机号不能为空',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (telephone.length < 11) {
            wx.showToast({
                title: '手机号有误',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
        if (!myreg.test(telephone)) {
            wx.showToast({
                title: '手机号有误',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (userName.length == 0) {
            wx.showToast({
                title: '姓名不能为空',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (loginPassWord.length == 0) {
            wx.showToast({
                title: '登录密码不能为空',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (loginPassWord.length < 6) {
            wx.showToast({
                title: '登录密码过于简单',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (againLoginPassword.length == 0) {
            wx.showToast({
                title: '再次输入登录密码',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (loginPassWord != againLoginPassword) {
            wx.showToast({
                title: '登录密码不一致',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (!that.data.isSend) {
            wx.request({
                url: app.projectName + '/applet/querySMSCode',
                data: {
                    "type": "1",
                    "telephone": telephone,
                    "storeId": app.getExtStoreId(),
                    "companyId": app.getExtCompanyId()
                },
                success: function(res) {
                    var flag = res.data.flag;
                    var message = res.data.message;
                    if (flag) {
                        that.setData({
                            isSend: true
                        })
                        that.countdown(that);
                    } else {
                        app.showModal({
                            title: '提示',
                            content: message == "" ? "系统异常，稍后在试" : message
                        });
                        return;
                    }
                }
            })
        }
    },
    /**
     * 倒计时开始
     */
    countdown: function(that) {
        var second = that.data.second;
        if (second == 0) {
            that.setData({
                secondDesc: "获取短信验证码",
                second: 60,
                isSend: false
            });
            return;
        }
        var time = setTimeout(function() {
            that.setData({
                second: second - 1,
                secondDesc: second + "秒后重新获取"
            });
            that.countdown(that);
        }, 1000)
    },
    checkRegisterParmar: function() {
        var that = this;
        var telephone = that.data.telephone.replace(/\s+/g, '');
        var userName = that.data.userName.replace(/\s+/g, '');
        var loginPassWord = that.data.loginPassWord;
        var againLoginPassword = that.data.againLoginPassword;
        var smsCode = that.data.smsCode.replace(/\s+/g, '');
        if (telephone.length == 0) {
            wx.showToast({
                title: '手机号不能为空',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (telephone.length < 11) {
            wx.showToast({
                title: '手机号有误',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
        if (!myreg.test(telephone)) {
            wx.showToast({
                title: '手机号有误',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (userName.length == 0) {
            wx.showToast({
                title: '姓名不能为空',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (loginPassWord.length == 0) {
            wx.showToast({
                title: '登录密码不能为空',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (againLoginPassword.length == 0) {
            wx.showToast({
                title: '再次输入登录密码',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (loginPassWord != againLoginPassword) {
            wx.showToast({
                title: '登录密码不一致',
                icon: 'success',
                duration: 1000,
                mask: true
            })
            return false;
        }
        if (smsCode.length == 0) {
            wx.showToast({
                title: '请输入验证码',
                duration: 1000,
                icon: 'success',
                mask: true
            })
            return false;
        }
        if (!that.data.isDefault) {
            app.showModal({
                title: '提示',
                content: "请阅读并同意用户注册协议"
            });
            return false;
        }
        return true;
    },
    registerBindTap: function() {
        var that = this;
        if (!that.checkRegisterParmar()) {
            return;
        }
        wx.showLoading({
            title: '正在提交请稍后',
            mask: true
        })
        wx.request({
            header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: app.projectName + '/userRegister/userRegisterNoHaveCode',
            data: {
                "telephone": that.data.telephone,
                "password": that.data.loginPassWord,
                "code": that.data.smsCode,
                "userName": that.data.userName,
                "storeId": app.getExtStoreId(),
                "companyId": app.getExtCompanyId()
            },
            success: function(res) {
                wx.hideLoading();
                var birthdayFlag = res.data.birthdayFlag;
                var resultCode = res.data.resultCode;
                if (resultCode == 0) { //注册成功
                    if (birthdayFlag) {
                        var storeCardList = res.data.storeCardList;
                        that.setData({
                            storeCardList: storeCardList,
                            putCardHidden: false
                        });
                    } else {
                        wx.showToast({
                            title: "注册成功",
                            icon: 'success',
                            duration: 2000,
                            mask: true,
                            success: function() {
                                setTimeout(function() {
                                    wx.removeStorageSync("recommendBean");
                                    app.turnToPage("/pages/login/login?loginAccount=" + that.data.telephone);
                                }, 2000);
                            }
                        })
                    }
                } else if (resultCode == 2) {
                    app.showModal({
                        title: '提示',
                        content: "手机号码已经被注册"
                    });
                    return;
                } else if (resultCode == 1) {
                    app.showModal({
                        title: '提示',
                        content: "验证码不正确，请重新获取"
                    });
                    return;
                } else {
                    app.showModal({
                        title: '提示',
                        content: "注册失败，请稍后重试"
                    });
                    return;
                }
            }
        })
    },
    makePhoneBindTap: function(e) {
        wx.makePhoneCall({
            phoneNumber: "025-********"
        })
    },
    goToLogin: function() {
        app.turnToPage("/pages/login/login");
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function(res) {
        var that = this;
        if (res.from === 'button') {
            // 来自页面内转发按钮
        }
        return {
            title: app.storeName,
            path: '/pages/index/index?recommendId=' + app.getUserId() + '&recommendAccount=' + app.getLoginAccount() + '&recommendName=' + app.getLoginName() + '&identity=' + app.getIdentity(),
            imageUrl: app.shareImageUrl,
            success: function(res) {
                // 转发成功
            },
            fail: function(res) {
                // 转发失败
            }
        }
    }
})