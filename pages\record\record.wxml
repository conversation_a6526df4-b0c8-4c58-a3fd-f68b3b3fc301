<view class="reward">
	<view class="reward_one">
		赠送记录
	</view>
	<block wx:for="{{items}}" wx:key="index">
		<view class="reward_two" style="background: linear-gradient(90deg, #FEFAFA 0%, #FEEEA8 100%);" wx:if="{{item.ziState==1}}"
		>
			<view class="reward_three">
				<!-- <view class="reward_dashed">
					<image class="reward_img" src="{{coupon}}"  mode="widthFix"></image>
				</view> -->
			</view>
			<view style="width:50%;">
				<view class="reward_five">
					“{{item.ziWord}}”
				</view>
				<view class="reward_six">
					{{item.updateTime}}
				</view>
			</view>
			<view class="reward_seven" style="width:25%;">
				未领取
			</view>
			<view class="reward_nine">
				<view
					class="reward_eight" style="background: #FE5406;" bindtap="retrieve" data-id="{{item.id}}">
					取回
				</view>
			</view>
		</view>
		<view class="reward_two"
			style="background: linear-gradient(90deg, #FEFAFA 0%, #FEE6E6 100%);" 	wx:if="{{item.ziState==2}}">
			<view class="reward_three">
				<!-- <view class="reward_dashed">
					<image class="reward_img" src="{{integral}}"  mode="widthFix"></image>
				</view> -->
			</view>
			<view style="width:50%;">
				<view class="reward_five">
					“{{item.ziWord}}”
				</view>
				<view class="reward_six">
					{{item.updateTime}}
				</view>
			</view>
			<view class="reward_seven" style="width:25%;">
				已领取
			</view>
			<view class="reward_nine">
				<view
					class="reward_eight" style="background: #CECECE;">
					取回
				</view>
			</view>
		</view>
	</block>

	<load-more id="loadMoreView" bindloadMoreListener='loadMoreListener' bindclickLoadMore='clickLoadMore'></load-more>
</view>