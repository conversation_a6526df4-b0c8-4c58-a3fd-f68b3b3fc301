page {
  background: #f5f5f5;
}
.activity{
  width:430rpx;
  padding:20rpx;
  box-sizing: border-box;
}
.activityName{
  color:#525252;
  font-size:28rpx;
  margin:10rpx 0;
  width: 380rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.activityDescription{
  color:#A2A2A2;
  font-size:24rpx;
  margin:10rpx 0;
  line-height: 30rpx;
  max-height: 60rpx;
  width: 380rpx;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
}
.activityCondition{
  color:#525252;
  font-size:24rpx;
  margin:10rpx 0;
  width: 380rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.activityInfo{
  width: 380rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  color:#A2A2A2;
  font-size: 22rpx;
  line-height: 30rpx;
  max-height: 60rpx;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
}
.changeGoodsView {
  width: 100%;
  height: 90px;
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
  box-sizing: border-box;
  background-color: #fff;
}

.changeGoodsImage {
  width: 70px;
  height: 70px;
  float: left;
}

.changeGoodschildView {
  padding-left: 80px;
  height: 100%;
}

.changeGoodsName {
  width: 100%;
  font-size: 14px;
  color: #444;
  display: block;
  line-height: 22px;
  height: 40rpx;
  overflow: hidden;

}

.changeGoodsNumber {
  width: 100%;
  font-size: 12px;
  color: #999;
  display: block;
  line-height: 20px;
}

.changeGoodsPrice {
  color: #fa5468;
  font-size: 14px;
  line-height: 30px;
}

.changeGoodsPrice1 {
  text-decoration: line-through;
  font-size: 12px;
  color: #999;
  margin-left: 5px;
  line-height: 30px;
}

.goodsreduce {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #FF7E00;
  text-align: center;
  background: #fff;
  color: #FF7E00;
  float: left;
  line-height: 20px;
  margin-top: 4px;
}

.goodsNumber {
  width: 30px;
  text-align: center;
  float: left;
  line-height: 30px;
  display: block;
}

.goodsplus {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid grey;
  text-align: center;
  background: grey;
  color: #fff;
  float: left;
  line-height: 20px;
  margin-top: 4px;
}

.changeGoodsBtn {
  width: 60px;
  line-height: 28px;
  background: #FF7E00;
  color: #fff;
  font-size: 13px;
}
.submitBtn{
  width: 665rpx;
  height:88rpx;
  border-radius: 44rpx;
  margin:0 42rpx;
  line-height:88rpx;
  background-color: #FF7E00;
  color:#fff;
  text-align: center;
  position: fixed;
  bottom: 10rpx;
  left:0;
}
