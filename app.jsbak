const updateManager = wx.getUpdateManager();
App({
  /**
   * 当小程序初始化完成时，会触发 onLaunch（全局只触发一次）
   */
  onLaunch: function () {
    /*wx.switchTab({
      url: "/pages/index/index"
    });*/
    /*var that = this;
    that.initPageLoading();*/
  },
  onHide:function(){
    

  },
  /**
   * request请求路径
   */
  activityName: "https://www.cn2b2c.com/activity/api/",//活动路径如签到，集字
  findName: "https://www.cn2b2c.com/find/api/",//发现
  //projectName: "http://192.168.10.180:8080/common.external.service",
  projectName: "https://www.cn2b2c.com/custom.external.service",
  //gameProjectName: "https://www.cn2b2c.com/dbbgame",
  //projectName: "http://login.tuhaoli.com/custom.external.service",
  gameProjectName: "https://www.cn2b2c.com/activity",
  giftCardProjectName: "https://www.cn2b2c.com/card",
  imageUrl: "https://www.cn2b2c.com/gsf/img/zyht/",
  appletVersion: "R.5.4.12",
  examineVersion: 2,//审核版本-每次提交审核都需要改动+1
  onShow: function () {
    var that = this;
    console.log("我在app.js中")
    that.initPageLoading();
  },
  initPageLoading: function () {
    var that = this;
    
    that.init_getExtMessage().then(res => {
      that.init_userOpenId();
    })
  },
  /**
   * 校验铭感词汇 返回true为正常
   * @param {*} content 
   */
  msg_sec_check: function (content) {
    var that = this;
    return new Promise(function (resolve, reject) {
      that.init_getExtMessage().then(result => {
       wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: that.projectName + '/applet/msg_sec_check',
          data: {
            "content": content,
            "companyId": result.companyId,
            "odbtoken":   wx.getStorageSync('odbtoken'),
            "logintoken": wx.getStorageSync('loginToken')
          },
          success: function (res) {
            var flag = res.data.flag;
            resolve(flag);
          }
        })
      })
    });
  },
  /*获取LoginToken*/
  wechatAuthionTelephone1:function(odbtoken){
    console.log("我是app.js里面获取手机号的方法")
    var that = this;
    var logintoken = wx.getStorageSync('loginToken');
    if(logintoken != null && logintoken.length>0){
      return;
    }
    /*采用新接口获取用户的手机号*/
    var odbtoken = odbtoken;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: that.projectName + '/newAppletLogin/loginByTel',
        data: {
          "companyId": that.getExtCompanyId(),
          "odbtoken":odbtoken
        },
        success: function (res) {
          var returnCode = res.data.code;
          var loginToken = res.data.loginToken;
          var odbtoken = res.data.odbtoken;
          var clientOpenId = res.data.clientOpenId; 
          if (returnCode == 1) {
            var isLoginSession = true;
            wx.removeStorageSync("isLoginSession");
            wx.setStorageSync(
            'loginToken',loginToken);
            wx.setStorageSync(
              'clientOpenId',clientOpenId);
            that.setStorage({
              key: 'isLoginSession',
              data: isLoginSession
            });
            that.setStorage({
              key: 'clientOpenId',
              data: clientOpenId
            })
            console.log("此时login变成了true")
          } else {
            that.showModal({
              title: '提示',
              content: "获取失败，请您关闭小程序重试"
            });
            wx.removeStorageSync("odbtoken");
            wx.removeStorageSync("loginToken");
            wx.removeStorageSync("userSession");
            wx.removeStorageSync("isLoginSession");
            wx.removeStorageSync("");
            app.init_userOpenId();
          }
        },
        fail: function () {
          app.showModal({
            title: '提示',
            content: "获取失败，请您关闭小程序重试"
          });
          wx.removeStorageSync("odbtoken");
          wx.removeStorageSync("loginToken");
          wx.removeStorageSync("userSession");
          wx.removeStorageSync("isLoginSession");
          wx.removeStorageSync("");
          app.init_userOpenId();
        }
      })
  },
  /**
   * 新初始化用户信息
   **/
  init_userOpenId: function () {
    var that = this;
    var odbtoken = wx.getStorageSync("odbtoken");
    if(odbtoken != null && odbtoken.length>0){
      return;
    }
    return new Promise(function (resolve, reject) {
      wx.login({
        success: function (res) {
          if (res.code) {
            that.init_getExtMessage().then(result => {
              wx.request({
                header: {
                  'content-type': 'application/x-www-form-urlencoded' // 默认值
                },
                method: "POST",
                url: that.projectName +'/newAppletLogin/clientValid',

                data: {
                  "jscode": res.code,
                  "companyId": result.companyId,
                  "storeId": result.storeId,
                  "withEncryption":1
                },
                success: function (res) {
                  var odbtoken = res.data.odbtoken;
                  var clientGrantTel = res.data.clientGrantTel;
                  if(clientGrantTel == 1){/*代表已经获取了用户的手机号*/
                      that.wechatAuthionTelephone1(odbtoken);
                     
                    /*wx.setStorageSync({
                      key: 'isLoginSession',
                      data: "1"
                    });
                    console.log("4440");
                    console.log(wx.getStorageSync('isLoginSession')+"///");*/
                    wx.removeStorageSync("isLoginSession");
                    that.setStorage({
                      key: 'isLoginSession',
                      data: true
                    });

                  }
                  that.setStorage({
                    key: 'odbtoken',
                    data: odbtoken
                  });
                  console.log("我是已经执行的odbtoken的值")
                  var storeName = res.data.storeName;
                  var storeImage = res.data.storeImage;
                  var storePhone = res.data.storePhone;
                  var storeInfo = {
                    "storeName": storeName,
                    "storeImage": storeImage,
                    "storePhone": storePhone
                  };
                  wx.setNavigationBarTitle({
                    title: storeName
                  });
                  wx.removeStorageSync("storeInfoStorageKey");
                  wx.setStorageSync({
                    key: 'storeInfoStorageKey',
                    data: storeInfo
                  });
                  resolve(odbtoken);
                }
              })
            })
          }
        }
      })
    });

  },
  /**
 * 初始化用户信息
 */
  /*init_userOpenId1: function () {
    var that = this;
    return new Promise(function (resolve, reject) {
      wx.login({
        success: function (res) {
          if (res.code) {
            that.init_getExtMessage().then(result => {
              wx.request({
                header: {
                  'content-type': 'application/x-www-form-urlencoded' // 默认值
                },
                method: "POST",
                //url: that.projectName + '/applet/jscode2session',
                url: that.projectName +'/newAppletLogin/clientValid',

                data: {
                  "jscode": res.code,
                  "companyId": result.companyId,
                  "storeId": result.storeId,
                  "withEncryption":1
                },
                success: function (res) {
                  var odbtoken = res.data.odbtoken;
                  that.setStorage({
                    key: 'odbtoken',
                    data: odbtoken
                  });
                  /*var openid = res.data.openid;
                  that.setStorage({
                    key: 'openId',
                    data: openid
                  });*/
                  
                 /* var session_key = decodeURIComponent(res.data.session_key);
                  that.setStorage({
                    key: 'session_Key',
                    data: session_key
                  })
                  var storeName = res.data.storeName;
                  var storeImage = res.data.storeImage;
                  var storePhone = res.data.storePhone;
                  var storeInfo = {
                    "storeName": storeName,
                    "storeImage": storeImage,
                    "storePhone": storePhone
                  };
                  wx.setNavigationBarTitle({
                    title: storeName
                  });
                  wx.removeStorageSync("storeInfoStorageKey");
                  that.setStorage({
                    key: 'storeInfoStorageKey',
                    data: storeInfo
                  });
                  resolve(odbtoken);
                }
              })
            })
          }
        }
      })
    });

  },*/
  /**
   * 初始化读取配置信息
   */
  init_getExtMessage: function () {
    console.log("2222");
    var that = this;
    that.init_versionMessage();
    return new Promise(function (resolve, reject) {
      if (wx.getExtConfig) {
        wx.getExtConfig({
          success(res) {
            var extObj = {
              "storeId": res.extConfig.storeId,
              "companyId": res.extConfig.companyId,
              "appId": res.extConfig.appId,
              "enterpriseCode": res.extConfig.enterpriseCode
            };
            wx.removeStorageSync("extStorageKey");
            that.setStorage({
              key: 'extStorageKey',
              data: extObj
            });
            resolve(extObj);
            /*that.queryExamineState(res.extConfig.companyId).then(result => {
              var extObj = {
                "storeId": res.extConfig.storeId,
                "companyId": res.extConfig.companyId,
                "appId": res.extConfig.appId,
                "enterpriseCode": res.extConfig.enterpriseCode
              };
              wx.removeStorageSync("extStorageKey");
              that.setStorage({
                key: 'extStorageKey',
                data: extObj
              });
              resolve(extObj);
                /*if (result.data.isUpdate) {
                var extObj = {
                  "storeId": result.data.storeId,
                  "companyId": result.data.companyId,
                  "appId": "",
                  "enterpriseCode": result.data.enterpriseCode
                };
                wx.removeStorageSync("extStorageKey");
                that.setStorage({
                  key: 'extStorageKey',
                  data: extObj
                });
                resolve(extObj);
              } else {
                var extObj = {
                  "storeId": res.extConfig.storeId,
                  "companyId": res.extConfig.companyId,
                  "appId": res.extConfig.appId,
                  "enterpriseCode": res.extConfig.enterpriseCode
                };
                wx.removeStorageSync("extStorageKey");
                that.setStorage({
                  key: 'extStorageKey',
                  data: extObj
                });
                resolve(extObj);
              }
            })*/
          }
        })
      }
    })
  },
  queryExamineState: function (companyId) {
    var that = this;
    return new Promise(function (resolve, reject) {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: that.projectName + '/systemExamine/queryExamineState',
        data: {
          "companyId": companyId,
          "version": 1
        },
        success: function (res) {
          resolve(res);
          console.log(res);
          console.log("222");
        }
      })
    })
  },
  /**
   * 初始化最新版本信息
   */
  init_versionMessage: function () {
    var that = this;
    wx.getSystemInfo({
      success: function (res) {
        var version = res.SDKVersion;
        version = version.replace(/\./g, "")
        if (parseInt(version) < 250) { // 小于1.7.0的版本
          that.showModal({
            content: '您的微信版本过低，如不更新，将会影响使用~'
          });
        }
      }
    })
    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
    })
    updateManager.onUpdateReady(function () {
      wx.removeStorageSync("userSession");
      wx.removeStorageSync("isLoginSession");
      wx.showToast({
        title: "更新新版本，重启中!",
        icon: 'none',
        duration: 1000,
        mask: true,
        success: function () {
          setTimeout(function () {
            updateManager.applyUpdate()
          }, 1000);
        }
      })
    })
  },
  /**
   * 获取商家配置信息
   */
  getSupplierSetting: function (companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: that.projectName + '/newSupplierSetting/querySupplierState',
      data: {
        "companyId": companyId
      },
      success: function (res) {
        var code = res.data.code;
        if (code == -1 || code == 2 || code == 5) {
          that.reLaunchToPage("/pages/expireAccout/expireAccout");
        } else {
          wx.request({
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            method: "POST",
            url: that.projectName + '/wholesaleStore/getStoreInfoSetting',
            data: {
              "companyId": companyId
            },
            success: function (res) {
              var returnResult = res.data.returnResult;
              if (returnResult != null && returnResult != undefined) {
                var browseRole = returnResult.browseBean.browseRole;
                if (browseRole == 1) {
                  var isLogin = that.isLogin();
                  if (!isLogin) {
                    that.turnToPage("/pages/login/login");
                  }
                }
              }
            }
          })
        }
      }
    })
  },
  getSessionKey: function () {
    var session_key = wx.getStorageSync('session_key');
    return session_key;
  },
  getClientOpenId:function(){
    var openId = wx.getStorageSync('clientOpenId');
    return openId;
  },
  getOpenId: function () {
    var openId = wx.getStorageSync('openId');
    return openId;
  },
  getAvatarUrl: function () {
    return wx.getStorageSync('avatarUrl');
  },
  getExtStoreId: function () {
    var extStorageKey = wx.getStorageSync('extStorageKey');
    return extStorageKey.storeId;
  },
  getExtCompanyId: function () {
    var extStorageKey = wx.getStorageSync('extStorageKey');
    return extStorageKey.companyId;
  },
  getExtAppId: function () {
    var extStorageKey = wx.getStorageSync('extStorageKey');
    return extStorageKey.AppId;
  },
  getExtStoreName: function () {
    var storeInfoStorageKey = wx.getStorageSync('storeInfoStorageKey');
    return storeInfoStorageKey.storeName;
  },
  getExtStoreImage: function () {
    var storeInfoStorageKey = wx.getStorageSync('storeInfoStorageKey');
    return storeInfoStorageKey.storeImage;
  },
  getExtStorePhone: function () {
    var storeInfoStorageKey = wx.getStorageSync('storeInfoStorageKey');
    return storeInfoStorageKey.storePhone;
  },
  switchTab: function (url) {
    wx.switchTab({
      url: url
    })
  },
  /**
   * 关闭当前页面，跳转到应用内的某个页面。但是不允许跳转到 tabbar 页面
   */
  turnToPage: function (url) {
    wx.redirectTo({
      url: url
    });
  },
  /**
   * 保留当前页面，跳转到应用内的某个页面
   */
  navigateToPage: function (url) {
    wx.navigateTo({
      url: url
    });
  },
  /**
   * 关闭当前页面，跳转到应用内的某个页面
   */
  redirectToPage: function (url) {
    wx.redirectTo({
      url: url
    })
  },
  /**
   * 关闭所有页面，打开到应用内的某个页面
   */
  reLaunchToPage: function (url) {
    wx.reLaunch({
      url: url
    })
  },
  /**
   * 提示框
   */
  showModal: function (param) {
    wx.showModal({
      title: param.title || '提示',
      content: param.content,
      showCancel: param.showCancel || false,
      cancelText: param.cancelText || '取消',
      cancelColor: param.cancelColor || '#000000',
      confirmText: param.confirmText || '确定',
      confirmColor: param.confirmColor || '#3CC51F',
      success: function (res) {
        if (res.confirm) {
          typeof param.confirm == 'function' && param.confirm(res);
        } else {
          typeof param.cancel == 'function' && param.cancel(res);
        }
      },
      fail: function (res) {
        typeof param.fail == 'function' && param.fail(res);
      },
      complete: function (res) {
        typeof param.complete == 'function' && param.complete(res);
      }
    })
  },
  globalData: {
    isCollect:0,
    userInfo: null,
    isLogin: false,
    avatarUrl: "",
    bottomBean: {
      color: "#666",
      selectedColor: "#ff6600",
      backgroundStyle: "2",
      backContent: "#ffffff",
      wechatAppletIndexBottomContentEntity: [
        {
          iconPath: "/image/home.png",
          selectIconPath: "/image/home1.png",
          textContent: "首页",
          pagePath: 4,
          homePage: 0
        },
        {
          iconPath: "/image/classify.png",
          selectIconPath: "/image/classify1.png",
          textContent: "分类",
          pagePath: 5,
          homePage: 1
        },
        {
          iconPath: "/image/shopcart.png",
          selectIconPath: "/image/shopcart1.png",
          textContent: "购物车",
          pagePath: 6,
          homePage: 1
        },
        {
          textContent: "个人",
          selectIconPath: "/image/personal1.png",
          iconPath: "/image/personal.png",
          pagePath: 7,
          homePage: 1
        }
      ]
    }
  },
  /**
   * 判断当前是否登录
   */
  isLogin: function () {
    var isLoginSession = wx.getStorageSync('isLoginSession');
    console.log(isLoginSession);
    if (isLoginSession == "") {
      return false;
    }
    return true;
  },
  /**
   * 获取登录用户ID
   */
  getUserId: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.userId;
  },
  /**
   * 获取当前人身份
   */
  getIdentity: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.identity;
  },
  /**
   * 获取登录人登录的ID
   */
  getLoginId: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.loginId;
  },
  /**
   * 获取登录的角色
   */
  getUserRole: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.userRole;
  },
  /**
   * 获取登录账号
   */
  getLoginAccount: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.telephone;
  },
  /**
   * 获取登录名称
   */
  getLoginName: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.userName;
  },
  /**
   * 获取用户登录手机号码
   */
  getTelephone: function () {
    var userSession = wx.getStorageSync('isLoginSession');
    if(userSession){
      return "hasPhone";
    }
   
  },
  getHeadImage: function () {
    var userSession = wx.getStorageSync('userSession');
    return userSession.headImage;
  },
  /**
   * 获取当前登录人的odbtoken
   **/
   getodbtoken: function () {
    var odbtoken = wx.getStorageSync('odbtoken');
    return odbtoken;
  },
   /**
   * 获取当前登录人的loginToken
   **/
  getloginToken: function () {
    var loginToken = wx.getStorageSync('loginToken');
    return loginToken;
  },
  /**
   * type 1:导航栏跳转 2:普通页面跳转
   */
  returnLogin: function (backUrl, type) {
    console.log("eeeerrrr");
    this.turnToPage("/pages/login/login?backUrl=" + backUrl + "&type=" + type);
  },
  /**
   * 获取当前携带参数的地址
   */
  getCurrentPageUrlWithArgs: function () {
    var pages = getCurrentPages() //获取加载的页面
    var currentPage = pages[pages.length - 1] //获取当前页面的对象
    var url = currentPage.route //当前页面url
    var options = currentPage.options //如果要获取url中所带的参数可以查看options

    //拼接url的参数
    var urlWithArgs = url + '?'
    for (var key in options) {
      var value = options[key]
      urlWithArgs += key + '=' + value + '&'
    }
    urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)

    return "/" + urlWithArgs
  },
  /**
   * 获取不带参数的地址
   */
  getCurrentPageUrl: function () {
    var pages = getCurrentPages() //获取加载的页面
    var currentPage = pages[pages.length - 1] //获取当前页面的对象
    var url = currentPage.route //当前页面url
    return "/" + url;
  },
  /**
   * 存储数据缓存
   */
  setStorage: function (options) {
    options = options || {};
    wx.setStorage({
      key: options.key || '',
      data: options.data || '',
      success: function () {
        typeof options.success === 'function' && options.success();
      },
      fail: function () {
        typeof options.fail === 'function' && options.fail();
      },
      complete: function () {
        typeof options.complete === 'function' && options.complete();
      }
    })
  },
  /**
   * 获取缓存数据
   */
  getStorage: function (options) {
    options = options || {};
    wx.getStorage({
      key: options.key || '',
      success: function (res) {
        typeof options.success === 'function' && options.success(res);
      },
      fail: function () {
        typeof options.fail === 'function' && options.fail();
      },
      complete: function () {
        typeof options.complete === 'function' && options.complete();
      }
    })
  },
  /**
   * 获取购物车商品数量
   */
  countRetailCartTotal: function () {
    var that = this;
    var isLogin = that.isLogin();
    console.log(isLogin);
    if (!isLogin) {
    } else {
      that.init_getExtMessage().then(res => {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: that.projectName + '/applet/shopCart/countRetailCartTotal',
          data: {
            "userId": that.getUserId(),
            "loginId": that.getLoginId(),
            "userRole": that.getUserRole(),
            "storeId": res.storeId,
            "companyId": res.companyId,
            "odbtoken":that.getodbtoken(),
            "loginToken":that.getloginToken()
          },
          success: function (res) {
            if (res.data.shopCartNum > 0) {
              wx.setStorageSync("shopCartNum", res.data.shopCartNum.toString())
            } else {
              wx.setStorageSync("shopCartNum", "0")
            }
          }
        })
      });
    }
  },
  /**
   * 页面返回
   */
  turnBack: function (options) {
    options = options || {};
    wx.navigateBack({
      delta: options.delta || 1
    });
  },
  recommendData:{
    recommendUserId:null,
  },
  /*全部参数*/
  classifyData: {
    classifyId: null,
  },
  /**
   * 分销分享给好友
   */
  commonShareApplet: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})