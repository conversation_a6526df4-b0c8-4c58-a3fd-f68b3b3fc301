const ui = require('../utils/ui')
const app = getApp()
const request = function (method) {
  return (obj) => {

    if (!obj || !obj.url) return
    if (obj.showLoading != false) {
      ui.showLoading("加载中...")
    }
    var data = {}
    if (obj.data) data = obj.data
    var header = {}
    if (obj.header) {
      header = {
        "content-type": obj.header
      }
    } else {
      header = {
        "content-type": "application/x-www-form-urlencoded;charset=utf-8"
      }
    }

    if (!obj.url.startsWith('http')) {
      //"http://192.168.10.157:8080/find/api/"
      //"http://192.168.10.157:8080/activity/api/"
      //app.activityName
      switch (obj.urlName) {
        case "find":
          obj.url = app.findName + obj.url
          break;
        case "activity":
          obj.url = app.activityName + obj.url
          break;
      }

    }
    try {
      let res = wx.getStorageSync('res');
      var value = res.header['Set-Cookie'] //取出Cookie
      if (value) {
        header['cookie'] = value
      }
    } catch (e) {
      // Do something when catch error
    }
    wx.request({
      url: encodeURI(obj.url),
      method: method,
      data: data,
      header: header,
      success: (res) => {
        if (!res.data || res.statusCode >= 300 || res.statusCode < 200) {
          ui.showToast("接口请求失败, 请稍后重试!")
          if (obj.fail) {
            obj.fail()
          }
          return
        }
        if (res.data.errorcode == 1000) {
          if (obj.success) {
            if (obj.obtainResponse) {
              obj.success(res)
            } else {
              if (res.data.result != "") {
                obj.success(res.data.result)
              } else {
                obj.success(res.data)
              }
            }
            setTimeout(function () {
              ui.hideLoading()
            }, 300)
          }
        } else if (res.data.errorcode == 20007 || res.data.errorcode == 20014) {
          //当前页面栈
          var pages = getCurrentPages();
          if (pages.length > 1) {
            wx.navigateBack({
              delta: 1
            })
          }
          ui.showToast(res.data.errormsg, 2000);
        } else {
          obj.fail()
        }
      },
      fail: () => {
        ui.showToast("接口请求失败, 请稍后重试!")
        if (obj.fail) {
          obj.fail()
        }
      }
    })
  }
}

module.exports = {
  get: request('GET'),
  post: request('POST'),
  delete: request('DELETE'),
  put: request('PUT')
}