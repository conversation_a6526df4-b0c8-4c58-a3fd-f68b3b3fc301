<view style='width:100%;'>
	<!--单个订单-->
	<block wx:key="unique" wx:for="{{orderList}}" wx:for-item="order">

		<view class='single_order'>
			<label class='order_top'>
				<text>订单编号: {{order.orderNo}}</text>
				<text class='goods_static'>{{orderStatusArray[order.orderRejectedStatus]}}</text>
			</label>
			<block wx:key="unique" wx:for-index="goodsIndex" wx:for="{{order.orderDetail}}" wx:for-item="goods">
				<view class='goods_detail' data-orderId='{{order.orderId}}'>
					<image class='goods_pic' src='{{goods.commodityMainPic==""?goods_pic:goods.commodityMainPic}}'></image>
					<view class='right_detail'>
						<label class='right_top'>
							<text class='goods_name'>{{goods.commodityName}}</text>
							<text class='package_number'>￥{{goods.commoditySaleOtPrice}}</text>
						</label>
						<label class='right_bottom'>
							<text class='goods_tips' style="color:red;">{{goodsPurchaseArray[goods.commodityPurchaseStatus]}}</text>
							<text class='goods_number' style='float:right;font-size:13px;'>X{{goods.commodityOtNum}}{{goods.commodityOtUnit}}</text>
						</label>
					</view>
				</view>
			</block>
			<view style='width:100%;height:60px;'>
				<text style='float:right;color:#515151;font-size:13px;line-height:30px;'>下单时间:{{order.orderGenerateDate}}</text>
				<text style='float:right;color:#2c2c2c;font-size:14px;line-height:30px;'>共{{order.orderDetail.length}}件商品 合计{{order.orderTotalMoney}}元 (含运费 ￥{{order.orderDistributionPay}})</text>
			</view>
			<view class='button_box'>
				<button hidden="{{order.showReturnBtn}}" bindtap='returnGoodsBindTap' data-orderId='{{order.orderId}}'>退货/售后</button>
			</view>
		</view>
	</block>
	<!--单个订单-->
</view>