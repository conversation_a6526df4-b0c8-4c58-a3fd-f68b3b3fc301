var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    card_bg: app.imageUrl + "card_bg.png",
    payType: "",
    cart_id_arr: "",
    cardList: [],
    goodsId: "",
    storeId: "",
    buyNum: "",
    skuId: "",
    groupBuyUserId: "",
    joinPromotion: "",
    isFromBack: false,
    boundTelephoneHidden: true,
    exchangeCommodityId: "",
    smsCode: "", //短信验证码
    second: 60, //倒计时秒数
    secondDesc: "获取短信验证码",
    vipCardTelephone: "",
    getSmsCodeState: 1,
    isSend: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var exchangeCommodityId = options.exchangeCommodityId;
    that.setData({
      exchangeCommodityId: exchangeCommodityId,
      vipCardTelephone: app.getTelephone()
    });
    if (app.getTelephone().length > 0) {
      this.getMyVipCardInfo(exchangeCommodityId, app.getTelephone());
    }
  },
  wechatAuthionTelephone: function (e) {
    var that = this;
    var errMsg = e.detail.errMsg;
    var iv = e.detail.iv;
    var encryptedData = e.detail.encryptedData;
    if (errMsg == "getPhoneNumber:ok") {
      wx.checkSession({
        success() {
          //session_key 未过期，并且在本生命周期一直有效
          var session_Key = wx.getStorageSync("session_Key");
          that.getUserTelephone(iv, encryptedData, session_Key);
        },
        fail() {
          // session_key 已经失效，需要重新执行登录流程
          wx.login({
            success: function (res) {
              if (res.code) {
                wx.request({
                  header: {
                    'content-type': 'application/x-www-form-urlencoded'
                  },
                  method: "POST",
                  url: app.projectName + '/newAppletLogin/getUserOpenId',
                  data: {
                    "companyId": app.getExtCompanyId(),
                    "code": res.code
                  },
                  success: function (res) {
                    var openid = res.data.openid;
                    var session_Key = res.data.session_Key;
                    that.getUserTelephone(iv, encryptedData, session_Key);
                    wx.removeStorageSync("openId");
                    app.setStorage({
                      key: 'openId',
                      data: openid
                    });
                    wx.removeStorageSync("session_Key");
                    app.setStorage({
                      key: 'session_Key',
                      data: session_Key
                    });
                  }
                })
              }
            }
          })
        }
      })
    }
  },
  /**
   * 获取用户手机号码
   */
  getUserTelephone: function (iv, encryptedData, sessionKey) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newAppletLogin/getUserTelephone',
      data: {
        "sessionKey": sessionKey,
        "Iv": iv,
        "encryptedData": encryptedData,
        "companyId": app.getExtCompanyId(),
        "openId": app.getOpenId(),
        "userId": app.getUserId()
      },
      success: function (res) {
        var telephone = res.data.telephone;
        if (telephone.length > 0) {
          var userSession = wx.getStorageSync('userSession');
          userSession.telephone = telephone;
          userSession.loginAccount = telephone;
          app.setStorage({
            key: 'userSession',
            data: userSession
          });
          that.setData({
            telephone: telephone
          })
          that.getMyVipCardInfo(exchangeCommodityId, telephone);
        } else {
          app.showModal({
            title: '提示',
            content: "请您再试一次"
          });
        }
      },
      fail: function () {
        app.showModal({
          title: '提示',
          content: "请再试一次"
        });
      }
    })
  },
  /**
   * 点击获取短信验证码
   */
  smsBindTap: function () {
    var that = this;
    var telephone = that.data.vipCardTelephone.replace(/\s+/g, '');
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (telephone.length < 11) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (!that.data.isSend) {
      return;
    }
    wx.showToast({
      title: '获取成功',
      icon: 'success',
      duration: 1000,
      mask: true
    })
    wx.request({
      url: app.projectName + '/applet/querySMSCode',
      data: {
        "type": "5",
        "telephone": telephone,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        var message = res.data.message;
        if (flag) {
          that.setData({
            getSmsCodeState: 2
          })
          that.countdown(that);
        } else {
          app.showModal({
            title: '提示',
            content: message == "" ? "系统异常，稍后在试" : message
          });
          return;
        }
      }
    })
  },
  /**
   * 倒计时开始
   */
  countdown: function (that) {
    var second = that.data.second;
    if (second == 0) {
      that.setData({
        secondDesc: "获取短信验证码",
        second: 60,
        isSend: true
      });
      return;
    }
    var time = setTimeout(function () {
      that.setData({
        isSend: false,
        second: second - 1,
        secondDesc: second + "秒后重新获取"
      });
      that.countdown(that);
    }, 1000)
  },
  /**
   * 绑定手机号码
   */
  nowBoundTelephoneBindTap: function () {
    var that = this;
    var telephone = that.data.vipCardTelephone.replace(/\s+/g, '');
    if (telephone.length == 0) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    if (telephone.length < 11) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    if (!myreg.test(telephone)) {
      wx.showToast({
        title: '手机号有误',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var smsCode = that.data.smsCode.replace(/\s+/g, '');
    if (smsCode.length == 0) {
      wx.showToast({
        title: '请输入验证码',
        duration: 1000,
        icon: 'none',
        mask: true
      })
      return false;
    }
    if (that.data.getSmsCodeState == 1) {
      wx.showToast({
        title: '请先获取短信验证码',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "openId": app.getOpenId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId(),
        "telephone": telephone,
        "smsCode": smsCode
      },
      url: app.projectName + '/newAppletLogin/nowBoundTelephone',
      success: function (res) {
        var returnFlag = res.data.returnFlag;
        if (returnFlag) {
          wx.showToast({
            title: "绑定成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
              setTimeout(function () {
                var userSession = wx.getStorageSync('userSession');
                userSession.telephone = telephone;
                app.setStorage({
                  key: 'userSession',
                  data: userSession
                });
                that.setData({
                  boundTelephoneHidden: true
                });
                that.getMyVipCardInfo();
              }, 2000);
            }
          })
        } else {
          wx.showToast({
            title: res.data.message,
            duration: 1000,
            icon: 'none',
            mask: true
          })
        }
      }
    })
  },
  /**
   * 查询我的会员卡信息
   */
  getMyVipCardInfo: function (exchangeCommodityId, phone) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": phone
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          if (cardList.length == 1) {
            var cardBean = cardList[0];
            app.redirectToPage('/pages/scoreGoodsPay/scoreGoodsPay?exchangeCommodityId=' + exchangeCommodityId + '&vipCardNo=' + cardBean.cardId);
          } else if (cardList.length > 1) {
            that.setData({
              companyName: app.getExtStoreName(),
              cardList: cardList
            });
          }
        } else {
          wx.showToast({
            title: "暂无可用会员卡",
            icon: 'none',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1000);
            }
          })
        }
      }
    })
  },
  /**
   * 选择会员卡进行结算
   */
  chooseCardBindTap: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var cardBean = that.data.cardList[index];
    app.redirectToPage('/pages/scoreGoodsPay/scoreGoodsPay?exchangeCommodityId=' + that.data.exchangeCommodityId + '&vipCardNo=' + cardBean.cardId);
  },
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.getMyVipCardInfo(that.data.exchangeCommodityId, that.data.telephone);
    } else {
      that.setData({
        isFromBack: true
      });
    }
  },
  /**
   * 暂不绑定手机号码
   */
  noBoundTelephoneBindTap: function () {
    this.setData({
      boundTelephoneHidden: true
    })
    app.turnBack();
  },
  bindVipCardTelephoneBindInput: function (e) {
    this.setData({
      vipCardTelephone: e.detail.value
    })
  },
  smsCodeBindInput: function (e) {
    this.setData({
      smsCode: e.detail.value
    })
  }
})