<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<!--无会员卡-->
<block wx:if="{{vipList.length<=0}}">
	<view class="novip">
		<image src="{{vip}}" class="novip_img" mode="widthFix"></image>
		<view class="novip_text">暂无会员卡套餐</view>
	</view>

</block>
<!--有会员卡-->
<block wx:else>
	<view class='yxxrui-slider'>
		<view style='overflow:hidden;'>
			<view bindtouchcancel="sliderTouchCancel" bindtouchstart='sliderTouchStart' bindtouchend='sliderTouchEnd'
				bindtouchmove='sliderTouchMove'
				style='width:{{yxxruiSliderData.totalWidth}}px;display:flex;transform:translate({{yxxruiSliderX}}px,0)'>
				<block wx:for="{{yxxruiSliderData.datas}}" wx:for-index="i" wx:key="index" wx:for-item="card">
					<scroll-view scroll-y="{{false}}" class="slider-item" style="padding-left:{{yxxruiSliderData.blankWidth}}px;">
						<form class="slider-form" report-submit="true">
							<image class="slider-img" src="{{card.cardImage}}" />
						</form>
					</scroll-view>
				</block>
			</view>
		</view>
		<view class="slider-indicate-dots">
			<block wx:for="{{yxxruiSliderData.indicateDots}}" wx:for-index="i" wx:key="index">
				<view class="slider-indicate-dot {{i==yxxruiSliderCurPage-1?'active':''}}">
				</view>
			</block>
		</view>
	</view>

	<view class="card">
		<view class="card_one">
			<view hidden="{{planList.length>0?false:true}}" class="card_two">选择会员套餐</view>
			<view hidden="{{planList.length>0?false:true}}" class="card_three"
				style="height:{{planList.length>3?183+mealHeight:183}}rpx;">
				<block wx:for="{{planList}}" wx:for-item="plan" wx:for-index="planIndex" wx:key="unique">
					<view class="card_four"
						style="border:3rpx solid {{plan.select?'#FF7E00':'#E3E3E3'}};background:{{plan.select?'#FCF4E9':''}}; "
						bindtap="checkMeal" data-index="{{planIndex}}">
						<block wx:if="{{plan.expireType==1}}">
							<view class="card_five">长期</view>
						</block>
						<block wx:elif="{{plan.expireType==2}}">
							<view class="card_five">{{plan.expireNum}}年</view>
						</block>
						<block wx:elif="{{plan.expireType==3}}">
							<view class="card_five">{{plan.expireNum}}月</view>
						</block>
						<view class="card_six"><span style="font-size:40rpx;">￥</span>{{plan.payAmount}}</view>
					</view>
				</block>
			</view>
		</view>
		<view class="card_seven">使用规则</view>
		<view class="card_eight" style="margin-bottom:16rpx;">
			<text>{{cardRule}}</text>
		</view>
		<view class="write">
			<view class="write_one">
				<view class="write_two">填写会员资料</view>
				<view class="cell-wrapper">
					<view class="label">姓名</view>
					<view class="input-wrapper">
						<input type="text" class="input" value="{{userName}}" maxlength="6" bindinput="userNameBindInput"
							name="username" placeholder="请输入会员姓名" />
					</view>
				</view>

				<view class="cell-wrapper">
					<view class="label">手机</view>
					<view class="input-wrapper">
						<input type="number" class="input" value="{{telephone}}" maxlength="11" disabled="true" name="usertel"
							placeholder="请输入会员手机号码" />
					</view>
				</view>
				<radio-group bindchange="sexBindChange" style="margin-top:40rpx;">
					<text class="radio_text">性别</text>
					<radio color="#fff" checked="true" value="1" />
					<span class="write_five">男</span>
					<radio color="#fff" value="2" style='margin-left:30px;' />
					<span class="write_five">女</span>
				</radio-group>
			</view>
		</view>
		<view class="attent-wrapper">
			<button class="button" bindtap="openVipCard">立即购买</button>
		</view>
	</view>
</block>
<!--调用弹窗-->
<template is="reward" data="{{...item}}" />