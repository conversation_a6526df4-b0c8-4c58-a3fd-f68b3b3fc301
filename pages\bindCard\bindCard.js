var app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: "",
    bank_list: [], //银行名称
    bankName: "", //输入的银行名称
    branchNameList: [], //支行信息
    userName: "", //用户名称
    cardNo: "", //银行卡卡号
    telephone: "", //手机号码
    pac: ['请选择开户地区'],
    branch: "请选择开户支行", //支行信息
    cardLength: 0, //银行卡长度
    name: app.imageUrl + "bankIcon/name.png",
    card: app.imageUrl + "bankIcon/card.png",
    location: app.imageUrl + "bankIcon/location.png",
    message: app.imageUrl + "bankIcon/message.png",
    phone: app.imageUrl + "bankIcon/phone.png",
    submitTitle: "提交信息"
  },
  bindRegionChange: function(e) {
    var that = this;
    var pac = e.detail.value;
    var province = pac[0];
    var city = pac[1];
    that.queryBankBranchInfo(that.data.bankName, province, city);
    that.setData({
      pac: e.detail.value,
      branch: "请选择开户支行"
    })
  },
  bindRegionBindTap: function(e) {
    var that = this;
    var pac = that.data.pac;
    var province = pac[0];
    var city = pac[1];
    that.queryBankBranchInfo(that.data.bankName, province, city);
  },
  bindPickerChange: function(e) {
    var that = this;
    this.setData({
      branch: that.data.branchNameList[e.detail.value]
    })
  },
  bankCardBindInput: function(e) {
    var that = this;
    var cardNo = e.detail.value;
    if (cardNo.length >= 10) {
      that.queryBankInfo(cardNo);
    }
    that.setData({
      cardNo: cardNo,
      pac: ['请选择开户地区'],
      branch: "请选择开户支行"
    })
  },
  telephoneBindInput: function(e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  userNameBindInput: function(e) {
    this.setData({
      userName: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.queryBoundCardInfo();
  },
  queryBoundCardInfo: function() {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/boundCard/queryBoundCardInfo',
      success: function(res) {
        var boundBean = res.data.BoundBean;
        if (boundBean != null) {
          var id = boundBean.id;
          var openingBank = boundBean.openingBank;
          var branch = boundBean.branch;
          var bankNo = boundBean.bankNo;
          var cardUsername = boundBean.cardUsername;
          var telephone = boundBean.telephone;
          var userId = boundBean.userId;
          var province = boundBean.province;
          var city = boundBean.city;
          that.setData({
            id: id,
            userName: cardUsername,
            cardNo: bankNo,
            bankName: openingBank,
            telephone: telephone,
            pac: [province, city],
            branch: branch,
            submitTitle: "更新信息"
          });
        }
      }
    })
  },
  /**
   * 根据银行卡号查询银行信息
   */
  queryBankInfo: function(cardNo) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "cardNo": cardNo
      },
      url: app.projectName + '/boundCard/queryBankInfo',
      success: function(res) {
        that.setData({
          bankName: res.data.bank_list[0].bankName,
          cardLength: res.data.bank_list[0].bankLength
        });
      }
    })
  },
  /**
   * 根据银行卡名称、省市查询支行信息
   */
  queryBankBranchInfo: function(bankName, province, city) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "bankName": bankName,
        "province": province,
        "city": city
      },
      url: app.projectName + '/boundCard/queryBankBranchInfo',
      success: function(res) {
        that.setData({
          branchNameList: res.data.branchNameList
        });
      }
    })
  },
  /**
   * 绑定银行卡信息
   */
  boundBankInfo: function() {
    var that = this;
    var id = that.data.id;
    var bankName = that.data.bankName;
    if (bankName == null || bankName == "") {
      app.showModal({
        title: '提示',
        content: "开户行不能为空"
      });
      return;
    }
    var branch = that.data.branch;
    if (branch == null || branch == "") {
      app.showModal({
        title: '提示',
        content: "支行不能为空"
      });
      return;
    }
    var cardNo = that.data.cardNo;
    if (cardNo == null || cardNo == "") {
      app.showModal({
        title: '提示',
        content: "银行卡卡号不能为空"
      });
      return;
    }
    var userName = that.data.userName;
    if (userName == null || userName == "") {
      app.showModal({
        title: '提示',
        content: "姓名不能为空"
      });
      return;
    }
    var pac = that.data.pac;
    var province = pac[0];
    var city = pac[1];
    if (province == "省" && city == "市") {
      app.showModal({
        title: '提示',
        content: "省市不能为空"
      });
      return;
    }
    var telephone = that.data.telephone;
    if (telephone == null || telephone == "") {
      app.showModal({
        title: '提示',
        content: "手机号码不能为空"
      });
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "id": id,
        "openingBank": bankName,
        "branch": branch,
        "bankNo": cardNo,
        "cardUserName": userName,
        "province": province,
        "city": city,
        "telephone": telephone,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/boundCard/boundBankInfo',
      success: function(res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: "提交成功",
            icon: 'success',
            duration: 1000,
            mask: true,
            success: function() {
              setTimeout(function() {
                app.turnBack();
              }, 1000);
            }
          })
        } else {
          app.showModal({
            title: '提示',
            content: res.data.returnMessage
          });
        }
      }
    })
  }
})