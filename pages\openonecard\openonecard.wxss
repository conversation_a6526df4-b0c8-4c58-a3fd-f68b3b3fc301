@import '../popupTemplate/popupTemplate.wxss';
page {
  background: #F2F2F2;
}

.novip {
  width: 250rpx;
  height: 300rpx;
  margin: 0px auto;
  margin-top: 150rpx;
}

.novip_text {
  text-align: center;
  color: grey;
  font-size: 32rpx;
  font-family: PingFang SC;
}

.novip_img {
  width: 100%;
  height: auto;
}

/*自定义轮播图样式  */
.yxxrui-slider {
  display: block;
  position: absolute;
  z-index: 2;
  margin-bottom: 40rpx;
  width: 750rpx;
  margin-top:10rpx;
}

.yxxrui-slider .slider-item {
  position: relative;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 0;
}

.yxxrui-slider .slider-form {
  position: relative;
  display: inline-block;
  width: 100%;
  border-radius: 16px;
}

.yxxrui-slider .slider-img {
  border-radius: 16px;
  width: 100%;
  height: 420rpx;
}

.yxxrui-slider .slider-item button {
  line-height: 0;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
  padding-left: 0;
  padding-right: 0;
}

.yxxrui-slider .slider-indicate-dots {
  line-height: 0;
  z-index: 9999;
  margin-top: -14px;
  padding-bottom: 8px;
  position: relative;
  text-align: center;
}

.yxxrui-slider .slider-indicate-dot {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.5);
  display: inline-block;
  margin-right: 4px;
  border-radius: 100%;
  line-height: 0;
  box-sizing: border-box;
}

.yxxrui-slider .button-hover {
  background: none;
}

.yxxrui-slider .slider-indicate-dot.active {
  background: white;
  width: 16px;
  border-radius: 4px;
}


.input-placeholder {
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #919398;
}

/* 自定义样式.... */
/* radio .wx-radio-input {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #C4C4C4;
  border-radius: 50%;
  background: #fff;
} */

radio .wx-radio-input.wx-radio-input-checked::before {
  border-radius: 50%;
  width: 20rpx;
  height: 20rpx;
  text-align: center;
  font-size: 0rpx;
  /* 对勾大小 去掉 */
  background-color: #4A79E5;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}

radio {
  border-radius: 50%;
  width: 36rpx;
  /* 最好是4的倍数，不然会不在中间 */
  height: 36rpx;
  border: 2rpx solid #4A79E5;
  /* 设置边框（外圆） */
  font-size: 0;
  vertical-align: 10rpx;
}

radio .wx-radio-input {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  border: none;
}

.attent-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 346rpx;
}

.card {
  width: 750rpx;
  background: #FFFFFF;
  opacity: 1;
  border-radius: 20rpx 20rpx 2rpx 2rpx;
  margin-top: 250rpx;
  position: absolute;
  z-index: 1;
}

.card_one {
  width: 90%;
  margin: 0px auto;
  border: 0px solid red;
  margin-top: 220rpx;
}

.card_two {
  border: 0px solid blue;
  height: 45rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 45rpx;
  color: #000000;
  opacity: 1;
}

.card_three {
  margin: 0px auto;
  align-items: center;
  justify-content: center;
  margin-bottom: 48rpx;
}

.card_four {
  width: 201rpx;
  height: 183rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  float: left;
  margin-top: 24rpx;
}

.card_five {
  border: 0px solid red;
  height: 73rpx;
  line-height: 110rpx;
  font-size: 34rpx;
  font-family: PingFang SC;
  font-weight: 1000;
  color: #000000;
  text-align: center;
}

.card_six {
  border: 0px solid blue;
  height: 110rpx;
  font-size: 60rpx;
  font-family: PingFang-SC-Bold;
  color: #FF7E00;
  text-align: center;
}

.card_seven {
  border: 0px solid blue;
  height: 45rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 45rpx;
  color: #000000;
  opacity: 1;
  margin-bottom: 16rpx;
  width:90%;
  margin:0px auto;
}

.card_eight {
  border: 0px solid blue;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #686868;
  opacity: 1;
  line-height: 55rpx;
  width:90%;
  margin:0px auto;
}

.write {
  border: 0px solid red;
  width: 750rpx;
  height: 450rpx;
  background: #FFFFFF;
}

.write_one {
  width: 90%;
  margin: 0px auto;
  border: 0px solid red;
}

.write_two {
  border: 0px solid blue;
  height: 45rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 45rpx;
  color: #000000;
  opacity: 1;
  margin-bottom: 30rpx;
  padding-top: 20rpx;
}

.write_three {
  border: 0px solid blue;
  height: 45rpx;
  font-size: 32rpx;
  font-family: PingFang-SC-Medium;
  line-height: 45rpx;
  color: #525252;
  margin-top: 30rpx;
}

.write_four {
  margin: 0px auto;
  width: 100%;
  height: 88rpx;
  border: 2px solid #C7C7CC;
  opacity: 1;
  margin-bottom: 40rpx;
}

.write_five {
  padding-left: 16rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #525252;
}

.button {
  font-family: PingFang SC;
  font-weight: 500;
  background: linear-gradient(90deg, #FF9002 0%, #FE5406 100%);
  border-radius: 44rpx;
  font-size: 32rpx;
  width: 90%;
  color: white;
  height: 88rpx;
  line-height: 88rpx;
}

.cell-wrapper {
  display: flex;
  height: 110rpx;
  font-size: 32rpx;
  font-family: PingFang-SC-Medium;
  line-height: 110rpx;
  color: #525252;
  border-bottom: 3rpx solid #e9e9e9;
}

.cell-wrapper .label {
  width: 20%;
}

.cell-wrapper .input-wrapper {
  flex: 1;
}

.cell-wrapper .input-wrapper .input {
  display: inline-block;
  width: 100%;
  height: 48px;
  outline: none;
  background: transparent;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  color:#000;
  /* 点击高亮的颜色*/
}

.radio_text {
  color: #525252;
  font-size: 32rpx;
  font-family: PingFang-SC-Medium;
  margin-right: 80rpx;
}