const app = getApp();
const popup = require('../popupTemplate/popupTemplate.js')
const http = require('../../utils/http')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //跑马灯文字参数
    marqueePace: 1, //滚动速度
    marqueeDistance: 0, //初始滚动距离
    marqueeDistance2: 0,
    marquee2copy_status: false,
    marquee2_margin: 60,
    size: 26,
    orientation: 'left', //滚动方向
    interval: 15, // 时间间隔 ,
    notice: app.imageUrl + 'sign_notice.png',
    tiange: app.imageUrl + 'collect_tiange.png',
    reward: app.imageUrl + 'collect_reward.png',
    sign: app.imageUrl + 'collect_sign.png',
    expression: app.imageUrl + 'collect_expression.png',
    money: app.imageUrl + 'sign_spmoney.png',
    intergral: app.imageUrl + 'sign_integral.png',
    coupon: app.imageUrl + 'sign_coupon.png',
    consume: app.imageUrl + 'collect_consume.png',
    wechatShareImg: app.imageUrl + 'wechatShareImg.png',
    r_charge: app.imageUrl + 'r_charge.png',
    r_consuption: app.imageUrl + 'r_consuption.png',
    r_evaluate: app.imageUrl + 'r_evaluate.png',
    r_refill: app.imageUrl + 'r_refill.png',
    r_vip: app.imageUrl + 'r_vip.png',
    shareIsShow: true,   //显示分享
    fullSign: true,
    ziArray: [],
    isFromBack: false,
    isFill: false,
    cardFlag: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    if (options) {
      if (options.configId != null && options.configId != '') {
        that.getPrize(options.configId);
      }
      //that.countRetailCartTotal();
      //从用户分享字的链接进来
      if (options.zicode) {
        http.post({
          urlName: 'activity',
          url: 'collect/shareSetWord',
          showLoading: false,
          data: {
            merchantId: app.getExtCompanyId(),
            userId: app.getUserId(),
            ziCode: options.zicode
          }
        })
      }
    }
    that.getIndexRetailRecommend();
    that.getIndentityMark();
    that.getUserCard();
    that.findConfig().then(that.getWordWay()).then(that.getIndexRetailRecommend());
  },
  getIndentityMark: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId(),
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
      },
      url: app.projectName + '/applet/queryUserIsWritePersonInfo',
      success: function (res) {
        var flag = res.data.flag;
        if (!flag) {
          that.setData({
            isFill: true
          })
        }
      }
    })
  },
  /**
* 查询获奖配置
*/
  getPrize: function (configId) {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.addWord != null && res.addWord.length > 0) {
          var wordResult = res.addWordImg;
          that.setData({
            addWordImg: encodeURIComponent(wordResult)
          })
          //that.findConfig();
          that.getWord();
          /*wx.showToast({
            title: '您已获取成功一个字！',
            icon: 'success',
            duration: 2000
          })*/
        }
      }
    })
  },
  imageClick: function (e) {
    var goodsId = e.target.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
* 获取购物车商品数量
*/
  countRetailCartTotal: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      that.getTabBar().setData({
        shopCartNum: 0
      })
    } else {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/applet/shopCart/countRetailCartTotal',
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        success: function (res) {
          that.getTabBar().setData({
            shopCartNum: res.data.shopCartNum
          })
        }
      })
    }
  },
  /**
   *获取字的任务
  */
  getWordWay: function () {
    var that = this;
    http.get({
      urlName: 'activity',
      url: 'config/getActivityConfig',
      showLoading: false,
      data: {
        merchantId: app.getExtCompanyId(),
        rightsType: 3
      },
      success: (res) => {
        var supplyData = res;
        that.setData({
          supplyData: supplyData
        })
      }
    })
  },
  /**
   * 查询集字配置信息
   */
  findConfig: function () {
    var that = this;
    return new Promise(function () {
      var isLogin = app.isLogin();
      if (!isLogin) {
        app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 2);
        return;
      }
      http.get({
        urlName: 'activity',
        url: 'collect/findConfig',
        showLoading: false,
        data: {
          merchantId: app.getExtCompanyId(),
          userId: app.getUserId()
        },
        success: (res) => {
          var collectConfig = res.collectConfig;
          var length = 0;
          var windowWidth = 0;
          var collectList = res.collectList;
          var ziArray = [];
          if (collectConfig.collectNotice) {
            length = collectConfig.collectNotice.length * that.data.size; //文字长度
            windowWidth = wx.getSystemInfoSync().windowWidth; // 屏幕宽度   
          }
          that.setData({
            prizeType: res.prizeType,
            result: res,
            fullSign: res.signinCode == 1 ? true : false,
            length: length,
            windowWidth: windowWidth,
            marquee2_margin: length < windowWidth ? windowWidth - length : that.data.marquee2_margin //当文字长度小于屏幕长度时，需要增加补白
          });
          that.marquee();
          //word:字，wordImg：字图片，color:字颜色，bgColor:字背景色，num:字个数
          ziArray.push({ "id": 0, "word": collectConfig.word1, "wordImg": collectConfig.word1Img, "color": "#c4c4c4", "bgColor": "", "num": 0, "zicode": "" });
          ziArray.push({ "id": 0, "word": collectConfig.word2, "wordImg": collectConfig.word2Img, "color": "#c4c4c4", "bgColor": "", "num": 0, "zicode": "" });
          ziArray.push({ "id": 0, "word": collectConfig.word3, "wordImg": collectConfig.word3Img, "color": "#c4c4c4", "bgColor": "", "num": 0, "zicode": "" });
          ziArray.push({ "id": 0, "word": collectConfig.word4, "wordImg": collectConfig.word4Img, "color": "#c4c4c4", "bgColor": "", "num": 0, "zicode": "" });
          ziArray.push({ "id": 0, "word": collectConfig.word5, "wordImg": collectConfig.word5Img, "color": "#c4c4c4", "bgColor": "", "num": 0, "zicode": "" });
          for (let i = 0; i < ziArray.length; i++) {
            const element1 = ziArray[i];
            for (let j = 0; j < collectList.length; j++) {
              const element2 = collectList[j];
              if (element1.word == element2.ziWord) {
                element1.color = "#FF7E00";
                element1.bgColor = "#FFEFE4";
                element1.zicode = element2.ziCode;
                element1.id = element2.id;
                element1.num += 1;
              }
            }
          }
          var flag = true;
          for (var k = 0; k < ziArray.length; k++) {
            var mark = 0;
            if (ziArray[k].num < 1) {
              mark++;
            }
          }
          if (mark > 0) {
            flag = false;
          }
          if (flag) {
            //that.toExchange();
          }
          that.setData({
            ziArray: ziArray
          });
        }
      })
    })
  },
  /**
  * 签到
  */
  toSign: function () {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'collect/addUserWord',
      showLoading: false,
      data: {
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId()
      },
      success: (res) => {
        that.setData({
          addWordImg: encodeURIComponent(res.addWordImg)
        })
        that.findConfig();
        that.getWord();
      }
    })
  },
  getUserCard: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "phone": app.getTelephone()
      },
      url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
      success: function (res) {
        var cardList = res.data.show_vipCardList;
        if (cardList != null && cardList.length > 0) {
          that.setData({
            cardFlag: true
          })
        } else {

        }
      }
    })
  },
  /**
   * 兑换奖励
   */
  toExchange: function () {
    var that = this;
    if (that.data.prizeType == 1 || that.data.prizeType == 2) {
      var c_text = "今日签到为";
      c_text += that.data.prizeType == 1 ? "积分" : "购物金";
      c_text += "奖励,需绑定会员卡,请前往注册";
      if (!that.data.cardFlag) {
        wx.showModal({
          content: c_text,
          confirmText: "确定",
          cancelText: "取消",
          success: function (res) {
            if (res.confirm) {
              app.navigateToPage('/pages/vipCard/vipCard');
            }
            else {
            }
          }
        })
        return;
      }
    }
    http.post({
      urlName: 'activity',
      url: 'collect/convert',
      showLoading: false,
      data: {
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        that.getExchange();
        that.findConfig();
        /*that.setData({
          fullState:true
        })*/
      }
    })
  },
  //调用获取字模板弹窗
  getWord: function () {
    var that = this;
    that.setData({
      state1: true,
      state2: false,
      which: "collect"
    })
    popup.animationEvents(that, 0, true);
  },
  //调用兑换模板弹窗
  getExchange: function () {
    var that = this;
    that.setData({
      state1: false,
      state2: true,
      which: "exchange"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
  ziClick: function (e) {
    var that = this;
    var zigroup = e.currentTarget.dataset.zigroup;
    that.setData({
      zigroup: zigroup
    })
  },
  /**
   * 攻略
   */
  toCollectStrategy: function () {
    app.navigateToPage('/pages/collectStrategy/collectStrategy');
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    var that = this;
    var zigroup = options.target.dataset.zigroup;
    if (zigroup.num == 0) {
      return {
        title: `请送我一个"${zigroup.word}"字`
      };
    } else if (zigroup.num > 0) {
      http.post({
        urlName: 'activity',
        url: 'collect/shareWord',
        showLoading: false,
        data: {
          collectListId: zigroup.id
        },
        success: (res) => {
        }
      })
      that.findConfig();
      that.setData({
        zigroup: ""
      })
      return {
        title: `送你一个"${zigroup.word}"字`,
        path: 'pages/collect/collect?zicode=' + zigroup.zicode
      };
    }
  },
  /**
  * 页面文字跑马灯
  */
  marquee: function () {
    /*var that = this;
    var interval = setInterval(function () {
      if (-that.data.marqueeDistance < that.data.length) {
        that.setData({
          marqueeDistance: that.data.marqueeDistance - that.data.marqueePace,
        });
      } else {
        clearInterval(interval);
        that.setData({
          marqueeDistance: 600
        });
        that.marquee();
      }
    }, that.data.interval);*/
  },
  /**
* 去购物
*/
  toIndexClick: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
* 去评价页面
*/
  toOrderClick: function () {
    app.navigateToPage("/pages/indexThree/indexThree?currentTab=7");
  },
  /**
* 去成为会员
*/
  toCardClick: function () {
    app.navigateToPage('/pages/openonecard/openonecard');
  },
  /**
* 去完善资料
*/
  toProfileClick: function () {
    app.navigateToPage('/pages/changeProfile/changeProfile');
  },
  /** 
 * 获取首页推荐商品
 */
  getIndexRetailRecommend: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/getIndexRetailRecommend',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var wechatAppletList = res.data.wechatAppletList;
        for (var i = 0; i < wechatAppletList.length; i++) {
          wechatAppletList[i].goodsPrice = parseFloat(wechatAppletList[i].goodsPrice.toFixed(2));
          wechatAppletList[i].cutOffThePrice = parseFloat(wechatAppletList[i].cutOffThePrice.toFixed(2));
        }
        that.setData({
          indexGoodsList: wechatAppletList
        });
        wx.hideLoading();
      },
      fail: function () {
        app.showModal({
          title: "提示",
          content: "数据加载异常"
        });
        wx.hideLoading();
      }
    })
  },
  /**
   * 确认送字弹窗
   */
  goShareTap: function (e) {
    this.setData({
      shareIsShow: false
    })
  },
  closeShareBindTap: function () {
    this.setData({
      shareIsShow: true
    })
  },
  toRecord: function () {
    app.navigateToPage('/pages/record/record');
  },
  onShow: function () {
    var that = this;
    if (that.data.isFromBack) {
      that.onLoad();
    } else {
      that.setData({
        isFromBack: true
      });
    }
  }
})