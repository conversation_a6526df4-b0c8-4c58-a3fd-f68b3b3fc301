<!--<label class="current_account">
  当前登录账号
</label>-->
<!--引入弹窗模板-->
<import src="../popupTemplate/popupTemplate.wxml" />
<!--<view class='account_head'>
	<view class='head_box'>
		<open-data type="userAvatarUrl" style="border-radius:50%;"></open-data>
	</view>
	<view style="text-align:center;margin-top:10rpx;">
		<open-data type="userNickName"></open-data>
	</view>
</view>-->
<button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
  <image class="avatar" src="{{headImage}}"></image>
</button> 
<view class='account_detail'>
	<label>
		<text class="d_r">
			<text style="color:red;">*</text>
			<text>昵称</text>
		</text>
		<input type="nickname" bindchange ="userNameInput" value="{{userName}}" class="weui-input" placeholder="请输入昵称"></input>
	</label>
	<label style="position:relative">
		<text class="d_r">
			<text style="color:red;">*</text>
			<text>手机号</text>
		</text>
		<block wx:if="{{telephone.length>0}}">
			<input disabled='disabled' value='{{telephone}}' type='text' style="width:240rpx;"></input>
			<view
				style="border-radius:16rpx;background-color:#FF7E00;color:#fff;position:absolute;top:0;right:160rpx;width:140rpx;margin:14rpx auto;line-height:60rpx;text-align:center;font-size:28rpx;"
				bindtap="boundUserTelephoneBindTap">更换</view>
		</block>
		<block wx:else>
			<!-- <block wx:if="{{isHaveTelephoneFail}}">boundTelephoneHidden -->
			<button
				style="margin:0 auto;line-height:88rpx;padding-left:0px;color:#666;text-align:left;font-size:28rpx;background:transparent;"
				class='confirm_btn' open-type="getPhoneNumber" bindgetphonenumber="wechatAuthionTelephone">获取手机号 </button>
			<!-- </block>
			<block wx:else>
				<input disabled='disabled' value='{{telephone}}' type='text' style="width:240rpx;"></input>
				<view style="border-radius:16rpx;background-color:#FF7E00;color:#fff;position:absolute;top:0;right:160rpx;width:140rpx;margin:14rpx auto;line-height:60rpx;text-align:center;font-size:28rpx;" bindtap="boundUserTelephoneBindTap">立即绑定</view>
			</block> -->
		</block>
	</label>
	<label>
		<text class="d_r">
			<text>性别</text>
		</text>
		<radio-group class="radio-group" bindchange="radioChange">
			<radio value="男" checked="{{sex=='男'?true:false}}" />男
			<radio value="女" checked="{{sex=='女'?true:false}}" style='margin-left:40px;' />女
		</radio-group>
	</label>
	<block wx:if="{{isUpdateBirthday}}">
		<label class="picker">
			<picker mode="date" value="{{date}}" bindchange="bindDateChange" style='height:45px;'>
				出生日期
				<text style='float:none; margin-left:24px;'>{{birthday}}</text>
			</picker>
		</label>
	</block>
	<block wx:else>
		<label>
			<text class="d_r">
				<text style="color:red;">*</text>
				<text>出生日期</text>
			</text>
			<input value='{{birthday}}' type='text' disabled="true"></input>
		</label>
	</block>
	<label>
		<text class="d_r">
			<text>联系地址</text>
		</text>
		<picker mode="region" bindchange="bindRegionChange" value="{{pac}}" custom-item="{{customItem}}">
			<view class="picker">
				<block wx:for="{{pac}}" wx:for-item="region" wx:for-index="addressIndex" wx:key="">
					{{region}}
				</block>
			</view>
		</picker>
	</label>
	<label>
		<text class="d_r">
			<text>详细地址</text>
		</text>
		<textarea style="font-size:28rpx;" value='{{address}}' bindinput='addressBindInput'></textarea>
	</label>
</view>
<view class='add_address'>
	<button bindtap='savePersonalBindTap'>
		保存
	</button>
	<button style="background:#fff;color:#666;" bindtap="returnIndexBindTap">
		暂不完善
	</button>
</view>

<!--获取手机号-->
<view class="bl_bg" hidden="{{boundTelephoneHidden}}" style="z-index:20;"></view>
<view hidden="{{boundTelephoneHidden}}"
	style="position:absolute;z-index:999;top:20%;width:90%;background:#fff;margin-left:5%;border-radius:10rpx;">
	<view class="topWrap">
		<view style="width:200rpx;margin:0 auto;">
			<open-data type="userAvatarUrl" style="border-radius:50%;"></open-data>
		</view>
		<view>
			<open-data type="userNickName"></open-data>
		</view>
	</view>
	<view style="width:80%;margin:0 auto">
		<view class="fillCode">
			<input placeholder='请输入手机号' type='number' bindinput="bindVipCardTelephoneBindInput" maxlength="11"
				style="width:300rpx;"></input>
			<text class="smsButton darkBlack" bindtap='smsBindTap'>{{secondDesc}}</text>
		</view>
		<view class="fillCode" style="margin-top:20rpx;">
			<input placeholder='请输入验证码' maxlength="6" type='number' bindinput="smsCodeBindInput"></input>
		</view>
	</view>
	<!-- <view style="text-align:center;font-size:30rpx;">您还未绑定手机号</view> -->
	<view style="width:600rpx;margin:0 auto;">
		<button class='confirm_btn' style="background:#999;color:#fff;"
			bindtap='noBoundTelephoneBindTap'>{{boundTelephoneHidden==true?'暂不更换':'暂不绑定'}}</button>
		<button class='confirm_btn' style="background:#07c160;color:#fff;"
			bindtap="nowBoundTelephoneBindTap">{{boundTelephoneHidden==true?'立即更换':'立即绑定'}}</button>
	</view>
</view>
<!--调用弹窗-->
<template is="reward" data="{{...item}}" />