var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var TimeUtil = require('../../utils/util.js');
var wxMarkerData = [];
Page({
  data: {
    order_none: app.imageUrl + 'order/order_none.png',
    imgUrls: [
      app.imageUrl + 'production/production_bg.png'
    ],
    array: [{
      mode: 'aspectFit'
    }],
    //1: 买家申请退货 2:卖家不同意退货 3:买家撤销退货 4:卖家同意退货
    //5:等待买家发货  6:买家已经部分发货 7:买家已经发货完成 8:等待卖家确认收货
    //9:卖家已经部分确认收货 10:卖家已经收货 11:等待卖家退款审核 12:等待卖家退款
    //13:退货结束 14:待买家确认
    orderStatusArray: ["", "买家申请退货", "卖家拒绝退货", "买家撤销退货", "卖家同意退货", "等待买家发货", "买家已部分发货", "等待卖家收货", "等待卖家确认收货", "卖家已经部分确认收货", "卖家已经收货", "等待卖家退款审核", "等待卖家退款", "退货完成", "待买家确认"],
    rejectedId: "",
    returnGoodsList: [],
    offSet: 1,
    pageSize: 10,
    totalHidden: true,
    isBack: false
  },
  onLoad: function (options) {
    this.queryReturnGoodsList();
  },
  /**
   * 去退货
   */
  goToSendGoodsBindTap: function (e) {
    var that = this;
    var rejectedId = e.currentTarget.dataset.id;
    var rejectedDetailList = that.data.returnGoodsList;
    var returnDetail = '';
    for (var i = 0; i < rejectedDetailList.length; i++) {
      if (rejectedId == rejectedDetailList[i].rejectedId) {
        returnDetail = rejectedDetailList[i];
        break;
      }
    }
    //var rejectedDetailList = this.data.returnGoodsList;
    app.navigateToPage('/pages/acceptance/acceptance?rejectedId=' + rejectedId + "&rejectedDetailList=" + JSON.stringify(returnDetail));
  },
  /**
   * 查询已申请退货列表
   */
  queryReturnGoodsList: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/returnGoods/queryReturnGoodsList',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "offSet": that.data.offSet,
        "pageSize": that.data.pageSize,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var totalRecords = res.data.totalRecords;
        var pageResult = res.data.pageResult;
        var oldGoodsList = [];
        if (that.data.offSet > 1) {
          oldGoodsList = that.data.returnGoodsList;
        }
        var showTotal = that.data.offSet * that.data.pageSize;
        if (pageResult != null && pageResult.length > 0) {
          if (oldGoodsList != null && oldGoodsList.length > 0) {
            pageResult = oldGoodsList.concat(pageResult);
          }
          for (var i = 0; i < pageResult.length; i++) {
            pageResult[i].rejectedGenerateDate = TimeUtil.getSmpFormatDateByLong(pageResult[i].rejectedGenerateDate, true);
          }
          that.setData({
            totalHidden: showTotal < totalRecords ? true : false,
            returnGoodsList: pageResult
          });
        } else {
          wx.showToast({
            title: "暂无可操作订单",
            icon: 'none',
            duration: 1000,
            mask: true,
            success: function () {
              setTimeout(function () {
                app.turnBack();
              }, 1000);
            }
          })
        }

      }
    })
  },
  onReachBottom: function () {
    var that = this;
    if (that.data.totalHidden) {
      that.setData({
        offSet: that.data.offSet + 1
      });
      that.queryReturnGoodsList();
    }
  },
  onShow: function () {
    var that = this;
    if (that.data.isBack) {
      that.setData({
        offSet: 1,
        returnGoodsList: []
      });
      that.queryReturnGoodsList();
    } else {
      that.setData({
        isBack: true
      })
    }
  },
  /**
   * 买家取消申请退货
   */
  buyerCancleRejectedOrderBindTap: function (e) {
    var that = this;
    var rejectedId = e.currentTarget.dataset.id;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/returnGoods/buyerCancleRejectedOrder',
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "levelRole": app.getIdentity(),
        "rejectedId": rejectedId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          wx.showToast({
            title: '取消成功',
            icon: 'success',
            duration: 1500
          })
          that.setData({
            offSet: 1,
            returnGoodsList: []
          });
          that.queryReturnGoodsList();
        } else {
          wx.showToast({
            title: '取消申请退款失败',
            icon: 'none',
            duration: 1500
          })
        }
      }
    })
  },
  onShareAppMessage: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.getExtStoreName(),
      path: '/pages/index/index',
      imageUrl: that.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.getExtStoreName(),
      imageUrl: app.getExtStoreImage(),
      success: function (res) {
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
      }
    }
  }
})