.content_box {
  width: 100%;
  position: relative;
}

.picker{
  font-size: 12px;
  padding: 3px;
  background-color:rgb(249, 242, 224);
  position: absolute; 
  top:0;
  right: 0; 
}

.month_forms {
  color: #221816;
  /**border: 1px solid #fa6680;
  font-weight: bold;**/
  width: 130px;
  height: 34px;
  line-height: 34px;
  display: block;
  margin:5px auto;
  text-align: center;
  border-radius: 5px;
  font-size: 15px;
}

.reportforms_bg {
  width: 100%;
  background: url('https://www.cn2b2c.com/gsf/img/wa/accountManager/reportforms_bg.png') no-repeat;
  position: relative;
  height: 0;
  padding-bottom: 32%;
  margin-bottom: 10px;
}

.report_title {
  font-size: 16px;
  color: #000;
  font-weight: bold;
  text-align: center;
  display: block;
  width: 100%;
  top: 20%;
  position: absolute;
}

.report_number {
  color: #b8a376;
  font-size: 34px;
  text-align: center;
  width: 100%;
  display: block;
  position: absolute;
  bottom: 12%;
}

.report_number text {
  font-size: 14px;
}

/**.circle_box {
  width: 144px;
  height: 144px;
  border-radius: 50%;
  background: #fff;
  border: 22px solid #ffb4c8;
  display: block;
  margin: 20px auto 30px auto;
}**/

canvas {
  width: 200px;
  height: 212px;
  margin: 20px auto;
}

.data_box {
  width: 60%;
  margin: 0 0 0 30%;
}

.data_box label {
  line-height: 16px;
  display: block;
  margin-bottom: 8px;
  color: #000;
  font-size: 14px;
}

.little_circle {
  width: 12px;
  height: 12px;
  background: #b69e7a;
  display: block;
  border-radius: 50%;
  float: left;
  margin-right: 5px;
  margin-top: 2px;
}

.tips_box {
  width:82%;
  margin: 10px auto 10px auto;
  display: block;
}

.tips_box text {
  display: block;
  line-height: 24px;
  color: #000;
  font-size: 14px;
}
.report_title text{
  font-size:26rpx;
  float:right;
  margin-right:20rpx;

}
.bankCard{
  background:rgb(249, 242, 224);
  position:fixed;
  bottom:0;
  line-height:90rpx;
  height:90rpx;
  text-align:center;
  width:100%;
}