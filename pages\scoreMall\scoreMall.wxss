/* pages/customerScore/customerScore.wxss */
page{
 background:#fff; 
}
.score_top{
  text-align:center;
  margin:20rpx;
  background:#f5f5f5;
  border-radius:10rpx;
}
.top_title{
  padding:30rpx 0 20rpx 0;
  color:#666;
  font-size:28rpx;
}
.top_score{
  padding:0 0 30rpx 0;
  color:#FF7E00;
  font-size:50rpx;
}
.top_inner{
  margin:20rpx;
  padding-top:20rpx;
  margin-top:30rpx;
}
.inner_score{
  float:right;
  font-size:26rpx;
}
.inner_get{
  color:#FF7E00;
  margin-right:10rpx;
}
.inner_cont{
  margin:20rpx;
}
.cont_wrap{
  border:1rpx solid #ececec;
  border-radius:10rpx;
}
.wrap_one{
  padding:20rpx;
  border-bottom:1rpx solid #ececec;
}
.one_top{
  font-size:28rpx;
}
.one_amount{
  font-weight:normal;
  float:right;
}
.amount_append{
  color:#FF7E00;
}
.one_time{
  color:#666;
  font-size:26rpx;
  margin-top:10rpx;
}

.flex-wrp {
  flex-direction: row;
  background: #fff;
  padding-top:30rpx;
  padding-left:10rpx;
  padding-right:10rpx;

}

.flex-item {
  width: 356rpx;
  float: left;
  background: #fff;
  margin-bottom: 10rpx;
  border-radius:6rpx;
  box-shadow:1px 1px 1px 1px #f8f8f8;
  border:1rpx solid #dedede;
}
.flex-item:nth-child(2n+1) {
  margin-right: 10rpx;
}

.goods_tips {
  color: #958353;
  border: 1px solid #bcb396;
  top: 20rpx;
  left: 20rpx;
  width: 24rpx;
  height: 124rpx;
  line-height: 32rpx;
  display: block;
  position: absolute;
  font-size: 24rpx;
  word-wrap: break-word;
  padding: 8rpx;
  border-radius: 6rpx;
}

.goods_pic {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.soonlist{ 
  font-size: 28rpx;
  writing-mode:vertical-lr; 
  position: absolute;
  top:10rpx;
  left: 10rpx;
  z-index: 2;
  color: #988250;
  border: 1px solid #988250;
  border-radius: 10rpx;
  padding: 10rpx 0; 
}

.goods_pic image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  border-top-right-radius:10rpx;
  border-top-left-radius:10rpx;
}


.goods_title {
  /*display: block;
  height: 78rpx;
  line-height: 50rpx;
  height:40rpx;
  overflow: hidden;
  font-size: 28rpx;
  margin: 10rpx 0;
  text-align:center;*/
  display: block;
  font-size: 28rpx;
  height: 40rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align:center;
  margin: 10rpx 0;
}
.goods_ad{
  /*display: block;
  height: 78rpx;
  line-height: 50rpx;
  height:40rpx;
  overflow: hidden;
  font-size: 28rpx;
  margin: 10rpx 0;
  text-align:center;*/
  display: block;
  color: #666;
  font-size: 24rpx;
  margin-top: 10rpx;
  height: 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align:center;

}

.goods_price {
  display: block;
  color: #FF7E00;
  font-size: 26rpx;
  margin: 12rpx 0;
  text-align:center;
}
.good_shopCart{
  text-align:center;
  background:#FF7E00;
  display:inline-block;
  color:#fff;
  margin:8rpx 0 30rpx 0;
  padding:4rpx 20rpx;
  border-radius:6rpx;
  margin-left:calc(50% - 68rpx);
  font-size:24rpx;
}
.cartImg{
  margin-right:10rpx;
  width:26rpx;
  vertical-align:middle;
}