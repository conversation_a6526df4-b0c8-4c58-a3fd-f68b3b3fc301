.location_box {
  height: 26px;
  position: fixed;
  top: 0px;
  left: 0;
  font-size: 12px;
  line-height: 26px;
  width: 100%;
  padding: 10px 0 0 0;
  z-index: 100;
  background: #fff;
}

.location_box text {
  line-height: 26px;
  height: 26px;
  width: 30%;
  display: block;
  float: left;
  margin-right: 2%;
  background: #a6a6a6;
  color: #fff;
  border-radius: 15px !important;
  padding: 0 2%;
  overflow: hidden;
}

.location_box icon {
  float: left;
  position: absolute;
  top: 15px;
  left: 9%;
  z-index: 10;
  line-height: 34px;
}

.location_box input {
  line-height: 26px;
  height: 26px;
  width: 76%;
  display: block;
  float: left;
  margin: 0 6%;
  background: #ededed;
  color: #272727;
  border-radius: 15px;
  padding-left: 8%;
  padding-right: 4%;
}

.classify_box {
  margin-top: 50px;
}

.classify_left {
  width: 84px;
  float: left;
  text-align: center;
  /**border-right: 1px solid #ddd;**/
  height: 100%;
  position: fixed;
  top: 50px;
}

.classify_left text {
  display: block;
  line-height: 28px;
  margin: 10px 0;
  color: #000;
  border-left: 4px solid #fff;
  font-size: 13px;
}

.active {
  color: #590f10 !important;
  border-left: 4px solid #590f10 !important;
  font-size: 15px !important;
}

.classify_right {
  padding-left: 84px;
  padding-right: 10px;
}

/**单个商品**/

.single_goods {
  /**background: url('https://www.cn2b2c.com/gsf/img/wa/classify_bg.png');
  background-size: 100% 100%;height: 120px;**/
  width: 100%;
  position: relative;
  margin-bottom: 10px;
}

.single_goods image {
  width: 100%;
}

.single_goods label {
  position: absolute;
  bottom: 14%;
  /**bottom:20px;**/
  left: 5%;
}

.tips_box {
  color: #beb58e;
  border: 1px solid #beb58e;
  border-radius: 3px;
  font-size: 10px;
  padding: 1px 3px;
  /**position: absolute;
  bottom: 76%;
  bottom: 100px;**/
  left: 5%;
}

.price_box {
  color: #590f10;
  font-weight: bold;
}

.big_title {
  margin-top: 5px;
  color: #3a3a3c;
  font-size: 15px;
  font-weight: bold;
  display: block;
}

.little_title {
  color: #4c4c4e;
  font-size: 12px;
  display: block;
  line-height: 17px;
  height: 34px;
}

.titleBox {
  font-size: 13px;
  display: block;
  text-align: center;
  line-height: 40px;
  color: #666;
}

.classify_goods {
  width: 33%;
  height: 110px;
  margin-top: 10px;
  float: left;
}

.classify_goods image {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  background: green;
  display: block;
  border-radius: 50%;
  overflow: hidden;
}

.classify_goods text {
  text-align: center;
  font-size: 13px;
  color: #000;
  display: block;
  line-height: 32px;
}
.specialMark{
  border-radius:4px;
  font-size:12px;
  position:absolute;
  padding:1px 6px;
  right:0px;
  top:0px;
  background:#ff6600;
  color:#fff;
}
.eventWrap{
  position:absolute;
  top:0;
  left:0;
  z-index:999;
  width:50px;
  height:50px
}
.eventWrap image{
  width:50px;
  height:50px;
  top:-2px;
  left:-2px;
}
.eventWrap text{
  color:#fff;
  position:absolute;
  display:inline-block;
  font-size:12px;
  top:8px;
  left:3px;
  transform:rotate(-45deg);
  -ms-transform:rotate(-45deg);
  -moz-transform:rotate(-45deg);
  -webkit-transform:rotate(-45deg);
  -o-transform:rotate(-45deg);
}
.c_active {
  background: #fff;
  color: #ff6600;
}