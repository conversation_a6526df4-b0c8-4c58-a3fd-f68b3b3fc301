<view style="height:100%;">
  <view class="search_box">
    <input placeholder='请输入城市中文名称' bindinput='cityBindInput'></input>
  </view>
  <scroll-view scroll-y>
    <!-- 关键字展示 -->
    <view class='keyword_show' hidden='{{isKeyword}}'>
      <block wx:key="" wx:for="{{cityList}}" wx:for-item="city">
        <text bindtap='selectCityBindTap' data-name='{{city}}'>{{city}}</text>
      </block>
    </view>
    <!-- 关键字展示 -->
    <!-- 当前城市 -->
    <view class='hot_city'>
      <label>当前城市</label>
      <view class='clearfix'>
        <text bindtap='localCityBindTap'>{{localCity}}</text>
      </view>
    </view>

    <scroll-view class='scrollView1' scroll-into-view="{{toView}}" scroll-y="true" scroll-with-animation="true">
      <block wx:key="" wx:for="{{cityArray}}" wx:for-item="city" wx:for-index="cityIndex">
        <block wx:if="{{cityMap[city].length!=undefined}}">
          <view class='order_city'>
            <label id="{{'inToView'+city}}">{{city}}</label>
            <view class='single_city'>
              <block wx:key="unique" wx:for="{{cityMap[city]}}" wx:for-item="cityChild">
                <text bindtap='selectCityBindTap' data-name='{{cityChild}}'>{{cityChild}}</text>
              </block>
            </view>
          </view>
        </block>
      </block>
    </scroll-view>
    <!-- 顺序排列 -->
    <!-- 首字母快速查找 -->
    <view class='quick_look'>
      <text>当前</text>
      <block wx:key="" wx:for="{{cityArray}}" wx:for-item="city" wx:for-index="cityIndex">
        <block wx:if="{{cityMap[city].length!=undefined}}">
          <text bindtap="scrollToViewFn" data-id="{{city}}">{{city}}</text>
        </block>
      </block>
    </view>
    <!-- 首字母快速查找 -->
  </scroll-view>
</view>