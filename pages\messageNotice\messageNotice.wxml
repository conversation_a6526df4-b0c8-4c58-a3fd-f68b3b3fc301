<wxs src="../../wxs/subutil.wxs" module="tools" />
<import src="../../components/wxParse/wxParse.wxml" />
<view>
    <view class="title_top" style="position:relative;text-align:center;background:#fff;color:#666;height:100rpx;line-height:70rpx;">
        <view bindtap="storeMessageClick" class="{{isShow?'c_choose':''}}">店铺快讯<label class="{{isShow?'arcStore':''}}"></label></view>
        <view bindtap="systemMessageClick" class="{{!isShow?'c_choose':''}}">系统消息<label class="{{!isShow?'arcStore':''}}"></label><label class="message-bar-dot" hidden="{{unreadData>0?false:true}}">{{unreadData}}</label></view>
    </view>
    <view>
        <!--<view style="text-align:center;">
            <image style="margin-top:300rpx;width:300rpx;" mode="widthFix" src="{{order_none}}"></image>
            <view style="margin-top:50rpx;color:#666;">没有你的消息</view>
        </view>-->
        <view style="background:#fff;" hidden="{{!isShow}}">
            <block wx:for="{{cList}}" wx:for-item="oneData" wx:for-index="xIndex">
                <view class="oneMessageStore" style="border-bottom:1rpx solid #f9f9f9;">
                    <rich-text style="font-size:29rpx;" nodes="{{oneData}}"></rich-text>
                    <!--<view style="text-align:right;position:relative;">
                        <input style="top:0;right:0;position:absolute;width:60rpx;" type="text" data-mid="{{oneList.messageId}}" bindconfirm="addRichEvaluate" confirm-type="send"></input>
                    </view>-->
                    <view>
                        <view style="display:flex;margin-top:30rpx;">
                            <input value="{{evalueData}}" data-mid="{{richData[xIndex].messageId}}" bindconfirm="addRichEvaluate" confirm-type="send" style="padding:4rpx 0;padding-left:20rpx;text-indent:10rpx;font-size:28rpx;border:1px solid #c8c8c8;width:200rpx;border-radius:10rpx;" placeholder="说点什么~" />
                            <view style="margin-left:100rpx;text-align:center;">
                                <image style="width:50rpx;height:50rpx;" src="{{chatMessage}}"></image>
                                <view style="margin-top:-4rpx;font-size:24rpx;" hidden="{{richData[xIndex].messageEvaluateTimes>0?false:true}}">{{richData[xIndex].messageEvaluateTimes}}</view>
                            </view>
                            <view style="margin-left:100rpx;text-align:center;">
                                <image style="width:50rpx;height:50rpx;" src="{{loveMessage}}"></image>
                                <view bindtap="lovePressClick" style="margin-top:-4rpx;font-size:24rpx;" hidden="{{richData[xIndex].messageSupportTimes>0?false:true}}">{{richData[xIndex].messageSupportTimes}}</view>
                            </view>
                        </view>
                    </view>
                    <block wx:if="{{richData[xIndex].eValue.length>0}}">
                        <view style="margin-top:10rpx;padding:16rpx;background:#f3f3f3;font-size:28rpx;color:#666;">
                            <block wx:for="{{richData[xIndex].eValue}}" wx:for-item="oneList">
                                <view style="color:#000">
                                    <label style="color:#999;opcatity:0.5">{{oneList.clientName != null?oneList.clientName:''}}</label>：{{tools.sub.toEvaluate(oneList.evaluateContent)}}
                                </view>
                                    <block wx:for="{{oneList.replyRecordList}}" wx:for-item="oneReply">
                                        <view style="color:#000;padding-left:50rpx;">
                                            <label style="color:#999;opcatity:0.5">商家回复</label>：{{tools.sub.toEvaluate(oneReply.evaluateContent)}}
                                        </view>
                                    </block>

                            </block>
                        </view>
                    </block>
                </view>
            </block> 
        </view>
        <view hidden="{{isShow}}">
            <!--timeline-->
                <view id='timeline'>
                    <block wx:for="{{messageList}}" wx:for-item="oneMessage">
                        <view class="timeWrap">
                            <view class="timeLeft">
                                <view>{{tools.sub.formatMillToDate(oneMessage.noticePublishTime)}}</view>
                            </view>
                            <view class="timeRight">
                                <view class="time_title"> {{tools.sub.formatMillToSecond(oneMessage.noticePublishTime)}}</view>
                                <view class="time_content">
                                    {{oneMessage.noticeContent}}
                                </view>
                            </view>
                        </view>
                    </block>
                </view>
            <!--timeline-->
        </view>
    </view>
</view>
