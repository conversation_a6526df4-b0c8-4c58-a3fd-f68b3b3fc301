<view style="padding-bottom:100rpx;">
	<view class="config_bg">
		<image lazy-load='true' src="{{bgPic}}" mode="widthFix" style="width:100%;opacity:0.9;"></image>
	</view>
	<!--强制登录-->
	<view class="bl_bg" hidden="{{boundTelephoneHidden}}" style="z-index:1;"></view>
	<view hidden="{{boundTelephoneHidden}}"
		style="position:absolute;z-index:999;top:20%;width:90%;background:#fff;margin-left:5%;border-radius:10rpx;">
		<view class="topWrap" data-type="11" bindtap='menuClickBindTap'>
			<image src="{{headImage==''?avatarUrl:headImage}}">
			</image>
			<view>{{userName}}{{headImage}}eee</view>
		</view>
		<view style="text-align:center;font-size:30rpx;">您还未绑定手机号</view>
		<view style="text-align:center;color:#666;font-size:26rpx;margin-top:20rpx;">为让你更好的使用会员卡，请先绑定手机号</view>
		<view style="width:600rpx;margin:0 auto;">
			<button class='confirm_btn' style="background:#999;color:#fff;" bindtap='noBoundTelephoneBindTap'>暂不绑定</button>
			<button class='confirm_btn' style="background:#07c160;color:#fff;" open-type="getPhoneNumber"
				bindgetphonenumber="getPhoneNumber">立即绑定</button>
		</view>
	</view>
	
	<!--强制登录-->
	<view style="position:relative;background:{{bgColor}}">
		<view style="color:#666;position:absolute;top:68rpx;right:30rpx;z-index:999;" bindtap="goMessageClick">
			<image style="width:60rpx;height:60rpx;" src="{{noticeIcon}}"></image>
		</view>
		<!--<image bindtap="goSignClick" style="width:160rpx;position:absolute;top:98rpx;right:30rpx;z-index:999;"
			mode="widthFix" hidden="{{signShow==1?false:true}}" src="{{sign_mark}}"></image>-->
		<view class='account_head' style="padding:40rpx 0 120rpx 0;color:#666;">
			<view class='head_box clearfix' data-type="11" bindtap='menuClickBindTap'>
				<image src='{{headImage==""?avatarUrl:headImage}}'></image>
				<view style="color:#000;float:left;font-weight:bold;line-height:120rpx;margin-left: 16px;">
					{{userName}}
				</view>
			</view>
		</view>
		<view bindtap='goVipCardBind' style="background:{{vipColor}};color:{{vipTextColor}}" class="goCard"
			hidden="{{vipShow==1?false:true}}">
			<label style="float:left;margin-left:20rpx;">{{vipText}}</label>
			<label>我的会员卡 >
			</label>
		</view>
	</view>
	<!--会员码-->
	<view style="width:700rpx;margin:0 auto;border-radius:20rpx;background:#fff;" hidden="{{assetOpen==1?false:true}}">
		<view class="orderWrap clearfix" style="padding:30rpx 0;margin-bottom:20rpx;margin-top:20rpx;">
			<view bindtap="goCardsBind" style="width:33.3%;float:left;text-align:center;">
				<view class="middleText" style="font-size:30rpx;font-weight:bold">{{card_score}}</view>
				<view class="middleIcon" style="margin-top:10rpx;">积分</view>
			</view>
			<view bindtap="goCardsBind" style="width:33.3%;float:left;text-align:center;">
				<view class="middleText" style="font-size:30rpx;font-weight:bold">{{card_Amount}}</view>
				<view class="middleIcon" style="margin-top:10rpx;">余额</view>
			</view>
			<view bindtap="goCouponBind" style="width:33.3%;float:left;text-align:center;">
				<view class="middleText" style="font-size:30rpx;font-weight:bold">{{couponNum}}</view>
				<view class="middleIcon" style="margin-top:10rpx;">优惠券</view>
			</view>
		</view>
	</view>
	<!--会员码-->
	<view style="width:700rpx;margin:0 auto;border-radius:20rpx;background:#fff;">
		<view bindtap='myOrderBindTap' data-id='0'
			style="border-bottom:1px solid #f6f6f6;height:90rpx;font-size:28rpx;color:#333;margin-left:30rpx;padding-right:30rpx;">
			<label style="font-size:28rpx;line-height:90rpx;font-weight:bold;">我的订单</label>
			<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
			<text style="font-size:24rpx;color:#999;float:right;margin-right:10rpx;margin-top:30rpx;">查看全部订单</text>
		</view>
		<view class="orderWrap clearfix" style="padding:40rpx 0 48rpx 0;">
			<view bindtap='myOrderBindTap' class="orderState" data-id='2'>
				<image lazy-load='true' src='{{waitPay}}' style="width:50rpx;height:auto;" mode="widthFix" /> 待付款
				<text class="cartNum" hidden="{{waitfk>0?false:true}}">{{waitfk}}</text>
			</view>
			<view bindtap='myOrderBindTap' class="orderState" data-id='3'>
				<image lazy-load='true' src='{{waitDeliver}}' style="width:50rpx;height:auto;" mode="widthFix" /> 待发货
				<text class="cartNum" hidden="{{waitfh>0?false:true}}">{{waitfh}}</text>
			</view>
			<view bindtap='myOrderBindTap' class="orderState" data-id='4'>
				<image lazy-load='true' src='{{alreadyDeliver}}' style="width:50rpx;height:auto;" mode="widthFix" /> 待收货
				<text class="cartNum" hidden="{{waitsh>0?false:true}}">{{waitsh}}</text>
			</view>
			<view bindtap='myOrderBindTap' class="orderState" data-id='7'>
				<image lazy-load='true' src='{{waitEvaluate}}' style="width:50rpx;height:auto;" mode="widthFix" /> 待评价
				<text class="cartNum" hidden="{{waitpj>0?false:true}}">{{waitpj}}</text>
			</view>
			<!-- <view bindtap='menuClickBindTap' class="orderState" data-type="14">
				<image lazy-load='true' src='{{menuImageArray[14]}}' style="width:50rpx" mode="widthFix" /> 退货售后
			</view> -->
		</view>
	</view>
	<!--图片模块-->
	<view style="width:700rpx;margin:0 auto;border-radius:20rpx;margin-top:20rpx;" bindtap="jumpPageBindTap"
		data-id="{{adId}}" data-type="{{adType}}" hidden="{{adOpen==1?false:true}}">
		<image lazy-load='true' style="width:100%;border-radius:20rpx;" src="{{adPic}}" mode="widthFix"></image>
	</view>
	<!--宫格菜单-->
	<view class='clearfix col_detail' hidden="{{menuShowType==2?false:true}}">
		<!--<block wx:for="{{menuIdArray}}" wx:for-index="idx" wx:for-item="item" wx:key="unique">
			<view class="oneItem" data-type="{{item}}" bindtap='menuClickBindTap'>
				<image src="{{menuImageArray[item]}}" style="width:50rpx;" mode="widthFix"></image>
				<view style="font-size:26rpx;color:#666;margin-top:10rpx;">{{menuNameArray[item]}}</view>
			</view>
		</block>-->
		
		<block wx:for="{{menuList}}" wx:for-index="idx" wx:for-item="item" wx:key="unique">
			<block wx:if="{{item.name == '推广奖励' || item.name == '我的团队'}}">
				<block wx:if="{{isFlag}}">
					<view wx:if="{{item.isDefault == 1}}" class="oneItem" data-type="{{item.menuUrl}}" bindtap='menuClickBindTap'>
						<image src="{{item.image}}" style="width:50rpx;height:50rpx;" mode="widthFix"></image>
						<view style="font-size:26rpx;color:#666;margin-top:10rpx;">{{item.name}}</view>
					</view>
				</block>
			</block>
			<block wx:else>
				<view wx:if="{{item.isDefault == 1}}" class="oneItem" data-type="{{item.menuUrl}}" bindtap='menuClickBindTap'>
					<image src="{{item.image}}" style="width:50rpx;height:50rpx;" mode="widthFix"></image>
					<view style="font-size:26rpx;color:#666;margin-top:10rpx;">{{item.name}}</view>
				</view>
			</block>
		</block>
		<!--<view  class="oneItem"  bindtap="goLuckDrawClick">
			<image src="{{lotteryPic}}" style="width:50rpx;height:50rpx;" mode="widthFix"></image>
			<view style="font-size:26rpx;color:#666;margin-top:10rpx;">抽奖权益</view>
		</view>-->
	</view>
	<!--宫格菜单-->
	<view hidden="{{menuShowType==1?false:true}}" class='account_detail'
		style="margin-bottom:20rpx;width:700rpx;margin:0 auto;border-radius:20rpx;background:#fff;margin-top:20rpx;">
		<!--<block wx:for="{{menuIdArray}}" wx:for-index="idx" wx:for-item="item" wx:key="unique">
			<label data-type="{{item}}" bindtap='menuClickBindTap'>
				<image lazy-load='true' src='{{menuImageArray[item]}}'></image>{{menuNameArray[item]}}
				<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
			</label>
		</block>-->
		<block wx:for="{{menuList}}" wx:for-index="idx" wx:for-item="item" wx:key="unique">
				<block wx:if="{{item.name == '推广奖励' || item.name == '我的团队'}}">
					<block wx:if="{{isFlag}}">
						<label wx:if="{{item.isDefault == 1}}" data-type="{{item.menuUrl}}" bindtap='menuClickBindTap'>
							<image lazy-load='true' src='{{item.image}}'></image>{{item.name}}
							<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
						</label>
					</block>
				</block>
				<block wx:else>
					<label wx:if="{{item.isDefault == 1}}" data-type="{{item.menuUrl}}" bindtap='menuClickBindTap'>
						<image lazy-load='true' src='{{item.image}}'></image>{{item.name}}
						<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
					</label>
				</block>
		</block>
		<!--<label bindtap='goLuckDrawClick'>
			<image lazy-load='true' src='{{lotteryPic}}'></image>去抽奖
			<image lazy-load='true' class='personal_more' src='{{personal_more}}'></image>
		</label>-->
	</view>
	<!--营业时间-->
	<view class='account_detail clearfix' hidden="{{openStoreTime.length>0?false:true}}"
		style="margin-bottom:20rpx;width:700rpx;margin:0 auto;border-radius:20rpx;background:#fff;margin-top:20rpx;">
		<label>
			营业时间
			<label style="float:right;font-size:24rpx;">{{openStoreTime}}</label>
		</label>
	</view>
	<!--<view bindtap="giftCardBindTap">礼券</view>-->
	<!--营业时间-->
	<label style='height:32px;display:block; line-height:32px; color:#999; font-size:12px;text-align:center;'>当前版本
		{{appletVersion}}</label>
</view>