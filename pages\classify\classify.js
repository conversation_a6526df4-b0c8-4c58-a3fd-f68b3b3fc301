var app = getApp();
var bmap = require('../../libs/bmap-wx.js');
var wxMarkerData = [];
Page({
  data: {
    second_height: 0,
    classify_banner: app.imageUrl + 'activity/classify_banner.png',
    categoryBean: [],
    commodityBaseList: [],
    pic_bg: app.imageUrl + 'classify_bg.png',
    event: app.imageUrl + 'event.png',
    categoryBean: [],
    isFromBack: false
  },
  onLoad: function() {
    var that = this
    that.getSecondHeight();
    that.queryGoodsType();
  },
  getSecondHeight: function() {
    var that = this
    // 获取系统信息
    wx.getSystemInfo({
      success: function(res) {
        
        that.setData({
          // second部分高度 = 利用窗口可使用高度 - first部分高度（这里的高度单位为px，所有利用比例将300rpx转换为px）
          second_height: res.windowHeight - res.windowWidth / 750 * 80
        })
      }
    })
  },
  /**
   * 点击搜索框跳转
   */
  searchBindFocus: function() {
    app.navigateToPage('/pages/searchPage/searchPage');
  },
  /**
   * 跳转到商品详情
   */
  imageClick: function(e) {
    var goodsId = e.currentTarget.dataset.commodityid;
    app.navigateToPage("/pages/goodsDetail/goodsDetail?goodsId=" + goodsId);
  },
  /**
   * 查询商品分类
   */
  queryGoodsType: function() {
    var that = this;
    that.fastSelectGoodsType("", "", "", "", "", "");
    /*wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/applet/goods/queryGoodsType',
      success: function(res) {
        var categoryBean = res.data.categoryBean;
        var commodityBaseList = res.data.commodityBaseList;
        /*for (var i = 0; i < commodityBaseList.length; i++) {
            commodityBaseList[i].goodsPrice = commodityBaseList[i].goodsPrice.toFixed(2);
        }*/
        /*that.setData({
          stockBean: res.data.stockBean,
          categoryBean: categoryBean,
          commodityBaseList: commodityBaseList,
          isFromBack: true
        });
      }
    })*/
  },
  /**
   * 选择一级分类
   */
  selectGoodsTypeBindTap: function(e) {
    var that = this;
    var id = e.target.dataset.id;
    var categoryBean = that.data.categoryBean;
    for (var i = 0; i < categoryBean.length; i++) {
      if (categoryBean[i].categoryId == id) {
        categoryBean[i].select = true;
      } else {
        categoryBean[i].select = false;
      }
    }
    that.setData({
      categoryBean: categoryBean,
      commodityList: [],
      currentPage: 1,
      categoryId:id,
      fastType:1
    });
    //that.queryGoodsByTypeId(id);
    that.fastSelectGoodsType(id, "", "", "", "", "");
  },
  /**
   * 查询商品根据分类Id
   */
  queryGoodsByTypeId: function(categoryId) {
    var that = this;
    that.setData({
      currentPage:1
    })
    that.fastSelectGoodsType(categoryId, "", "", "", "", "");
    /*wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "categoryId": categoryId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "pageSize": 10,
        "currpage": 1
      },
      url: app.projectName + '/applet/goods/queryGoodsByTypeId',
      success: function(res) {
        var commodityBaseList = res.data.commodityBaseList;
        that.setData({
          commodityBaseList: commodityBaseList
        });
      }
    })*/
  },
  /**
   * 选择二级分类
   */
  goodsDetailByTypeBindTap: function(e) {
    var id = e.target.dataset.id;
  },
  onShow: function() {
    if (this.data.isFromBack) {
      this.getSecondHeight();
      this.queryGoodsType();
    } else {
      this.setData({
        isFromBack: true
      });
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: app.storeName,
      path: '/pages/index/index?recommendId=' + app.getUserId() + '&recommendAccount=' + app.getLoginAccount() + '&recommendName=' + app.getLoginName() + '&identity=' + app.getIdentity(),
      imageUrl: app.shareImageUrl,
      success: function(res) {
        // 转发成功
      },
      fail: function(res) {
        // 转发失败
      }
    }
  }
})