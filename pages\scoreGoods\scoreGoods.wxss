page {
  position: relative;
  height: 100%;
  width: 100%;
  background: #f4f4f4;
}

.contant_box {
  padding-bottom: 44px;
  position: absolute;
  width: 100%;
}

.goods_content {
  padding: 1px 10px;
  background: #fff;
}

.goods_title {
  font-size: 15px;
  color: #333;
  /*height: 20rpx;
  line-height: 20rpx;8*/
  overflow: hidden;
  /*display: block;*/
  height:60rpx;
  line-height:60rpx;
  width: 100%;
}

.goods_adv {
  font-size: 13px;
  color: #666;
  overflow: hidden;
  display: block;
  width: 100%;
}

.goods_price {
  color: #FF7E00;
  display: block;
  width: 100%;
  font-size: 15px;
  padding: 8px 0;
}

.goods-all-stock, .express-fee {
  margin: 0 20px 6px 0;
  color: #666;
  display: inline-block;
  font-size: 12px;
  line-height: 20px;
}

.clock {
  font-size: 14px;
}

.goods_evaluate {
  padding: 8px;
  text-align: center;
  background: #f1f1f1;
  color: #b2b2b2;
  font-size: 12px;
}

.line {
  width: 45px;
  height: 1px;
  background: #ccc;
  display: inline-block;
  vertical-align: middle;
}

.icon-good-comment {
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px;
}

.goods_evaluateBox {
  padding: 10px;
  background: #fff;
}

.evaluate_box {
  border-bottom: 1px solid #f4f4f4;
  padding-bottom: 8px;
  font-size: 14px;
  margin-bottom: 10px;
}

.goods-comment-label {
  display: inline-block;
  padding: 2px 3px;
  background-color: #ffebe9;
  color: #333;
  margin-right: 5px;
  border-radius: 100px;
  font-size: 12px;
  width: 76px;
  text-align: center;
  height: 24px;
  line-height: 24px;
  margin-bottom: 10px;
}

.comment_detail {
  color: #666;
  padding: 5px 0;
  margin-bottom: 5px;
  border-bottom: 1px dashed #dadada;
}

.level_star {
  float: left;
  display: block;
  width: 24%;
  height: 20px;
  font-size: 16px;
  color: #FF7E00;
  line-height: 20px;
}

.comment_time {
  float: left;
  height: 20px;
  line-height: 20px;
  width: 46%;
  display: block;
  font-size: 12px;
}

.comment_name {
  float: left;
  width: 30%;
  line-height: 20px;
  overflow: hidden;
  font-size: 12px;
  text-align: right;
}

.commnet_text {
  width: 100%;
  display: block;
  font-size: 13px;
  color: #333;
  margin-top: 5px;
}

.more_evaluate {
  text-align: center;
  margin-top: 10px;
}

.allComment {
  color: #fa6a85;
  border: 1px solid #fa6a85;
  width: 104px;
  line-height: 26px;
  font-size: 12px;
  border-radius: 13px;
  display: inline-block;
}

.goods_detail {
  border-bottom: 1px solid #f4f4f4;
  font-size: 14px;
  display: block;
  padding: 5px 10px;
  background: #fff;
}

/*.goods_pic {
  padding: 0 10px;
   margin-top: 8px;
  
  margin-bottom: 50px;
}*/

.wxParse-section view {
  padding: 0 !important;
}

.wxParse-p image {
  width: 750rpx !important;
}
.wxParse-inline{
  width:750rpx;
}
.foot_box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 44px;
  border-top: 1px solid #dfdfdf;
  box-sizing: border-box;
}

.flex-sub-box-3 {
  width: 33.3%;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
}

.little_icon {
  background: #fff;
  height: 44px;
}

.flex-sub-box-2 {
  width: 49%;
  float: left;
  margin-top: 9px;
}

.flex-sub-box-2:first-child {
  width: 50%;
  border-right: 1px solid #e5e5e5;
}

.flex-sub-box-2 image {
  width: 26px;
  height: 26px;
  margin: 0px auto 0;
  display: block;
}

.flex-sub-box-2 label {
  font-size: 12px;
  text-align: center;
  width: 100%;
  display: block;
}

.btn {
  display: inline-block;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  /*padding: 7px;*/
  padding:7px 0;
  margin: 0;
  font-size: 14px;
  cursor: pointer;
  line-height: 18px;
  -webkit-appearance: none;
  text-align: center;
}

.btn-yellow {
  background: #f5a623;
}

.btn-red {
  background: #af423d;
}

.add-to-shoppingcart, .buy-goods-directly {
  width: 100%;
  padding-top: 10px;
  padding-bottom: 10px;
  border: none;
  box-sizing: border-box;
  line-height: 24px;
  border-radius: 0;
  color: #fff;
}

/**黑色背景**/

.black_bg1 {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10;
  background: #000;
  top: 0;
  left: 0;
  opacity: 0.5;
}

/**加入购物车 弹框**/

.scroll_block {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 130;
  
}

.addgoods_box {
  width: 100%;
  height: 60px;
  /* border-bottom: 1px solid #e5e5e5; */
  background: #fff;
}

.addgoods_pic {
  width: 86px;
  height: 86px;
  background: #fff;
  position: absolute;
  top: -43px;
  left: 15px;
  border: 1px solid #ccc;
  /* overflow: hidden; */
}

.addgoods_pic image {
  width: 100%;
  height: 100%;
}

.addgoods_title, .addgoods_price {
  padding-left: 115px;
  /*height: 20px;*/
  max-height:150rpx;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  padding-right: 30px;
  text-overflow: ellipsis;
  padding-top: 3px;
}

.addgoods_title {
  color: #353535;
  font-size: 14px;
  background: #fff;
}

.addgoods_price {
  color: #fa6a85;
  font-size: 13px;
}

.goods_classify {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 10px 5px 10px;
}

.goods_classify label {
  margin-bottom: 10px;
  line-height: 28px;
  font-size: 14px;
}

.goods_classify  view text {
  float: left;
  padding: 0 16px;
  color: #333;
  background: #ececec;
  font-size: 12px;
  margin-bottom: 8px;
  margin-right: 10px;
  line-height: 28px;
  border-radius: 5px;
}

.addgoods_number {
  width: 100%;
  background: #fff;
  padding-top: 15px;
  padding-bottom: 15px;
  /**border-bottom: 1px solid #e5e5e5;**/
}

.limited_quantity {
  font-size: 13px;
  float: left;
  padding-left: 15px;
  padding-top: 5px;
}

.stock {
  font-size: 13px;
  float: right;
  margin-right: 5px;
  margin-top: 4px;
}

.plus_minus {
  float: right;
  padding-right: 15px;
  margin-top: 3px;
}

.minus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 22px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_box {
  width: 26px;
  height: 26px;
  color: #333;
  background: #e4e4e4;
  display: block;
  line-height: 24px;
  text-align: center;
  font-size: 18px;
  float: left;
}

.plus_minus input {
  /*width: 50px;
  height: 26px;
  line-height: 10px;
  background: #e4e4e4;
  float: left;
  border: none;
  font-size: 13px;
  margin: 0 5px;
  text-align: center;
  color: #333;*/
  border-radius:10px;
  height:30px;
  border:1px solid #ddd;
  display:inline-block;
  width:60%;
}

.pay-add-to-shoppingcart {
  display: block;
  padding: 0px;
  font-size: 14px;
  line-height: 44px;
  height: 44px;
  border-radius: 0px;
  flex: 1;
  color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
}

.page-dialog-close {
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 10;
  display: block;
  width: 20px;
  height: 20px;
  /* border: 1px solid #67666f;
  color: #67666f; 
  text-align: center;
  line-height: 16px;
  border-radius: 50%;
  font-size: 26px;
  margin: 5px auto;*/
}

/**团购**/

.goods-seckill-original {
  margin-top: -2px;
  font-size: 12px;
}

.group_box {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: #f31e4a;
  z-index: 50;
}

.goods-seckill-left {
  width: 60%;
  float: left;
  position: absolute;
  padding-left: 20px;
  height: 50px;
}

.goods-seckill-left:after {
  content: '';
  width: 0;
  height: 0;
  border-top: 50px solid #f31e4a;
  border-right: 10px solid transparent;
  vertical-align: top;
  position: absolute;
  right: 20px;
  top: 0;
  z-index: 1;
}

.goods-current-price {
  display: inline-block;
  vertical-align: middle;
  color: #fff;
  margin-top: 1px;
  font-size: 13px;
}

.goods-current-price label {
  font-size: 18px;
}

.goods-original-price {
  text-decoration: line-through;
  vertical-align: middle;
  color: #fff;
  display: inline-block;
  font-size: 13px;
}

.goods-seckill-sign {
  display: inline-block;
  padding:19px 4px;
  /**border: 1px solid #fff;**/
  font-size: 30px;
  color: #fff;
  margin-left: 10px;
  line-height: 11px;
}

.activity_time {
  width: 40%;
  float: right;
  background: #feef72;
  height: 50px;
  padding-left: 10px;
  text-align: center;
  color: #583512;
}

.activity_time label:first-child {
  color: #583512;
  text-align: center;
  width: 100%;
  display: block;
  line-height: 13px;
  margin-top: 8px;
  font-weight: bold;
  font-size: 13px;
  background: none;
}

.activity_time label {
  font-size: 12px;
  background: #583512;
  color: #fff;
  padding: 0 3px;
  border-radius: 3px;
}

.active_classify {
  background: #fa6a85 !important;
  color: #fff !important;
}

/*店铺入口*/

.shop_entrance {
  height: 40px;
  background: #fff;
  margin-top: 10px;
  padding: 10px;
  position: relative;
}

.shop_logo {
  float: left;
  width: 40px;
  height: 40px;
  border-radius: 3px;
}

.boxright {
  padding-left: 50px;
  padding-right: 90px;
}

.boxright label:first-child {
  width: 100%;
  display: block;
  color: #2e2e2e;
  font-size: 14px;
}

.boxright label:last-child {
  width: 100%;
  display: block;
  color: #9c9da1;
  font-size: 12px;
  margin-top: 5px;
}

.sales_volume {
  margin-left: 10px;
}

.enter_button {
  position: absolute;
  top: 18px;
  padding: 0 3px;
  right: 10px;
  font-size: 12px;
  border: 1px solid #b4b4b4;
  border-radius: 5px;
  line-height: 25px;
  color: #3f3f3f;
}

.enter_button image {
  width: 18px;
  height: 18px;
  float: left;
  margin-top: 4px;
}

/**晒图**/

.show_pic {
  width: 100%;
  margin: 10px 0 0 0;
}

.show_pic image {
  width: 60px;
  height: 60px;
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 5px;
}

/**服务**/

.service_note {
  padding: 10px 10px 0 10px;
  position: relative;
  margin-top: 10px;
  background: #fff;
}

.note_title {
  float: left;
  font-size: 14px;
}

.note_tips {
  padding-left: 50px;
  height: 60px;
}

.note_tips label {
  float: left;
  margin-right: 10px;
  height: 30px;
  color: #b2b2b2;
  font-size: 12px;
}
.quduan_tips label {
  display:block;
  height: 30px;
  color: #b2b2b2;
  font-size: 12px;
}
.quduan_tips label text{
  width: 5px;
  height: 5px;
  background: #FF7E00;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}
.note_tips label text {
  width: 5px;
  height: 5px;
  background: #FF7E00;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.personal_more {
  width: 18px;
  height: 18px;
  position: absolute;
  right: 10px;
  top: 20px;
}

/**服务弹框**/

.serviceBox {
  width: 100%;
  background: #fff;
  position: fixed;
  z-index: 130;
  bottom: 0;
}

.serviceBox_title {
  width: 100%;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #ececec;
  display: block;
  text-align: center;
  font-size: 15px;
}

.single_service {
  width: 94%;
  margin: 10px 3%;
}

.single_top {
  font-size: 14px;
}

.single_top text {
  width: 5px;
  height: 5px;
  background: #fa6a85;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.single_bottom {
  padding: 10px 13px;
  color: #888;
  display: block;
  font-size: 12px;
  line-height: 20px;
}

/**掌柜回复**/

.reply_box {
  width: 100%;
  background: #f1f1f1;
  border-radius: 5px;
  position: relative;
  margin-top: 15px;
}

.reply_row {
  position: absolute;
  top: -10px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #f1f1f1;
}

.reply_word {
  color: #666;
  font-size: 13px;
  line-height: 24px;
  padding: 10px;
  display: block;
}

.collageView {
  width: 100%;
  background: #fff;
  margin-top: 10px;
}

.collageFristView {
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
}

.collageFristLable {
  float: left;
  font-size: 14px;
  color: #444;
}

.collageTwoLable {
  float: right;
  color: #999;
  font-size: 12px;
}

.collageUserView {
  padding: 10px;
  height: 60px;
  border-top: 1px solid #f1f1f1;
}

.collageUserImage {
  width: 60px;
  height: 60px;
  float: left;
  border-radius: 50%;
}

.collageUserFristView {
  padding-left: 70px;
  height: 60px;
}

.collageUserLable {
  width: 38%;
  height: 60px;
  float: left;
  display: block;
  line-height: 60px;
  overflow: hidden;
  font-size: 13px;
}

.collageUserTwoView {
  float: left;
  height: 60px;
  width: 32%;
  text-align: right;
}
.collageUserpopView {
  float: left;
  height: 60px;
  width: 60%;
  text-align: left;
}
.collageUserThreeView {
  display: block;
  color: #444;
  font-size: 12px;
  line-height: 20px;
  margin-top: 9px;
}

.collageUserFristLable {
  font-size: 12px;
  color: #999;
  line-height: 20px;
}

.collageUserButton {
  width: 66px;
  height: 28px;
  line-height: 28px;
  margin-top: 16px;
  float: right;
  background: red;
  color: #fff;
  font-size: 12px;
}
.titleWrap{
  text-align:center;padding:15px 0 20px 0;
}
.saleWrap{
  border:1px solid #fff;
  padding:0 5px;
  width:80%;
  height:350px;
  background: #fff;
  border-radius:8px;
  position: absolute;
  top: 22%;
  left:9%;
  z-index: 20;
}
.userWrap{
  border:1px solid #fff;
  padding:20px 5px 20px;
  width:80%;
  background: #fff;
  border-radius:8px;
  position: absolute;
  top: 2%;
  left:9%;
  z-index: 20;
  text-align:center;
}
.personNeed{
  font-size:11px;
  margin-left:5px;
}
.black_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: #000;
  opacity: 0.6;
}

.popUserImage {
  width: 60px;
  height: 60px;
  background: pink;
  border-radius: 50%;
}

.waitUserImage{
  width: 60px;
  height: 60px;
  background:#fff;
  border-radius: 50%;
  border:1px dashed #666;
  margin-left:20px;

}
.goButton {
  width: 80%;
  height: 36px;
  line-height: 36px;
  margin-top: 16px;
  background: red;
  color: #fff;
  font-size: 14px;
}
.firstuser{
  padding:2px 4px;
  position:absolute;
  top:0;left:0;
  border-radius:8px;
  background:#988250;
  border:1px solid #988250;
  font-size:12px;

}
.imageWrap{
  text-align:center;
  margin-top:20px;
}
.firstWrap{
  position:relative;
  display:inline-block
}
.unit_num{
  margin-left:16rpx;
  font-size:28rpx;
  color:#666;
  line-height:60rpx;
}
/**详情展示的属性**/
.goods_attr{
  padding:20rpx;
  background:#fff;
  font-size:26rpx;
  color:#666;
  margin-top:20rpx;
}
.goods_attr > view > view{
  margin-top:20rpx;
}
.goods_attr text{
  color:#000;
}
.om_attr{
  font-size:26rpx;
  color:#FF7E00;
}
.om_attr text{
  font-size:32rpx;
}
.ot_attr{
  font-size:26rpx;
  color:#FF7E00;
  margin-left:10rpx;
}
.ot_attr text{
  font-size:32rpx;
}
/*新的底部加入购物车*/
.active_classify {
  background: #fa6a85 !important;
  color: #fff !important;
}
.goods_sku{
  margin-top:20rpx;
  margin-left:40rpx;
}
.goods_sku view{
  margin-top:10rpx;
}
.goods_sku text{
  float: left;
  padding:10rpx 20rpx;
  color: #333;
  background: #ececec;
  font-size: 24rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}
.goods_sku label{
  font-size:28rpx;
}
.shopcart_b{
  z-index:100;
  background:#fff;
  left:0;
  right:0;
  position:fixed;
  bottom:0;
}
.shop_top{
  position:relative;
  /*min-height:100rpx;*/
}
.shop_top image{
  border:1rpx solid #ccc;
  width:160rpx;
  height:160rpx;
  position:absolute;
  top:-80rpx;
  margin-left:40rpx;
}
.shop_top .shop_goodsInfo{
  padding-top:10rpx;
  margin-left:250rpx;
  font-size:26rpx;
  color:#666;
}
.shop_top .shop_goodsInfo label{
  color: #353535;
}
.shop_top .shop_goodsInfo view{
  padding:6rpx 0;
  font-size:28rpx;
}
.shop_goodsInfo view text{
  color:#FF7E00;
}
.goods_stock{
  margin:20rpx 0;
  font-size:28rpx;
  color:#353535;
  margin-left:40rpx;
}
.shopcart_num{
  font-size:28rpx;
  color:#353535;
  margin-bottom:40rpx;
  margin-top:30rpx;
}
.shop_om{
  margin-left:140rpx;
  margin-bottom:20rpx;
}
.shop_om text{
  width:100rpx;
  float:left;
}
.shop_om input{
  float:left;
  border:1rpx solid #ccc;
}
.shop_om{
  margin-left:140rpx;
  margin-bottom:20rpx;
}
.add_toCart{
  padding:24rpx 0;
  background:#FF7E00;
  color:#fff;
  text-align:center;
  font-size:30rpx;
}
.numBox{
  height:60rpx;
  border:1rpx solid #ddd;
  padding-left:20rpx;
  width:80rpx;
}
.mini_buy{
  margin:20rpx 0 0 0;
  font-size:28rpx;
  color:#353535;
  margin-left:40rpx;
}
.minus_num{
  height:60rpx;
  border:1rpx solid #ccc;
  border-right:none;
  line-height:60rpx;
  width:60rpx;
  text-align:center;
  font-size:40rpx;
  float:left;
}
.plus_num{
  height:60rpx;
  border:1rpx solid #ccc;
  border-left:none;
  line-height:60rpx;
  width:60rpx;
  text-align:center;
  font-size:40rpx;
  float:left;
}