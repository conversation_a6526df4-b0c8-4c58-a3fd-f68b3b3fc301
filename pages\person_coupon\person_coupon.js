var app = getApp()
var TimeUtil = require('../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cardList: [],
    isFromBack: false,
    index: 1,
    couponBox: app.imageUrl + 'couponBox.png',
    hongbaoImg: app.imageUrl + 'hongbaoImg.png',
    discountImg: app.imageUrl + 'discountImg.png',
    downArrow_grey: app.imageUrl + 'downArrow_grey.png',
    upArrow_grey: app.imageUrl + 'upArrow_grey.png',
    invalidCoupon: app.imageUrl + 'invalidCoupon.png',
    cardShare: app.imageUrl + 'cardShare.png',
    wechatShareImg: app.imageUrl + 'wechatShareImg.png',
    shareIsShow: true,   //显示分享
    sCardId: '',
    sCardNo: ''
  },
  selectCardTypeBindTap: function (e) {
    var index = e.currentTarget.dataset.index;
    this.setData({
      index: index
    });
    this.queryMyHaveCard(index);
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrl())), 1);
      return;
    } else {
      this.queryMyHaveCard(1);
    }
  },
  nowUseCardBindTap: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  goCouperBind: function () {
    app.navigateToPage('/pages/couponCenter/couponCenter');
  },
  goToIndexBindTap: function (e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    app.navigateToPage('/pages/verifyCard/verifyCard?id=' + id);
  },
  queryMyHaveCard: function (index) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/retailCoupon/queryUserCardPackage',
      data: {
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "companyId":app.getExtCompanyId(),
        "storeId": app.getExtStoreId(),
        "cardType": index
      },
      success: function (res) {
        var cardList = res.data.returnList;
        for (var i = 0; i < cardList.length; i++) {
          cardList[i]['ruleSwitch'] = false;
          cardList[i].receiveTime = TimeUtil.getSmpFormatDateByLong(cardList[i].receiveTime, true).substring(0, 10).replace(/-/g, "."); 
        }
        that.setData({
          cardList: cardList
        });
      }
    })
  },
  // 点击使用规则
  ruleSwitchFun: function (e) {
    var that = this
    var StoreCardListData = this.data.cardList;
    var ruleSwitchData = StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch;
    if (ruleSwitchData == false) {
      if (typeof (StoreCardListData[e.currentTarget.dataset.idx].rule) == "undefined") {
        wx.request({
          header: {
            'content-type': 'application/x-www-form-urlencoded' // 默认值
          },
          method: "POST",
          url: app.projectName + '/retailCoupon/queryCardUseRule',
          data: {
            "cardId": e.currentTarget.dataset.cardid,
            "storeId": app.getExtStoreId(),
            "companyId":app.getExtCompanyId(),
          },
          success: function (res) {
            StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch = true;

            StoreCardListData[e.currentTarget.dataset.idx].rule = res.data.cardRule;
            that.setData({
              cardList: StoreCardListData
            })
          }
        })
      } else {
        StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch = true;
        that.setData({
          cardList: StoreCardListData
        })
      }

    } else {
      StoreCardListData[e.currentTarget.dataset.idx].ruleSwitch = false;
      that.setData({
        cardList: StoreCardListData
      })
    }

  },
  updateShareCard: function (cardId, cardNo) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/newCardShare/updateShareCard',
      data: {
        "cardId": cardId,
        "cardNo": cardNo,
        "shareUserId": app.getUserId(),
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          that.queryMyHaveCard(0);
        }
      }
    })
  },
  /**
    * 用户分享卡券
    */
  onShareAppMessage: function (e) {
    var that = this;
    that.setData({
      shareIsShow: true
    })
    if (e.from === 'button') {
      var cardId = e.target.dataset.id;
      var cardNo = e.target.dataset.no;
      that.updateShareCard(cardId, cardNo);
      return {
        title: '拼手速抢券啦',
        path: '/pages/shareCoupon/shareCoupon?cardId=' + cardId + '&shareUserId=' + app.getUserId(),
        imageUrl: that.data.cardShare,
        success: function (res) { },
        fail: function (res) { }
      }
    }
  },
  goShareTap: function (e) {
    this.setData({
      sCardId: e.currentTarget.dataset.id,
      sCardNo: e.currentTarget.dataset.no,
      shareIsShow: false
    })
  },
  closeShareBindTap: function () {
    this.setData({
      shareIsShow: true
    })
  }
})