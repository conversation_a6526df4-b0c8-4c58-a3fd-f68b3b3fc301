var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    logisticsList: [],
    logisticNo: "",
    logisticName: "",
    receiveAddress: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    wx.showLoading({
      title: '正在加载数据。。。',
      mask: true
    })
    var logisticNo = options.logisticNo;
    var logisticName = options.logisticName;
    var receivePhoneNum = options.receivePhoneNum;
    var receiveAddress = decodeURIComponent(decodeURIComponent(options.receiveAddress));
    that.setData({
      logisticNo: logisticNo == "undefined" ? "暂无" : logisticNo,
      logisticName: logisticName == "undefined" ? "暂无" : logisticName,
      receiveAddress: receiveAddress
    });
    if (logisticNo == "undefined") {
      wx.hideLoading();
      return;
    } else {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: 'https://www.cn2b2c.com/applet.message/template/getLogisticsMessage',
        data: {
          "logisticsNo": logisticNo,
          "logisticsName": logisticName,
          "receivePhoneNum":receivePhoneNum
        },
        success: function (res) {
          wx.hideLoading();
          var logisticsList = res.data.logisticsList;
          if (logisticsList != null && logisticsList.length > 0) {
            that.setData({
              logisticsList: logisticsList
            });
          } else {
            app.showModal({
              content: '暂无物流信息'
            });
          }
        }
      })
    }
  }
})