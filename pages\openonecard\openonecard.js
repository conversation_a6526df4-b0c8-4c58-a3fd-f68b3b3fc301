const app = getApp();
const myslider = require('../../utils/yxxrui.slider.js');
const popup = require('../popupTemplate/popupTemplate.js')
const http = require('../../utils/http');
Page({
  data: {
    indicatorDots: false,
    autoplay: false,
    interval: 5000,
    duration: 5000,
    curPage: 0,
    x: 0,
    borderColor: "#E3E3E3",
    background: "#FFFFFF",
    length: 4,//套餐数量
    mealHeight: 0,//对应套餐高度
    vipList: [],
    planList: [],
    selectPlanId: 0,
    selectTypeId: 0,
    userName: "",
    telephone: "",
    sex: "1",
    cardRule: "",//会员卡使用规则
    vip: app.imageUrl + 'openonecard_vip.png',
  },
  onLoad: function () {
    var that = this;
    that.setData({
      userName: app.getLoginName(),
      telephone: app.getTelephone()
    })
    that.queryWechatAppletVipCard();
  },
  /**
   * 查询商家会员卡配置信息
   */
  queryWechatAppletVipCard: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId()
      },
      url: app.projectName + '/vipCard/queryWechatAppletVipCard',
      success: function (res) {
        var vipList = res.data.vipList;
        if (vipList != null && vipList.length > 0) {
          var length = that.data.length;
          myslider.initMySlider({
            that: that,
            datas: vipList,
            autoRun: false,
            blankWidth: 12,
            newImgWidth: 18,
            interval: 1500,
            duration: 200,
            direction: 'left',
            startSlide: function (curPage) {

            },
            endSlide: function (curPage) {
              if (that.data.curPage != curPage) {
                var vipList = that.data.vipList;
                if (vipList != null && vipList.length > 0) {
                  var planId = vipList[curPage].id;
                  that.queryWechatAppletVipCardPlanByCardId(planId);
                  that.setData({
                    curPage: curPage,
                    selectPlanId: 0,
                    selectTypeId: planId,
                    cardRule: vipList[curPage].rule
                  })
                }
              }
            }
          });

          //如果获取的套餐数量>3
          if (length > 3) {
            that.setData({
              mealHeight: length / 3 * 210,//获取的套餐数量除3*203即可   
            })
          }
          that.queryWechatAppletVipCardPlanByCardId(vipList[1].id);
          that.setData({
            selectTypeId: vipList[1].id,
            vipList: vipList,
            cardRule: vipList[1].rule
          })
        }
      }
    })
  },
  queryWechatAppletVipCardPlanByCardId: function (planId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "planId": planId
      },
      url: app.projectName + '/vipCard/queryWechatAppletVipCardPlanByCardId',
      success: function (res) {
        var planList = res.data.planList;
        that.setData({
          planList: planList
        })
      }
    })
  },
  checkMeal: function (e) {
    var that = this;
    var index = e.currentTarget.dataset.index;
    var planList = that.data.planList;
    for (var i = 0; i < planList.length; i++) {
      if (i == index) {
        planList[i].select = true;
      } else {
        planList[i].select = false;
      }
    }
    that.setData({
      selectPlanId: planList[index].id,
      planList: planList
    })
  },
  /**
   * 开通会员卡
   */
  openVipCard: function () {
    var that = this;
    var planList = that.data.planList;
    if (planList != null && planList.length > 0) {
      var isSelect = false;
      for (var i = 0; i < planList.length; i++) {
        if (planList[i].select) {
          isSelect = true;
          break;
        }
      }
      if (!isSelect) {
        wx.showToast({
          title: '请选择一种套餐方案',
          icon: 'none',
          duration: 1000,
          mask: true
        })
        return false;
      }
    }
    var userName = that.data.userName.replace(/\s+/g, '');
    if (userName.length <= 0) {
      wx.showToast({
        title: '会员姓名不能为空',
        icon: 'none',
        duration: 1000,
        mask: true
      })
      return false;
    }
    var telephone = that.data.telephone;
    var sex = that.data.sex;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/vipCard/newUserOpenVipCard',
      data: {
        "sex": sex,
        "telephone": telephone,
        "userName": userName,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "openId": app.getOpenId(),
        "companyId": app.getExtCompanyId(),
        "id": that.data.selectTypeId,
        "planId": that.data.selectPlanId,
        "storeId": app.getExtStoreId()
      },
      success: function (res) {
        wx.hideLoading();
        var flag = res.data.flag;
        var message = res.data.returnMessage;
        var code = res.data.code;
        if (flag) {
          if (code == 1) {//收费
            var param = res.data;
            wx.requestPayment({
              'timeStamp': param.timeStamp,
              'nonceStr': param.nonceStr,
              'package': param.package,
              'signType': param.signType,
              'paySign': param.paySign,
              success: function (res) {
                that.checkReward(3)
              },
              fail: function (res) {
                wx.showToast({
                  title: "取消支付",
                  icon: 'none',
                  duration: 1000,
                  mask: true
                })
              }
            })
          } else if (code == 2) {//免费
            that.checkReward(3)
          } else {
            wx.showToast({
              title: "开通会员卡失败，请稍后在试",
              icon: 'none',
              duration: 1000,
              mask: true
            })
          }
        } else {
          wx.showToast({
            title: message,
            icon: "none",
            mask: true
          })
        }
      }
    })
  },
  //查询是否有奖励
  checkReward: function (eventType) {
    var that = this;
    http.post({
      urlName: 'activity',
      url: 'config/loadConfigList',
      showLoading: false,
      data: {
        eventType: 3,
        minMoney: 0,
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName: app.getLoginName()
      },
      success: (res) => {
        if (res.length > 0) {
          for (var i = 0; i < res.length; i++) {
            res[i]["checked"] = true;
            if (res[i].rightsType == 4 || res[i].rightsType == 5 || res[i].rightsType == 6) {
              that.toUser(res[i].configId);
            }
          }
          that.setData({
            rewardList: res,
            result: res
          })
          that.reward();
        } else {
          wx.showToast({
            title: "开通会员卡成功",
            icon: 'success',
            duration: 2000,
            mask: true,
            success: function () {
            }
          })
        }

      }
    })
    /*http.post({
      urlName: 'activity',
      url: 'config/loadConfig',
      showLoading: false,
      data: {
        eventType:eventType,
        merchantId: app.getExtCompanyId(),
        userId: app.getUserId(),
        userName:app.getLoginName()
      },
      success: (res) => {
        if(res.length>0){
          that.setData({
            rewardList:res
          })    
          that.reward();
        }else{
           wx.showToast({
                  title: "开通会员卡成功",
                  icon: 'success',
                  duration: 1000,
                  mask: true,
                  success: function () {
                    setTimeout(function () {
                      app.turnBack();
                    }, 1000);
                  }
                })
        }
            
      }
    })*/
  },
  //调用奖励弹窗
  reward: function () {
    var that = this;
    that.setData({
      which: "reward"
    })
    popup.animationEvents(that, 0, true);
  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
  userNameBindInput: function (e) {
    this.setData({
      userName: e.detail.value
    })
  },
  telephoneBindInput: function (e) {
    this.setData({
      telephone: e.detail.value
    })
  },
  sexBindChange: function (e) {
    this.setData({
      sex: e.detail.value
    })
  },
  /*给用户发放优惠券等*/
  toUser: function (configId) {
    http.post({
      urlName: 'activity',
      url: 'config/addConfigPrize',
      showLoading: false,
      data: {
        configId: configId,
        userId: app.getUserId(),
        userName: app.getLoginName() == "" ? "匿名" : app.getLoginName()
      },
      success: (res) => {
        if (res.errorcode == '1000') {
          console.log("获取成功！")
        }
      }
    })
  },
  gocjBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/turntableActivity/turntableActivity?gameId=' + e.currentTarget.dataset.gameid);
  },
  goSignBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/sign/sign?configId=' + configId);
  },
  goWordBindTap: function (e) {
    var that = this;
    var configId = e.currentTarget.dataset.configid;
    var resData = that.data.result;
    for (var i = 0; i < resData.length; i++) {
      if (resData[i].configId == configId) {
        if (resData[i]["checked"] == false) {
          return;
        }
        else {
          resData[i]["checked"] = false;
        }
      }
    }
    that.setData({
      result: resData,
      rewardList: resData
    })
    popup.animationEvents(that, 0, true);
    app.navigateToPage('/pages/collect/collect?configId=' + configId);
  }
})
