//index.js
//获取应用实例
var WxParse = require('../../components/wxParse/wxParse.js');
const popup = require('../popupTemplate/popupTemplate.js');
const app = getApp()
var commodityId; //商品ID
var storeId; //店铺ID
Page({
  data: {
    autoplay: false, //是否自动切换
    interval: 2000, //自动切换时间间隔
    duration: 500, //滑动动画时长
    circular: true, //是否采用衔接滑动
    indicatorDots: true, //是否显示面板指示点
    indicatorColor: "rgba(0, 0, 0, .3)", //指示点颜色
    indicatorActiveColor: "#fa6a85", //当前选中的指示点颜色
    array: [{
      mode: 'aspectFill',
    }], 
    src: app.imageUrl + 'banner3.png',
    src2: app.imageUrl + 'banner1.png',
    shopcart: app.imageUrl + 'shopDetails/shopcart_icon.png',
    online: app.imageUrl + 'shopDetails/online.png',
    home_icon: app.imageUrl + 'shopDetails/home_icon.png',
    addgoods: app.imageUrl + 'banner1.png',
    personal_more: app.imageUrl + 'accountManager/personal_more.png',
    addToShoppingCartHidden: true, //添加购物车
    joinOrBuy: "加入购物车",
    buyCount: 1, //购买数量
    buyOmCount: 0, //购买大单位数量
    activityHidden: true,
    goodsDetail: [],
    showskuAllAttrList: [],
    showGoodsPrice: "0",
    skuId: "",
    item: app.imageUrl + 'activity/activity.png',
    countDownDay: 0,
    countDownHour: 0,
    countDownMinute: 0,
    countDownSecond: 0,
    intervarID: null,
    imgheights: [],
    current: 0,
    imgwidth: 750,
    serverHidden: true,
    groupMoreHidden: true,
    groupUserHidden: true,
    skuRegionPriceList: [], //区段价格集合
    unitRegionPriceList: [],
    showViewGoodsPrice: 0,
    joinPromotion: 1, //默认不参团
    groupBuyUserId: 0,
    share: app.imageUrl + 'share.png',
    online_service: app.imageUrl + 'online_service.png',
    windowWidth: 600,
    posterHeight: 900,
    shareBlackBgHidden: true,
    posterHidden: true,
    shareShowHidden: true,
    localPostQrcodeImage: "",
    localImageMain: "",
    localStoreImage: "",
    userHeadImage: "",
    fullCut: 0,
    indexIcon: app.imageUrl + 'accountManager/home.png',
    goShare: app.imageUrl + 'share.png',
    dbbLogo: app.imageUrl + 'dbb_logo.jpg',
    isOnlineService: false,
    overallStock: 1,
    isDistribution: false,
    salesVolume: 2, //是否展示销量 1：展示 2：不展示
    promotionOm: 0,
    promotionOt: 0,
    showGoodsRangePrice: '',
    cardHidden: true,
    goodTypeId: '',
    recommendUserId: "",//推荐人Id,
    d_way:[]
  },

  /*分享二维码海报*/
  initCanvas: function (returnUrl) {
    const query = wx.createSelectorQuery()
    console.log(query);
    query.select('#canvas_box')
      .fields({
        id: true,
        node: true,
        size: true
      })
      .exec(this.init.bind(this));

  },
  init: function (res) {
    var that = this;
    that.setData({
      posterHidden: false,
      shareBlackBgHidden: false
    })
    var userName = "";
    if (app.getLoginName() == undefined) {
      userName = app.getExtStoreName();
    } else {
      userName = app.getLoginName();
    }

    const canvas = res[0].node;
    const ctx = canvas.getContext('2d');
    const dpr = wx.getSystemInfoSync().pixelRatio;
    //新接口需显示设置画布宽高；
    canvas.width = 300 * dpr
    canvas.height = 450 * dpr;
    this.setData({
      canvas: canvas
    });

    ctx.scale(dpr, dpr);
    
    ctx.fillStyle = 'white';//填充白色
    ctx.fillRect(0, 0, 300, 450);//画出矩形白色背景
    ctx.font = '15px';
    ctx.fillStyle = '#000';
    ctx.fillText(userName, 90, 40);
    ctx.font = '14px';
    ctx.fillStyle = '#666';
    ctx.fillText('给你推荐了一个好东西', 90, 60);
  

    

    ctx.font = '20px';
    ctx.fillStyle = '#FF7E00';
    ctx.fillText('￥' + this.data.showGoodsPrice + '/' + this.data.goodsOtUnit, 25, 370);
    if (this.data.cutPrice > 0 && this.data.cutPrice - this.data.showGoodsPrice > 0) {
      var tmp_width = 80 + ctx.measureText(this.data.showGoodsPrice).width;
      var end_width = ctx.measureText(this.data.cutPrice).width;
      ctx.font = '15px';
      ctx.fillStyle = '#666';
      ctx.fillText('￥' + this.data.cutPrice, tmp_width, 370);
      ctx.beginPath();
      ctx.moveTo(tmp_width, 365);
      ctx.lineTo(tmp_width + end_width + 10, 365);
      ctx.lineWidth = 1;
      ctx.strokeStyle = '#666';
      ctx.stroke();
    }
    ctx.font = '14px';
    ctx.fillStyle = '#666';
    ctx.fillText(this.data.goodsDetail.commodityName, 25, 390, 160, 300);
    ctx.stroke();
    let img = canvas.createImage();
    img.src = this.data.localStoreImage;
    img.onload = () => {
      ctx.drawImage(img, 25, 20, 50, 50,); //画用户头像
      let img1 = canvas.createImage();
      img1.onload = () => {
        console.log("1");
        ctx.drawImage(img1, 25, 80, 250, 250); //商品图片
        let img2 = canvas.createImage();
        img2.onload = () => {
          console.log("2");
          ctx.drawImage(img2, 200, 350, 80, 80); //分享二维码
          that.save();
        }
        img2.src = this.data.localPostQrcodeImage;
      }
      img1.src = this.data.localImageMain;
    }
  },
  save: function () {
    var that = this;
    setTimeout(() =>
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: that.data.imageWidth,
        height: that.data.imageHeight,
        destWidth: that.data.imageWidth,
        destHeight: that.data.imageHeight,
        canvas: this.data.canvas,
        fileType: "jpg",
        success: function (res) {
          that.data.saveUrl = res.tempFilePath //保存临时模板文件路径
          that.setData({
            posterHidden: false,
            shareBlackBgHidden: false,
            shareShowHidden: true
          });
        },
        fail: function (res) {
          wx.showModal({
            title: '提示',
            content: res.errMsg
          })
          return;
        }
      })
    ,500)
    wx.hideLoading();
  },
  savePosterToPhoneBindTap: function () {
    var that = this;
    if (!wx.saveImageToPhotosAlbum) {
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
      })
      return;
    }
    wx.getSetting({
      success(res) {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          // 接口调用询问  
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              that.saveImageToPhotosAlbum1();
            },
            fail() {
              wx.showModal({
                title: '提示',
                content: '检测到您保存到相册权限未开启，是否开启?',
                success: function (res) {
                  if (res.confirm) {
                    wx.openSetting({
                      success: function (data) {
                      },
                      fail: function (data) {
                      }
                    });
                  }
                }
              })
            }
          })
        } else {
          that.saveImageToPhotosAlbum1();
        }
      },
      fail(res) {
      }
    })
  },
  saveImageToPhotosAlbum1: function () {
    var that = this;
    if (that.data.saveUrl == null || that.data.saveUrl == "") {
      return;
    }
    wx.showLoading({
      title: '下载中...'
    })
    wx.saveImageToPhotosAlbum({
      filePath: that.data.saveUrl,
      success(result) {
        wx.hideLoading();
        wx.showToast({
          title: '已保存至相册',
          icon: 'none',
        })
      },
      fail(result) {
        wx.hideLoading()
        wx.showToast({
          title: '下载失败',
          icon: 'none',
        })
      }
    })
    that.setData({
      posterHidden: true,
      shareBlackBgHidden: true
    })
  },
  cancelButtonBindTap: function () {
    this.setData({
      posterHidden: true,
      shareBlackBgHidden: true
    })
  },
  /**
   * 获取商品详情海报
   */
  getPosterBindTap: function (e) {
    var that = this;
    //隐藏弹窗模板
    that.hiddenFloatView();
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    /*if (app.getTelephone() == null || app.getTelephone().length == 0) {
      app.reLaunchToPage("/pages/changeProfile/changeProfile");
      return;
    }*/
    wx.showLoading({
      title: '正在加载，请稍后',
      mask: true
    })
    that.setData({
      shareBlackBgHidden: true,
      shareShowHidden: true
    });
    var share = e.currentTarget.dataset.share;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "isDistribution": share,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "commodityId": commodityId,
        //"recommendUserId": app.getClientOpenId(),
        "type": 1
      },
      url: app.projectName + '/newAppletQR/getWechatAppletPersonalPosterQRcode',
      success: function (res) {
        var returnUrl = res.data.returnUrl;
        var code = res.data.code;
        var msg = res.data.msg;
        if(code == 1){
          if (returnUrl != "") {
            wx.getImageInfo({
              src: returnUrl, //服务器返回的图片地址
              success: function (res) {
                //res.path是网络图片的本地地址
                let qrcodePath = res.path;
                that.setData({
                  localPostQrcodeImage: qrcodePath
                })
                var commodityMainPic = that.data.goodsDetail.commodityMainPic;
                if (commodityMainPic.indexOf("https") == -1) {
                  commodityMainPic = commodityMainPic.replace("http", "https");
                }
                wx.getImageInfo({
                  src: commodityMainPic, //服务器返回的图片地址
                  success: function (res) {
                    //res.path是网络图片的本地地址
                    let imagePath = res.path;
                    that.setData({
                      localImageMain: imagePath
                    })
                    if (app.getExtStoreImage() != null && app.getExtStoreImage() != "") {
                      wx.getImageInfo({
                        src: app.getExtStoreImage(), //服务器返回的图片地址
                        success: function (res) {
                          //res.path是网络图片的本地地址
                          let Path = res.path;
                          that.setData({
                            localStoreImage: Path
                          })
                          that.initCanvas();
                        },
                        fail: function (res) {
                          //失败回调
                          wx.hideLoading();
                        }
                      });
  
                    } else {
                      that.setData({
                        localStoreImage: qrcodePath
                      })
                      that.initCanvas();
                    }
                  },
                  fail: function (res) {
                    //失败回调
                    wx.hideLoading();
                  }
                });
              },
              fail: function (res) {
                //失败回调
                wx.hideLoading();
              }
            });
          }
        }
        else{
          wx.showToast({
            title: res.data.msg,
            icon: "none",
            duration: 2000
          })
        }

      },
      fail: function (res) {
        wx.hideLoading();
      }
    })
  },
  /**
   * 分享按钮事件
   */
  shareGoodsBind: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    } else {
      var that = this;
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        url: app.projectName + '/newDistrution/queryUserIsDistribution',
        success: function (res) {
          var flag = res.data.flag;
          if (flag) {
            that.share();
          } else {
            wx.showToast({
              title: '抱歉，您没有分享权限',
              icon: 'none',
            })
          }
          that.setData({
            isDistribution: flag
          })
        }
      })
    }
  },
  //调用分享弹窗
  share: function () {
    var that = this;
    popup.animationEvents(that, 0, true);

  },
  //隐藏弹窗
  hiddenFloatView: function (e) {
    var that = this;
    popup.animationEvents(that, 200, false);
  },
  nono: function () {

  },
  /*到首页*/
  goToHomeBindTap: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
   * 取消分享事件
   */
  cancelShareBindTap: function () {
    this.setData({
      shareBlackBgHidden: true,
      shareShowHidden: true
    });
  },
  chooseAttrBind: function () {
    this.setData({
      addToShoppingCartHidden: false
    })
  },
  openServerBindTap: function () {
    this.setData({
      serverHidden: false
    })
  },
  hideServerBindTap: function () {
    this.setData({
      serverHidden: true
    })
  },
  closeUserGroupBindTap: function () {
    this.setData({
      groupMoreHidden: true
    })
  },
  imageLoad: function (e) {
    //获取图片真实宽度  
    var imgwidth = e.detail.width,
      imgheight = e.detail.height,
      //宽高比  
      ratio = imgwidth / imgheight;
    //计算的高度值  
    var viewHeight = 750 / ratio;
    var imgheight = viewHeight
    var imgheights = this.data.imgheights
    //把每一张图片的高度记录到数组里  
    imgheights.push(imgheight)
    this.setData({
      imgheights: imgheights,
      current: 0
    })
  },
  //事件处理函数
  bindViewTap: function () {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  /**
   * 初始化加载
   */
  onLoad: function (options) {
    var that = this;
    var recommendUserId = options.recommendUserId == undefined ? 0 : options.recommendUserId;
    const scene = decodeURIComponent(options.scene);
    commodityId = options.goodsId;
    if (commodityId == "undefined" || commodityId == null || commodityId == "") {
      if (scene != "undefined" && scene != null && scene != "") {
        commodityId = scene.substring(0, scene.indexOf("@"));
      } else {
        commodityId = options.goodsId;
      }
    }
    app.init_getExtMessage().then(res => {
      storeId = res.storeId;
      that.setData({
        recommendUserId: recommendUserId?recommendUserId:app.getClientOpenId(),
        tmpCompany: res.companyId
      })
      that.queryStoreInfoByStoreId(storeId);
      that.dataInitial(res.storeId, res.companyId);
    });
  },
  getCard: function (goodTypeId) {   /* 获取当前商品的优惠券 */
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      data: {
        "companyId": app.getExtCompanyId(),
        "goodId": commodityId,
        "goodTypeId": goodTypeId,
        "userId": app.getUserId()
      },
      url: app.projectName + '/newPushCard/pushCardForGoodsDetail',
      success: function (res) {
        that.setData({
          storeCard: res.data.returnList
        })
      }
    })
  },
  cardHiddenBindTap: function () {
    this.setData({
      cardHidden: true
    });
  },
  cardshowBindTap: function () {
    this.setData({
      cardHidden: false
    });
  },
  /**
 * 领取券
 */
  goToUserCardBindTap: function (e) {
    var that = this;
    if (!e.currentTarget.dataset.have) {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/retailCoupon/addUserCard',
        data: {
          "cardId": e.currentTarget.dataset.cardid,
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "userName": app.getLoginName(),
          "companyId": app.getExtCompanyId(),
          "openId": app.getOpenId()
        },
        success: function (res) {
          wx.hideLoading();
          var flag = res.data.flag;
          if (flag) {
            wx.showToast({
              title: '领取成功',
              duration: 2000
            })
            that.getCard(that.data.goodTypeId)
          } else {
            wx.showToast({
              title: res.data.message,
              icon: "none",
              duration: 2000
            })
          }
        }
      })
    }

  },
  /**
   * 获取购物车商品数量
   */
  countRetailCartTotal: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      that.setData({
        shopCartNum: 0
      })
    } else {
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        url: app.projectName + '/applet/shopCart/countRetailCartTotal',
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId()
        },
        success: function (res) {
          that.setData({
            shopCartNum: res.data.shopCartNum
          })
        }
      })
    }
  },
  queryStoreInfoByStoreId: function (storeId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/queryStoreInfoByStoreId',
      data: {
        "storeId": storeId
      },
      success: function (res) {
        var storeName = res.data.storeName;
        var storeImage = res.data.storeImage;
        var storePhone = res.data.storePhone;
        var storeInfo = {
          "storeName": storeName,
          "storeImage": storeImage,
          "storePhone": storePhone
        };
        wx.setNavigationBarTitle({
          title: storeName
        });
        wx.removeStorageSync("storeInfoStorageKey");
        app.setStorage({
          key: 'storeInfoStorageKey',
          data: storeInfo
        });
        that.setData({
          storeName: storeName,
          storeImage: storeImage
        })
      }
    })
  },
  previewImage: function (e) {
    var that = this;
    var imageUrl = e.currentTarget.dataset.src;
    var imageList = [];
    imageList.push(imageUrl);
    wx.previewImage({
      current: imageUrl,
      urls: imageList
    })
  },
  dataInitial: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/appletGoodsDetail',
      data: {
        "commodityId": commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      success: function (res) {
        that.getCard(res.data.list.commodityCategoryId)
        that.setData({
          goodTypeId: res.data.list.commodityCategoryId
        })
        var participatePromotion = res.data.list.participatePromotion;
        if (participatePromotion) {
          var retailPromotionList = res.data.list.retailPromotionList;
          var promotionEndDate = "";
          for (var i = 0; i < retailPromotionList.length; i++) {
            promotionEndDate = retailPromotionList[i].promotionEndDate;
          }
          if (promotionEndDate != null && promotionEndDate != "") {
            that.count_down(promotionEndDate);
          }
        }
        var participatePromotion = res.data.list.participatePromotion;
        var skuList = res.data.list.skuList;
        var skuRegionPriceList = [];
        var unitRegionPriceList = [];
        if (participatePromotion) {
          if (skuList != null && skuList.length > 0) {
            var flag = false;
            for (var j = 0; j < skuList.length; j++) {
              if (skuList[j].regionPriceList != null && skuList[j].regionPriceList.length > 0) {
                flag = true;
                break;
              }
            }
            if (!flag) {
              unitRegionPriceList = res.data.list.unitList;
            } else {
              skuRegionPriceList = res.data.list.skuList;
            }
          } else {
            unitRegionPriceList = res.data.list.unitList;
          }
        }
        var showskuAllAttrList = res.data.skuList;
        var selectSkuStr = "";
        if (showskuAllAttrList != null && showskuAllAttrList.length > 0) {
          for (var k = 0; k < showskuAllAttrList.length; k++) {
            selectSkuStr += showskuAllAttrList[k].skuAttrName + "、";
          }
          selectSkuStr = selectSkuStr.substring(0, selectSkuStr.length - 1);
        }
        var groupList = res.data.groupList;
        if (groupList != null && groupList.length > 0) {
          for (var i = 0; i < groupList.length; i++) {
            groupList[i].userName = groupList[i].userName.substring(0, 1) + "*****" + groupList[i].userName.substring(groupList[i].userName.length - 1);
          }
        }
        //计算有sku属性时显示价格范围
        var showGoodsRangePrice;
        if (res.data.list.skuList.length > 0) {
          var max = res.data.list.skuList[0].skuPrice;
          for (var i = 1; i < res.data.list.skuList.length; i++) {
            if (Number(max) < Number(res.data.list.skuList[i].skuPrice)) max = res.data.list.skuList[i].skuPrice;
          }
          var min = res.data.list.skuList[0].skuPrice;
          for (var i = 1; i < res.data.list.skuList.length; i++) {
            if (Number(min) > Number(res.data.list.skuList[i].skuPrice)) min = res.data.list.skuList[i].skuPrice;

          }
          if (min == max) {
            showGoodsRangePrice = min;
          } else {
            showGoodsRangePrice = min + '~' + max;
          }

        } else {
          showGoodsRangePrice = res.data.goodsPrice;
        }

        that.setData({
          commodityUnitOtDefault: res.data.commodityUnitOtDefault,
          commodityUnitOmDefault: res.data.commodityUnitOmDefault,
          cutPriceOM: res.data.cutPriceOM,
          goodsOMUnit: res.data.goodsOMUnit,
          commodityVirtualStoreOM: res.data.commodityVirtualStoreOM,
          commodityMultiple: res.data.commodityMultiple,
          commodityVirtualStore: res.data.commodityVirtualStore,
          goodsPriceOM: res.data.goodsPriceOM,
          goodsOtUnit: res.data.goodsOtUnit,
          selectSkuStr: selectSkuStr,
          goodsDetail: res.data.list,
          showskuAllAttrList: res.data.skuList,
          cutPrice: res.data.cutPrice,
          showGoodsPrice: res.data.goodsPrice,
          showGoodsRangePrice: showGoodsRangePrice,
          showViewGoodsPrice: res.data.goodsPrice,
          groupList: groupList,
          skuRegionPriceList: skuRegionPriceList,
          unitRegionPriceList: unitRegionPriceList,
          userHeadImage: app.getExtStoreImage()
        });
        /**临时大小单位活动价**/
        /*if (participatePromotion) {
          var retailPromotionList = res.data.list.retailPromotionList;
          for (var i = 0; i < retailPromotionList.length;i++){
            if (retailPromotionList[i].commodityOmPrice) {
              that.setData({
                goodsPriceOM: retailPromotionList[i].commodityOmPrice
              })
            }
            else if (retailPromotionList[i].commodityOtPrice) {
              that.setData({
                showGoodsPrice: retailPromotionList[i].commodityOtPrice
              })
            }
          }
        }*/
        /**临时大小单位活动价**/
        that.getGoodsDetailEvaluate(storeId, companyId);
        if (res.data.list != null && res.data.list.commodityIntroduce.length > 0) {
          WxParse.wxParse("commodityIntroduce", 'html', res.data.list.commodityIntroduce, that, 5);
        }
        that.getSupplierSetting();
      }
    })
  },
  getGoodsDetailEvaluate: function (storeId, companyId) {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/goods/getGoodsDetailEvaluate',
      data: {
        "commodityId": commodityId,
               "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "storeId": storeId,
        "companyId": companyId
      },
      success: function (res) {
        that.setData({
          salesVolume: res.data.salesVolume,
          overallStock: res.data.overallStock,
          stockBean: res.data.stockBean,
          isOnlineService: res.data.isOnlineService,
          fullCut: res.data.fullCut,
          orderEvaluateList: res.data.orderEvaluateList,
          shopCartNum: res.data.shopCartNum,
        });
        that.countRetailCartTotal();
      }
    })
  },
    /*控制库存时,检查商品是否超过了库存数量*/
    checkStock: function () {
      var that = this;
      var openStock = that.data.stockBean.openStock;
      var overallStock = that.data.overallStock;
      var buyNum = that.data.buyOtNum + (that.data.buyOmNum * that.data.commodityMultiple);
      var commodityVirtualStore = that.data.commodityVirtualStore;
      var flag = true;
      console.log('33333333333'+parseInt(buyNum));
      console.log('33333333333'+parseInt(that.data.commodityMultiple));
      console.log('==========='+parseInt(commodityVirtualStore));
      //if(that.data.commodityMultiple!=0){
        if (openStock || overallStock == 1) {
          //if (parseInt(buyNum) > parseInt(commodityVirtualStore) || that.data.commodityMultiple==0) {
            if (Number.isNaN(parseInt(buyNum, 10)) && parseInt(commodityVirtualStore)<=0) {
            app.showModal({
              title: '提示',
              content: "购买数量超过库存"
            });
            flag = false;
          }
        }
      //}
      
      return flag;
    },
  queryAllEvaluateBindTap: function () {
    app.navigateToPage('/pages/evaluateDetail/evaluateDetail?goodsId=' + commodityId);
  },
  changeSKUBindTap: function (e) {
    var that = this;
    var name = e.target.dataset.name;
    var childname = e.target.dataset.childname;
    var updateSkuList = that.data.showskuAllAttrList;
    for (var i = 0; i < updateSkuList.length; i++) {
      if (name == updateSkuList[i].skuAttrName) {
        for (var j = 0; j < updateSkuList[i].skuAttrValueList.length; j++) {
          if (childname == updateSkuList[i].skuAttrValueList[j].skuAttrName) {
            updateSkuList[i].skuAttrValueList[j].isSelect = true;
          } else {
            updateSkuList[i].skuAttrValueList[j].isSelect = false;
          }
        }
      }
    }
    that.setData({
      showskuAllAttrList: updateSkuList
    });
    that.bachGoodsPrice();
  },
  bachGoodsPrice: function () {
    var that = this;
    that.setData({
      skuId: ''
    })
    var selectSKUArray = [];
    var skuList = that.data.showskuAllAttrList;
    for (var i = 0; i < skuList.length; i++) {
      for (var j = 0; j < skuList[i].skuAttrValueList.length; j++) {
        if (skuList[i].skuAttrValueList[j].isSelect) {
          var skuBean = {};
          skuBean.name = skuList[i].skuAttrName;
          skuBean.value = skuList[i].skuAttrValueList[j].skuAttrName;
          selectSKUArray.push(skuBean);
        }
      }
    }

    var skuGoodsPrice = "";
    var skuGroup = that.data.goodsDetail.skuList;
    for (var i = 0; i < skuGroup.length; i++) {
      var skuAttrList = skuGroup[i].skuAttrList;
      var count = 0;
      for (var j = 0; j < skuAttrList.length; j++) {
        var skuName = skuAttrList[j].skuName;
        var skuValue = skuAttrList[j].skuValue;
        var flag = false;
        for (var z = 0; z < selectSKUArray.length; z++) {
          var skuBean = selectSKUArray[z];
          if (skuName == skuBean.name && skuValue == skuBean.value) {
            flag = true;
            break;
          }
        }
        if (flag) {
          count++;
        } else {
          count--;
        }
      }
      if (count == selectSKUArray.length) {
        if (skuGroup[i].skuPrice != null && skuGroup[i].skuPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPrice;
        }
        if (skuGroup[i].skuPromotionPrice != null && skuGroup[i].skuPromotionPrice != "") {
          skuGoodsPrice = skuGroup[i].skuPromotionPrice;
        }
        if (skuGroup[i].regionPriceList != null && skuGroup[i].regionPriceList.length > 0) {
          var goodsCount = that.data.buyCount + (that.data.buyOmCount * that.data.commodityMultiple);
          for (var k = 0; k < skuGroup[i].regionPriceList.length; k++) {
            var beginRegion = skuGroup[i].regionPriceList[k].beginRegion;
            var endRegion = skuGroup[i].regionPriceList[k].endRegion;
            var commoditySalePrice = skuGroup[i].regionPriceList[k].commoditySalePrice;
            if (goodsCount >= beginRegion && goodsCount <= endRegion) {
              skuGoodsPrice = commoditySalePrice;
              break;
            }
          }
        }
        var selectSkuStr = "";
        for (var m = 0; m < skuGroup[i].skuAttrList.length; m++) {
          selectSkuStr += skuGroup[i].skuAttrList[m].skuName + ":" + skuGroup[i].skuAttrList[m].skuValue + ";";
        }
        selectSkuStr = selectSkuStr.substring(0, selectSkuStr.length - 1);
        that.setData({
          selectSkuStr: selectSkuStr,
          showGoodsPrice: skuGoodsPrice,
          skuId: skuGroup[i].skuId,
          commodityVirtualStore: skuGroup[i].skuInventory
        });
        break;
        
      }
    }
    if (that.data.skuId == '' && selectSKUArray.length == skuList.length && skuList.length != 0) {
      app.showModal({
        title: '提示',
        content: "您选择的分类暂无货"
      });
    }
    //区段价格展示
    var showRegionPriceList = [];
    if (skuGroup != null && skuGroup.length > 0) {
      var flag = false;
      for (var j = 0; j < skuGroup.length; j++) {
        if (skuGroup[j].regionPriceList != null && skuGroup[j].regionPriceList.length > 0) {
          flag = true;
          break;
        }
      }
      if (!flag) { //从商品里面取区段价格
        showRegionPriceList = that.data.goodsDetail.unitList[0].regionPriceList;
      }
    } else {
      showRegionPriceList = that.data.goodsDetail.unitList[0].regionPriceList;
    }
    if (showRegionPriceList != null && showRegionPriceList.length > 0) {
      var goodsCount = that.data.buyCount;
      for (var k = 0; k < showRegionPriceList.length; k++) {
        var beginRegion = showRegionPriceList[k].beginRegion;
        var endRegion = showRegionPriceList[k].endRegion;
        var commoditySalePrice = showRegionPriceList[k].commoditySalePrice;
        if (goodsCount >= beginRegion && goodsCount <= endRegion) {
          skuGoodsPrice = commoditySalePrice;
          break;
        }
      }
      that.setData({
        showGoodsPrice: skuGoodsPrice == "" ? that.data.showViewGoodsPrice : skuGoodsPrice
      });
    }
   
  },
  /**
   * 加入购物车
   */
  showAddToShoppingCart: function () {
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          that.setData({
            addToShoppingCartHidden: false,
            joinOrBuy: "加入购物车"
          });
        }
      }
    })
  },
  /**
   * 隐藏购物车
   */
  hiddeAddToShoppingCart: function () {
    this.setData({
      addToShoppingCartHidden: true,
      joinOrBuy: "加入购物车"
    })
  },
  clickMinusButton: function () {
    var that = this;
    var count = that.data.buyCount;
    if (!(that.data.commodityUnitOmDefault == 1 && that.data.commodityUnitOtDefault == 1)) {
      if (count <= 1) {
        return;
      }
    }
    else {
      if (count < 1) {
        return;
      }
    }
    that.setData({
      'buyCount': count - 1
    });
    that.bachGoodsPrice();
  },
  clickPlusButton: function () {
    var that = this;
    var count = that.data.buyCount;
    that.setData({
      'buyCount': count + 1
    });
    that.bachGoodsPrice();
  },
  inputBuyCount: function (e) {
    var that = this;
    var count = +e.detail.value;
    that.setData({
      'buyCount': +count
    });
    that.bachGoodsPrice();
  },
  clickMinusOmButton: function () {
    var that = this;
    var count = that.data.buyOmCount;
    if (!(that.data.commodityUnitOmDefault == 1 && that.data.commodityUnitOtDefault == 1)) {
      if (count <= 1) {
        return;
      }
    }
    else {
      if (count < 1) {
        return;
      }
    }
    that.setData({
      'buyOmCount': count - 1
    });
    that.bachGoodsPrice();
  },
  clickPlusOmButton: function () {
    var that = this;
    var count = that.data.buyOmCount;
    that.setData({
      'buyOmCount': count + 1
    });
    that.bachGoodsPrice();
  },
  inputBuyOmCount: function (e) {
    var that = this;
    var count = +e.detail.value;
    that.setData({
      'buyOmCount': +count
    });
    that.bachGoodsPrice();
  },
  count_down: function (promotionEndDate) {
    var that = this;
    this.data.intervarID = setInterval(function () {
      var leftTime = promotionEndDate - Date.parse(new Date()); //计算剩余的毫秒数
      var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
      if (days < 10) {
        days = "0" + days
      }
      var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10) + days * 24; //计算剩余的小时
      var minutes = parseInt(leftTime / 1000 / 60 % 60, 10); //计算剩余的分钟
      var seconds = parseInt(leftTime / 1000 % 60, 10); //计算剩余的秒数

      that.setData({
        days: days,
        hours: (hours - days * 24).toString().length == 1 ? '0' + (hours - days * 24) : ((hours - days * 24)),
        minutes: minutes.toString().length == 1 ? '0' + minutes : minutes,
        seconds: seconds.toString().length == 1 ? '0' + seconds : seconds,
        clock: hours + ":" + minutes + ":" + seconds,
      })
      if (days == '00' && hours == '00' && minutes == '00' && seconds == '00') {
        clearInterval(that.data.intervarID);
      }
    }, 1000)
  },
  fill_zero_prefix: function (num) {
    return num < 10 ? "0" + num : num
  },
  addGoodsToShopCart: function () {
    var that = this;
    var goodsDetail = that.data.goodsDetail;
    var unitList = goodsDetail.unitList;
    var commodityMoq = 0;
    var commodityWeightUnit = "";
    var commodityMultiple = 0;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (unitList.length == 1) {
      commodityMoq = goodsDetail.unitList[0].commodityMoq;
      commodityWeightUnit = goodsDetail.unitList[0].commodityWeightUnit;
    } else if (unitList.length == 2) {
      for (var i = 0; i < unitList.length; i++) {
        if (unitList[i].commodityWeightType == "OT") {
          commodityMoq = unitList[i].commodityMoq;
          commodityWeightUnit = unitList[i].commodityWeightUnit;
        } else if (unitList[i].commodityWeightType == "OM") {
          commodityMultiple = unitList[i].commodityMultiple;
        }
      }
    }
    if (parseInt(commodityMoq) > 0) {
      if (that.data.buyCount + (that.data.buyOmCount * commodityMultiple) < parseInt(commodityMoq)) {
        app.showModal({
          title: '提示',
          content: "商品购买数量必须满足" + commodityMoq + commodityWeightUnit + "!"
        });
        return;
      }
    }
    if (that.data.commodityUnitOtDefault != 1) {
      that.setData({
        buyCount: 0
      })
    }
    if (that.data.commodityUnitOmDefault != 1) {
      that.setData({
        buyOmCount: 0
      })
    }
    if (!that.checkStock()) {
      return;
    };
    if (that.data.buyCount == 0 && that.data.buyOmCount == 0) {
      app.showModal({
        title: '提示',
        content: "购物车商品数量不能为0"
      });
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/shopCart/addRetailShppingCart',
      data: {
        "a_num": that.data.buyCount,
        "omNum": that.data.buyOmCount,
        "a_id": commodityId,
        "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
        "loginId": app.getLoginId(),
        "userRole": app.getUserRole(),
        "skuId": that.data.skuId,
        "storeId": app.getExtStoreId(),
        "companyId": app.getExtCompanyId(),
        "recommendUserId": app.recommendData.recommendUserId
      },
      success: function (res) {
        var flag = res.data.flag;
        if (flag) {
          app.showModal({
            title: '提示',
            content: "添加购物车成功"
          });
          var shopCartNum = res.data.shopCartNum;
          that.setData({
            addToShoppingCartHidden: true,
            shopCartNum: shopCartNum
          })
        } else {
          if(res.data.code === -99){
            app.showModal({
              title: '提示',
              content: "登录认证失效，请重新登录”"
            });
            app.turnToPage("/pages/login/login");
          }else{
            app.showModal({
              title: '提示',
              content: "添加购物车失败"
            });
          }          
        }
      }
    })
  },
  addShoppingCartBindTap: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    if (that.data.commodityUnitOtDefault != 1) {
      that.setData({
        buyCount: 0
      })
    }
    if (that.data.commodityUnitOmDefault != 1) {
      that.setData({
        buyOmCount: 0
      })

    }
    if (that.data.buyCount == 0 && that.data.buyOmCount == 0) {
      app.showModal({
        title: '提示',
        content: "请选择数量"
      });
      return;
    }
    if (that.data.joinOrBuy == "加入购物车") {
      if (that.data.showskuAllAttrList != null && that.data.showskuAllAttrList.length > 0) {
        if (that.data.skuId == "" || that.data.skuId == null) {
          app.showModal({
            title: '提示',
            content: "请选择分类"
          });
          return;
        }
      }
      that.addGoodsToShopCart();
    } else if (that.data.joinOrBuy == "立即购买") {
      if (that.data.showskuAllAttrList != null && that.data.showskuAllAttrList.length > 0) {
        if (that.data.skuId == "" || that.data.skuId == null) {
          app.showModal({
            title: '提示',
            content: "请选择分类"
          });
          return;
        }
      }
      if (that.data.groupBuyUserId.length == 0 || that.data.groupBuyUserId == undefined) {
        that.goToBuyGoods();
      } else {

        app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + that.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&isTuanGou=1" + "&recommendUserId=" + that.data.recommendUserId);
      }
    } else if (that.data.joinOrBuy == "单独购买") {
      if (that.data.showskuAllAttrList != null && that.data.showskuAllAttrList.length > 0) {
        if (that.data.skuId == "" || that.data.skuId == null) {
          app.showModal({
            title: '提示',
            content: "请选择分类"
          });
          return;
        }
      }
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + this.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&recommendUserId=" + that.data.recommendUserId);
    } else if (that.data.joinOrBuy == "发起拼团") {
      if (that.data.showskuAllAttrList != null && that.data.showskuAllAttrList.length > 0) {
        if (that.data.skuId == "" || that.data.skuId == null) {
          app.showModal({
            title: '提示',
            content: "请选择分类"
          });
          return;
        }
      }
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + this.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&recommendUserId=" + that.data.recommendUserId);
    }
  },
  goToShopCartBindTap: function () {
    wx.switchTab({
      url: "/pages/shopCart/shopCart"
    });
  },
  goToStoreBindTap: function () {
    wx.switchTab({
      url: "/pages/index/index"
    });
  },
  /**
   * 立即购买
   */
  nowBuyBindTap: function (e) {
    var that = this;
    var groupBuyUserId = e.currentTarget.dataset.id;
    var type = e.currentTarget.dataset.type;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          that.setData({
            addToShoppingCartHidden: false,
            joinOrBuy: "立即购买",
            joinPromotion: type,
            groupBuyUserId: groupBuyUserId == undefined ? "" : groupBuyUserId
          });
        }
      }
    })
  },
  /**
   * 发起拼团
   */
  nowGroupBuyBindTap: function (e) {
    var that = this;
    var groupBuyUserId = e.currentTarget.dataset.id;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          var skuList = that.data.showskuAllAttrList;
          if (skuList != null && skuList.length > 0) {
            that.setData({
              addToShoppingCartHidden: false,
              joinOrBuy: "发起拼团",
              joinPromotion: 2
            });
          } else {
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + that.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&groupBuyUserId=" + groupBuyUserId + "&joinPromotion=2" + "&isTuanGou=1" + "&recommendUserId=" + that.data.recommendUserId);
          }
        }
      }
    })
  },
  /**
   * 不参加拼团，独自购买
   */
  aloneNowBuyBindTap: function (e) {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/applet/buyOrder/getSupplierOpenStoreTime',
      data: {
        "companyId": app.getExtCompanyId()
      },
      success: function (res) {
        var isBuy = res.data.isBuy;
        if (!isBuy) {
          app.showModal({
            content: res.data.openStoreTime
          });
          return;
        } else {
          var skuList = that.data.showskuAllAttrList;
          if (skuList != null && skuList.length > 0) {
            that.setData({
              addToShoppingCartHidden: false,
              joinOrBuy: "单独购买",
              joinPromotion: 1
            });
          } else {
            that.goToBuyGoods();
          }
        }
      }
    })
  },
  nowBuyGoodsBindTap: function () {
    var that = this;
    var isLogin = app.isLogin();
    if (!isLogin) {
      app.returnLogin(encodeURIComponent(encodeURIComponent(app.getCurrentPageUrlWithArgs())), 2);
      return;
    }
    app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + this.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&recommendUserId=" + that.data.recommendUserId);
  },
  /**
   * 开团查询更多
   */
  queryGroupByMoreBindTap: function () {
    this.setData({
      groupMoreHidden: false
    });
  },
  /**
   * 结算商品信息
   */
  goToBuyGoods: function () {
    var that = this;
    if (app.getTelephone() == null || app.getTelephone().length == 0 || app.getTelephone() == undefined) {
      app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + that.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&recommendUserId=" + that.data.recommendUserId);
    } else {
      var goodsDetail = that.data.goodsDetail;
      var unitList = goodsDetail.unitList;
      var commodityMoq = 0;
      var commodityWeightUnit = "";
      var commodityMultiple = 0;
      if (unitList.length == 1) {
        commodityMoq = goodsDetail.unitList[0].commodityMoq;
        commodityWeightUnit = goodsDetail.unitList[0].commodityWeightUnit;
      } else if (unitList.length == 2) {
        for (var i = 0; i < unitList.length; i++) {
          if (unitList[i].commodityWeightType == "OT") {
            commodityMoq = unitList[i].commodityMoq;
            commodityWeightUnit = unitList[i].commodityWeightUnit;
          } else if (unitList[i].commodityWeightType == "OM") {
            commodityMultiple = unitList[i].commodityMultiple;
          }
        }
      }
      if (!that.checkStock()) {
        return;
      };
      if (parseInt(commodityMoq) > 0) {
        if (that.data.buyCount + (that.data.buyOmCount * commodityMultiple) < parseInt(commodityMoq)) {
          app.showModal({
            title: '提示',
            content: "商品购买数量必须满足" + commodityMoq + commodityWeightUnit + "!"
          });
          return;
        }
      }
      wx.request({
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        method: "POST",
        data: {
                 "odbtoken":app.getodbtoken(),
        "loginToken":app.getloginToken(),
          "loginId": app.getLoginId(),
          "userRole": app.getUserRole(),
          "storeId": app.getExtStoreId(),
          "companyId": app.getExtCompanyId(),
          "phone": app.getTelephone()
        },
        url: app.projectName + '/vipCard/getUnderTheLineVipCardMessage',
        success: function (res) {
          var cardList = res.data.show_vipCardList;
          if (cardList != null && cardList.length > 0) {
            if (cardList.length == 1) { //一张会员卡自动选择
              var cardBean = cardList[0];
              app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + that.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&vipCardNo=" + cardBean.cardId + '&vipCardCode=' + cardBean.vipCardCode + "&recommendUserId=" + that.data.recommendUserId);
            } else if (cardList.length > 1) {
              //多张会员卡进行选择
              app.navigateToPage("/pages/chooseCard/chooseCard?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + that.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&recommendUserId=" + that.data.recommendUserId);
            }
          } else {
            //没有会员卡
            app.turnToPage("/pages/waitPay/waitPay?payType=2&goodsId=" + commodityId + "&storeId=" + storeId + "&buyNum=" + that.data.buyCount + "&buyOmNum=" + that.data.buyOmCount + "&skuId=" + that.data.skuId + "&joinPromotion=" + that.data.joinPromotion + "&groupBuyUserId=" + that.data.groupBuyUserId + "&recommendUserId=" + that.data.recommendUserId);
          }
        }
      })
    }
  },
  onShareTimeline: function (res) {
    var that = this;
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.data.goodsDetail.commodityName,
      query: 'goodsId=' + that.data.goodsDetail.commodityId,
      imageUrl: that.data.goodsDetail.commodityMainPic,
      success: function (res) {
        console.info("转发成功.......");
        // 转发成功
      },
      fail: function (res) {
        // 转发失败
        console.info("转发失败.......");
      }
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    var that = this;
    //隐藏弹窗模板
    that.hiddenFloatView();
    that.setData({
      shareBlackBgHidden: true,
      shareShowHidden: true
    })
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      title: that.data.goodsDetail.commodityName,
      path: '/pages/goodsDetail/goodsDetail?goodsId=' + that.data.goodsDetail.commodityId + '&recommendUserId=' + app.getClientOpenId(),
      imageUrl: that.data.goodsDetail.commodityMainPic,
      success: function (res) {
        that.setData({
          shareBlackBgHidden: true,
          shareShowHidden: true
        })
      },
      fail: function (res) {
        that.setData({
          shareBlackBgHidden: true,
          shareShowHidden: true
        })
      }
    }
  },
  //图片点击事件
  enlarge: function (event) {
    var src = event.currentTarget.dataset.src;
    var list = event.currentTarget.dataset.list;
    var imgList = [];
    for (var i = 0; i < list.length; i++) {
      imgList.push(list[i].commodityPicPath)
    }
    //图片预览
    wx.previewImage({
      current: src, // 当前显示图片的http链接
      urls: imgList // 需要预览的图片http链接列表
    })
  },
  /*查询配置*/
    /**
   * 获取商家配置信息
   */
  getSupplierSetting: function () {
    var that = this;
    wx.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      method: "POST",
      url: app.projectName + '/wholesaleStore/getStoreInfoSetting',
      data: {
        "companyId": app.getExtCompanyId(),
      },
      success: function (res) {
        var returnResult = res.data.returnResult;
        if (returnResult != null && returnResult != undefined) {
            let disWay = returnResult.distributionBean.distributionArray;
            that.setData({
              d_way:disWay
            })
        }
      }
    })
  }
})