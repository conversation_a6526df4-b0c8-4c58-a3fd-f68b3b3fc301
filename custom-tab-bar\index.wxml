<!--miniprogram/custom-tab-bar/index.wxml-->
<cover-view class="tab-bar" hidden="{{showFlag}}">
  <cover-view class="tab-bar-border"></cover-view>
  <cover-view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <cover-image src="{{selected == item.pagePath ? item.selectIconPath : item.iconPath}}" >
    </cover-image>
    <cover-view style="color: {{selected == item.pagePath ? selectedColor : color}};">{{item.textContent}}</cover-view>
    <cover-view wx:if="{{item.pagePath==6&&shopCartNum>0}}"  class="tab-bar-dot">{{shopCartNum}}</cover-view> 
  </cover-view>
</cover-view>